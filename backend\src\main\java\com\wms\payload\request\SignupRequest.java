package com.wms.payload.request;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.Set;

@Data
public class SignupRequest {
    @NotBlank
    @Size(min = 3, max = 20)
    private String username;

    @NotBlank
    @Size(max = 50)
    @Email
    private String email;

    private Set<String> roles;

    @NotBlank
    @Size(min = 6, max = 40)
    private String password;
    
    @NotBlank
    @Size(max = 20)
    private String contact;

    @NotBlank(message = "Full Name is required")
    @Size(max = 50)
    private String fullName;

    // Client-specific fields
    private String companyName;
    private String designation;
    private String altDesignation;
    private String altContact;

    // Manager-specific fields
    private String department;

    // Driver-specific fields
    private String licenseNumber;
} 