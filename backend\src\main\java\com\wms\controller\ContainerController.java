package com.wms.controller;

import com.wms.dto.ApiResponse;
import com.wms.dto.PortnetEtaUpdateResponse;
import com.wms.entity.Container;
import com.wms.entity.ContainerStatus;
import com.wms.exception.ResourceNotFoundException;
import com.wms.security.services.UserDetailsImpl;
import com.wms.service.ContainerService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

@RestController
@CrossOrigin(origins = "*")
@PreAuthorize("hasAnyRole('ADMIN', 'MANAGER', 'CLIENT')")
public class ContainerController {

    private final ContainerService containerService;

    @Autowired
    public ContainerController(ContainerService containerService) {
        this.containerService = containerService;
    }

    @GetMapping({"/api/containers", "/containers"})
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER', 'CLIENT')")
    public ResponseEntity<ApiResponse<List<Container>>> getAllContainers() {
        try {
            // Get the current authenticated user
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            UserDetailsImpl userDetails = (UserDetailsImpl) authentication.getPrincipal();
            
            List<Container> containers;
            
            // If the user is a client, only return their containers
            if (userDetails.getAuthorities().stream().anyMatch(a -> a.getAuthority().equals("ROLE_CLIENT"))) {
                containers = containerService.getContainersByUsername(userDetails.getUsername());
            } else {
                // For admins and managers, return all containers
                containers = containerService.getAllContainers();
            }
            
            return ResponseEntity.ok(ApiResponse.success(containers));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    @GetMapping({"/api/containers/{containerNo}", "/containers/{containerNo}"})
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER', 'CLIENT')")
    public ResponseEntity<ApiResponse<Container>> getContainerByNo(@PathVariable String containerNo) {
        try {
            Container container = containerService.getContainerByNo(containerNo);
            
            // Get the current authenticated user
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            UserDetailsImpl userDetails = (UserDetailsImpl) authentication.getPrincipal();
            
            // If the user is a client, they can only view their own containers
            if (userDetails.getAuthorities().stream().anyMatch(a -> a.getAuthority().equals("ROLE_CLIENT"))) {
                if (!container.getClient().getUsername().equals(userDetails.getUsername())) {
                    return ResponseEntity.status(HttpStatus.FORBIDDEN)
                            .body(ApiResponse.error("You can only view your own containers"));
                }
            }
            
            return ResponseEntity.ok(ApiResponse.success(container));
        } catch (ResourceNotFoundException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    @GetMapping({"/api/containers/my-containers", "/containers/my-containers"})
    @PreAuthorize("hasRole('CLIENT')")
    public ResponseEntity<ApiResponse<List<Container>>> getMyContainers() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        UserDetailsImpl userDetails = (UserDetailsImpl) authentication.getPrincipal();
        
        List<Container> containers = containerService.getContainersByUsername(userDetails.getUsername());
        return ResponseEntity.ok(ApiResponse.success(containers));
    }

    @GetMapping({"/api/containers/status/{status}", "/containers/status/{status}"})
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER')")
    public ResponseEntity<ApiResponse<List<Container>>> getContainersByStatus(@PathVariable ContainerStatus status) {
        List<Container> containers = containerService.getContainersByStatus(status);
        return ResponseEntity.ok(ApiResponse.success(containers));
    }

    @PostMapping({"/api/containers", "/containers"})
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER', 'CLIENT')")
    public ResponseEntity<ApiResponse<Container>> createContainer(@Valid @RequestBody Container container) {
        try {
            // Get the current authenticated user
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            UserDetailsImpl userDetails = (UserDetailsImpl) authentication.getPrincipal();
            
            // If the user is a client, they can only create containers for themselves
            if (userDetails.getAuthorities().stream().anyMatch(a -> a.getAuthority().equals("ROLE_CLIENT"))) {
                if (!container.getClient().getUsername().equals(userDetails.getUsername())) {
                    return ResponseEntity.status(HttpStatus.FORBIDDEN)
                            .body(ApiResponse.error("You can only create containers for yourself"));
                }
            }
            
            Container createdContainer = containerService.createContainer(container);
            return ResponseEntity.ok(ApiResponse.success("Container created successfully", createdContainer));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    @PutMapping({"/api/containers/{containerNo}", "/containers/{containerNo}"})
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER', 'CLIENT')")
    public ResponseEntity<ApiResponse<?>> updateContainer(
            @PathVariable String containerNo,
            @Valid @RequestBody Container container) {
        try {
            // Get the current container
            Container existingContainer = containerService.getContainerByNo(containerNo);
            
            // Get the current authenticated user
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            UserDetailsImpl userDetails = (UserDetailsImpl) authentication.getPrincipal();
            
            // If the user is a client, they can only update their own containers
            if (userDetails.getAuthorities().stream().anyMatch(a -> a.getAuthority().equals("ROLE_CLIENT"))) {
                if (!existingContainer.getClient().getUsername().equals(userDetails.getUsername())) {
                    return ResponseEntity.status(HttpStatus.FORBIDDEN)
                            .body(ApiResponse.error("You can only update your own containers"));
                }
            }
            
            // Check if portnet ETA has changed
            if (container.getPortnetEta() != null && 
                (existingContainer.getPortnetEta() == null || 
                !container.getPortnetEta().equals(existingContainer.getPortnetEta()))) {
                
                // Update all containers with the same vessel/voyage number
                List<Container> updatedContainers = containerService.updatePortnetEtaForVesselVoyage(
                    existingContainer.getVesselVoyageNo(), container.getPortnetEta());
                
                // Create response DTO
                PortnetEtaUpdateResponse updateResponse = PortnetEtaUpdateResponse.fromContainers(
                    updatedContainers, container.getPortnetEta());
                
                // Return success with message about updating all related containers
                return ResponseEntity.ok(ApiResponse.success(
                    "Container updated successfully. Portnet ETA updated for all " + 
                    updatedContainers.size() + " containers with the same vessel/voyage number.",
                    updateResponse));
            }
            
            Container updatedContainer = containerService.updateContainer(containerNo, container);
            return ResponseEntity.ok(ApiResponse.success("Container updated successfully", updatedContainer));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    @PutMapping({"/api/containers/{containerNo}/status", "/containers/{containerNo}/status"})
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER')")
    public ResponseEntity<ApiResponse<Container>> updateContainerStatus(
            @PathVariable String containerNo,
            @RequestParam ContainerStatus status) {
        try {
            Container updatedContainer = containerService.updateContainerStatus(containerNo, status);
            return ResponseEntity.ok(ApiResponse.success("Container status updated successfully", updatedContainer));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    @PutMapping({"/api/containers/vessel-voyage/{vesselVoyageNo}/portnet-eta", "/containers/vessel-voyage/{vesselVoyageNo}/portnet-eta"})
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER')")
    public ResponseEntity<ApiResponse<PortnetEtaUpdateResponse>> updatePortnetEtaForVesselVoyage(
            @PathVariable String vesselVoyageNo,
            @RequestParam LocalDateTime portnetEta) {
        try {
            List<Container> updatedContainers = containerService.updatePortnetEtaForVesselVoyage(vesselVoyageNo, portnetEta);
            
            // Create response DTO
            PortnetEtaUpdateResponse updateResponse = PortnetEtaUpdateResponse.fromContainers(
                updatedContainers, portnetEta);
            
            return ResponseEntity.ok(ApiResponse.success(
                "Portnet ETA updated for " + updatedContainers.size() + " containers with vessel/voyage number: " + vesselVoyageNo,
                updateResponse));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    @GetMapping({"/api/containers/count", "/containers/count"})
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER')")
    public ResponseEntity<ApiResponse<Long>> getContainersCount() {
        Long count = containerService.getContainersCount();
        return ResponseEntity.ok(ApiResponse.success(count));
    }

    @DeleteMapping({"/api/containers/{containerNo}", "/containers/{containerNo}"})
    public ResponseEntity<ApiResponse<Void>> deleteContainer(@PathVariable String containerNo) {
        try {
            containerService.deleteContainer(containerNo);
            return ResponseEntity.ok(ApiResponse.success("Container deleted successfully", null));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }
} 