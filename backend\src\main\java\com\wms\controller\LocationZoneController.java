package com.wms.controller;

import com.wms.dto.ApiResponse;
import com.wms.entity.LocationZone;
import com.wms.service.LocationZoneService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@CrossOrigin(origins = "*")
@RequestMapping("/api/location-zones")
@PreAuthorize("hasRole('ADMIN')")
public class LocationZoneController {

    private final LocationZoneService locationZoneService;

    @Autowired
    public LocationZoneController(LocationZoneService locationZoneService) {
        this.locationZoneService = locationZoneService;
    }

    @GetMapping
    public ResponseEntity<ApiResponse<List<LocationZone>>> getAllLocationZones() {
        try {
            List<LocationZone> locationZones = locationZoneService.getAllLocationZones();
            return ResponseEntity.ok(ApiResponse.success(locationZones));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Error retrieving location zones: " + e.getMessage()));
        }
    }

    @GetMapping("/active")
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER')")
    public ResponseEntity<ApiResponse<List<LocationZone>>> getAllActiveLocationZones() {
        try {
            List<LocationZone> activeZones = locationZoneService.getAllActiveLocationZones();
            return ResponseEntity.ok(ApiResponse.success(activeZones));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Error retrieving active location zones: " + e.getMessage()));
        }
    }

    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<LocationZone>> getLocationZoneById(@PathVariable Long id) {
        try {
            return locationZoneService.getLocationZoneById(id)
                    .map(locationZone -> ResponseEntity.ok(ApiResponse.success(locationZone)))
                    .orElse(ResponseEntity.status(HttpStatus.NOT_FOUND)
                            .body(ApiResponse.error("Location zone not found with id: " + id)));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Error retrieving location zone: " + e.getMessage()));
        }
    }

    @PostMapping
    public ResponseEntity<ApiResponse<LocationZone>> createLocationZone(@Valid @RequestBody LocationZone locationZone) {
        try {
            LocationZone createdZone = locationZoneService.createLocationZone(locationZone);
            return ResponseEntity.status(HttpStatus.CREATED)
                    .body(ApiResponse.success("Location zone created successfully", createdZone));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Error creating location zone: " + e.getMessage()));
        }
    }

    @PutMapping("/{id}")
    public ResponseEntity<ApiResponse<LocationZone>> updateLocationZone(
            @PathVariable Long id,
            @Valid @RequestBody LocationZone locationZone) {
        try {
            LocationZone updatedZone = locationZoneService.updateLocationZone(id, locationZone);
            return ResponseEntity.ok(ApiResponse.success("Location zone updated successfully", updatedZone));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Error updating location zone: " + e.getMessage()));
        }
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<ApiResponse<String>> deleteLocationZone(@PathVariable Long id) {
        try {
            locationZoneService.deleteLocationZone(id);
            return ResponseEntity.ok(ApiResponse.success("Location zone deleted successfully"));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Error deleting location zone: " + e.getMessage()));
        }
    }

    @GetMapping("/postal-code/{postalCode}")
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER')")
    public ResponseEntity<ApiResponse<LocationZone>> getLocationZoneByPostalCode(@PathVariable String postalCode) {
        try {
            LocationZone locationZone = locationZoneService.determineLocationZoneByPostalCode(postalCode);
            return ResponseEntity.ok(ApiResponse.success(locationZone));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Error finding location zone by postal code: " + e.getMessage()));
        }
    }

    @GetMapping("/abbreviation/{locationName}")
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER', 'CLIENT')")
    public ResponseEntity<ApiResponse<String>> getLocationAbbreviation(@PathVariable String locationName) {
        try {
            String abbreviation = locationZoneService.getLocationAbbreviation(locationName);
            return ResponseEntity.ok(ApiResponse.success(abbreviation));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Error getting location abbreviation: " + e.getMessage()));
        }
    }
} 