package com.wms.controller;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.ResponseEntity;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.wms.repository.ContainerRepository;
import com.wms.repository.ManifestRepository;
import com.wms.repository.RoleRepository;
import com.wms.repository.UserRepo.UserRepository;

@RestController
@RequestMapping("/api/debug")
public class DebugController {
    
    private static final Logger logger = LoggerFactory.getLogger(DebugController.class);
    
    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private RoleRepository roleRepository;
    
    @Autowired
    private ContainerRepository containerRepository;
    
    @Autowired
    private ManifestRepository manifestRepository;
    
    @GetMapping("/database-check")
    public ResponseEntity<?> checkDatabaseStatus() {
        logger.info("Performing database health check");
        
        Map<String, Object> result = new HashMap<>();
        result.put("status", "OK");
        
        // Count records in main tables
        long userCount = userRepository.count();
        long roleCount = roleRepository.count();
        long containerCount = containerRepository.count();
        long manifestCount = manifestRepository.count();
        
        Map<String, Long> counts = new HashMap<>();
        counts.put("users", userCount);
        counts.put("roles", roleCount);
        counts.put("containers", containerCount);
        counts.put("manifests", manifestCount);
        
        result.put("counts", counts);
        
        // Add system information
        Map<String, String> systemInfo = new HashMap<>();
        systemInfo.put("java.version", System.getProperty("java.version"));
        systemInfo.put("os.name", System.getProperty("os.name"));
        systemInfo.put("working.directory", System.getProperty("user.dir"));
        
        result.put("systemInfo", systemInfo);
        
        logger.info("Database check complete. Users: {}, Roles: {}, Containers: {}, Manifests: {}", 
                userCount, roleCount, containerCount, manifestCount);
        
        return ResponseEntity.ok(result);
    }
    
    @GetMapping("/reload-data")
    public ResponseEntity<?> reloadData() {
        logger.info("Starting data reload process");
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            // Get the SQL script content
            ClassPathResource resource = new ClassPathResource("data.sql");
            String sqlScript;
            
            try (BufferedReader reader = new BufferedReader(
                    new InputStreamReader(resource.getInputStream(), StandardCharsets.UTF_8))) {
                sqlScript = reader.lines().collect(Collectors.joining("\n"));
            }
            
            // Split on semicolons and execute each statement
            String[] statements = sqlScript.split(";");
            int executedCount = 0;
            
            for (String statement : statements) {
                String trimmedStatement = statement.trim();
                if (!trimmedStatement.isEmpty()) {
                    try {
                        jdbcTemplate.execute(trimmedStatement);
                        executedCount++;
                    } catch (Exception e) {
                        logger.error("Error executing SQL statement: {}", trimmedStatement, e);
                        // Continue with other statements
                    }
                }
            }
            
            // Get updated counts
            long userCount = userRepository.count();
            long roleCount = roleRepository.count();
            long containerCount = containerRepository.count();
            long manifestCount = manifestRepository.count();
            
            result.put("status", "SUCCESS");
            result.put("message", "Data reload completed successfully");
            result.put("statementsExecuted", executedCount);
            
            Map<String, Long> counts = new HashMap<>();
            counts.put("users", userCount);
            counts.put("roles", roleCount);
            counts.put("containers", containerCount);
            counts.put("manifests", manifestCount);
            
            result.put("counts", counts);
            
            logger.info("Data reload complete. Executed {} SQL statements. Users: {}, Roles: {}, Containers: {}, Manifests: {}", 
                    executedCount, userCount, roleCount, containerCount, manifestCount);
            
        } catch (IOException e) {
            logger.error("Error reading data.sql file", e);
            result.put("status", "ERROR");
            result.put("message", "Failed to read data.sql file: " + e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        } catch (Exception e) {
            logger.error("Error during data reload", e);
            result.put("status", "ERROR");
            result.put("message", "Failed to reload data: " + e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        }
        
        return ResponseEntity.ok(result);
    }
} 