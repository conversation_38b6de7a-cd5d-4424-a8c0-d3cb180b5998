package com.wms.service.impl;

import com.wms.entity.user.Client;
import com.wms.entity.user.UserStatus;
import com.wms.exception.ResourceNotFoundException;
import com.wms.repository.UserRepo.ClientRepository;
import com.wms.service.UserServices.ClientService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Service
@Transactional
public class ClientServiceImpl implements ClientService {

    private final ClientRepository clientRepository;

    @Autowired
    public ClientServiceImpl(ClientRepository clientRepository) {
        this.clientRepository = clientRepository;
    }

    @Override
    public List<Client> getAllClients() {
        return clientRepository.findAll();
    }

    @Override
    public Optional<Client> getClientByUsername(String username) {
        return clientRepository.findByUsername(username);
    }

    @Override
    public Client createClient(Client client) {
        if (clientRepository.existsByEmail(client.getEmail())) {
            throw new IllegalArgumentException("Email already exists");
        }
        if (clientRepository.existsByUsername(client.getUsername())) {
            throw new IllegalArgumentException("Username already exists");
        }
        client.setStatus(UserStatus.ACTIVE);
        return clientRepository.save(client);
    }

    @Override
    public Client updateClient(String username, Client client) {
        Client existingClient = clientRepository.findByUsername(username)
                .orElseThrow(() -> new IllegalArgumentException("Client not found with username: " + username));
        
        if (existingClient.getStatus() == UserStatus.INACTIVE) {
            throw new IllegalArgumentException("Cannot update an inactive client");
        }

        // Check if email is being changed and if new email already exists
        if (!existingClient.getEmail().equals(client.getEmail()) && 
            clientRepository.existsByEmail(client.getEmail())) {
            throw new IllegalArgumentException("Email already exists");
        }

        // Username cannot be changed as it's the primary key
        if (!username.equals(client.getUsername())) {
            throw new IllegalArgumentException("Username cannot be changed");
        }

        // Update client-specific fields
        existingClient.setCompanyName(client.getCompanyName());
        existingClient.setDesignation(client.getDesignation());
        existingClient.setAltContact(client.getAltContact());
        existingClient.setAltDesignation(client.getAltDesignation());
        
        // Update common user fields
        existingClient.setEmail(client.getEmail());
        existingClient.setContact(client.getContact());
        
        // Only update password if a new one is provided
        if (client.getPassword() != null && !client.getPassword().isEmpty()) {
            existingClient.setPassword(client.getPassword());
        }

        return clientRepository.save(existingClient);
    }

    @Override
    public Client deactivateClient(String username) {
        Client client = clientRepository.findByUsername(username)
                .orElseThrow(() -> new IllegalArgumentException("Client not found with username: " + username));

        if (client.getStatus() == UserStatus.INACTIVE) {
            throw new IllegalArgumentException("Client is already inactive");
        }

        client.setStatus(UserStatus.INACTIVE);
        return clientRepository.save(client);
    }

    @Override
    public boolean existsByEmail(String email) {
        return clientRepository.existsByEmail(email);
    }

    @Override
    public boolean existsByUsername(String username) {
        return clientRepository.existsByUsername(username);
    }
} 