package com.wms.service.UserServices;

import com.wms.entity.user.Admin;
import com.wms.entity.user.Manager;
import com.wms.entity.user.Client;
import com.wms.entity.user.Driver;
import com.wms.entity.user.User;
import java.util.List;
import java.util.Optional;

public interface AdminService {
    // Admin specific operations
    List<Admin> getAllAdmins();
    Admin getAdminByUsername(String username);
    Admin createAdmin(Admin admin);
    Admin updateAdmin(String username, Admin admin);
    Admin updateAdmin(String username, Admin admin, String newPassword);
    Admin deactivateAdmin(String username);
    Admin activateAdmin(String username);

    // Manager operations
    List<Manager> getAllManagers();
    Manager getManagerByUsername(String username);
    Manager createManager(Manager manager);
    Manager updateManager(String username, Manager manager);
    Manager updateManager(String username, Manager manager, String newPassword);
    Manager deactivateManager(String username);
    Manager activateManager(String username);

    // Client operations
    List<Client> getAllClients();
    Client getClientByUsername(String username);
    Client createClient(Client client);
    Client updateClient(String username, Client client);
    Client updateClient(String username, Client client, String newPassword);
    Client deactivateClient(String username);
    Client activateClient(String username);

    // Driver operations
    List<Driver> getAllDrivers();
    Driver getDriverByUsername(String username);
    Driver createDriver(Driver driver);
    Driver updateDriver(String username, Driver driver);
    Driver updateDriver(String username, Driver driver, String newPassword);
    Driver deactivateDriver(String username);
    Driver activateDriver(String username);

    // Generic user operations
    List<User> getAllUsers();
    User getUserByUsername(String username);
    User deactivateUser(String username);
    User activateUser(String username);
    Long getUsersCount();
    void changeUserPassword(String username, String currentPassword, String newPassword);
}