import { Client, Driver } from './User';
import { Container } from './Container';
import { <PERSON><PERSON><PERSON> } from './Pallet';

export enum ManifestStatus {
  CREATED = 'CREATED',
  ETA_TO_WAREHOUSE = 'ETA_TO_WAREHOUSE',
  ARRIVED = 'ARRIVED',
  ON_HOLD = 'ON_HOLD',
  INBOUNDING = 'INBOUNDING',
  INBOUNDED_TO_WAREHOUSE = 'INBOUNDED_TO_WAREHOUSE',
  READY_TO_DELIVER = 'READY_TO_DELIVER',
  PENDING_DELIVER = 'PENDING_DELIVER',
  DELIVERING = 'DELIVERING',
  DELIVERED = 'DELIVERED',
  DISCREPANCY = 'DISCREPANCY'
}

export enum TrackingLogActionType {
  CREATED = 'CREATED',
  UPDATED = 'UPDATED',
  STATUS_CHANGED = 'STATUS_CHANGED',
  ASSIGNED = 'ASSIGNED',
  DELIVERED = 'DELIVERED',
  DELETED = 'DELETED',
  PALLET_ADDED = 'PALLET_ADDED',
  PALLET_DELETED = 'PALLET_DELETED'
}

export interface ManifestTrackingHistory {
  id: number;
  trackingNo: string;
  previousStatus?: string;
  newStatus: string;
  fieldName?: string;
  oldValue?: string;
  newValue?: string;
  actionType: TrackingLogActionType;
  updatedBy: string;
  updatedAt: string;
  remarks?: string;
  ipAddress?: string;
  userAgent?: string;
}

// Legacy interface for backward compatibility (will be phased out)
export interface LegacyManifestTrackingHistory {
  id: number;
  trackingNo: string;
  status: ManifestStatus;
  previousStatus?: ManifestStatus;
  newStatus?: ManifestStatus;
  updatedAt: string;
  updatedBy: string;
  changedAt?: string;
  changedBy?: string;
  remarks?: string;
}

export interface Manifest {
  trackingNo: string;
  client: Client;
  container: Container;
  driver: Driver;
  sequenceNo: number;
  internalId?: string;
  status: ManifestStatus;
  customerName: string;
  phoneNo: string;
  address: string;
  postalCode: string;
  country: string;
  pieces: number;
  inboundPieces?: number; // Total pieces inbounded from pallets
  cbm: number;
  weight: number;
  createdDate?: string;
  noOfPallets?: number;
  actualPalletsCount?: number;
  noOfPalletsFromObjects?: number;
  pallets?: Pallet[];
  location?: string;
  deliveryDate?: string | null;
  timeSlot?: string | null; // Time slot ID (morning_12, morning_13, afternoon)
  deliveredDate?: string | null;
  deliveryVehicle?: string;
  driverRemarks?: string;
  remarks?: string;
  trackingHistory?: ManifestTrackingHistory[];
} 