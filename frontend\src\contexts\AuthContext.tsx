import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import axios from 'axios';
import { TOKEN_EXPIRED_EVENT } from '../config/api.config';

// Define a custom event name for session expiration
export const SESSION_EXPIRED_EVENT = 'session_expired';

interface User {
  token: string;
  id: number;
  username: string;
  email: string;
  roles: string[];
}

interface AuthContextType {
  currentUser: User | null;
  setCurrentUser: React.Dispatch<React.SetStateAction<User | null>>;
  logout: () => void;
  hasRole: (role: string) => boolean;
  isAuthenticated: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [currentUser, setCurrentUser] = useState<User | null>(() => {
    // Initialize state from localStorage
    const savedUser = localStorage.getItem('currentUser');
    return savedUser ? JSON.parse(savedUser) : null;
  });

  useEffect(() => {
    console.log('AuthProvider state updated:', {
      hasCurrentUser: !!currentUser,
      username: currentUser?.username,
      roles: currentUser?.roles
    });
    
    if (currentUser) {
      // Save to localStorage when user changes
      localStorage.setItem('currentUser', JSON.stringify(currentUser));
      // Set the Authorization header for API requests
      axios.defaults.headers.common['Authorization'] = `Bearer ${currentUser.token}`;
    } else {
      // Remove from localStorage when user is null
      localStorage.removeItem('currentUser');
      // Remove the Authorization header
      delete axios.defaults.headers.common['Authorization'];
    }
  }, [currentUser]);

  // Add effect to listen for token expiration events
  useEffect(() => {
    const handleTokenExpired = () => {
      console.log('Token expired event received. Logging out user.');
      
      // Dispatch a session expired event that can be caught by components to show notifications
      if (currentUser) {
        window.dispatchEvent(new CustomEvent(SESSION_EXPIRED_EVENT, {
          detail: { message: 'Your session has expired. Please log in again.' }
        }));
      }
      
      // Log out the user
      logout();
    };

    // Add event listener for token expiration
    window.addEventListener(TOKEN_EXPIRED_EVENT, handleTokenExpired);

    // Clean up the event listener when component unmounts
    return () => {
      window.removeEventListener(TOKEN_EXPIRED_EVENT, handleTokenExpired);
    };
  }, [currentUser]); // Add currentUser as dependency to access it in the handler

  const logout = () => {
    console.log('Logging out user:', currentUser?.username);
    localStorage.removeItem('currentUser');
    setCurrentUser(null);
  };

  const hasRole = (role: string): boolean => {
    const hasRole = currentUser?.roles?.includes(role) || false;
    console.log('Checking role:', {
      role,
      hasRole,
      currentRoles: currentUser?.roles
    });
    return hasRole;
  };

  const value = {
    currentUser,
    setCurrentUser,
    logout,
    hasRole,
    isAuthenticated: !!currentUser,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}; 