{"version": 2, "buildCommand": "npm run build", "outputDirectory": "frontend/dist", "framework": "vite", "rewrites": [{"source": "/login", "destination": "/index.html"}, {"source": "/dashboard", "destination": "/index.html"}, {"source": "/(.*)", "destination": "/index.html"}], "headers": [{"source": "/(.*)", "headers": [{"key": "Cache-Control", "value": "s-maxage=1, stale-while-revalidate"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}]}]}