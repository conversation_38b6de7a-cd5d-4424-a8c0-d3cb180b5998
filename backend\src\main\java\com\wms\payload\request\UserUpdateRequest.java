package com.wms.payload.request;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * DTO for updating user information without requiring a password
 */
@Data
public class UserUpdateRequest {
    
    @NotBlank(message = "Username is required")
    @Size(max = 20)
    private String username;

    @NotBlank(message = "Email is required")
    @Size(max = 50)
    @Email
    private String email;

    // Password is optional for updates
    @Size(max = 120)
    private String password;

    @NotBlank(message = "Contact is required")
    @Size(max = 20)
    private String contact;
    
    @NotBlank(message = "Full Name is required")
    @Size(max = 50)
    private String fullName;
    
    // Fields for Client
    private String companyName;
    private String designation;
    private String altDesignation;
    private String altContact;
    
    // Fields for Manager
    private String department;
    
    // Fields for Driver
    private String licenseNumber;
} 