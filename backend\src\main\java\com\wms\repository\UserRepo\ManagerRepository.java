package com.wms.repository.UserRepo;

import com.wms.entity.user.Manager;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface ManagerRepository extends JpaRepository<Manager, String> {
    @Query("SELECT m FROM Manager m LEFT JOIN FETCH m.roles WHERE m.email = :email")
    Optional<Manager> findByEmail(String email);

    @Query("SELECT m FROM Manager m LEFT JOIN FETCH m.roles WHERE m.username = :username")
    Optional<Manager> findByUsername(String username);

    @Query("SELECT COUNT(m) > 0 FROM Manager m WHERE m.email = :email")
    boolean existsByEmail(String email);

    @Query("SELECT COUNT(m) > 0 FROM Manager m WHERE m.username = :username")
    boolean existsByUsername(String username);
} 