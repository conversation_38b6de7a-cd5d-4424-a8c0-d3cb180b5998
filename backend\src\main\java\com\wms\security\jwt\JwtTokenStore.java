package com.wms.security.jwt;

import io.jsonwebtoken.Claims;
import java.util.Optional;

/**
 * Interface for storing and retrieving JWT tokens
 */
public interface JwtTokenStore {
    /**
     * Store a token with associated username and claims
     *
     * @param token JWT token
     * @param username User username
     * @param claims Token claims
     */
    void storeToken(String token, String username, Claims claims);
    
    /**
     * Get username associated with token
     *
     * @param token JWT token
     * @return Optional containing username if token is valid
     */
    Optional<String> getUsernameFromToken(String token);
    
    /**
     * Get claims associated with token
     *
     * @param token JWT token
     * @return Optional containing claims if token is valid
     */
    Optional<Claims> getClaimsFromToken(String token);
    
    /**
     * Validate if token exists in store
     *
     * @param token JWT token
     * @return true if token is valid
     */
    boolean validateToken(String token);
    
    /**
     * Invalidate a token
     *
     * @param token JWT token to invalidate
     */
    void invalidateToken(String token);
} 