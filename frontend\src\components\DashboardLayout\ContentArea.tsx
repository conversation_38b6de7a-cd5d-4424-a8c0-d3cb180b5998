import React from 'react';
import { Box, useTheme } from '@mui/material';
import { useLayoutContext } from './LayoutContext';

interface ContentAreaProps {
  children: React.ReactNode;
}

const ContentArea: React.FC<ContentAreaProps> = ({ children }) => {
  const { drawerWidth } = useLayoutContext();
  const theme = useTheme();

  return (
    <Box
      component="main"
      role="main"
      aria-label="Main content"
      sx={{
        flexGrow: 1,
        width: { 
          xs: '100%',
          sm: `100%`
        },
        minWidth: 0,
        marginLeft: { 
          xs: 0,
          sm: `${drawerWidth}px`
        },
        marginTop: '48px', // Dense AppBar height
        minHeight: 'calc(100vh - 48px)', // Full height minus AppBar
        backgroundColor: theme.palette.grey[50], // Use theme color
        display: 'flex',
        flexDirection: 'column',
        position: 'relative',
        overflow: 'auto',
        padding: {
          xs: theme.spacing(0.5),
          sm: theme.spacing(0.75),
          md: theme.spacing(2),
        },
        WebkitOverflowScrolling: 'touch',
        scrollBehavior: 'smooth',
        transition: theme.transitions.create(['width', 'margin-left'], {
          easing: theme.transitions.easing.easeInOut,
          duration: theme.transitions.duration.standard,
        }),
        willChange: 'margin-left',
        '&:focus': {
          outline: 'none',
        },
        '&:focus-visible': {
          outline: `2px solid ${theme.palette.primary.main}`,
        },
        fontFamily: theme.typography.fontFamily,
        lineHeight: theme.typography.body1.lineHeight,
        '& > *': {
          width: '100%',
          minWidth: '100%',
          boxSizing: 'border-box',
          flexGrow: 1,
          display: 'flex',
          flexDirection: 'column'
        }
      }}
    >
      {children}
    </Box>
  );
};

export default ContentArea; 