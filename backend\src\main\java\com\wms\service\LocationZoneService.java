package com.wms.service;

import com.wms.entity.LocationZone;
import java.util.List;
import java.util.Optional;

public interface LocationZoneService {
    List<LocationZone> getAllLocationZones();
    
    List<LocationZone> getAllActiveLocationZones();
    
    Optional<LocationZone> getLocationZoneById(Long id);
    
    Optional<LocationZone> getLocationZoneByName(String name);
    
    LocationZone createLocationZone(LocationZone locationZone);
    
    LocationZone updateLocationZone(Long id, LocationZone locationZone);
    
    void deleteLocationZone(Long id);
    
    LocationZone determineLocationZoneByPostalCode(String postalCode);
    
    boolean existsByName(String name);
    
    /**
     * Gets the abbreviation for a location zone by name
     * @param locationName The name of the location zone
     * @return The abbreviation if found, otherwise a generated abbreviation based on the name
     */
    String getLocationAbbreviation(String locationName);
} 