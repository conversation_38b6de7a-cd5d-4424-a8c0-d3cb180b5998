
<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Manifest Template</title>
  <style>
    table { border-collapse: collapse; width: 100%; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; font-weight: bold; }
    th.required { background-color: #ffcccc; }
    .instructions { margin-top: 20px; background-color: #f9f9f9; padding: 10px; border: 1px solid #ddd; }
  </style>
</head>
<body>
  <h1>Manifest Template</h1>
  <p>Use this template to prepare your manifest data. Save as Excel or CSV when complete.</p>
  
  <table>
    <tr>
      <th class="required">trackingNo (REQUIRED)</th><th class="required">containerNo (REQUIRED)</th><th class="required">clientUsername (REQUIRED)</th><th class="required">customerName (REQUIRED)</th><th class="required">phoneNo (REQUIRED)</th><th class="required">address (REQUIRED)</th><th class="required">postalCode (REQUIRED)</th><th class="required">country (REQUIRED)</th><th class="required">pieces (REQUIRED)</th><th class="required">cbm (REQUIRED)</th><th class="required">weight (REQUIRED)</th><th>noOfPallets</th><th>location</th><th>deliveryVehicle</th><th>deliveryDate</th><th>remarks</th>
    </tr>
    <tr><td>TRK00001234</td><td>CONT1234567</td><td>client1</td><td>John Doe</td><td>+123456789</td><td>123 Main St</td><td>12345</td><td>Singapore</td><td>5</td><td>1.2</td><td>50</td><td>2</td><td>Zone A</td><td>Truck</td><td>2023-12-31</td><td>Handle with care</td></tr><tr><td>TRK00005678</td><td>CONT1234567</td><td>client1</td><td>Jane Smith</td><td>+987654321</td><td>456 Second Ave</td><td>67890</td><td>Singapore</td><td>2</td><td>0.5</td><td>25</td><td>1</td><td>Zone B</td><td>Van</td><td>2023-12-31</td><td>Fragile</td></tr>
  </table>
  
  <div class="instructions">
    <h2>Instructions</h2>
    <p>Fields marked with (REQUIRED) must be filled in for all manifests.</p>
    <p>You can download this page and open it in Excel, or copy the data into your own spreadsheet.</p>
  </div>
</body>
</html>
    