package com.wms.repository;

import com.wms.entity.Pallet;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface PalletRepository extends JpaRepository<Pallet, String> {
    
    List<Pallet> findByManifestTrackingNo(String manifestTrackingNo);
    
    @Query("SELECT COALESCE(MAX(p.palletSequence), 0) FROM Pallet p WHERE p.manifest.trackingNo = :manifestTrackingNo")
    Integer findMaxPalletSequenceByManifestTrackingNo(@Param("manifestTrackingNo") String manifestTrackingNo);
    
    void deleteByManifestTrackingNo(String manifestTrackingNo);
    
    @Query("SELECT COUNT(p) FROM Pallet p WHERE p.manifest.trackingNo = :manifestTrackingNo")
    Long countByManifestTrackingNo(@Param("manifestTrackingNo") String manifestTrackingNo);
} 