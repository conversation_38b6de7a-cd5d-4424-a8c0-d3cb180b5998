import React, { useState, useEffect } from 'react';
import { Box, Button, Typography, Paper, Alert, CircularProgress } from '@mui/material';
import usePageTitle from '../hooks/usePageTitle';
import API_CONFIG from '../config/api.config';

const TestApi: React.FC = () => {
  const [status, setStatus] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Set page title
  usePageTitle('API Test');

  // Display the current API configuration
  useEffect(() => {
    console.log('API_CONFIG in TestApi component:', API_CONFIG);
  }, []);

  const testApiConnection = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // Try to fetch without authentication first
      const url = `${API_CONFIG.baseUrl}/api/auth/signin`;
      console.log('Testing API connection to:', url);
      
      // Use fetch instead of axios to get more detailed error messages
      const response = await fetch(url, {
        method: 'OPTIONS', // Just send an OPTIONS request to check CORS
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      console.log('API Response status:', response.status);
      console.log('API Response headers:', response.headers);
      
      setStatus(`Connection successful! Status: ${response.status}`);
    } catch (err: any) {
      console.error('API connection error:', err);
      setError(`Connection failed: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" sx={{ mb: 3 }}>API Connection Test</Typography>
      
      <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>Current API Configuration</Typography>
        <Box sx={{ bgcolor: '#f5f5f5', p: 2, borderRadius: 1, mb: 2, fontFamily: 'monospace' }}>
          <pre>{JSON.stringify(API_CONFIG, null, 2)}</pre>
        </Box>
        
        <Button 
          variant="contained" 
          onClick={testApiConnection}
          disabled={loading}
          sx={{ mt: 2 }}
        >
          {loading ? <CircularProgress size={24} sx={{ mr: 1 }} /> : 'Test API Connection'}
        </Button>
        
        {status && (
          <Alert severity="success" sx={{ mt: 2 }}>
            {status}
          </Alert>
        )}
        
        {error && (
          <Alert severity="error" sx={{ mt: 2 }}>
            {error}
          </Alert>
        )}
      </Paper>
    </Box>
  );
};

export default TestApi; 