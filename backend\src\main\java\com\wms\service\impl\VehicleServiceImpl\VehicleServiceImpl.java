package com.wms.service.impl.VehicleServiceImpl;

import com.wms.dto.VehicleDTO;
import com.wms.entity.vehicle.Vehicle;
import com.wms.entity.vehicle.VehicleStatus;
import com.wms.entity.vehicle.VehicleType;
import com.wms.entity.user.Driver;
import com.wms.exception.ResourceNotFoundException;
import com.wms.repository.VehicleRepo.VehicleRepository;
import com.wms.repository.UserRepo.DriverRepository;
import com.wms.service.VehicleServices.VehicleService;
import com.wms.service.VehicleServices.VehicleTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class VehicleServiceImpl implements VehicleService {

    @Autowired
    private VehicleRepository vehicleRepository;

    @Autowired
    private VehicleTypeService vehicleTypeService;

    @Autowired
    private DriverRepository driverRepository;

    @Override
    public VehicleDTO createVehicle(VehicleDTO vehicleDTO) {
        Vehicle vehicle = mapToEntity(vehicleDTO);
        Vehicle savedVehicle = vehicleRepository.save(vehicle);
        return mapToDTO(savedVehicle);
    }

    @Override
    public VehicleDTO updateVehicle(Long id, VehicleDTO vehicleDTO) {
        Vehicle vehicle = vehicleRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Vehicle not found with id: " + id));
        
        // Update fields
        vehicle.setLicensePlate(vehicleDTO.getLicensePlate());
        
        // Update vehicle type if changed
        if (vehicleDTO.getVehicleTypeId() != null) {
            VehicleType vehicleType = vehicleTypeService.getVehicleTypeEntityById(vehicleDTO.getVehicleTypeId());
            vehicle.setVehicleType(vehicleType);
        }
        
        // Update status if provided
        if (vehicleDTO.getStatus() != null) {
            vehicle.setStatus(vehicleDTO.getStatus());
        }
        
        // Update assigned drivers if provided
        if (vehicleDTO.getAssignedDriverUsernames() != null) {
            List<Driver> drivers = new ArrayList<>();
            for (String username : vehicleDTO.getAssignedDriverUsernames()) {
                Driver driver = driverRepository.findById(username)
                        .orElseThrow(() -> new ResourceNotFoundException("Driver not found with username: " + username));
                drivers.add(driver);
            }
            vehicle.setAssignedDrivers(drivers);
        }
        
        Vehicle updatedVehicle = vehicleRepository.save(vehicle);
        return mapToDTO(updatedVehicle);
    }

    @Override
    public void deleteVehicle(Long id) {
        Vehicle vehicle = vehicleRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Vehicle not found with id: " + id));
        vehicleRepository.delete(vehicle);
    }

    @Override
    public VehicleDTO getVehicleById(Long id) {
        Vehicle vehicle = vehicleRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Vehicle not found with id: " + id));
        return mapToDTO(vehicle);
    }

    @Override
    public List<VehicleDTO> getAllVehicles() {
        List<Vehicle> vehicles = vehicleRepository.findAll();
        return vehicles.stream()
                .map(this::mapToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public List<VehicleDTO> getVehiclesByType(Long vehicleTypeId) {
        VehicleType vehicleType = vehicleTypeService.getVehicleTypeEntityById(vehicleTypeId);
        List<Vehicle> vehicles = vehicleRepository.findByVehicleType(vehicleType);
        return vehicles.stream()
                .map(this::mapToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public VehicleDTO updateVehicleStatus(Long id, VehicleStatus status) {
        Vehicle vehicle = vehicleRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Vehicle not found with id: " + id));
        vehicle.setStatus(status);
        Vehicle updatedVehicle = vehicleRepository.save(vehicle);
        return mapToDTO(updatedVehicle);
    }

    @Override
    public VehicleDTO assignDriverToVehicle(Long id, String driverUsername) {
        Vehicle vehicle = vehicleRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Vehicle not found with id: " + id));
        
        Driver driver = driverRepository.findById(driverUsername)
                .orElseThrow(() -> new ResourceNotFoundException("Driver not found with username: " + driverUsername));
        
        List<Driver> drivers = vehicle.getAssignedDrivers();
        if (!drivers.contains(driver)) {
            drivers.add(driver);
            vehicle.setAssignedDrivers(drivers);
            vehicleRepository.save(vehicle);
        }
        
        return mapToDTO(vehicle);
    }

    @Override
    public VehicleDTO removeDriverFromVehicle(Long id, String driverUsername) {
        Vehicle vehicle = vehicleRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Vehicle not found with id: " + id));
        
        Driver driver = driverRepository.findById(driverUsername)
                .orElseThrow(() -> new ResourceNotFoundException("Driver not found with username: " + driverUsername));
        
        List<Driver> drivers = vehicle.getAssignedDrivers();
        if (drivers.contains(driver)) {
            drivers.remove(driver);
            vehicle.setAssignedDrivers(drivers);
            vehicleRepository.save(vehicle);
        }
        
        return mapToDTO(vehicle);
    }

    @Override
    public Vehicle getVehicleEntityById(Long id) {
        return vehicleRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Vehicle not found with id: " + id));
    }

    private VehicleDTO mapToDTO(Vehicle vehicle) {
        VehicleDTO vehicleDTO = new VehicleDTO();
        vehicleDTO.setId(vehicle.getId());
        vehicleDTO.setLicensePlate(vehicle.getLicensePlate());
        vehicleDTO.setVehicleTypeId(vehicle.getVehicleType().getId());
        vehicleDTO.setVehicleTypeName(vehicle.getVehicleType().getName());
        vehicleDTO.setStatus(vehicle.getStatus());
        
        List<String> driverUsernames = vehicle.getAssignedDrivers().stream()
                .map(Driver::getUsername)
                .collect(Collectors.toList());
        vehicleDTO.setAssignedDriverUsernames(driverUsernames);
        
        return vehicleDTO;
    }

    private Vehicle mapToEntity(VehicleDTO vehicleDTO) {
        Vehicle vehicle = new Vehicle();
        vehicle.setLicensePlate(vehicleDTO.getLicensePlate());
        
        if (vehicleDTO.getStatus() != null) {
            vehicle.setStatus(vehicleDTO.getStatus());
        }
        
        // Set vehicle type - this is required
        if (vehicleDTO.getVehicleTypeId() == null) {
            throw new IllegalArgumentException("Vehicle type ID is required");
        }
        
        VehicleType vehicleType = vehicleTypeService.getVehicleTypeEntityById(vehicleDTO.getVehicleTypeId());
        if (vehicleType == null) {
            throw new ResourceNotFoundException("Vehicle type not found with id: " + vehicleDTO.getVehicleTypeId());
        }
        vehicle.setVehicleType(vehicleType);
        
        // Set assigned drivers if provided
        if (vehicleDTO.getAssignedDriverUsernames() != null && !vehicleDTO.getAssignedDriverUsernames().isEmpty()) {
            List<Driver> drivers = new ArrayList<>();
            for (String username : vehicleDTO.getAssignedDriverUsernames()) {
                Driver driver = driverRepository.findById(username)
                        .orElseThrow(() -> new ResourceNotFoundException("Driver not found with username: " + username));
                drivers.add(driver);
            }
            vehicle.setAssignedDrivers(drivers);
        }
        
        return vehicle;
    }
} 