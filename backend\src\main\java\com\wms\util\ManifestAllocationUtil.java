package com.wms.util;

import com.wms.entity.LocationZone;
import com.wms.entity.Manifest;
import com.wms.entity.vehicle.VehicleType;
import com.wms.service.LocationZoneService;
import com.wms.service.VehicleServices.VehicleTypeService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
public class ManifestAllocationUtil {

    private final LocationZoneService locationZoneService;
    private final VehicleTypeService vehicleTypeService;
    private static final Logger logger = LoggerFactory.getLogger(ManifestAllocationUtil.class);

    @Autowired
    public ManifestAllocationUtil(LocationZoneService locationZoneService, VehicleTypeService vehicleTypeService) {
        this.locationZoneService = locationZoneService;
        this.vehicleTypeService = vehicleTypeService;
    }

    /**
     * @deprecated This enum is no longer used. Vehicle types are now managed through the VehicleType entity.
     */
    @Deprecated
    public enum VehicleType {
        LORRY,
        VAN
    }

    /**
     * @deprecated This method is replaced by the VehicleTypeService.determineVehicleTypeByWeightAndCbm method.
     */
    @Deprecated
    public VehicleType determineVehicleType(Double weight, Double cbm) {
        logger.warn("Using deprecated determineVehicleType method. Please use VehicleTypeService.determineVehicleTypeByWeightAndCbm instead.");
        if (weight == null || cbm == null) {
            throw new IllegalArgumentException("Weight and CBM must not be null");
        }

        if (weight > 50 || cbm > 12) {
            return VehicleType.LORRY;
        } else {
            return VehicleType.VAN;
        }
    }

    /**
     * Allocates both location and vehicle for a manifest
     * @param manifest The manifest to allocate location and vehicle for
     */
    public void allocateLocationAndVehicle(Manifest manifest) {
        allocateLocation(manifest);
        allocateVehicle(manifest);
    }
    
    /**
     * Allocates only the location for a manifest based on postal code
     * @param manifest The manifest to allocate location for
     */
    public void allocateLocation(Manifest manifest) {
        try {
            // Determine location zone based on postal code using the service
            LocationZone locationZone = locationZoneService.determineLocationZoneByPostalCode(manifest.getPostalCode());
            manifest.setLocation(locationZone.getName());
            
            logger.debug("Allocated location zone {} for manifest with postal code {}", 
                locationZone.getName(), manifest.getPostalCode());
        } catch (Exception e) {
            // If there's an error determining the location, log it and use a default
            logger.error("Error determining location zone for postal code {}: {}", 
                manifest.getPostalCode(), e.getMessage());
            manifest.setLocation("UNKNOWN");
        }
    }
    
    /**
     * Allocates only the vehicle for a manifest based on weight, CBM, and pieces
     * @param manifest The manifest to allocate vehicle for
     */
    public void allocateVehicle(Manifest manifest) {
        try {
            // First try with pieces (CBM per piece) if pieces is available
            Optional<com.wms.entity.vehicle.VehicleType> vehicleTypeOpt = Optional.empty();
            
            if (manifest.getPieces() != null && manifest.getPieces() > 0) {
                vehicleTypeOpt = vehicleTypeService.determineVehicleTypeByWeightCbmAndPieces(
                    manifest.getWeight(), manifest.getCbm(), manifest.getPieces());
                
                if (vehicleTypeOpt.isPresent()) {
                    logger.debug("Vehicle type determined using CBM per piece calculation");
                }
            }
            
            // If no match found with pieces, fall back to standard weight and CBM
            if (vehicleTypeOpt.isEmpty()) {
                vehicleTypeOpt = vehicleTypeService.determineVehicleTypeByWeightAndCbm(
                    manifest.getWeight(), manifest.getCbm());
            }
            
            if (vehicleTypeOpt.isPresent()) {
                com.wms.entity.vehicle.VehicleType vehicleType = vehicleTypeOpt.get();
                manifest.setDeliveryVehicle(vehicleType.getName());
                logger.debug("Allocated vehicle type {} for manifest with weight {} kg, CBM {}, and pieces {}", 
                           vehicleType.getName(), manifest.getWeight(), manifest.getCbm(), manifest.getPieces());
            } else {
                // Fallback to empty string if no matching vehicle type found
                manifest.setDeliveryVehicle("");
                logger.warn("No matching vehicle type found for manifest with weight {} kg, CBM {}, and pieces {}. Setting empty vehicle type.", 
                          manifest.getWeight(), manifest.getCbm(), manifest.getPieces());
            }
        } catch (Exception e) {
            logger.error("Error determining vehicle type for manifest with weight {} kg, CBM {}, and pieces {}: {}", 
                        manifest.getWeight(), manifest.getCbm(), manifest.getPieces(), e.getMessage());
            manifest.setDeliveryVehicle("");
        }
    }
} 