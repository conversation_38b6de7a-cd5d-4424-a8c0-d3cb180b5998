package com.wms.service.impl;

import com.wms.entity.user.Admin;
import com.wms.entity.user.UserStatus;
import com.wms.entity.user.*;
import com.wms.repository.UserRepo.AdminRepository;
import com.wms.repository.UserRepo.*;
import com.wms.service.UserServices.AdminService;
import com.wms.entity.Role;
import com.wms.repository.RoleRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.TransactionSystemException;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.orm.jpa.JpaSystemException;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;

@Service
public class AdminServiceImpl implements AdminService {
    private static final Logger logger = LoggerFactory.getLogger(AdminServiceImpl.class);

    private final AdminRepository adminRepository;
    private final ManagerRepository managerRepository;
    private final ClientRepository clientRepository;
    private final DriverRepository driverRepository;
    private final UserRepository userRepository;
    private final RoleRepository roleRepository;
    private final PlatformTransactionManager transactionManager;
    private final PasswordEncoder passwordEncoder;

    @Autowired
    public AdminServiceImpl(
            AdminRepository adminRepository,
            ManagerRepository managerRepository,
            ClientRepository clientRepository,
            DriverRepository driverRepository,
            UserRepository userRepository,
            RoleRepository roleRepository,
            PlatformTransactionManager transactionManager,
            PasswordEncoder passwordEncoder) {
        this.adminRepository = adminRepository;
        this.managerRepository = managerRepository;
        this.clientRepository = clientRepository;
        this.driverRepository = driverRepository;
        this.userRepository = userRepository;
        this.roleRepository = roleRepository;
        this.transactionManager = transactionManager;
        this.passwordEncoder = passwordEncoder;
    }

    private Long getNextUserNumber() {
        Long maxUserNumber = userRepository.findMaxUserNumber();
        return maxUserNumber != null ? maxUserNumber + 1 : 1L;
    }

    private Set<Role> getRolesFromUserType(String userType) {
        Set<Role> roles = new HashSet<>();
        try {
            switch (userType.toUpperCase()) {
                case "ADMIN":
                    roles.add(roleRepository.findByName("ROLE_ADMIN")
                            .orElseThrow(() -> new IllegalArgumentException("Admin role not found")));
                    break;
                case "MANAGER":
                    roles.add(roleRepository.findByName("ROLE_MANAGER")
                            .orElseThrow(() -> new IllegalArgumentException("Manager role not found")));
                    break;
                case "CLIENT":
                    roles.add(roleRepository.findByName("ROLE_CLIENT")
                            .orElseThrow(() -> new IllegalArgumentException("Client role not found")));
                    break;
                case "DRIVER":
                    roles.add(roleRepository.findByName("ROLE_DRIVER")
                            .orElseThrow(() -> new IllegalArgumentException("Driver role not found")));
                    break;
                default:
                    throw new IllegalArgumentException("Invalid user type: " + userType);
            }
            return roles;
        } catch (Exception e) {
            logger.error("Error getting roles for user type {}: {}", userType, e.getMessage());
            throw new IllegalArgumentException("Failed to get roles: " + e.getMessage());
        }
    }

    // Admin operations
    @Override
    @Transactional(readOnly = true)
    public List<Admin> getAllAdmins() {
        return adminRepository.findAll();
    }

    @Override
    @Transactional(readOnly = true)
    public Admin getAdminByUsername(String username) {
        return adminRepository.findByUsername(username)
                .orElseThrow(() -> new IllegalArgumentException("Admin not found with username: " + username));
    }

    @Override
    public Admin createAdmin(Admin admin) {
        TransactionStatus status = null;
        try {
            logger.debug("Creating admin with username: {}", admin.getUsername());
            
            // Start transaction
            DefaultTransactionDefinition def = new DefaultTransactionDefinition();
            status = transactionManager.getTransaction(def);
            
            // Validate input
            if (admin.getUsername() == null || admin.getUsername().trim().isEmpty()) {
                throw new IllegalArgumentException("Username cannot be empty");
            }
            if (admin.getEmail() == null || admin.getEmail().trim().isEmpty()) {
                throw new IllegalArgumentException("Email cannot be empty");
            }
            if (admin.getPassword() == null || admin.getPassword().trim().isEmpty()) {
                throw new IllegalArgumentException("Password cannot be empty");
            }
            
            // Check for existing users
            if (adminRepository.existsByEmail(admin.getEmail())) {
                throw new IllegalArgumentException("Email already exists");
            }
            if (adminRepository.existsByUsername(admin.getUsername())) {
                throw new IllegalArgumentException("Username already exists");
            }

            // Set roles and user number
            admin.setRoles(getRolesFromUserType("ADMIN"));
            admin.setStatus(UserStatus.ACTIVE);
            admin.setUserNumber(getNextUserNumber());
            
            // Encode the password
            admin.setPassword(passwordEncoder.encode(admin.getPassword()));
            
            // Save the admin
            Admin savedAdmin = adminRepository.save(admin);
            
            // Commit transaction
            transactionManager.commit(status);
            status = null;
            
            logger.debug("Successfully created admin with username: {}", savedAdmin.getUsername());
            return savedAdmin;
        } catch (Exception e) {
            logger.error("Error creating admin: {}", e.getMessage());
            if (status != null) {
                transactionManager.rollback(status);
            }
            throw new IllegalArgumentException("Failed to create admin: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Admin updateAdmin(String username, Admin adminDetails, String newPassword) {
        try {
            Admin admin = adminRepository.findByUsername(username)
                    .orElseThrow(() -> new IllegalArgumentException("Admin not found with username: " + username));

            if (admin.getStatus() == UserStatus.INACTIVE) {
                throw new IllegalArgumentException("Cannot update an inactive admin");
            }

            if (!admin.getEmail().equals(adminDetails.getEmail()) && 
                adminRepository.existsByEmail(adminDetails.getEmail())) {
                throw new IllegalArgumentException("Email already exists");
            }

            if (!username.equals(adminDetails.getUsername())) {
                throw new IllegalArgumentException("Username cannot be changed");
            }

            admin.setEmail(adminDetails.getEmail());
            admin.setContact(adminDetails.getContact());
            admin.setFullName(adminDetails.getFullName());
            
            // Only update password if it's not null or empty
            if (newPassword != null && !newPassword.isEmpty()) {
                admin.setPassword(passwordEncoder.encode(newPassword));
            }

            return adminRepository.save(admin);
        } catch (DataIntegrityViolationException e) {
            logger.error("Data integrity violation while updating admin: {}", e.getMessage());
            throw new IllegalArgumentException("Failed to update admin: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Admin updateAdmin(String username, Admin adminDetails) {
        // Delegate to the new method, passing null for the password to indicate no password change
        return updateAdmin(username, adminDetails, null);
    }

    @Override
    @Transactional
    public Admin deactivateAdmin(String username) {
        try {
            Admin admin = adminRepository.findByUsername(username)
                    .orElseThrow(() -> new IllegalArgumentException("Admin not found with username: " + username));
            
            if (admin.getStatus() == UserStatus.INACTIVE) {
                throw new IllegalArgumentException("Admin is already inactive");
            }
            
            admin.setStatus(UserStatus.INACTIVE);
            return adminRepository.save(admin);
        } catch (DataIntegrityViolationException e) {
            logger.error("Data integrity violation while deactivating admin: {}", e.getMessage());
            throw new IllegalArgumentException("Failed to deactivate admin: " + e.getMessage());
        }
    }

    // Manager operations
    @Override
    @Transactional(readOnly = true)
    public List<Manager> getAllManagers() {
        List<Manager> managers = managerRepository.findAll();
        // Initialize roles to prevent LazyInitializationException
        managers.forEach(manager -> {
            if (manager.getRoles() != null) {
                manager.getRoles().size(); // Accessing size or iterating forces initialization
            }
        });
        return managers;
    }

    @Override
    @Transactional(readOnly = true)
    public Manager getManagerByUsername(String username) {
        return managerRepository.findByUsername(username)
                .orElseThrow(() -> new IllegalArgumentException("Manager not found with username: " + username));
    }

    @Override
    public Manager createManager(Manager manager) {
        TransactionStatus status = null;
        try {
            logger.debug("Creating manager with username: {}", manager.getUsername());
            
            // Start transaction
            DefaultTransactionDefinition def = new DefaultTransactionDefinition();
            status = transactionManager.getTransaction(def);
            
            // Validate input
            if (manager.getUsername() == null || manager.getUsername().trim().isEmpty()) {
                throw new IllegalArgumentException("Username cannot be empty");
            }
            if (manager.getEmail() == null || manager.getEmail().trim().isEmpty()) {
                throw new IllegalArgumentException("Email cannot be empty");
            }
            if (manager.getPassword() == null || manager.getPassword().trim().isEmpty()) {
                throw new IllegalArgumentException("Password cannot be empty");
            }
            
            // Check for existing users
            if (managerRepository.existsByEmail(manager.getEmail())) {
                throw new IllegalArgumentException("Email already exists");
            }
            if (managerRepository.existsByUsername(manager.getUsername())) {
                throw new IllegalArgumentException("Username already exists");
            }

            // Set roles and user number
            manager.setRoles(getRolesFromUserType("MANAGER"));
            manager.setStatus(UserStatus.ACTIVE);
            manager.setUserNumber(getNextUserNumber());
            
            // Encode the password
            manager.setPassword(passwordEncoder.encode(manager.getPassword()));
            
            // Save the manager
            Manager savedManager = managerRepository.save(manager);
            
            // Commit transaction
            transactionManager.commit(status);
            status = null;
            
            logger.debug("Successfully created manager with username: {}", savedManager.getUsername());
            return savedManager;
        } catch (Exception e) {
            logger.error("Error creating manager: {}", e.getMessage());
            if (status != null) {
                transactionManager.rollback(status);
            }
            throw new IllegalArgumentException("Failed to create manager: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Manager updateManager(String username, Manager managerDetails, String newPassword) {
        try {
            Manager manager = managerRepository.findByUsername(username)
                    .orElseThrow(() -> new IllegalArgumentException("Manager not found with username: " + username));

            if (manager.getStatus() == UserStatus.INACTIVE) {
                throw new IllegalArgumentException("Cannot update an inactive manager");
            }

            if (!manager.getEmail().equals(managerDetails.getEmail()) && 
                managerRepository.existsByEmail(managerDetails.getEmail())) {
                throw new IllegalArgumentException("Email already exists");
            }

            if (!username.equals(managerDetails.getUsername())) {
                throw new IllegalArgumentException("Username cannot be changed");
            }

            manager.setDepartment(managerDetails.getDepartment());
            manager.setEmail(managerDetails.getEmail());
            manager.setContact(managerDetails.getContact());
            manager.setFullName(managerDetails.getFullName());
            
            // Only update password if it's not null or empty
            if (newPassword != null && !newPassword.isEmpty()) {
                manager.setPassword(passwordEncoder.encode(newPassword));
            }

            return managerRepository.save(manager);
        } catch (DataIntegrityViolationException e) {
            logger.error("Data integrity violation while updating manager: {}", e.getMessage());
            throw new IllegalArgumentException("Failed to update manager: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Manager updateManager(String username, Manager managerDetails) {
        // Delegate to the new method, passing null for the password to indicate no password change
        return updateManager(username, managerDetails, null);
    }

    @Override
    @Transactional
    public Manager deactivateManager(String username) {
        try {
            Manager manager = managerRepository.findByUsername(username)
                    .orElseThrow(() -> new IllegalArgumentException("Manager not found with username: " + username));
            
            if (manager.getStatus() == UserStatus.INACTIVE) {
                throw new IllegalArgumentException("Manager is already inactive");
            }
            
            manager.setStatus(UserStatus.INACTIVE);
            return managerRepository.save(manager);
        } catch (DataIntegrityViolationException e) {
            logger.error("Data integrity violation while deactivating manager: {}", e.getMessage());
            throw new IllegalArgumentException("Failed to deactivate manager: " + e.getMessage());
        }
    }

    // Client operations
    @Override
    @Transactional(readOnly = true)
    public List<Client> getAllClients() {
        List<Client> clients = clientRepository.findAll();
        // Initialize roles to prevent LazyInitializationException
        clients.forEach(client -> {
            if (client.getRoles() != null) {
                client.getRoles().size(); // Accessing size or iterating forces initialization
            }
        });
        return clients;
    }

    @Override
    @Transactional(readOnly = true)
    public Client getClientByUsername(String username) {
        return clientRepository.findByUsername(username)
                .orElseThrow(() -> new IllegalArgumentException("Client not found with username: " + username));
    }

    @Override
    public Client createClient(Client client) {
        TransactionStatus status = null;
        try {
            logger.debug("Creating client with username: {}", client.getUsername());
            
            // Start transaction
            DefaultTransactionDefinition def = new DefaultTransactionDefinition();
            status = transactionManager.getTransaction(def);
            
            // Validate input
            if (client.getUsername() == null || client.getUsername().trim().isEmpty()) {
                throw new IllegalArgumentException("Username cannot be empty");
            }
            if (client.getEmail() == null || client.getEmail().trim().isEmpty()) {
                throw new IllegalArgumentException("Email cannot be empty");
            }
            if (client.getPassword() == null || client.getPassword().trim().isEmpty()) {
                throw new IllegalArgumentException("Password cannot be empty");
            }
            
            // Check for existing users
            if (clientRepository.existsByEmail(client.getEmail())) {
                throw new IllegalArgumentException("Email already exists");
            }
            if (clientRepository.existsByUsername(client.getUsername())) {
                throw new IllegalArgumentException("Username already exists");
            }

            // Set roles and user number
            client.setRoles(getRolesFromUserType("CLIENT"));
            client.setStatus(UserStatus.ACTIVE);
            client.setUserNumber(getNextUserNumber());
            
            // Encode the password
            client.setPassword(passwordEncoder.encode(client.getPassword()));
            
            // Save the client
            Client savedClient = clientRepository.save(client);
            
            // Commit transaction
            transactionManager.commit(status);
            status = null;
            
            logger.debug("Successfully created client with username: {}", savedClient.getUsername());
            return savedClient;
        } catch (Exception e) {
            logger.error("Error creating client: {}", e.getMessage());
            if (status != null) {
                transactionManager.rollback(status);
            }
            throw new IllegalArgumentException("Failed to create client: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Client updateClient(String username, Client clientDetails, String newPassword) {
        try {
            Client client = clientRepository.findByUsername(username)
                    .orElseThrow(() -> new IllegalArgumentException("Client not found with username: " + username));

            if (client.getStatus() == UserStatus.INACTIVE) {
                throw new IllegalArgumentException("Cannot update an inactive client");
            }

            if (!client.getEmail().equals(clientDetails.getEmail()) && 
                clientRepository.existsByEmail(clientDetails.getEmail())) {
                throw new IllegalArgumentException("Email already exists");
            }

            if (!username.equals(clientDetails.getUsername())) {
                throw new IllegalArgumentException("Username cannot be changed");
            }

            client.setCompanyName(clientDetails.getCompanyName());
            client.setDesignation(clientDetails.getDesignation());
            client.setAltDesignation(clientDetails.getAltDesignation());
            client.setAltContact(clientDetails.getAltContact());
            client.setEmail(clientDetails.getEmail());
            client.setContact(clientDetails.getContact());
            client.setFullName(clientDetails.getFullName());
            
            // Only update password if it's not null or empty
            if (newPassword != null && !newPassword.isEmpty()) {
                client.setPassword(passwordEncoder.encode(newPassword));
            }

            return clientRepository.save(client);
        } catch (DataIntegrityViolationException e) {
            logger.error("Data integrity violation while updating client: {}", e.getMessage());
            throw new IllegalArgumentException("Failed to update client: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Client updateClient(String username, Client clientDetails) {
        // Delegate to the new method, passing null for the password to indicate no password change
        return updateClient(username, clientDetails, null);
    }

    @Override
    @Transactional
    public Client deactivateClient(String username) {
        try {
            Client client = clientRepository.findByUsername(username)
                    .orElseThrow(() -> new IllegalArgumentException("Client not found with username: " + username));

            if (client.getStatus() == UserStatus.INACTIVE) {
                throw new IllegalArgumentException("Client is already inactive");
            }

            client.setStatus(UserStatus.INACTIVE);
            return clientRepository.save(client);
        } catch (DataIntegrityViolationException e) {
            logger.error("Data integrity violation while deactivating client: {}", e.getMessage());
            throw new IllegalArgumentException("Failed to deactivate client: " + e.getMessage());
        }
    }

    // Driver operations
    @Override
    @Transactional(readOnly = true)
    public List<Driver> getAllDrivers() {
        List<Driver> drivers = driverRepository.findAll();
        // Initialize roles to prevent LazyInitializationException
        drivers.forEach(driver -> {
            if (driver.getRoles() != null) {
                driver.getRoles().size(); // Accessing size or iterating forces initialization
            }
        });
        return drivers;
    }

    @Override
    @Transactional(readOnly = true)
    public Driver getDriverByUsername(String username) {
        return driverRepository.findById(username)
                .orElseThrow(() -> new IllegalArgumentException("Driver not found with username: " + username));
    }

    @Override
    public Driver createDriver(Driver driver) {
        TransactionStatus status = null;
        try {
            logger.debug("Creating driver with username: {}", driver.getUsername());
            
            // Start transaction
            DefaultTransactionDefinition def = new DefaultTransactionDefinition();
            status = transactionManager.getTransaction(def);
            
            // Validate input
            if (driver.getUsername() == null || driver.getUsername().trim().isEmpty()) {
                throw new IllegalArgumentException("Username cannot be empty");
            }
            if (driver.getEmail() == null || driver.getEmail().trim().isEmpty()) {
                throw new IllegalArgumentException("Email cannot be empty");
            }
            if (driver.getPassword() == null || driver.getPassword().trim().isEmpty()) {
                throw new IllegalArgumentException("Password cannot be empty");
            }
            
            // Check for existing users
            if (driverRepository.existsByEmail(driver.getEmail())) {
                throw new IllegalArgumentException("Email already exists");
            }
            if (driverRepository.existsByUsername(driver.getUsername())) {
                throw new IllegalArgumentException("Username already exists");
            }

            // Set roles and user number
            driver.setRoles(getRolesFromUserType("DRIVER"));
            driver.setStatus(UserStatus.ACTIVE);
            driver.setUserNumber(getNextUserNumber());
            
            // Encode the password
            driver.setPassword(passwordEncoder.encode(driver.getPassword()));
            
            // Save the driver
            Driver savedDriver = driverRepository.save(driver);
            
            // Commit transaction
            transactionManager.commit(status);
            status = null;
            
            logger.debug("Successfully created driver with username: {}", savedDriver.getUsername());
            return savedDriver;
        } catch (Exception e) {
            logger.error("Error creating driver: {}", e.getMessage());
            if (status != null) {
                transactionManager.rollback(status);
            }
            throw new IllegalArgumentException("Failed to create driver: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Driver updateDriver(String username, Driver driverDetails, String newPassword) {
        try {
            Driver driver = driverRepository.findById(username)
                    .orElseThrow(() -> new IllegalArgumentException("Driver not found with username: " + username));
            
            if (driver.getStatus() == UserStatus.INACTIVE) {
                throw new IllegalArgumentException("Cannot update an inactive driver");
            }

            // Check if email is being changed and if new email already exists
            if (!driver.getEmail().equals(driverDetails.getEmail()) && 
                driverRepository.existsByEmail(driverDetails.getEmail())) {
                throw new IllegalArgumentException("Email already exists");
            }

            // Username cannot be changed as it's the primary key
            if (!username.equals(driverDetails.getUsername())) {
                throw new IllegalArgumentException("Username cannot be changed");
            }
            
            // Update driver-specific fields
            driver.setLicenseNumber(driverDetails.getLicenseNumber());
            
            // Update common user fields
            driver.setEmail(driverDetails.getEmail());
            driver.setContact(driverDetails.getContact());
            driver.setFullName(driverDetails.getFullName());
            
            // Only update password if it's not null or empty
            if (newPassword != null && !newPassword.isEmpty()) {
                driver.setPassword(passwordEncoder.encode(newPassword));
            }
            
            return driverRepository.save(driver);
        } catch (DataIntegrityViolationException e) {
            logger.error("Data integrity violation while updating driver: {}", e.getMessage());
            throw new IllegalArgumentException("Failed to update driver: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Driver updateDriver(String username, Driver driverDetails) {
        // Delegate to the new method, passing null for the password to indicate no password change
        return updateDriver(username, driverDetails, null);
    }

    @Override
    @Transactional
    public Driver deactivateDriver(String username) {
        try {
            Driver driver = driverRepository.findById(username)
                    .orElseThrow(() -> new IllegalArgumentException("Driver not found with username: " + username));

            if (driver.getStatus() == UserStatus.INACTIVE) {
                throw new IllegalArgumentException("Driver is already inactive");
            }

            driver.setStatus(UserStatus.INACTIVE);
            return driverRepository.save(driver);
        } catch (DataIntegrityViolationException e) {
            logger.error("Data integrity violation while deactivating driver: {}", e.getMessage());
            throw new IllegalArgumentException("Failed to deactivate driver: " + e.getMessage());
        }
    }

    // Generic user operations
    @Override
    @Transactional(readOnly = true)
    public List<User> getAllUsers() {
        return userRepository.findAllWithRoles();
    }

    @Override
    @Transactional(readOnly = true)
    public Long getUsersCount() {
        // Count all users and log details for debugging
        List<User> allUsers = userRepository.findAll();
        Long count = (long) allUsers.size();
        
        // Log detailed information for debugging
        logger.debug("getUsersCount found {} users", count);
        if (count > 0) {
            logger.debug("First 5 usernames: {}", 
                      allUsers.stream()
                          .limit(5)
                          .map(User::getUsername)
                          .toList());
        }
        
        return count;
    }

    @Override
    @Transactional(readOnly = true)
    public User getUserByUsername(String username) {
        return userRepository.findByUsername(username)
                .orElseThrow(() -> new IllegalArgumentException("User not found with username: " + username));
    }

    @Override
    @Transactional
    public User deactivateUser(String username) {
        try {
            User user = userRepository.findByUsername(username)
                    .orElseThrow(() -> new IllegalArgumentException("User not found with username: " + username));

            if (user.getStatus() == UserStatus.INACTIVE) {
                throw new IllegalArgumentException("User is already inactive");
            }

            user.setStatus(UserStatus.INACTIVE);
            return userRepository.save(user);
        } catch (DataIntegrityViolationException e) {
            logger.error("Data integrity violation while deactivating user: {}", e.getMessage());
            throw new IllegalArgumentException("Failed to deactivate user: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public User activateUser(String username) {
        try {
            User user = userRepository.findByUsername(username)
                    .orElseThrow(() -> new IllegalArgumentException("User not found with username: " + username));

            if (user.getStatus() == UserStatus.ACTIVE) {
                throw new IllegalArgumentException("User is already active");
            }

            user.setStatus(UserStatus.ACTIVE);
            return userRepository.save(user);
        } catch (DataIntegrityViolationException e) {
            logger.error("Data integrity violation while activating user: {}", e.getMessage());
            throw new IllegalArgumentException("Failed to activate user: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Admin activateAdmin(String username) {
        try {
            Admin admin = adminRepository.findByUsername(username)
                    .orElseThrow(() -> new IllegalArgumentException("Admin not found with username: " + username));
            
            if (admin.getStatus() == UserStatus.ACTIVE) {
                throw new IllegalArgumentException("Admin is already active");
            }
            
            admin.setStatus(UserStatus.ACTIVE);
            return adminRepository.save(admin);
        } catch (DataIntegrityViolationException e) {
            logger.error("Data integrity violation while activating admin: {}", e.getMessage());
            throw new IllegalArgumentException("Failed to activate admin: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Manager activateManager(String username) {
        try {
            Manager manager = managerRepository.findByUsername(username)
                    .orElseThrow(() -> new IllegalArgumentException("Manager not found with username: " + username));
            
            if (manager.getStatus() == UserStatus.ACTIVE) {
                throw new IllegalArgumentException("Manager is already active");
            }
            
            manager.setStatus(UserStatus.ACTIVE);
            return managerRepository.save(manager);
        } catch (DataIntegrityViolationException e) {
            logger.error("Data integrity violation while activating manager: {}", e.getMessage());
            throw new IllegalArgumentException("Failed to activate manager: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Client activateClient(String username) {
        try {
            Client client = clientRepository.findByUsername(username)
                    .orElseThrow(() -> new IllegalArgumentException("Client not found with username: " + username));
            
            if (client.getStatus() == UserStatus.ACTIVE) {
                throw new IllegalArgumentException("Client is already active");
            }
            
            client.setStatus(UserStatus.ACTIVE);
            return clientRepository.save(client);
        } catch (DataIntegrityViolationException e) {
            logger.error("Data integrity violation while activating client: {}", e.getMessage());
            throw new IllegalArgumentException("Failed to activate client: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Driver activateDriver(String username) {
        try {
            Driver driver = driverRepository.findById(username)
                    .orElseThrow(() -> new IllegalArgumentException("Driver not found with username: " + username));

            if (driver.getStatus() == UserStatus.ACTIVE) {
                throw new IllegalArgumentException("Driver is already active");
            }

            driver.setStatus(UserStatus.ACTIVE);
            return driverRepository.save(driver);
        } catch (DataIntegrityViolationException e) {
            logger.error("Data integrity violation while activating driver: {}", e.getMessage());
            throw new IllegalArgumentException("Failed to activate driver: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public void changeUserPassword(String username, String currentPassword, String newPassword) {
        try {
            // Find the user
            User user = userRepository.findByUsername(username)
                    .orElseThrow(() -> new IllegalArgumentException("User not found with username: " + username));

            // Check if user is active
            if (user.getStatus() == UserStatus.INACTIVE) {
                throw new IllegalArgumentException("Cannot change password for an inactive user");
            }

            // Verify current password
            if (!passwordEncoder.matches(currentPassword, user.getPassword())) {
                throw new IllegalArgumentException("Current password is incorrect");
            }

            // Validate new password
            if (newPassword == null || newPassword.trim().isEmpty()) {
                throw new IllegalArgumentException("New password cannot be empty");
            }

            if (newPassword.length() < 6) {
                throw new IllegalArgumentException("New password must be at least 6 characters long");
            }

            // Check if new password is different from current password
            if (passwordEncoder.matches(newPassword, user.getPassword())) {
                throw new IllegalArgumentException("New password must be different from current password");
            }

            // Encode and set new password
            user.setPassword(passwordEncoder.encode(newPassword));
            
            // Save the user
            userRepository.save(user);
            
            logger.info("Password changed successfully for user: {}", username);
        } catch (Exception e) {
            logger.error("Error changing password for user {}: {}", username, e.getMessage());
            throw e;
        }
    }
}