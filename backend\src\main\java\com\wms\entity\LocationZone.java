package com.wms.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Data
@Entity
@Table(name = "location_zones")
public class LocationZone {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotBlank(message = "Zone name is required")
    @Column(nullable = false)
    private String name;

    @Size(max = 10, message = "Abbreviation must not exceed 10 characters")
    @Column(name = "abbreviation")
    private String abbreviation;

    @Column(name = "start_postal_code")
    private Integer startPostalCode;

    @Column(name = "end_postal_code")
    private Integer endPostalCode;

    @Column(name = "special_cases")
    private String specialCases;

    @Column(name = "description")
    private String description;

    @Column(name = "is_active", nullable = false)
    private Boolean isActive = true;
} 