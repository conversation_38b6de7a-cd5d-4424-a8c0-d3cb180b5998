{"name": "fukuyama-wms-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc --skipLibCheck && vite build --mode production", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/cache": "^11.14.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.0.2", "@mui/lab": "^7.0.0-beta.12", "@mui/material": "^7.0.2", "@mui/x-data-grid": "^8.2.0", "@mui/x-date-pickers": "^8.2.0", "@tanstack/react-query": "^5.74.4", "@types/date-fns": "^2.5.3", "axios": "^1.8.4", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "react": "^19.0.0", "react-barcode": "^1.6.1", "react-dom": "^19.0.0", "react-qr-code": "^2.0.15", "react-router-dom": "^7.5.1", "react-to-print": "^3.1.0", "recharts": "^2.15.3"}, "devDependencies": {"@eslint/js": "^9.22.0", "@stagewise-plugins/react": "^0.4.7", "@stagewise/toolbar-react": "^0.4.5", "@types/file-saver": "^2.0.7", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "typescript": "~5.7.2", "typescript-eslint": "^8.26.1", "vite": "^6.3.1"}}