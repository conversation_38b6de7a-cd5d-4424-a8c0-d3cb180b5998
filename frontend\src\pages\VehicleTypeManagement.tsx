import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Typography,
  Snackbar,
  Alert,
  CircularProgress,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  SelectChangeEvent,
  Link,
  Switch,
  FormControlLabel,
  Divider,
  Chip
} from '@mui/material';
import { Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon, DirectionsCar as CarIcon } from '@mui/icons-material';
import { Vehicle, VehicleType, VehicleStatus } from '../types/Vehicle';
import * as vehicleService from '../services/vehicle.service';
import { useNavigate } from 'react-router-dom';

const VehicleTypeManagement: React.FC = () => {
  const navigate = useNavigate();
  const [vehicleTypes, setVehicleTypes] = useState<VehicleType[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [openDialog, setOpenDialog] = useState<boolean>(false);
  const [dialogMode, setDialogMode] = useState<'add' | 'edit'>('add');
  const [currentVehicleType, setCurrentVehicleType] = useState<VehicleType>({
    id: null,
    name: '',
    abbreviation: '',
    description: '',
    minWeight: null,
    maxWeight: null,
    minCbm: null,
    maxCbm: null,
    minCbmPerPiece: null,
    maxCbmPerPiece: null,
    isActive: true
  });
  
  // New state for vehicle creation
  const [openVehicleDialog, setOpenVehicleDialog] = useState<boolean>(false);
  const [currentVehicle, setCurrentVehicle] = useState<Vehicle>({
    id: null,
    licensePlate: '',
    vehicleTypeId: 0,
    vehicleTypeName: '',
    status: VehicleStatus.AVAILABLE,
    assignedDriverUsernames: []
  });
  
  const [snackbar, setSnackbar] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'error';
  }>({
    open: false,
    message: '',
    severity: 'success',
  });

  useEffect(() => {
    fetchVehicleTypes();
  }, []);

  const fetchVehicleTypes = async () => {
    setLoading(true);
    try {
      const data = await vehicleService.getVehicleTypes();
      setVehicleTypes(data);
      setError(null);
    } catch (err) {
      console.error('Error fetching vehicle types:', err);
      setError('Failed to fetch vehicle types. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleOpenDialog = (mode: 'add' | 'edit', vehicleType?: VehicleType) => {
    setDialogMode(mode);
    if (mode === 'edit' && vehicleType) {
      setCurrentVehicleType({ ...vehicleType });
    } else {
      setCurrentVehicleType({
        id: null,
        name: '',
        abbreviation: '',
        description: '',
        minWeight: null,
        maxWeight: null,
        minCbm: null,
        maxCbm: null,
        minCbmPerPiece: null,
        maxCbmPerPiece: null,
        isActive: true
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setCurrentVehicleType(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    const numValue = value === '' ? null : parseFloat(value);
    setCurrentVehicleType(prev => ({
      ...prev,
      [name]: numValue
    }));
  };

  const handleSwitchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setCurrentVehicleType(prev => ({
      ...prev,
      [name]: checked
    }));
  };

  // New handlers for vehicle creation
  const handleOpenVehicleDialog = (vehicleType: VehicleType) => {
    // Ensure vehicleType.id is a valid number
    if (!vehicleType.id) {
      setSnackbar({
        open: true,
        message: 'Cannot create vehicle: Vehicle type ID is missing.',
        severity: 'error',
      });
      return;
    }
    
    setCurrentVehicle({
      id: null,
      licensePlate: '',
      vehicleTypeId: vehicleType.id,
      vehicleTypeName: vehicleType.name,
      status: VehicleStatus.AVAILABLE,
      assignedDriverUsernames: []
    });
    setOpenVehicleDialog(true);
  };

  const handleCloseVehicleDialog = () => {
    setOpenVehicleDialog(false);
  };

  const handleVehicleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setCurrentVehicle(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleVehicleStatusChange = (e: SelectChangeEvent<VehicleStatus>) => {
    const value = e.target.value as VehicleStatus;
    setCurrentVehicle(prev => ({
      ...prev,
      status: value
    }));
  };

  const handleCreateVehicle = async () => {
    try {
      await vehicleService.createVehicle(currentVehicle);
      setSnackbar({
        open: true,
        message: 'Vehicle created successfully!',
        severity: 'success',
      });
      handleCloseVehicleDialog();
    } catch (err) {
      console.error('Error creating vehicle:', err);
      setSnackbar({
        open: true,
        message: 'Failed to create vehicle. Please try again.',
        severity: 'error',
      });
    }
  };

  const handleSubmit = async () => {
    try {
      if (dialogMode === 'add') {
        await vehicleService.createVehicleType(currentVehicleType);
        setSnackbar({
          open: true,
          message: 'Vehicle type created successfully!',
          severity: 'success',
        });
      } else {
        if (currentVehicleType.id !== null) {
          await vehicleService.updateVehicleType(currentVehicleType.id, currentVehicleType);
          setSnackbar({
            open: true,
            message: 'Vehicle type updated successfully!',
            severity: 'success',
          });
        }
      }
      handleCloseDialog();
      fetchVehicleTypes();
    } catch (err) {
      console.error('Error saving vehicle type:', err);
      setSnackbar({
        open: true,
        message: 'Failed to save vehicle type. Please try again.',
        severity: 'error',
      });
    }
  };

  const handleDelete = async (id: number) => {
    if (window.confirm('Are you sure you want to delete this vehicle type?')) {
      try {
        await vehicleService.deleteVehicleType(id);
        setSnackbar({
          open: true,
          message: 'Vehicle type deleted successfully!',
          severity: 'success',
        });
        fetchVehicleTypes();
      } catch (err) {
        console.error('Error deleting vehicle type:', err);
        setSnackbar({
          open: true,
          message: 'Failed to delete vehicle type. It may be in use by vehicles.',
          severity: 'error',
        });
      }
    }
  };

  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  const handleViewVehicles = (typeId: number) => {
    navigate(`/vehicles?typeId=${typeId}`);
  };

  return (
    <Box sx={{ height: '100%' }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">Vehicle Type Management</Typography>
        <Button
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
          onClick={() => handleOpenDialog('add')}
        >
          Add Vehicle Type
        </Button>
      </Box>

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
          <CircularProgress />
        </Box>
      ) : error ? (
        <Alert severity="error" sx={{ mt: 2 }}>
          {error}
        </Alert>
      ) : (
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>ID</TableCell>
                <TableCell>Name</TableCell>
                <TableCell>Abbreviation</TableCell>
                <TableCell>Description</TableCell>
                <TableCell>Weight Range (kg)</TableCell>
                <TableCell>CBM Range</TableCell>
                <TableCell>CBM/Piece Range</TableCell>
                <TableCell>Status</TableCell>
                <TableCell align="right">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {vehicleTypes.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={9} align="center">
                    No vehicle types found. Create one to get started.
                  </TableCell>
                </TableRow>
              ) : (
                vehicleTypes.map((vehicleType) => (
                  <TableRow key={vehicleType.id}>
                    <TableCell>{vehicleType.id}</TableCell>
                    <TableCell>
                      <Link
                        component="button"
                        variant="body2"
                        onClick={() => handleViewVehicles(vehicleType.id || 0)}
                        sx={{
                          textDecoration: 'none',
                          cursor: 'pointer',
                          fontWeight: 'medium',
                          color: 'primary.main',
                          '&:hover': {
                            textDecoration: 'underline'
                          }
                        }}
                      >
                        {vehicleType.name}
                      </Link>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ 
                        display: 'flex', 
                        alignItems: 'center',
                        justifyContent: 'center',
                        fontWeight: 'bold',
                        color: vehicleType.abbreviation ? 'primary.main' : 'text.secondary',
                        bgcolor: vehicleType.abbreviation ? 'rgba(25, 118, 210, 0.08)' : 'transparent',
                        borderRadius: 1,
                        px: 1,
                        py: 0.5,
                        minWidth: '40px',
                        maxWidth: '80px'
                      }}>
                        {vehicleType.abbreviation || 'N/A'}
                      </Box>
                    </TableCell>
                    <TableCell>{vehicleType.description || 'N/A'}</TableCell>
                    <TableCell>
                      {vehicleType.minWeight !== null || vehicleType.maxWeight !== null ? (
                        <Typography variant="body2">
                          {vehicleType.minWeight !== null ? `${vehicleType.minWeight}` : '0'} - {vehicleType.maxWeight !== null ? `${vehicleType.maxWeight}` : '∞'}
                        </Typography>
                      ) : (
                        <Typography variant="body2" color="text.secondary">Any</Typography>
                      )}
                    </TableCell>
                    <TableCell>
                      {vehicleType.minCbm !== null || vehicleType.maxCbm !== null ? (
                        <Typography variant="body2">
                          {vehicleType.minCbm !== null ? `${vehicleType.minCbm}` : '0'} - {vehicleType.maxCbm !== null ? `${vehicleType.maxCbm}` : '∞'}
                        </Typography>
                      ) : (
                        <Typography variant="body2" color="text.secondary">Any</Typography>
                      )}
                    </TableCell>
                    <TableCell>
                      {vehicleType.minCbmPerPiece !== null || vehicleType.maxCbmPerPiece !== null ? (
                        <Typography variant="body2">
                          {vehicleType.minCbmPerPiece !== null ? `${vehicleType.minCbmPerPiece}` : '0'} - {vehicleType.maxCbmPerPiece !== null ? `${vehicleType.maxCbmPerPiece}` : '∞'}
                        </Typography>
                      ) : (
                        <Typography variant="body2" color="text.secondary">Any</Typography>
                      )}
                    </TableCell>
                    <TableCell>
                      <Chip 
                        label={vehicleType.isActive ? 'Active' : 'Inactive'} 
                        color={vehicleType.isActive ? 'success' : 'default'}
                        size="small"
                      />
                    </TableCell>
                    <TableCell align="right">
                      <IconButton
                        color="success"
                        onClick={() => handleOpenVehicleDialog(vehicleType)}
                        title="Add vehicle of this type"
                      >
                        <CarIcon />
                      </IconButton>
                      <IconButton
                        color="primary"
                        onClick={() => handleOpenDialog('edit', vehicleType)}
                      >
                        <EditIcon />
                      </IconButton>
                      <IconButton
                        color="error"
                        onClick={() => vehicleType.id && handleDelete(vehicleType.id)}
                      >
                        <DeleteIcon />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>
      )}

      {/* Add/Edit Vehicle Type Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {dialogMode === 'add' ? 'Add New Vehicle Type' : 'Edit Vehicle Type'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            {/* Basic Information */}
            <Typography variant="h6" gutterBottom>Basic Information</Typography>
            <Divider sx={{ mb: 2 }} />
            
            <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
              <TextField
                autoFocus
                name="name"
                label="Type Name"
                type="text"
                fullWidth
                variant="outlined"
                value={currentVehicleType.name}
                onChange={handleInputChange}
                required
                helperText="Unique name for this vehicle type"
              />
              
              <TextField
                name="abbreviation"
                label="Abbreviation"
                type="text"
                fullWidth
                variant="outlined"
                value={currentVehicleType.abbreviation || ''}
                onChange={handleInputChange}
                inputProps={{ maxLength: 10 }}
                helperText="Short abbreviation for pallet labels (e.g., 'V', 'L', 'LT')"
              />
            </Box>
            
            <TextField
              name="description"
              label="Description"
              type="text"
              fullWidth
              variant="outlined"
              value={currentVehicleType.description}
              onChange={handleInputChange}
              multiline
              rows={2}
              helperText="Optional description for this vehicle type"
              sx={{ mb: 2 }}
            />
            
            <FormControlLabel
              control={
                <Switch
                  name="isActive"
                  checked={currentVehicleType.isActive || false}
                  onChange={handleSwitchChange}
                />
              }
              label="Active"
              sx={{ mb: 2 }}
            />

            {/* Auto-Allocation Rules */}
            <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>Auto-Allocation Rules</Typography>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              Define weight and CBM ranges for automatic vehicle type assignment. Leave empty for no limits.
            </Typography>
            <Divider sx={{ mb: 2 }} />
            
            <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
              <TextField
                name="minWeight"
                label="Minimum Weight (kg)"
                type="number"
                fullWidth
                variant="outlined"
                value={currentVehicleType.minWeight?.toString() || ''}
                onChange={handleNumberChange}
                helperText="Minimum weight for this vehicle type"
                inputProps={{ step: "0.01", min: "0" }}
              />
              
              <TextField
                name="maxWeight"
                label="Maximum Weight (kg)"
                type="number"
                fullWidth
                variant="outlined"
                value={currentVehicleType.maxWeight?.toString() || ''}
                onChange={handleNumberChange}
                helperText="Maximum weight for this vehicle type"
                inputProps={{ step: "0.01", min: "0" }}
              />
            </Box>
            
            <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
              <TextField
                name="minCbm"
                label="Minimum CBM"
                type="number"
                fullWidth
                variant="outlined"
                value={currentVehicleType.minCbm?.toString() || ''}
                onChange={handleNumberChange}
                helperText="Minimum cubic meters for this vehicle type"
                inputProps={{ step: "0.01", min: "0" }}
              />
              
              <TextField
                name="maxCbm"
                label="Maximum CBM"
                type="number"
                fullWidth
                variant="outlined"
                value={currentVehicleType.maxCbm?.toString() || ''}
                onChange={handleNumberChange}
                helperText="Maximum cubic meters for this vehicle type"
                inputProps={{ step: "0.01", min: "0" }}
              />
            </Box>
            
            <Typography variant="subtitle2" color="text.secondary" sx={{ mt: 2, mb: 1 }}>
              CBM per Piece (CBM ÷ Pieces)
            </Typography>
            
            <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
              <TextField
                name="minCbmPerPiece"
                label="Min CBM per Piece"
                type="number"
                fullWidth
                variant="outlined"
                value={currentVehicleType.minCbmPerPiece?.toString() || ''}
                onChange={handleNumberChange}
                helperText="Minimum CBM per piece for this vehicle type"
                inputProps={{ step: "0.001", min: "0" }}
              />
              
              <TextField
                name="maxCbmPerPiece"
                label="Max CBM per Piece"
                type="number"
                fullWidth
                variant="outlined"
                value={currentVehicleType.maxCbmPerPiece?.toString() || ''}
                onChange={handleNumberChange}
                helperText="Maximum CBM per piece for this vehicle type"
                inputProps={{ step: "0.001", min: "0" }}
              />
            </Box>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button onClick={handleSubmit} variant="contained" color="primary">
            {dialogMode === 'add' ? 'Add' : 'Update'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Add Vehicle Dialog */}
      <Dialog open={openVehicleDialog} onClose={handleCloseVehicleDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          Add New Vehicle - {currentVehicle.vehicleTypeName}
        </DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            name="licensePlate"
            label="License Plate"
            type="text"
            fullWidth
            variant="outlined"
            value={currentVehicle.licensePlate}
            onChange={handleVehicleInputChange}
            required
          />
          <FormControl fullWidth margin="dense">
            <InputLabel id="status-label">Status</InputLabel>
            <Select
              labelId="status-label"
              id="status"
              value={currentVehicle.status}
              label="Status"
              onChange={handleVehicleStatusChange}
              MenuProps={{
                autoFocus: false,
                sx: {
                  zIndex: 10001,
                  '& .MuiPaper-root': {
                    zIndex: 10001
                  }
                }
              }}
              sx={{
                borderRadius: 1,
                '&:hover .MuiOutlinedInput-notchedOutline': {
                  borderColor: 'primary.main',
                },
              }}
            >
              {Object.values(VehicleStatus).map((status) => (
                <MenuItem 
                  key={status} 
                  value={status}
                  sx={{
                    '&:hover': {
                      backgroundColor: 'action.hover'
                    }
                  }}
                >
                  {status}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseVehicleDialog}>Cancel</Button>
          <Button 
            onClick={handleCreateVehicle} 
            variant="contained" 
            color="primary"
            disabled={!currentVehicle.licensePlate}
          >
            Create Vehicle
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default VehicleTypeManagement; 