package com.wms.entity.user;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.ManyToMany;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.wms.entity.vehicle.Vehicle;

@Entity
@Table(name = "drivers")
@Data
@EqualsAndHashCode(callSuper = true)
@DiscriminatorValue("DRIVER")
public class Driver extends User {
    
    @NotBlank(message = "License number is required")
    @Size(max = 20)
    @Column(name = "license_no")
    private String licenseNumber;
    
    @ManyToMany(mappedBy = "assignedDrivers")
    @JsonIgnore
    private List<Vehicle> vehicles = new ArrayList<>();
}
