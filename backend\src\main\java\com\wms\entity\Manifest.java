package com.wms.entity;

import com.wms.entity.user.Client;
import com.wms.entity.user.Driver;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty.Access;

@Data
@Entity
@Table(name = "manifests")
@EqualsAndHashCode(exclude = "pallets")
@ToString(exclude = "pallets")
public class Manifest {
    @Id
    @NotBlank(message = "Tracking Number is required")
    private String trackingNo;

    @ManyToOne
    @JoinColumn(name = "client_username", nullable = false)
    private Client client;

    @ManyToOne
    @JoinColumn(name = "container_no", nullable = false)
    private Container container;

    @ManyToOne
    @JoinColumn(name = "driver_username", nullable = true)
    private Driver driver;

    @Column(name = "sequence_no")
    private Integer sequenceNo;

    @Column(name = "internal_id")
    private String internalId;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private ManifestStatus status = ManifestStatus.CREATED;

    @Column(name = "customer_name", nullable = false)
    private String customerName;

    @Column(name = "phone_no", nullable = false)
    private String phoneNo;

    @Column(name = "address", nullable = false)
    private String address;

    @Column(name = "postal_code", nullable = false)
    private String postalCode;

    @Column(name = "country", nullable = false)
    private String country;

    @Column(name = "pieces", nullable = false)
    private Integer pieces;

    @Column(name = "cbm", nullable = false)
    private Double cbm;

    @Column(name = "weight", nullable = false)
    private Double weight;

    @Column(name = "created_date")
    @JsonFormat(pattern = "dd MMM yyyy HH:mm", timezone = "Asia/Singapore")
    private LocalDateTime createdDate;

    @OneToMany(mappedBy = "manifest", cascade = CascadeType.ALL, fetch = FetchType.LAZY, orphanRemoval = true)
    @JsonManagedReference
    private List<Pallet> pallets = new ArrayList<>();

    private String location;

    @Column(name = "delivery_date")
    @JsonFormat(pattern = "dd MMM yyyy", timezone = "Asia/Singapore")
    private LocalDate deliveryDate;

    @Column(name = "time_slot", length = 50)
    private String timeSlot;

    @Column(name = "delivered_date")
    @JsonFormat(pattern = "dd MMM yyyy HH:mm", timezone = "Asia/Singapore")
    private LocalDateTime deliveredDate;
    
    // Remove the custom setter since we're now using LocalDate which doesn't have time components

    @Column(name = "delivery_vehicle")
    private String deliveryVehicle;

    @Column(name = "driver_remarks")
    private String driverRemarks;

    private String remarks;

    @PrePersist
    @PreUpdate
    protected void prePersist() {
        if (container != null && sequenceNo != null) {
            this.internalId = container.getContainerNo() + "-" 	+ sequenceNo;
        }
        
        if (this.createdDate == null) {
            this.createdDate = LocalDateTime.now();
        }
    }

    public void addPallet(Pallet pallet) {
        pallets.add(pallet);
        pallet.setManifest(this);
    }
    
    public void removePallet(Pallet pallet) {
        pallets.remove(pallet);
        pallet.setManifest(null);
    }
    
    public void clearPallets() {
        for (Pallet pallet : pallets) {
            pallet.setManifest(null);
        }
        pallets.clear();
    }
    
    @JsonProperty("noOfPalletsFromObjects")
    public Integer getNoOfPalletsFromObjects() {
        return pallets != null ? pallets.size() : 0;
    }

    @JsonProperty(value = "actualPalletsCount", access = Access.READ_ONLY)
    public Integer getActualPalletsCount() {
        return pallets != null ? pallets.size() : 0;
    }

    public Double getWeight() {
        return weight;
    }

    public void setWeight(Double weight) {
        this.weight = weight;
    }

    public String getDeliveryVehicle() {
        return deliveryVehicle;
    }

    public void setDeliveryVehicle(String deliveryVehicle) {
        this.deliveryVehicle = deliveryVehicle;
    }
} 