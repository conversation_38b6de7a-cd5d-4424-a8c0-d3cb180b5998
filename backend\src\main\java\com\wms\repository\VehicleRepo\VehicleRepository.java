package com.wms.repository.VehicleRepo;

import com.wms.entity.vehicle.Vehicle;
import com.wms.entity.vehicle.VehicleType;
import com.wms.entity.user.Driver;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface VehicleRepository extends JpaRepository<Vehicle, Long> {
    Optional<Vehicle> findByLicensePlate(String licensePlate);
    boolean existsByLicensePlate(String licensePlate);
    List<Vehicle> findByVehicleType(VehicleType vehicleType);
    List<Vehicle> findByAssignedDriversContaining(Driver driver);
} 