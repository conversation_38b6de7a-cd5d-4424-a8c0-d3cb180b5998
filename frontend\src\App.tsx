import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { StyledEngineProvider } from '@mui/material';
import { CacheProvider } from '@emotion/react';
import CssBaseline from '@mui/material/CssBaseline';
import Login from './pages/Login';
import Dashboard from './pages/Dashboard';
import ModernDashboard from './pages/ModernDashboard';
import UserManagement from './pages/UserManagement';
import ContainerManagement from './pages/ContainerManagement';
import ClientContainerManagement from './pages/ClientContainerManagement';
import ContainerDetailTest from './pages/ContainerDetailTest';
import ManifestDetail from './pages/ManifestDetail';
import ManifestManagement from './pages/ManifestManagement';
import LocationZoneManagement from './pages/LocationZoneManagement';
import VehicleTypeManagement from './pages/VehicleTypeManagement';
import VehicleManagement from './pages/VehicleManagement';
import ManifestListTest from './pages/ManifestListTest';
import DashboardLayout from './components/DashboardLayout/index';
import TestApi from './pages/TestApi';
import PrivateRoute from './components/PrivateRoute';
import { AuthProvider } from './contexts/AuthContext';
import { ToastProvider } from './contexts/ToastContext';
import SessionExpirationHandler from './components/SessionExpirationHandler';
import { emotionCache } from './theme';
import { ThemeProvider } from './contexts/ThemeContext';

function App() {
  return (
    <ThemeProvider>
      <CacheProvider value={emotionCache}>
        <StyledEngineProvider injectFirst>
          <CssBaseline />
          <AuthProvider>
            <ToastProvider>
              <SessionExpirationHandler />
              <Router>
                <Routes>
                  <Route path="/login" element={<Login />} />
                  
                  <Route path="/test-api" element={<TestApi />} />
                  
                  <Route
                    path="/dashboard"
                    element={
                      <PrivateRoute>
                        <DashboardLayout>
                          <Dashboard key={window.location.pathname} />
                        </DashboardLayout>
                      </PrivateRoute>
                    }
                  />
                  
                  <Route
                    path="/modern-dashboard"
                    element={
                      <PrivateRoute>
                        <DashboardLayout>
                          <ModernDashboard key={window.location.pathname} />
                        </DashboardLayout>
                      </PrivateRoute>
                    }
                  />
                  
                  <Route
                    path="/users"
                    element={
                      <PrivateRoute roles={['ROLE_ADMIN']}>
                        <DashboardLayout>
                          <UserManagement key={window.location.pathname} />
                        </DashboardLayout>
                      </PrivateRoute>
                    }
                  />
                  
                  <Route
                    path="/location-zones"
                    element={
                      <PrivateRoute roles={['ROLE_ADMIN']}>
                        <DashboardLayout>
                          <LocationZoneManagement key={window.location.pathname} />
                        </DashboardLayout>
                      </PrivateRoute>
                    }
                  />
                  
                  <Route
                    path="/vehicle-types"
                    element={
                      <PrivateRoute roles={['ROLE_ADMIN', 'ROLE_MANAGER']}>
                        <DashboardLayout>
                          <VehicleTypeManagement key={window.location.pathname} />
                        </DashboardLayout>
                      </PrivateRoute>
                    }
                  />
                  
                  <Route
                    path="/vehicles"
                    element={
                      <PrivateRoute roles={['ROLE_ADMIN', 'ROLE_MANAGER']}>
                        <DashboardLayout>
                          <VehicleManagement key={window.location.pathname} />
                        </DashboardLayout>
                      </PrivateRoute>
                    }
                  />
                  
                  <Route
                    path="/containers"
                    element={
                      <PrivateRoute roles={['ROLE_ADMIN', 'ROLE_MANAGER']}>
                        <DashboardLayout>
                          <ContainerManagement key={window.location.pathname} />
                        </DashboardLayout>
                      </PrivateRoute>
                    }
                  />
                  
                  <Route
                    path="/containers/:containerNo"
                    element={
                      <PrivateRoute roles={['ROLE_ADMIN', 'ROLE_MANAGER']}>
                        <DashboardLayout>
                          <ContainerDetailTest key={window.location.pathname} />
                        </DashboardLayout>
                      </PrivateRoute>
                    }
                  />

                  <Route
                    path="/containers/:containerNo/test"
                    element={
                      <PrivateRoute roles={['ROLE_ADMIN', 'ROLE_MANAGER']}>
                        <DashboardLayout>
                          <ContainerDetailTest key={window.location.pathname} />
                        </DashboardLayout>
                      </PrivateRoute>
                    }
                  />
                  
                  <Route
                    path="/manifests/edit/:trackingNo"
                    element={
                      <PrivateRoute roles={['ROLE_ADMIN', 'ROLE_MANAGER']}>
                        <DashboardLayout>
                          <ManifestListTest key={window.location.pathname} />
                        </DashboardLayout>
                      </PrivateRoute>
                    }
                  />
                  
                  <Route
                    path="/manifests/:trackingNo"
                    element={
                      <PrivateRoute roles={['ROLE_ADMIN', 'ROLE_MANAGER']}>
                        <DashboardLayout>
                          <ManifestDetail key={window.location.pathname} />
                        </DashboardLayout>
                      </PrivateRoute>
                    }
                  />
                  
                  <Route
                    path="/manifests"
                    element={
                      <PrivateRoute roles={['ROLE_ADMIN', 'ROLE_MANAGER']}>
                        <DashboardLayout>
                          <ManifestListTest key={window.location.pathname} />
                        </DashboardLayout>
                      </PrivateRoute>
                    }
                  />

                  <Route
                    path="/manifests-test"
                    element={
                      <PrivateRoute roles={['ROLE_ADMIN', 'ROLE_MANAGER']}>
                        <DashboardLayout>
                          <ManifestManagement key={window.location.pathname} />
                        </DashboardLayout>
                      </PrivateRoute>
                    }
                  />

                  <Route
                    path="/manifests-test/edit/:trackingNo"
                    element={
                      <PrivateRoute roles={['ROLE_ADMIN', 'ROLE_MANAGER']}>
                        <DashboardLayout>
                          <ManifestManagement key={window.location.pathname} />
                        </DashboardLayout>
                      </PrivateRoute>
                    }
                  />
                  
                  <Route
                    path="/my-containers"
                    element={
                      <PrivateRoute roles={['ROLE_CLIENT']}>
                        <DashboardLayout>
                          <ClientContainerManagement key={window.location.pathname} />
                        </DashboardLayout>
                      </PrivateRoute>
                    }
                  />
                  
                  <Route
                    path="/my-manifests"
                    element={
                      <PrivateRoute roles={['ROLE_DRIVER']}>
                        <DashboardLayout>
                          <div key={window.location.pathname}>Driver Manifests Page</div>
                        </DashboardLayout>
                      </PrivateRoute>
                    }
                  />
                  
                  <Route
                    path="/profile"
                    element={
                      <PrivateRoute>
                        <DashboardLayout>
                          <div key={window.location.pathname}>User Profile Page</div>
                        </DashboardLayout>
                      </PrivateRoute>
                    }
                  />
                  
                  <Route path="/" element={<Navigate to="/dashboard" replace />} />
                  <Route path="*" element={<Navigate to="/login" replace />} />
                </Routes>
              </Router>
            </ToastProvider>
          </AuthProvider>
        </StyledEngineProvider>
      </CacheProvider>
    </ThemeProvider>
  );
}

export default App;
