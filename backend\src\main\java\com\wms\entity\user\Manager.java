package com.wms.entity.user;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.DiscriminatorValue;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Entity
@Table(name = "managers")
@Data
@EqualsAndHashCode(callSuper = true)
@DiscriminatorValue("MANAGER")
public class Manager extends User {

    @NotBlank(message = "Department is required")
    @Column(name = "department")
    private String department;
}