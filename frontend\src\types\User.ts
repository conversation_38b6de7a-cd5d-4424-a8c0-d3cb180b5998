export interface User {
  userNumber: number;
  username: string;
  email: string;
  fullName: string;
  contact: string;
  status: 'ACTIVE' | 'INACTIVE';
  roles: string[];
  token?: string;
}

export interface Client extends User {
  companyName: string;
  designation: string;
  altDesignation?: string;
  altContact?: string;
}

export interface Manager extends User {
  department: string;
}

export interface Admin extends User {
  // Admin specific fields can be added here if needed
}

export interface Driver extends User {
  licenseNumber: string;
} 