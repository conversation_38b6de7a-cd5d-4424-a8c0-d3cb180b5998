import { useState } from 'react';
import { Manifest } from '../../../types/manifest';

export function useManifestSelection() {
  const [selectedManifests, setSelectedManifests] = useState<Manifest[]>([]);
  
  const handleSelectionChange = (selected: Manifest[]) => {
    setSelectedManifests(selected);
  };
  
  const clearSelection = () => {
    setSelectedManifests([]);
  };
  
  return {
    selectedManifests,
    handleSelectionChange,
    clearSelection
  };
} 