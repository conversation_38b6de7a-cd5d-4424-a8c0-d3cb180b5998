import axios from 'axios';
import { LocationZone } from '../types/location';
import { ApiResponse } from '../types/api';
import API_CONFIG from '../config/api.config';

const API_BASE_URL = API_CONFIG.baseUrl;

class LocationService {
  async getAllLocationZones(token: string): Promise<ApiResponse<LocationZone[]>> {
    const response = await axios.get<ApiResponse<LocationZone[]>>(`${API_BASE_URL}/api/location-zones`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    return response.data;
  }

  async getActiveLocationZones(token: string): Promise<ApiResponse<LocationZone[]>> {
    const response = await axios.get<ApiResponse<LocationZone[]>>(`${API_BASE_URL}/api/location-zones/active`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    return response.data;
  }

  async getLocationZoneById(id: number, token: string): Promise<ApiResponse<LocationZone>> {
    const response = await axios.get<ApiResponse<LocationZone>>(`${API_BASE_URL}/api/location-zones/${id}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    return response.data;
  }

  async createLocationZone(locationZone: LocationZone, token: string): Promise<ApiResponse<LocationZone>> {
    const response = await axios.post<ApiResponse<LocationZone>>(
      `${API_BASE_URL}/api/location-zones`, 
      locationZone,
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      }
    );
    return response.data;
  }

  async updateLocationZone(id: number, locationZone: LocationZone, token: string): Promise<ApiResponse<LocationZone>> {
    const response = await axios.put<ApiResponse<LocationZone>>(
      `${API_BASE_URL}/api/location-zones/${id}`, 
      locationZone,
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      }
    );
    return response.data;
  }

  async deleteLocationZone(id: number, token: string): Promise<ApiResponse<string>> {
    const response = await axios.delete<ApiResponse<string>>(
      `${API_BASE_URL}/api/location-zones/${id}`,
      {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      }
    );
    return response.data;
  }

  async getLocationZoneByPostalCode(postalCode: string, token: string): Promise<ApiResponse<LocationZone>> {
    const response = await axios.get<ApiResponse<LocationZone>>(
      `${API_BASE_URL}/api/location-zones/postal-code/${postalCode}`,
      {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      }
    );
    return response.data;
  }

  async getLocationAbbreviation(locationName: string, token: string): Promise<ApiResponse<string>> {
    const response = await axios.get<ApiResponse<string>>(
      `${API_BASE_URL}/api/location-zones/abbreviation/${encodeURIComponent(locationName)}`,
      {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      }
    );
    return response.data;
  }
}

export default new LocationService(); 