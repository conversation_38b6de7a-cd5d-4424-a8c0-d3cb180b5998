package com.wms.controller;

import com.wms.entity.Role;
import com.wms.entity.user.Admin;
import com.wms.entity.user.Manager;
import com.wms.entity.user.UserStatus;
import com.wms.entity.user.User;
import com.wms.repository.RoleRepository;
import com.wms.repository.UserRepo.AdminRepository;
import com.wms.repository.UserRepo.ManagerRepository;
import com.wms.repository.UserRepo.UserRepository;
import jakarta.persistence.EntityManager;
import jakarta.persistence.LockModeType;
import jakarta.persistence.PersistenceContext;
import jakarta.transaction.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;
import org.springframework.http.HttpStatus;

@ControllerAdvice
@RestController
public class GlobalControllerAdvice {
    private static final Logger logger = LoggerFactory.getLogger(GlobalControllerAdvice.class);

    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private AdminRepository adminRepository;
    
    @Autowired
    private ManagerRepository managerRepository;
    
    @Autowired
    private RoleRepository roleRepository;
    
    @Value("${spring.sql.init.mode:always}")
    private String sqlInitMode;
    
    @Value("${spring.profiles.active:dev}")
    private String activeProfile;

    @GetMapping("/favicon.ico")
    public ResponseEntity<?> favicon() {
        return ResponseEntity.noContent().build();
    }
    
    @GetMapping("/")
    public ResponseEntity<?> root() {
        Map<String, Object> response = new HashMap<>();
        response.put("status", "UP");
        response.put("timestamp", System.currentTimeMillis());
        response.put("message", "WMS API is running");
        response.put("profile", activeProfile);
        response.put("sqlInit", sqlInitMode);
        
        // Add database statistics
        try {
            long userCount = userRepository.count();
            response.put("userCount", userCount);
            
            long adminCount = adminRepository.count();
            response.put("adminCount", adminCount);
            
            long managerCount = managerRepository.count();
            response.put("managerCount", managerCount);
            
            long roleCount = roleRepository.count();
            response.put("roleCount", roleCount);
        } catch (Exception e) {
            logger.error("Error getting database statistics: {}", e.getMessage());
            response.put("dbStatsError", e.getMessage());
        }
        
        return ResponseEntity.ok(response);
    }
} 