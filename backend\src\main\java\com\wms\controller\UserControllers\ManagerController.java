package com.wms.controller.UserControllers;

import com.wms.dto.ApiResponse;
import com.wms.entity.user.Manager;
import com.wms.entity.user.UserStatus;
import com.wms.security.services.UserDetailsImpl;
import com.wms.service.UserServices.ManagerService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/managers")
@CrossOrigin(origins = "*")
@PreAuthorize("hasAnyRole('ADMIN', 'MANAGER')")
public class ManagerController {

    private final ManagerService managerService;

    @Autowired
    public ManagerController(ManagerService managerService) {
        this.managerService = managerService;
    }

    @GetMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<List<Manager>>> getAllManagers() {
        List<Manager> managers = managerService.getAllManagers();
        return ResponseEntity.ok(ApiResponse.success(managers));
    }

    @GetMapping("/{username}")
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER')")
    public ResponseEntity<ApiResponse<Manager>> getManagerByUsername(@PathVariable String username) {
        try {
            // Get the current authenticated user
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            UserDetailsImpl userDetails = (UserDetailsImpl) authentication.getPrincipal();
            
            // If the user is a manager, they can only view their own details
            if (userDetails.getAuthorities().stream().anyMatch(a -> a.getAuthority().equals("ROLE_MANAGER"))) {
                if (!userDetails.getUsername().equals(username)) {
                    return ResponseEntity.status(HttpStatus.FORBIDDEN)
                            .body(ApiResponse.error("You can only view your own details"));
                }
            }
            
            Manager manager = managerService.getManagerByUsername(username);
            return ResponseEntity.ok(ApiResponse.success(manager));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.notFound().build();
        }
    }

    @PostMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Manager>> createManager(@Valid @RequestBody Manager manager) {
        try {
            Manager createdManager = managerService.createManager(manager);
            return ResponseEntity.ok(ApiResponse.success("Manager created successfully", createdManager));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    @PutMapping("/{username}")
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER')")
    public ResponseEntity<ApiResponse<Manager>> updateManager(
            @PathVariable String username,
            @Valid @RequestBody Manager manager) {
        try {
            // Get the current authenticated user
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            UserDetailsImpl userDetails = (UserDetailsImpl) authentication.getPrincipal();
            
            // If the user is a manager, they can only update their own details
            if (userDetails.getAuthorities().stream().anyMatch(a -> a.getAuthority().equals("ROLE_MANAGER"))) {
                if (!userDetails.getUsername().equals(username)) {
                    return ResponseEntity.status(HttpStatus.FORBIDDEN)
                            .body(ApiResponse.error("You can only update your own details"));
                }
            }
            
            Manager updatedManager = managerService.updateManager(username, manager);
            return ResponseEntity.ok(ApiResponse.success("Manager updated successfully", updatedManager));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    @PutMapping("/{username}/deactivate")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Manager>> deactivateManager(@PathVariable String username) {
        try {
            Manager deactivatedManager = managerService.deactivateManager(username);
            return ResponseEntity.ok(ApiResponse.success("Manager deactivated successfully", deactivatedManager));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }
} 