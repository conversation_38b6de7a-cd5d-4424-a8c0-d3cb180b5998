package com.wms.entity;

import com.wms.entity.user.User;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "manifest_tracking_logs")
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonIgnoreProperties(value = {"hibernateLazyInitializer", "handler"})
public class ManifestTrackingLog {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "tracking_no", nullable = false)
    private String trackingNo;
    
    @Column(name = "previous_status")
    private String previousStatus;
    
    @Column(name = "new_status", nullable = false)
    private String newStatus;
    
    @Column(name = "field_name")
    private String fieldName;
    
    @Column(name = "old_value", columnDefinition = "TEXT")
    private String oldValue;
    
    @Column(name = "new_value", columnDefinition = "TEXT")
    private String newValue;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "action_type", nullable = false)
    private ActionType actionType;
    
    @Column(name = "updated_by", nullable = false)
    private String updatedBy;
    
    @Column(name = "updated_at", nullable = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;
    
    @Column(name = "remarks", columnDefinition = "TEXT")
    private String remarks;
    
    @Column(name = "ip_address")
    private String ipAddress;
    
    @Column(name = "user_agent")
    private String userAgent;
    
    // Reference to the manifest (fetch only when needed to avoid circular references)
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "tracking_no", insertable = false, updatable = false)
    @JsonIgnore
    private Manifest manifest;
    
    // Reference to the user who made the change
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "updated_by", insertable = false, updatable = false)
    @JsonIgnore
    private User user;
    
    public enum ActionType {
        CREATED,
        UPDATED,
        STATUS_CHANGED,
        ASSIGNED,
        DELIVERED,
        DELETED,
        PALLET_ADDED,
        PALLET_DELETED
    }
    
    // Helper constructor for simple status changes
    public ManifestTrackingLog(String trackingNo, String previousStatus, String newStatus, 
                              String updatedBy, String remarks) {
        this.trackingNo = trackingNo;
        this.previousStatus = previousStatus;
        this.newStatus = newStatus;
        this.updatedBy = updatedBy;
        this.remarks = remarks;
        this.actionType = ActionType.STATUS_CHANGED;
        this.updatedAt = LocalDateTime.now();
    }
    
    // Helper constructor for field updates
    public ManifestTrackingLog(String trackingNo, String fieldName, String oldValue, 
                              String newValue, String updatedBy, String remarks) {
        this.trackingNo = trackingNo;
        this.fieldName = fieldName;
        this.oldValue = oldValue;
        this.newValue = newValue;
        this.updatedBy = updatedBy;
        this.remarks = remarks;
        this.actionType = ActionType.UPDATED;
        this.updatedAt = LocalDateTime.now();
    }
} 