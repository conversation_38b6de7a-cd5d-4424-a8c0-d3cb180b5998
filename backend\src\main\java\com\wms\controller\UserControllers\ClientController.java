package com.wms.controller.UserControllers;

import com.wms.dto.ApiResponse;
import com.wms.entity.user.Client;
import com.wms.entity.user.UserStatus;
import com.wms.security.services.UserDetailsImpl;
import com.wms.service.UserServices.ClientService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@CrossOrigin(origins = "*")
@PreAuthorize("hasAnyRole('ADMIN', 'MANAGER', 'CLIENT')")
public class ClientController {

    private final ClientService clientService;

    @Autowired
    public ClientController(ClientService clientService) {
        this.clientService = clientService;
    }

    @GetMapping({"/api/clients", "/clients"})
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER')")
    public ResponseEntity<ApiResponse<List<Client>>> getAllClients() {
        List<Client> clients = clientService.getAllClients();
        return ResponseEntity.ok(ApiResponse.success(clients));
    }

    @GetMapping({"/api/clients/{username}", "/clients/{username}"})
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER', 'CLIENT')")
    public ResponseEntity<ApiResponse<Client>> getClientByUsername(@PathVariable String username) {
        try {
            // Get the current authenticated user
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            UserDetailsImpl userDetails = (UserDetailsImpl) authentication.getPrincipal();
            
            // If the user is a client, they can only view their own details
            if (userDetails.getAuthorities().stream().anyMatch(a -> a.getAuthority().equals("ROLE_CLIENT"))) {
                if (!userDetails.getUsername().equals(username)) {
                    return ResponseEntity.status(HttpStatus.FORBIDDEN)
                            .body(ApiResponse.error("You can only view your own details"));
                }
            }
            
            Client client = clientService.getClientByUsername(username)
                    .orElseThrow(() -> new IllegalArgumentException("Client not found"));
            return ResponseEntity.ok(ApiResponse.success(client));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.notFound().build();
        }
    }

    @PostMapping({"/api/clients", "/clients"})
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER')")
    public ResponseEntity<ApiResponse<Client>> createClient(@Valid @RequestBody Client client) {
        try {
            Client createdClient = clientService.createClient(client);
            return ResponseEntity.ok(ApiResponse.success("Client created successfully", createdClient));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    @PutMapping({"/api/clients/{username}", "/clients/{username}"})
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER', 'CLIENT')")
    public ResponseEntity<ApiResponse<Client>> updateClient(
            @PathVariable String username,
            @Valid @RequestBody Client client) {
        try {
            // Get the current authenticated user
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            UserDetailsImpl userDetails = (UserDetailsImpl) authentication.getPrincipal();
            
            // If the user is a client, they can only update their own details
            if (userDetails.getAuthorities().stream().anyMatch(a -> a.getAuthority().equals("ROLE_CLIENT"))) {
                if (!userDetails.getUsername().equals(username)) {
                    return ResponseEntity.status(HttpStatus.FORBIDDEN)
                            .body(ApiResponse.error("You can only update your own details"));
                }
            }
            
            Client updatedClient = clientService.updateClient(username, client);
            return ResponseEntity.ok(ApiResponse.success("Client updated successfully", updatedClient));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    @PutMapping({"/api/clients/{username}/deactivate", "/clients/{username}/deactivate"})
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER')")
    public ResponseEntity<ApiResponse<Client>> deactivateClient(@PathVariable String username) {
        try {
            Client deactivatedClient = clientService.deactivateClient(username);
            return ResponseEntity.ok(ApiResponse.success("Client deactivated successfully", deactivatedClient));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }
} 