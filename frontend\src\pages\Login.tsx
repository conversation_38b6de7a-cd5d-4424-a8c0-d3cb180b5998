import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Button,
  TextField,
  Typography,
  Container,
  Paper,
  Alert,
  Avatar,
  useTheme,
  Divider,
} from '@mui/material';
import WarehouseIcon from '@mui/icons-material/Warehouse';
import axios from 'axios';
import { useAuth } from '../contexts/AuthContext';
import usePageTitle from '../hooks/usePageTitle';
import API_CONFIG from '../config/api.config';

interface AuthResponse {
  token: string;
  id: number;
  username: string;
  email: string;
  roles: string[];
}

const Login: React.FC = () => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const { setCurrentUser, currentUser } = useAuth();
  const theme = useTheme();

  // Set page title
  usePageTitle('Login');

  console.log("Login component rendered");

  // Added test mode to toggle between real auth and test auth
  const useTestEndpoint = false;
  
  // Check if the user is already logged in
  useEffect(() => {
    if (currentUser && currentUser.token) {
      console.log('User already logged in, redirecting to dashboard');
      navigate('/dashboard', { replace: true });
    }
  }, [currentUser, navigate]);

  const handleLogin = () => {
    console.log("Login button clicked");
    if (loading) {
      console.log("Already loading, ignoring click");
      return;
    }
    
    setLoading(true);
    setError('');

    console.log('Attempting login with:', { 
      username,
      password: password ? '********' : 'missing'
    });
    
    // Use a setTimeout to ensure we don't get caught in a refresh cycle
    setTimeout(async () => {
      try {
        // Choose which endpoint to use
        const endpoint = useTestEndpoint 
          ? `${API_CONFIG.baseUrl}${API_CONFIG.testAuthEndpoint}/login`
          : `${API_CONFIG.baseUrl}${API_CONFIG.authEndpoint}/signin`;
          
        console.log("Making API request to:", endpoint);
        
        const response = await axios.post<AuthResponse>(
          endpoint,
          {
            username,
            password,
          },
          {
            timeout: 100000,
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json'
            },
            withCredentials: false
          }
        );

        console.log('Login response received:', {
          token: response.data.token ? 'exists' : 'missing',
          roles: response.data.roles,
          username: response.data.username
        });

        if (response.data.token) {
          // Set the default Authorization header for future requests
          axios.defaults.headers.common['Authorization'] = `Bearer ${response.data.token}`;
          
          setCurrentUser(response.data);
          console.log('Updated AuthProvider state');
          console.log('Navigating to dashboard...');
          navigate('/dashboard', { replace: true });
        }
      } catch (err: any) {
        console.error('Login error:', err);
        
        if (err.response) {
          console.error('Error response:', {
            status: err.response.status,
            data: err.response.data,
            headers: err.response.headers
          });
        }
        
        // Handle specific error cases
        if (err.message === 'Request timed out. Please try again.') {
          setError('The request timed out. Please check your internet connection and try again.');
        } else if (err.message === 'Invalid username or password') {
          setError('Invalid username or password. Please try again.');
        } else if (err.message === 'Login service not found') {
          setError('Unable to reach the login service. Please try again later.');
        } else if (err.message === 'Server error occurred') {
          setError('A server error occurred. Please try again later.');
        } else if (err.response?.data?.message) {
          setError(err.response.data.message);
        } else {
          setError('An unexpected error occurred. Please try again.');
        }
      } finally {
        setLoading(false);
      }
    }, 10);
  };

  // Don't use onKeyPress as it might trigger form submission
  const handleEnterKey = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      console.log("Enter key pressed");
      e.preventDefault();
      e.stopPropagation();
      handleLogin();
    }
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        width: '100%',
        position: 'fixed',
        top: 0,
        left: 0,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: theme.palette.mode === 'light' 
          ? 'rgba(25, 118, 210, 0.05)' 
          : theme.palette.background.default,
        backgroundImage: 'linear-gradient(rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.9))',
        overflow: 'auto',
        padding: 2,
      }}
    >
      <Container component="main" maxWidth="xs" sx={{ my: 4 }}>
        <Paper 
          elevation={6} 
          sx={{ 
            p: { xs: 3, sm: 4 }, 
            width: '100%',
            borderRadius: 2,
            boxShadow: '0 8px 24px rgba(0, 0, 0, 0.12)',
            background: 'white',
          }}
        >
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
            }}
          >
            <Avatar sx={{ 
              m: 1, 
              bgcolor: theme.palette.primary.main, 
              width: 70, 
              height: 70,
              boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',
            }}>
              <WarehouseIcon sx={{ fontSize: 40 }} />
            </Avatar>
            <Typography component="h1" variant="h5" sx={{ fontWeight: 'bold', mt: 1 }}>
              Fukuyama WMS Login {useTestEndpoint ? '(Test Mode)' : ''}
            </Typography>
            <Typography variant="subtitle1" color="text.secondary" sx={{ mt: 1, mb: 3, textAlign: 'center' }}>
              Fukuyama Logistics Warehouse Management System
            </Typography>
            
            <Divider sx={{ width: '100%', mb: 3 }} />
          
            {error && (
              <Alert 
                severity="error" 
                variant="filled"
                sx={{ 
                  mb: 3, 
                  width: '100%',
                  '& .MuiAlert-message': { 
                    fontWeight: 'medium'
                  }
                }}
              >
                {error}
              </Alert>
            )}
          
            <Box component="div" sx={{ width: '100%' }}>
              <TextField
                margin="normal"
                required
                fullWidth
                id="username"
                label="Username"
                name="username"
                autoComplete="username"
                autoFocus
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                onKeyDown={handleEnterKey}
                sx={{ mb: 2 }}
              />
              <TextField
                margin="normal"
                required
                fullWidth
                name="password"
                label="Password"
                type="password"
                id="password"
                autoComplete="current-password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                onKeyDown={handleEnterKey}
                sx={{ mb: 3 }}
              />
              <Button
                type="button"
                onClick={handleLogin}
                fullWidth
                variant="contained"
                size="large"
                disabled={loading}
                sx={{ 
                  mt: 2, 
                  mb: 2, 
                  py: 1.5,
                  fontWeight: 'bold',
                  borderRadius: 1.5,
                  boxShadow: '0 4px 12px rgba(25, 118, 210, 0.3)',
                  '&:hover': {
                    boxShadow: '0 6px 16px rgba(25, 118, 210, 0.4)',
                  }
                }}
              >
                {loading ? 'Signing in...' : 'Sign In'}
              </Button>
            </Box>
          </Box>
        </Paper>
      </Container>
    </Box>
  );
};

export default Login; 