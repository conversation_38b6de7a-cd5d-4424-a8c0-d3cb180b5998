package com.wms.repository.UserRepo;

import com.wms.entity.user.Driver;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface DriverRepository extends JpaRepository<Driver, String> {
    @Query("SELECT d FROM Driver d LEFT JOIN FETCH d.roles WHERE d.email = :email")
    Optional<Driver> findByEmail(String email);

    @Query("SELECT d FROM Driver d LEFT JOIN FETCH d.roles WHERE d.username = :username")
    Optional<Driver> findByUsername(String username);

    @Query("SELECT COUNT(d) > 0 FROM Driver d WHERE d.email = :email")
    boolean existsByEmail(String email);

    @Query("SELECT COUNT(d) > 0 FROM Driver d WHERE d.username = :username")
    boolean existsByUsername(String username);
} 