package com.wms.security.services;

import com.wms.entity.user.User;
import com.wms.entity.user.UserStatus;
import com.wms.repository.UserRepo.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class UserDetailsServiceImpl implements UserDetailsService {
    @Autowired
    UserRepository userRepository;

    @Override
    @Transactional
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new UsernameNotFoundException("User Not Found with username: " + username));

        if (user.getStatus() == UserStatus.INACTIVE) {
            throw new UsernameNotFoundException("Your account has been deactivated. Please contact the administrator for assistance.");
        }

        return UserDetailsImpl.build(user);
    }
} 