import React, { useState, useEffect, useImperativeHandle, forwardRef } from 'react';
import {
  Box,
  Typography,
  Chip,
  CircularProgress,
  Alert,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Paper,
  Stack,
  IconButton,
  Tooltip
} from '@mui/material';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import {
  History as HistoryIcon,
  Update as UpdateIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
  Assignment as AssignmentIcon,
  CheckCircle as CheckCircleIcon,
  Timeline as TimelineIcon,
  Person as PersonIcon,
  Schedule as ScheduleIcon,
  Filter as FilterIcon,
  Refresh as RefreshIcon,
  ArrowForward as ArrowForwardIcon,
  Inventory as InventoryIcon,
  Clear as ClearIcon
} from '@mui/icons-material';
import manifestService from '../services/manifest.service';
import { useAuth } from '../contexts/AuthContext';
import { ManifestTrackingHistory, TrackingLogActionType } from '../types/manifest';
import { ApiResponse } from '../types/api';
import { formatDateString } from '../utils/dateUtils';
import { useToast } from '../contexts/ToastContext';

interface ManifestTrackingHistoryProps {
  trackingNo: string;
  onClose?: () => void;
  isDialog?: boolean;
}

export interface ManifestTrackingHistoryRef {
  refreshTrackingHistory: () => void;
}

const ManifestTrackingHistoryComponent = forwardRef<ManifestTrackingHistoryRef, ManifestTrackingHistoryProps>(({
  trackingNo,
  onClose,
  isDialog = false
}, ref) => {
  const { currentUser, hasRole } = useAuth();
  const toast = useToast();
  const [trackingHistory, setTrackingHistory] = useState<ManifestTrackingHistory[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filterDialogOpen, setFilterDialogOpen] = useState(false);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [deleteAllConfirmOpen, setDeleteAllConfirmOpen] = useState(false);
  const [selectedLogId, setSelectedLogId] = useState<number | null>(null);
  const [deleting, setDeleting] = useState(false);
  const [dateRange, setDateRange] = useState<{
    startDate: Date | null;
    endDate: Date | null;
  }>({
    startDate: null,
    endDate: null
  });
  const [actionTypeFilter, setActionTypeFilter] = useState<TrackingLogActionType | ''>('');

  const isAdmin = hasRole('ROLE_ADMIN');

  const fetchTrackingHistory = async () => {
    if (!currentUser?.token) {
      setError('Authentication required');
      return;
    }

    try {
      setLoading(true);
      setError(null);
      
      const response = await manifestService.getManifestTrackingHistory(trackingNo, currentUser.token);
      
      if (response.success && response.data) {
        setTrackingHistory(response.data);
      } else {
        setError(response.message || 'Failed to fetch tracking history');
      }
    } catch (err: any) {
      console.error('Error fetching tracking history:', err);
      setError('An error occurred while fetching tracking history');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTrackingHistory();
  }, [trackingNo, currentUser?.token, dateRange, actionTypeFilter]);

  // Expose refresh method to parent components
  useImperativeHandle(ref, () => ({
    refreshTrackingHistory: fetchTrackingHistory
  }), [trackingNo, currentUser?.token, dateRange, actionTypeFilter]);

  const getActionIcon = (actionType: TrackingLogActionType) => {
    switch (actionType) {
      case TrackingLogActionType.CREATED:
        return <AddIcon />;
      case TrackingLogActionType.UPDATED:
        return <UpdateIcon />;
      case TrackingLogActionType.STATUS_CHANGED:
        return <TimelineIcon />;
      case TrackingLogActionType.ASSIGNED:
        return <AssignmentIcon />;
      case TrackingLogActionType.DELIVERED:
        return <CheckCircleIcon />;
      case TrackingLogActionType.DELETED:
        return <DeleteIcon />;
      case TrackingLogActionType.PALLET_ADDED:
        return <InventoryIcon />;
      case TrackingLogActionType.PALLET_DELETED:
        return <InventoryIcon />;
      default:
        return <HistoryIcon />;
    }
  };

  const getActionColor = (actionType: TrackingLogActionType) => {
    switch (actionType) {
      case TrackingLogActionType.CREATED:
        return 'primary';
      case TrackingLogActionType.UPDATED:
        return 'info';
      case TrackingLogActionType.STATUS_CHANGED:
        return 'warning';
      case TrackingLogActionType.ASSIGNED:
        return 'secondary';
      case TrackingLogActionType.DELIVERED:
        return 'success';
      case TrackingLogActionType.DELETED:
        return 'error';
      case TrackingLogActionType.PALLET_ADDED:
        return 'success';
      case TrackingLogActionType.PALLET_DELETED:
        return 'error';
      default:
        return 'default';
    }
  };

  const handleFilterApply = () => {
    setFilterDialogOpen(false);
    fetchTrackingHistory();
  };

  const handleFilterReset = () => {
    setDateRange({ startDate: null, endDate: null });
    setActionTypeFilter('');
    setFilterDialogOpen(false);
  };

  const handleDeleteLog = async () => {
    if (!selectedLogId || !currentUser?.token) return;

    try {
      setDeleting(true);
      const response = await manifestService.deleteTrackingLog(selectedLogId, currentUser.token);
      
      if (response.success) {
        toast.success('Tracking log deleted successfully');
        fetchTrackingHistory(); // Refresh the list
      } else {
        toast.error(response.message || 'Failed to delete tracking log');
      }
    } catch (err: any) {
      console.error('Error deleting tracking log:', err);
      toast.error('An error occurred while deleting the tracking log');
    } finally {
      setDeleting(false);
      setDeleteConfirmOpen(false);
      setSelectedLogId(null);
    }
  };

  const handleDeleteAllLogs = async () => {
    if (!currentUser?.token) return;

    try {
      setDeleting(true);
      const response = await manifestService.deleteAllTrackingLogsForManifest(trackingNo, currentUser.token);
      
      if (response.success) {
        toast.success('All tracking logs cleared successfully');
        fetchTrackingHistory(); // Refresh the list
      } else {
        toast.error(response.message || 'Failed to clear tracking logs');
      }
    } catch (err: any) {
      console.error('Error clearing tracking logs:', err);
      toast.error('An error occurred while clearing tracking logs');
    } finally {
      setDeleting(false);
      setDeleteAllConfirmOpen(false);
    }
  };

  const confirmDeleteLog = (logId: number) => {
    setSelectedLogId(logId);
    setDeleteConfirmOpen(true);
  };

  const confirmDeleteAllLogs = () => {
    setDeleteAllConfirmOpen(true);
  };

  const renderContent = () => (
    <Box sx={{ p: 2 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <HistoryIcon fontSize="small" color="primary" />
          <Typography variant="subtitle2" sx={{ fontWeight: 600, color: 'text.primary' }}>
            Tracking History
          </Typography>
          <Chip 
            label={trackingHistory.length} 
            size="small" 
            variant="outlined" 
            sx={{ 
              height: 18, 
              fontSize: '0.65rem',
              minWidth: 'auto',
              '& .MuiChip-label': { px: 0.75 }
            }}
          />
        </Box>
        <Box sx={{ display: 'flex', gap: 0.5 }}>
          <Tooltip title="Filter logs">
            <IconButton
              size="small"
              onClick={() => setFilterDialogOpen(true)}
              sx={{ p: 0.5 }}
            >
              <FilterIcon fontSize="small" />
            </IconButton>
          </Tooltip>
          <Tooltip title="Refresh">
            <IconButton
              size="small"
              onClick={fetchTrackingHistory}
              sx={{ p: 0.5 }}
            >
              <RefreshIcon fontSize="small" />
            </IconButton>
          </Tooltip>
          {isAdmin && trackingHistory.length > 0 && (
            <Tooltip title="Clear all tracking logs (Admin only)">
              <IconButton
                size="small"
                onClick={confirmDeleteAllLogs}
                sx={{ p: 0.5, color: 'error.main' }}
                disabled={deleting}
              >
                <ClearIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          )}
        </Box>
      </Box>

      {/* Content */}
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', py: 4 }}>
          <CircularProgress size={20} />
        </Box>
      ) : error ? (
        <Alert severity="error" sx={{ fontSize: '0.8rem', py: 1 }}>
          {error}
        </Alert>
      ) : trackingHistory.length === 0 ? (
        <Box sx={{ 
          display: 'flex', 
          flexDirection: 'column', 
          alignItems: 'center', 
          justifyContent: 'center', 
          py: 4,
          color: 'text.secondary'
        }}>
          <HistoryIcon sx={{ fontSize: 32, mb: 1, opacity: 0.5 }} />
          <Typography variant="body2" sx={{ fontSize: '0.8rem' }}>
            No tracking history
          </Typography>
        </Box>
      ) : (
        <Box sx={{ position: 'relative', pl: 2 }}>
          {/* Timeline Line */}
          <Box
            sx={{
              position: 'absolute',
              left: 8,
              top: 0,
              bottom: 0,
              width: 2,
              backgroundColor: 'grey.200',
              zIndex: 0
            }}
          />
          
          {trackingHistory.map((log, index) => (
            <Box 
              key={log.id} 
              sx={{ 
                position: 'relative',
                pb: index === trackingHistory.length - 1 ? 0 : 3,
                '&:hover': {
                  '& .timeline-content': {
                    backgroundColor: 'grey.100'
                  }
                }
              }}
            >
              {/* Timeline Dot */}
              <Box
                sx={{
                  position: 'absolute',
                  left: -12,
                  top: 2,
                  width: 12,
                  height: 12,
                  borderRadius: '50%',
                  backgroundColor: getActionColor(log.actionType) === 'primary' ? 'primary.main' :
                                   getActionColor(log.actionType) === 'success' ? 'success.main' :
                                   getActionColor(log.actionType) === 'warning' ? 'warning.main' :
                                   getActionColor(log.actionType) === 'error' ? 'error.main' :
                                   getActionColor(log.actionType) === 'info' ? 'info.main' :
                                   'grey.400',
                  border: '2px solid white',
                  boxShadow: '0 0 0 1px rgba(0,0,0,0.1)',
                  zIndex: 1
                }}
              />
              
              {/* Content */}
              <Box 
                className="timeline-content"
                sx={{ 
                  ml: 1,
                  p: 1.5,
                  borderRadius: 1,
                  transition: 'background-color 0.2s ease'
                }}
              >
                {/* Header */}
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 0.5 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.75, flex: 1 }}>
                    <Typography 
                      variant="subtitle2" 
                      sx={{ 
                        fontSize: '0.875rem', 
                        fontWeight: 600,
                        color: 'text.primary',
                        textTransform: 'uppercase',
                        letterSpacing: '0.5px'
                      }}
                    >
                      {log.actionType.replace(/_/g, ' ')}
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                    <Typography 
                      variant="caption" 
                      sx={{ 
                        fontSize: '0.75rem', 
                        color: 'text.secondary',
                        fontWeight: 500
                      }}
                    >
                      {formatDateString(log.updatedAt)}
                    </Typography>
                    {isAdmin && (
                      <Tooltip title="Delete this log entry (Admin only)">
                        <IconButton
                          size="small"
                          onClick={() => confirmDeleteLog(log.id)}
                          sx={{ 
                            p: 0.25, 
                            color: 'error.main',
                            opacity: 0.7,
                            '&:hover': {
                              opacity: 1,
                              backgroundColor: 'error.light'
                            }
                          }}
                          disabled={deleting}
                        >
                          <DeleteIcon sx={{ fontSize: 14 }} />
                        </IconButton>
                      </Tooltip>
                    )}
                  </Box>
                </Box>
                
                {/* Description */}
                <Box sx={{ mb: 1 }}>
                  {log.previousStatus && log.newStatus ? (
                    <Typography 
                      variant="body2" 
                      sx={{ 
                        fontSize: '0.8rem', 
                        color: 'text.secondary',
                        lineHeight: 1.4
                      }}
                    >
                      Status changed from <strong>{log.previousStatus}</strong> to <strong>{log.newStatus}</strong>
                    </Typography>
                  ) : log.fieldName === 'deliveryDate' ? (
                    <Typography 
                      variant="body2" 
                      sx={{ 
                        fontSize: '0.8rem', 
                        color: 'text.secondary',
                        lineHeight: 1.4
                      }}
                    >
                      {log.remarks || 'Delivery date updated'}
                      {(log.oldValue !== null || log.newValue !== null) && (
                        <>
                          {' - '}
                          <strong>
                            {log.oldValue === null ? 'None' : 
                             log.oldValue ? new Date(log.oldValue).toLocaleDateString() : 'None'}
                          </strong>
                          {' → '}
                          <strong>
                            {log.newValue === null ? 'None' : 
                             log.newValue ? new Date(log.newValue).toLocaleDateString() : 'None'}
                          </strong>
                        </>
                      )}
                    </Typography>
                  ) : (log.actionType as string) === 'PALLET_ADDED' ? (
                    <Typography 
                      variant="body2" 
                      sx={{ 
                        fontSize: '0.8rem', 
                        color: 'text.secondary',
                        lineHeight: 1.4
                      }}
                    >
                      Pallet <strong>{log.newValue}</strong> added
                    </Typography>
                  ) : (log.actionType as string) === 'PALLET_DELETED' ? (
                    <Typography 
                      variant="body2" 
                      sx={{ 
                        fontSize: '0.8rem', 
                        color: 'text.secondary',
                        lineHeight: 1.4
                      }}
                    >
                      Pallet <strong>{log.oldValue}</strong> removed
                    </Typography>
                  ) : log.fieldName === 'actualPallets' ? (
                    <Typography 
                      variant="body2" 
                      sx={{ 
                        fontSize: '0.8rem', 
                        color: 'text.secondary',
                        lineHeight: 1.4
                      }}
                    >
                      Pallet count: <strong>{log.oldValue}</strong> → <strong>{log.newValue}</strong>
                    </Typography>
                  ) : log.fieldName && (log.oldValue !== null || log.newValue !== null) ? (
                    <Typography 
                      variant="body2" 
                      sx={{ 
                        fontSize: '0.8rem', 
                        color: 'text.secondary',
                        lineHeight: 1.4
                      }}
                    >
                      <strong>{log.fieldName === 'actualPallets' ? 'Actual pallets' :
                               log.fieldName}</strong> changed from{' '}
                      <strong>
                        {log.oldValue === null ? 'None' : log.oldValue}
                      </strong> to{' '}
                      <strong>
                        {log.newValue === null ? 'None' : log.newValue}
                      </strong>
                    </Typography>
                  ) : log.remarks ? (
                    <Typography 
                      variant="body2" 
                      sx={{ 
                        fontSize: '0.8rem', 
                        color: 'text.secondary',
                        lineHeight: 1.4
                      }}
                    >
                      {log.remarks}
                    </Typography>
                  ) : (
                    <Typography 
                      variant="body2" 
                      sx={{ 
                        fontSize: '0.8rem', 
                        color: 'text.secondary',
                        lineHeight: 1.4
                      }}
                    >
                      {log.actionType === 'PALLET_ADDED' ? 'Pallet added to manifest' :
                     log.actionType === 'PALLET_DELETED' ? 'Pallet removed from manifest' :
                     log.actionType === 'CREATED' ? 'Manifest created in system' :
                     log.actionType === 'UPDATED' ? 'Manifest information updated' :
                     log.actionType === 'ASSIGNED' ? 'Manifest assigned to driver' :
                     log.actionType === 'DELIVERED' ? 'Manifest marked as delivered' :
                     'Action performed on manifest'}
                    </Typography>
                  )}
                </Box>
                
                {/* User info */}
                <Typography 
                  variant="caption" 
                  sx={{ 
                    fontSize: '0.7rem', 
                    color: 'text.secondary',
                    display: 'flex',
                    alignItems: 'center',
                    gap: 0.5
                  }}
                >
                  by {log.updatedBy}
                </Typography>
              </Box>
            </Box>
          ))}
        </Box>
      )}

      {/* Filter Dialog */}
      <Dialog open={filterDialogOpen} onClose={() => setFilterDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>Filter Tracking History</DialogTitle>
        <DialogContent>
          <LocalizationProvider dateAdapter={AdapterDateFns}>
            <Box sx={{ mt: 2 }}>
              <Box sx={{ display: 'flex', gap: 2, mb: 3, flexDirection: { xs: 'column', md: 'row' } }}>
                <Box sx={{ flex: 1 }}>
                  <DateTimePicker
                    label="Start Date"
                    value={dateRange.startDate}
                    onChange={(newValue) => setDateRange(prev => ({ ...prev, startDate: newValue }))}
                    slotProps={{
                      textField: {
                        fullWidth: true
                      }
                    }}
                  />
                </Box>
                <Box sx={{ flex: 1 }}>
                  <DateTimePicker
                    label="End Date"
                    value={dateRange.endDate}
                    onChange={(newValue) => setDateRange(prev => ({ ...prev, endDate: newValue }))}
                    slotProps={{
                      textField: {
                        fullWidth: true
                      }
                    }}
                  />
                </Box>
              </Box>
              <Box sx={{ width: '100%' }}>
                <FormControl fullWidth>
                  <InputLabel>Action Type</InputLabel>
                  <Select
                    value={actionTypeFilter}
                    label="Action Type"
                    onChange={(e) => setActionTypeFilter(e.target.value as TrackingLogActionType | '')}
                  >
                    <MenuItem value="">All Actions</MenuItem>
                    {Object.values(TrackingLogActionType).map((type) => (
                      <MenuItem key={type} value={type}>
                        {type.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Box>
            </Box>
          </LocalizationProvider>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleFilterReset}>Reset</Button>
          <Button onClick={() => setFilterDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleFilterApply} variant="contained">Apply</Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteConfirmOpen} onClose={() => !deleting && setDeleteConfirmOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <DeleteIcon color="error" />
          Delete Tracking Log
        </DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete this tracking log entry? This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteConfirmOpen(false)} disabled={deleting}>
            Cancel
          </Button>
          <Button 
            onClick={handleDeleteLog} 
            color="error" 
            variant="contained"
            disabled={deleting}
            startIcon={deleting ? <CircularProgress size={16} /> : <DeleteIcon />}
          >
            {deleting ? 'Deleting...' : 'Delete'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete All Confirmation Dialog */}
      <Dialog open={deleteAllConfirmOpen} onClose={() => !deleting && setDeleteAllConfirmOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <ClearIcon color="error" />
          Clear All Tracking History
        </DialogTitle>
        <DialogContent>
          <Typography gutterBottom>
            Are you sure you want to clear all tracking history for this manifest? This will permanently delete all {trackingHistory.length} tracking log entries.
          </Typography>
          <Typography variant="body2" color="error.main" sx={{ fontWeight: 'bold' }}>
            This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteAllConfirmOpen(false)} disabled={deleting}>
            Cancel
          </Button>
          <Button 
            onClick={handleDeleteAllLogs} 
            color="error" 
            variant="contained"
            disabled={deleting}
            startIcon={deleting ? <CircularProgress size={16} /> : <ClearIcon />}
          >
            {deleting ? 'Clearing...' : 'Clear All'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );

  if (isDialog) {
    return (
      <Dialog open={true} onClose={onClose} maxWidth="lg" fullWidth>
        <DialogTitle>Manifest Tracking History - {trackingNo}</DialogTitle>
        <DialogContent>
          {renderContent()}
        </DialogContent>
        <DialogActions>
          <Button onClick={onClose}>Close</Button>
        </DialogActions>
      </Dialog>
    );
  }

  return renderContent();
});

export default ManifestTrackingHistoryComponent; 