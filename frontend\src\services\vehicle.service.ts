import axios from 'axios';
import { Vehicle, VehicleType, VehicleStatus, Driver } from '../types/Vehicle';
import authHeader from './auth-header.js';
import API_CONFIG from '../config/api.config';

const API_URL = `${API_CONFIG.baseUrl}/api`;

// Vehicle Type Services
export const getVehicleTypes = async (): Promise<VehicleType[]> => {
  const response = await axios.get<VehicleType[]>(`${API_URL}/vehicle-types`, { headers: authHeader() });
  return response.data;
};

export const getVehicleTypeById = async (id: number): Promise<VehicleType> => {
  const response = await axios.get<VehicleType>(`${API_URL}/vehicle-types/${id}`, { headers: authHeader() });
  return response.data;
};

export const createVehicleType = async (vehicleType: VehicleType): Promise<VehicleType> => {
  const response = await axios.post<VehicleType>(`${API_URL}/vehicle-types`, vehicleType, { headers: authHeader() });
  return response.data;
};

export const updateVehicleType = async (id: number, vehicleType: VehicleType): Promise<VehicleType> => {
  const response = await axios.put<VehicleType>(`${API_URL}/vehicle-types/${id}`, vehicleType, { headers: authHeader() });
  return response.data;
};

export const deleteVehicleType = async (id: number): Promise<void> => {
  await axios.delete(`${API_URL}/vehicle-types/${id}`, { headers: authHeader() });
};

export const getVehicleTypeAbbreviation = async (vehicleTypeName: string): Promise<string> => {
  const response = await axios.get<{ success: boolean; data: string; message?: string }>(
    `${API_URL}/vehicle-types/abbreviation/${encodeURIComponent(vehicleTypeName)}`, 
    { headers: authHeader() }
  );
  return response.data.data;
};

export const suggestVehicleType = async (
  weight: number, 
  cbm: number
): Promise<VehicleType | null> => {
  const response = await axios.get<{ data: VehicleType | null }>(
    `${API_URL}/vehicle-types/suggest?weight=${weight}&cbm=${cbm}`, 
    { headers: authHeader() }
  );

  return response.data.data; // Returns VehicleType or null if no match found
};

// Vehicle Services
export const getVehicles = async (): Promise<Vehicle[]> => {
  const response = await axios.get<Vehicle[]>(`${API_URL}/vehicles`, { headers: authHeader() });
  return response.data;
};

export const getVehicleById = async (id: number): Promise<Vehicle> => {
  const response = await axios.get<Vehicle>(`${API_URL}/vehicles/${id}`, { headers: authHeader() });
  return response.data;
};

export const getVehiclesByType = async (typeId: number): Promise<Vehicle[]> => {
  const response = await axios.get<Vehicle[]>(`${API_URL}/vehicles/type/${typeId}`, { headers: authHeader() });
  return response.data;
};

export const createVehicle = async (vehicle: Vehicle): Promise<Vehicle> => {
  const response = await axios.post<Vehicle>(`${API_URL}/vehicles`, vehicle, { headers: authHeader() });
  return response.data;
};

export const updateVehicle = async (id: number, vehicle: Vehicle): Promise<Vehicle> => {
  const response = await axios.put<Vehicle>(`${API_URL}/vehicles/${id}`, vehicle, { headers: authHeader() });
  return response.data;
};

export const deleteVehicle = async (id: number): Promise<void> => {
  await axios.delete(`${API_URL}/vehicles/${id}`, { headers: authHeader() });
};

export const updateVehicleStatus = async (id: number, status: VehicleStatus): Promise<Vehicle> => {
  const response = await axios.put<Vehicle>(`${API_URL}/vehicles/${id}/status`, { status }, { headers: authHeader() });
  return response.data;
};

export const assignDriverToVehicle = async (vehicleId: number, driverUsername: string): Promise<Vehicle> => {
  const response = await axios.post<Vehicle>(`${API_URL}/vehicles/${vehicleId}/drivers/${driverUsername}`, {}, { headers: authHeader() });
  return response.data;
};

export const removeDriverFromVehicle = async (vehicleId: number, driverUsername: string): Promise<Vehicle> => {
  const response = await axios.delete<Vehicle>(`${API_URL}/vehicles/${vehicleId}/drivers/${driverUsername}`, { headers: authHeader() });
  return response.data;
};

// Get all drivers for dropdown selection
export const getDrivers = async (): Promise<Driver[]> => {
  try {
    const response = await axios.get<Driver[]>(`${API_URL}/users/drivers`, { headers: authHeader() });
    console.log('Driver API response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error fetching drivers:', error);
    throw error;
  }
}; 