services:
  - type: web
    name: wms-backend
    env: java
    buildCommand: ./mvnw clean package -DskipTests
    startCommand: java -jar target/*.jar --spring.profiles.active=prod
    plan: free
    envVars:
      - key: DATABASE_URL
        fromDatabase:
          name: wms-db
          property: connectionString
      - key: DATABASE_USERNAME
        fromDatabase:
          name: wms-db
          property: user
      - key: DATABASE_PASSWORD
        fromDatabase:
          name: wms-db
          property: password
      - key: CORS_ALLOWED_ORIGINS
        value: https://wmsfuku-ol81xnipx-zlhwnns-projects.vercel.app
      - key: JWT_SECRET
        generateValue: true
      - key: SPRING_PROFILES_ACTIVE
        value: prod
      - key: PORT
        value: 8080

databases:
  - name: wms-db
    plan: free 