package com.wms.controller;

import com.wms.security.jwt.JwtTokenStore;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/admin/metrics")
public class AuthMetricsController {
    private static final Logger logger = LoggerFactory.getLogger(AuthMetricsController.class);
    
    @Autowired
    private DataSource dataSource;
    
    @PersistenceContext
    private EntityManager entityManager;
    
    @Autowired
    private JwtTokenStore tokenStore;
    
    /**
     * Get system health metrics - only accessible by admins
     */
    @GetMapping("/health")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> getSystemHealth() {
        Map<String, Object> metrics = new HashMap<>();
        metrics.put("timestamp", System.currentTimeMillis());
        
        // Add memory metrics
        Runtime runtime = Runtime.getRuntime();
        Map<String, Object> memory = new HashMap<>();
        memory.put("free", runtime.freeMemory() / (1024 * 1024) + " MB");
        memory.put("total", runtime.totalMemory() / (1024 * 1024) + " MB");
        memory.put("max", runtime.maxMemory() / (1024 * 1024) + " MB");
        memory.put("processors", runtime.availableProcessors());
        metrics.put("memory", memory);
        
        // Database connectivity test
        Map<String, Object> database = new HashMap<>();
        try (Connection connection = dataSource.getConnection()) {
            database.put("status", "Connected");
            database.put("valid", connection.isValid(2000));
        } catch (SQLException e) {
            logger.error("Database connection error: {}", e.getMessage());
            database.put("status", "Error");
            database.put("error", e.getMessage());
        }
        metrics.put("database", database);
        
        // Run a simple database query for performance testing
        try {
            long startTime = System.currentTimeMillis();
            Query query = entityManager.createNativeQuery("SELECT 1");
            query.getSingleResult();
            long queryTime = System.currentTimeMillis() - startTime;
            database.put("testQueryTimeMs", queryTime);
        } catch (Exception e) {
            logger.error("Database test query error: {}", e.getMessage());
            database.put("testQueryError", e.getMessage());
        }
        
        return ResponseEntity.ok(metrics);
    }
    
    /**
     * Get authentication stats - only accessible by admins
     */
    @GetMapping("/auth")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> getAuthMetrics() {
        Map<String, Object> metrics = new HashMap<>();
        metrics.put("timestamp", System.currentTimeMillis());
        
        try {
            // Get token store metrics if possible through reflection
            java.lang.reflect.Field tokenStoreField = tokenStore.getClass().getDeclaredField("tokenStore");
            tokenStoreField.setAccessible(true);
            Map<?, ?> tokenStoreMap = (Map<?, ?>) tokenStoreField.get(tokenStore);
            metrics.put("activeTokenCount", tokenStoreMap.size());
        } catch (Exception e) {
            logger.error("Error accessing token store: {}", e.getMessage());
            metrics.put("activeTokenCount", "Unknown");
        }
        
        return ResponseEntity.ok(metrics);
    }
} 