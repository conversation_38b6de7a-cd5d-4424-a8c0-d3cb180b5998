import axios from 'axios';
import { Container, ContainerStatus } from '../types/Container';
import { ApiResponse } from '../types/api';
import API_CONFIG from '../config/api.config';

const API_BASE_URL = API_CONFIG.baseUrl;

class ContainerService {
  async getAllContainers(token: string): Promise<ApiResponse<Container[]>> {
    const response = await axios.get<ApiResponse<Container[]>>(`${API_BASE_URL}/api/containers`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    return response.data;
  }

  async getContainerByNo(containerNo: string, token: string): Promise<ApiResponse<Container>> {
    const response = await axios.get<ApiResponse<Container>>(`${API_BASE_URL}/api/containers/${containerNo}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    return response.data;
  }

  async getMyContainers(token: string): Promise<ApiResponse<Container[]>> {
    const response = await axios.get<ApiResponse<Container[]>>(`${API_BASE_URL}/api/containers/my-containers`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    return response.data;
  }

  async getContainersByStatus(status: ContainerStatus, token: string): Promise<ApiResponse<Container[]>> {
    const response = await axios.get<ApiResponse<Container[]>>(`${API_BASE_URL}/api/containers/status/${status}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    return response.data;
  }

  async createContainer(container: Container, token: string): Promise<ApiResponse<Container>> {
    const response = await axios.post<ApiResponse<Container>>(`${API_BASE_URL}/api/containers`, container, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    return response.data;
  }

  async updateContainer(containerNo: string, container: Container, token: string): Promise<ApiResponse<Container>> {
    console.log(`Updating container: containerNo=${containerNo}`, container);
    
    const url = `${API_BASE_URL}/api/containers/${containerNo}`;
    console.log(`Making API request to: ${url}`);
    
    try {
      // Make sure status is properly set to a string
      const containerData = {
        ...container,
        status: container.status.toString()
      };
      
      const response = await axios.put<ApiResponse<Container>>(
        url, 
        containerData,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );
      
      console.log('Container update successful:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error in updateContainer:', error);
      throw error;
    }
  }

  async updateContainerStatus(containerNo: string, status: ContainerStatus, token: string): Promise<ApiResponse<Container>> {
    console.log(`Updating status: containerNo=${containerNo}, status=${status}`);
    
    // Properly encode the status parameter
    const encodedStatus = encodeURIComponent(status);
    const url = `${API_BASE_URL}/api/containers/${containerNo}/status?status=${encodedStatus}`;
    
    console.log(`Making API request to: ${url}`);
    
    try {
      const response = await axios.put<ApiResponse<Container>>(
        url, 
        {}, // Empty body
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );
      
      console.log('Status update successful:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error in updateContainerStatus:', error);
      throw error;
    }
  }

  async deleteContainer(containerNo: string, token: string): Promise<ApiResponse<void>> {
    const response = await axios.delete<ApiResponse<void>>(`${API_BASE_URL}/api/containers/${containerNo}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    return response.data;
  }

  async getContainersCount(token: string): Promise<ApiResponse<number>> {
    const response = await axios.get<ApiResponse<number>>(`${API_BASE_URL}/api/containers/count`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    return response.data;
  }
}

export default new ContainerService(); 