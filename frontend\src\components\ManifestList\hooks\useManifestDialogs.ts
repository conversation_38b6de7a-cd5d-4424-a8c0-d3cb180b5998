import { useState } from 'react';
import { Manifest, ManifestStatus } from '../../../types/manifest';

interface DialogHandlers {
  onDelete?: (trackingNo: string) => Promise<void>;
  onBulkDelete?: (trackingNos: string[]) => Promise<void>;
  onStatusChange?: (trackingNo: string, status: ManifestStatus) => Promise<void>;
  onUpdate?: (manifest: Manifest) => Promise<void>;
  onDeliveryDateChange?: (trackingNo: string, date: Date | null, timeSlot?: string | null) => Promise<void>;
  onDeliveryVehicleChange?: (trackingNo: string, vehicle: string) => Promise<void>;
  onPrintLabel?: (manifest: Manifest, count: number) => Promise<void>;
}

interface DialogStates {
  editManifest: boolean;
  confirmDelete: boolean;
  confirmBulkDelete: boolean;
  changeStatus: boolean;
  labelGeneration: boolean;
  palletManagement: boolean;
  deliveryDate: boolean;
  deliveryVehicle: boolean;
  editValue: boolean;
}

interface DialogProps {
  editManifest: { manifest: Manifest | null };
  confirmDelete: { trackingNo: string; title?: string; message?: string };
  confirmBulkDelete: { trackingNos: string[]; title?: string; message?: string };
  changeStatus: { trackingNo: string; status: ManifestStatus };
  labelGeneration: { manifest: Manifest | null; count?: number };
  palletManagement: { manifest: Manifest | null };
  deliveryDate: { trackingNo: string; internalId?: string; currentDate: Date | null; currentTimeSlot?: string | null };
  deliveryVehicle: { manifest: Manifest | null; currentVehicle?: string };
  editValue: { 
    trackingNo: string; 
    field: 'cbm' | 'actualPalletsCount' | 'weight' | 'pieces'; 
    currentValue: number;
    title?: string;
  };
}

export function useManifestDialogs(handlers: DialogHandlers) {
  // Track which dialogs are open
  const [dialogs, setDialogs] = useState<DialogStates>({
    editManifest: false,
    confirmDelete: false,
    confirmBulkDelete: false,
    changeStatus: false,
    labelGeneration: false,
    palletManagement: false,
    deliveryDate: false,
    deliveryVehicle: false,
    editValue: false,
  });
  
  // Store dialog-specific data
  const [dialogProps, setDialogProps] = useState<DialogProps>({
    editManifest: { manifest: null },
    confirmDelete: { trackingNo: '' },
    confirmBulkDelete: { trackingNos: [] },
    changeStatus: { trackingNo: '', status: ManifestStatus.CREATED },
    labelGeneration: { manifest: null },
    palletManagement: { manifest: null },
    deliveryDate: { trackingNo: '', currentDate: null },
    deliveryVehicle: { manifest: null },
    editValue: { trackingNo: '', field: 'cbm', currentValue: 0 },
  });
  
  // Loading state for async operations
  const [loading, setLoading] = useState<boolean>(false);
  
  // Error message for dialogs
  const [error, setError] = useState<string | null>(null);
  
  // Open a dialog with optional props
  const openDialog = <K extends keyof DialogStates>(dialogName: K, props?: Partial<DialogProps[K]>) => {
    setDialogs(prev => ({
      ...prev,
      [dialogName]: true
    }));
    
    if (props) {
      setDialogProps(prev => ({
        ...prev,
        [dialogName]: {
          ...prev[dialogName],
          ...props
        }
      }));
    }
    
    // Reset error when opening dialog
    setError(null);
  };
  
  // Close a dialog
  const closeDialog = (dialogName: keyof DialogStates) => {
    setDialogs(prev => ({
      ...prev,
      [dialogName]: false
    }));
    
    // Reset error when closing dialog
    setError(null);
  };
  
  // Generic handler for async operations with error handling
  const handleAsyncOperation = async (operation: () => Promise<void>, dialogName: keyof DialogStates) => {
    setLoading(true);
    setError(null);
    
    try {
      await operation();
      closeDialog(dialogName);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    } finally {
      setLoading(false);
    }
  };
  
  // Dialog action handlers
  const handleEditManifestSubmit = async (manifest: Manifest) => {
    if (handlers.onUpdate) {
      await handleAsyncOperation(
        async () => await handlers.onUpdate!(manifest),
        'editManifest'
      );
    }
  };
  
  const handleConfirmDelete = async () => {
    const { trackingNo } = dialogProps.confirmDelete;
    if (handlers.onDelete && trackingNo) {
      await handleAsyncOperation(
        async () => await handlers.onDelete!(trackingNo),
        'confirmDelete'
      );
    }
  };
  
  const handleConfirmBulkDelete = async () => {
    const { trackingNos } = dialogProps.confirmBulkDelete;
    if (handlers.onBulkDelete && trackingNos.length > 0) {
      await handleAsyncOperation(
        async () => await handlers.onBulkDelete!(trackingNos),
        'confirmBulkDelete'
      );
    }
  };
  
  const handleStatusChange = async () => {
    const { trackingNo, status } = dialogProps.changeStatus;
    if (handlers.onStatusChange && trackingNo && status) {
      await handleAsyncOperation(
        async () => await handlers.onStatusChange!(trackingNo, status),
        'changeStatus'
      );
    }
  };
  
  const handleDeliveryDateChange = async (date: Date | null, timeSlot: string | null = null) => {
    const { trackingNo } = dialogProps.deliveryDate;
    if (handlers.onDeliveryDateChange && trackingNo) {
      await handleAsyncOperation(
        async () => await handlers.onDeliveryDateChange!(trackingNo, date, timeSlot),
        'deliveryDate'
      );
    }
  };
  
  const handleDeliveryVehicleChange = async (vehicle: string) => {
    const { manifest } = dialogProps.deliveryVehicle;
    if (handlers.onDeliveryVehicleChange && manifest?.trackingNo) {
      await handleAsyncOperation(
        async () => await handlers.onDeliveryVehicleChange!(manifest.trackingNo, vehicle),
        'deliveryVehicle'
      );
    }
  };
  
  const handlePrintLabel = async (count: number = 1) => {
    const { manifest } = dialogProps.labelGeneration;
    if (handlers.onPrintLabel && manifest) {
      await handleAsyncOperation(
        async () => await handlers.onPrintLabel!(manifest, count),
        'labelGeneration'
      );
    }
  };
  
  const handleEditValue = async (newValue: number) => {
    const { trackingNo, field } = dialogProps.editValue;
    if (handlers.onUpdate && trackingNo) {
      await handleAsyncOperation(
        async () => {
          // Create a partial manifest with just the field to update
          const partialManifest: Partial<Manifest> = {
            trackingNo,
            [field]: newValue
          };
          
          await handlers.onUpdate!(partialManifest as Manifest);
        },
        'editValue'
      );
    }
  };
  
  return {
    dialogs,
    dialogProps,
    loading,
    error,
    openDialog,
    closeDialog,
    actions: {
      handleEditManifestSubmit,
      handleConfirmDelete,
      handleConfirmBulkDelete,
      handleStatusChange,
      handleDeliveryDateChange,
      handleDeliveryVehicleChange,
      handlePrintLabel,
      handleEditValue
    }
  };
} 