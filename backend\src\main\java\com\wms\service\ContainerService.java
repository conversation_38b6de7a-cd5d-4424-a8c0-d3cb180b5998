package com.wms.service;

import com.wms.entity.Container;
import com.wms.entity.ContainerStatus;
import java.util.List;
import java.util.Optional;
import java.time.LocalDateTime;

public interface ContainerService {
    List<Container> getAllContainers();
    Container getContainerByNo(String containerNo);
    List<Container> getContainersByUsername(String username);
    List<Container> getContainersByStatus(ContainerStatus status);
    Container createContainer(Container container);
    Container updateContainer(String containerNo, Container container);
    Container updateContainerStatus(String containerNo, ContainerStatus status);
    void deleteContainer(String containerNo);
    Long getContainersCount();
    Optional<Container> getContainerByContainerNo(String containerNo);
    List<Container> updatePortnetEtaForVesselVoyage(String vesselVoyageNo, LocalDateTime portnetEta);
} 