package com.wms.entity;

import com.wms.entity.user.Client;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;

@Data
@Entity
@Table(name = "containers")
public class Container {
    @Id
    @NotBlank(message = "Container Number is required")
    @Column(name = "container_number", unique = true)
    private String containerNo;

    @ManyToOne
    @JoinColumn(name = "client_username",nullable = false)
    private Client client;

    @NotBlank(message = "Truck Number is required")
    @Column(name = "truck_no")
    private String truckNo;

    @NotBlank(message = "Vessel Voyage Number is required")
    @Column(name = "vessel_voyage_no")
    private String vesselVoyageNo;

    @NotNull(message = "ETA Requested Date is required")
    @Column(name = "eta_requested_date")
    @JsonFormat(pattern = "dd MMM yyyy HH:mm", timezone = "Asia/Singapore")
    private LocalDateTime etaRequestedDate;

    @NotNull(message = "Manifest Quantity is required")
    @Column(name = "manifest_quantity")
    private Integer manifestQuantity;

    @Column(name = "portnet_eta")
    @JsonFormat(pattern = "dd MMM yyyy HH:mm", timezone = "Asia/Singapore")
    private LocalDateTime portnetEta;

    @Column(name = "eta_allocated")
    @JsonFormat(pattern = "dd MMM yyyy HH:mm", timezone = "Asia/Singapore")
    private LocalDateTime etaAllocated;

    @Column(name = "created_date")
    @JsonFormat(pattern = "dd MMM yyyy HH:mm", timezone = "Asia/Singapore")
    private LocalDateTime createdDate;

    @Column(name = "arrival_date")
    @JsonFormat(pattern = "dd MMM yyyy HH:mm", timezone = "Asia/Singapore")
    private LocalDateTime arrivalDate;

    @Column(name = "loading_bay")
    private String loadingBay;

    @Column(name = "unstuff_date")
    @JsonFormat(pattern = "dd MMM yyyy HH:mm", timezone = "Asia/Singapore")
    private LocalDateTime unstuffDate;

    @Column(name = "unstuff_completed_date")
    @JsonFormat(pattern = "dd MMM yyyy HH:mm", timezone = "Asia/Singapore")
    private LocalDateTime unstuffCompletedDate;

    @Column(name = "pull_out_date")
    @JsonFormat(pattern = "dd MMM yyyy HH:mm", timezone = "Asia/Singapore")
    private LocalDateTime pullOutDate;

    @Column(name = "unstuff_team")
    private String unstuffTeam;

    @Column(name = "remark")
    private String remark;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private ContainerStatus status = ContainerStatus.CREATED;

    @PrePersist
    @PreUpdate
    protected void prePersist() {
        if (this.createdDate == null) {
            this.createdDate = LocalDateTime.now();
        }
    }
} 