// @ts-nocheck - TODO: Fix TypeScript errors properly in a follow-up task
import React, { useState, useEffect, useMemo } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import {
  Box,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Typography,
  Alert,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  Paper,
  CircularProgress,
  Divider,
  Tabs,
  Tab,
  Snackbar,
  Stack,
  Chip,
  IconButton,
  Tooltip,
  useTheme,
  DialogContentText,
  SelectChangeEvent,
  DialogProps,
  InputAdornment,
  Menu,
  FormHelperText,
  Toolbar,
  alpha,
  LinearProgress,
  Grid,
  Collapse,
  Checkbox,
  FormControlLabel
} from '@mui/material';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { DataGrid, GridColDef, useGridApiRef } from '@mui/x-data-grid';
import type { 
  GridRenderCellParams, 
  GridRowId,
  GridValueFormatter
} from '@mui/x-data-grid';
import { useToast } from '../contexts/ToastContext';
import AddIcon from '@mui/icons-material/Add';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import LocalShippingIcon from '@mui/icons-material/LocalShipping';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import WarehouseIcon from '@mui/icons-material/Warehouse';
import CloseIcon from '@mui/icons-material/Close';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import PersonIcon from '@mui/icons-material/Person';
import PhoneIcon from '@mui/icons-material/Phone';
import HomeIcon from '@mui/icons-material/Home';
import LabelIcon from '@mui/icons-material/Label';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import FileDownloadIcon from '@mui/icons-material/FileDownload';
import UploadFileIcon from '@mui/icons-material/UploadFile';
import GetAppIcon from '@mui/icons-material/GetApp';
import ViewInArIcon from '@mui/icons-material/ViewInAr';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import { useAuth } from '../contexts/AuthContext';
import QuickEditCell from '../components/QuickEditCell';
import usePageTitle from '../hooks/usePageTitle';
import { format } from 'date-fns';
import { styled } from '@mui/material/styles';
import { css } from '@emotion/react';
import { Global } from '@emotion/react';
import SearchIcon from '@mui/icons-material/Search';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import EventAvailableIcon from '@mui/icons-material/EventAvailable';
import LocalShippingOutlinedIcon from '@mui/icons-material/LocalShippingOutlined';
import ManifestLabelDialog from '../components/ManifestLabelDialog';
import PalletManagementDialog from '../components/PalletManagementDialog';
import AutorenewIcon from '@mui/icons-material/Autorenew';
import Excel from 'exceljs';
import { saveAs } from 'file-saver';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import PrintIcon from '@mui/icons-material/Print';
import FormatListNumberedIcon from '@mui/icons-material/FormatListNumbered';
import TimelineIcon from '@mui/icons-material/Timeline';
import ClearAllIcon from '@mui/icons-material/ClearAll';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import FilterListIcon from '@mui/icons-material/FilterList';
import ClearIcon from '@mui/icons-material/Clear';
import Autocomplete from '@mui/material/Autocomplete';
import InfoIcon from '@mui/icons-material/Info';

import manifestService from '../services/manifest.service';
import userService from '../services/user.service';
import containerService from '../services/container.service';
import { Manifest, ManifestStatus } from '../types/manifest';
import { Client } from '../types/User';
import { ApiResponse } from '../types/api';
import locationService from '../services/location.service';
import { LocationZone } from '../types/location';
import { normalizeDate, getDaysDifference, formatDateString, formatDate, formatTimestamp, formatDateOnly, formatDateOnlyLocal } from '../utils/dateUtils';
import DeliveryDateDialog from '../components/DeliveryDateDialog';
import { VehicleType } from '../types/Vehicle';
import * as vehicleService from '../services/vehicle.service';
import QuickDeliveryVehicleDialog from '../components/QuickDeliveryVehicleDialog';
import SplitDeliveryDatePicker from '../components/SplitDeliveryDatePicker';
import { formatDateTime, formatDeliveryDate, formatTimeSlot, getTimeSlotIdFromDate, parseDateString } from '../utils/dateUtils';

// Update the global styles for pickers
const globalStyles = css`
  .MuiPickersPopper-root, 
  .MuiDialog-root,
  .MuiPopover-root,
  .MuiModal-root,
  .MuiPickersLayout-root,
  .MuiDateCalendar-root,
  .MuiPickersDay-root,
  .MuiClock-root {
    z-index: 9999 !important;
  }
  
  .MuiBackdrop-root {
    z-index: 9998 !important;
  }
`;

// Add a custom Dialog component
const StaticPositionDialog = (props: DialogProps) => {
  return (
    <Dialog
      {...props}
      sx={{
        position: 'static',
        '& .MuiDialog-container': {
          position: 'static'
        },
        ...props.sx
      }}
    />
  );
};

// Styled component for the file input
const VisuallyHiddenInput = styled('input')({
  clip: 'rect(0 0 0 0)',
  clipPath: 'inset(50%)',
  height: 1,
  overflow: 'hidden',
  position: 'absolute',
  bottom: 0,
  left: 0,
  whiteSpace: 'nowrap',
  width: 1,
});

// Styled component for the upload area
const UploadArea = styled(Box)(({ theme }) => ({
  border: `2px dashed ${theme.palette.primary.main}`,
  borderRadius: theme.shape.borderRadius,
  padding: theme.spacing(3),
  textAlign: 'center',
  backgroundColor: alpha(theme.palette.primary.main, 0.04),
  cursor: 'pointer',
  transition: 'all 0.2s ease-in-out',
  '&:hover': {
    backgroundColor: alpha(theme.palette.primary.main, 0.08),
    borderColor: theme.palette.primary.dark,
  },
}));

// Add an error boundary component
const ErrorFallback = ({ error }: { error: Error }) => {
  return (
    <Box sx={{ p: 2, border: '1px solid red', borderRadius: 1, bgcolor: 'error.light', color: 'error.dark' }}>
      <Typography variant="h6" component="h3">Error loading data</Typography>
      <Typography variant="body1">{error.message}</Typography>
    </Box>
  );
};

interface ConfirmDialogProps {
  open: boolean;
  title: string;
  message: string;
  onConfirm: () => void;
  onCancel: () => void;
}

interface ManifestFormData {
  trackingNo: string;
  client: { username: string };
  container: { containerNo: string };
  driver: { username: string };
  sequenceNo: number;
  status: ManifestStatus;
  customerName: string;
  phoneNo: string;
  address: string;
  postalCode: string;
  country: string;
  pieces: number;
  cbm: number;
  actualPalletsCount: number;
  location: string;
  deliveryDate: Date | null;
  deliveredDate: Date | null;
  deliveryVehicle: string;
  driverRemarks: string;
  remarks: string;
  weight: number;
  internalId?: string;
  createdDate?: string | null;
}

const ManifestManagement: React.FC = () => {
  const { currentUser, isAuthenticated } = useAuth();
  const toast = useToast();
  const navigate = useNavigate();
  const { trackingNo } = useParams<{ trackingNo: string }>();
  const [manifests, setManifests] = useState<Manifest[]>([]);
  const [loading, setLoading] = useState(true);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedManifest, setSelectedManifest] = useState<Manifest | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [filterState, setFilterState] = useState({
    search: '',
    status: [] as ManifestStatus[],
    dateRange: {
      start: null as Date | null,
      end: null as Date | null
    },
    location: '',
    deliveryVehicle: '',
    hasDeliveryDate: false
  });
  const theme = useTheme();
  const [clients, setClients] = useState<Client[]>([]);
  const [containers, setContainers] = useState<any[]>([]);
  const [drivers, setDrivers] = useState<any[]>([]);
  const [locationZones, setLocationZones] = useState<LocationZone[]>([]);
  const [locationsLoading, setLocationsLoading] = useState(false);
  const [vehicleTypes, setVehicleTypes] = useState<VehicleType[]>([]);
  const [vehicleTypesLoading, setVehicleTypesLoading] = useState(false);
  
  const initialFormData: ManifestFormData = {
    trackingNo: '',
    client: { username: '' },
    container: { containerNo: '' },
    driver: { username: '' },
    sequenceNo: 0,
    status: ManifestStatus.CREATED,
    customerName: '',
    phoneNo: '',
    address: '',
    postalCode: '',
    country: '',
    pieces: 0,
    cbm: 0,
    actualPalletsCount: 0,
    location: '',
    deliveryDate: null,
    deliveredDate: null,
    deliveryVehicle: '',
    driverRemarks: '',
    remarks: '',
    weight: 0,
    internalId: 'CONT-SEQ',
    createdDate: null,
  };

  const [formData, setFormData] = useState<ManifestFormData>(initialFormData);

  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [confirmAction, setConfirmAction] = useState<() => void>(() => {});
  const [confirmTitle, setConfirmTitle] = useState('');
  const [confirmMessage, setConfirmMessage] = useState('');

  const [labelDialogOpen, setLabelDialogOpen] = useState(false);
  const [selectedLabelManifest, setSelectedLabelManifest] = useState<Manifest | null>(null);
  const [selectedManifests, setSelectedManifests] = useState<Manifest[]>([]);
  const [dataGridKey, setDataGridKey] = useState<number>(0);
  const [palletManagementDialogOpen, setPalletManagementDialogOpen] = useState(false);
  const [selectedPalletManifest, setSelectedPalletManifest] = useState<Manifest | null>(null);
  const [bulkUploadDialogOpen, setBulkUploadDialogOpen] = useState(false);
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [bulkUploadResults, setBulkUploadResults] = useState<{
    total: number;
    successful: number;
    failed: number;
    errors: Array<{ row: number; error: string }>;
  } | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = React.useRef<HTMLInputElement>(null);

  // Column visibility state with default columns in order of importance
  const [visibleColumns, setVisibleColumns] = useState({
    actions: true, // Always visible - moved to first position
    location: true,
    internalId: true,
    trackingNo: true,
    customerName: false,
    address: false,
    postalCode: true,
    deliveryDate: true,
    timeSlot: true, // Show time slot by default
    phoneNo: false,
    driverRemarks: false,
    driver: false,
    status: true,
    deliveryVehicle: true,
    pieces: false,
    inboundPieces: true, // Show inbound pieces by default
    actualPalletsCount: true,
    cbm: true,
    remarks: false,
    weight: true,
    deliveredDate: false,
    createdDate: false,
    country: false,
    container: true,
    client: false,
  });

  const [manifestError, setManifestError] = useState<string | null>(null);
  const apiRef = useGridApiRef();

  const [columnSearchFilter, setColumnSearchFilter] = useState('');
  
  // State variables for quick edit delivery date
  const [editingDeliveryDate, setEditingDeliveryDate] = useState<string | null>(null);
  const [editingInternalId, setEditingInternalId] = useState<string | null>(null);
  const [tempDeliveryDate, setTempDeliveryDate] = useState<Date | null>(null);
  const [tempTimeSlot, setTempTimeSlot] = useState<string | null>(null);
  const [deliveryDateDialogOpen, setDeliveryDateDialogOpen] = useState(false);

  // Default column visibility to use when resetting
  const defaultColumnVisibility = {
    actions: true, // Always visible - moved to first position
    location: true,
    internalId: true,
    trackingNo: true,
    customerName: false,
    address: false,
    postalCode: true,
    deliveryDate: true,
    timeSlot: true, // Show time slot by default
    phoneNo: false,
    driverRemarks: false,
    driver: false,
    status: true,
    deliveryVehicle: true,
    pieces: false,
    inboundPieces: true, // Show inbound pieces by default
    actualPalletsCount: true,
    cbm: true,
    remarks: false,
    weight: true,
    deliveredDate: false,
    createdDate: false,
    country: false,
    container: true,
    client: false,
  };

  // Add state for quick edit of CBM and No of Pallets
  const [editingCell, setEditingCell] = useState<{ trackingNo: string, field: string } | null>(null);
  const [tempCellValue, setTempCellValue] = useState<number | null>(null);

  // Add state for quick edit dialog for CBM and No of Pallets
  const [editValueDialogOpen, setEditValueDialogOpen] = useState(false);
  const [editValueField, setEditValueField] = useState<'cbm' | 'actualPalletsCount' | null>(null);
  const [editValueTrackingNo, setEditValueTrackingNo] = useState<string | null>(null);
  const [editValueTemp, setEditValueTemp] = useState<number | null>(null);

  // Add state for quick delivery vehicle edit
  const [quickVehicleDialogOpen, setQuickVehicleDialogOpen] = useState(false);
  const [selectedManifestForVehicleEdit, setSelectedManifestForVehicleEdit] = useState<Manifest | null>(null);

  // States for bulk delivery date update
  const [bulkDeliveryDateDialogOpen, setBulkDeliveryDateDialogOpen] = useState(false);
  const [bulkDeliveryDate, setBulkDeliveryDate] = useState<Date | null>(null);
  const [bulkTimeSlot, setBulkTimeSlot] = useState<string | null>(null);

  // Determine if we're in edit mode based on URL
  const isEditMode = Boolean(trackingNo);
  
  // Set page title based on mode
  usePageTitle(isEditMode ? `Edit Manifest ${trackingNo} - Test` : 'Manifest Management - Test');

  // Helper function to convert tab index to manifest status
  const getStatusFromTabIndex = (tabIndex: number): ManifestStatus | null => {
    switch (tabIndex) {
      case 0: return null; // All manifests
      case 1: return ManifestStatus.CREATED;
      case 2: return ManifestStatus.ARRIVED; // In Warehouse tab
      case 3: return ManifestStatus.READY_TO_DELIVER; // Ready for Delivery tab
      case 4: return ManifestStatus.DELIVERING;
      case 5: return ManifestStatus.DELIVERED;
      default: return null;
    }
  };

  // Reset column visibility to defaults
  const resetColumnVisibility = () => {
    setVisibleColumns({...defaultColumnVisibility});
    
    // Save default settings to localStorage
    try {
      localStorage.setItem('manifestColumnVisibility', JSON.stringify(defaultColumnVisibility));
    } catch (error) {
      console.error('Failed to save default column visibility to localStorage:', error);
    }
    
    // Auto-size columns after a short delay to ensure the DOM has updated
    setTimeout(() => {
      handleAutosizeColumns();
    }, 100);
  };

  // Column groups for better organization in the UI
  const columnGroups = [
    {
      name: 'Actions',
      columns: ['actions']
    },
    {
      name: 'Identification',
      columns: ['trackingNo', 'internalId', 'status'] // Remove 'sequenceNo'
    },
    {
      name: 'Relationships',
      columns: ['client', 'container', 'driver']
    },
    {
      name: 'Customer',
      columns: ['customerName', 'phoneNo', 'address', 'postalCode', 'country']
    },
    {
      name: 'Shipment',
      columns: ['pieces', 'inboundPieces', 'cbm', 'weight', 'actualPalletsCount', 'location']
    },
    {
      name: 'Dates',
      columns: ['createdDate', 'deliveryDate', 'timeSlot', 'deliveredDate']
    },
    {
      name: 'Other',
      columns: ['deliveryVehicle', 'remarks']
    }
  ];

  // Format column name for display
  const formatColumnName = (column: string): string => {
    return column
      .replace(/([A-Z])/g, ' $1')
      .split(/(?=[A-Z])/).join(' ')
      .replace(/([a-z])([A-Z])/g, '$1 $2')
      .replace(/^./, str => str.toUpperCase())
      .trim();
  };

  useEffect(() => {
    // Create a style element for consistent styling
    const style = document.createElement('style');
    style.innerHTML = `
      .no-cell-focus-outline *:focus,
      .no-cell-focus-outline *:focus-visible,
      .no-cell-focus-outline *:focus-within,
      .no-cell-focus-outline .MuiDataGrid-root *,
      .no-cell-focus-outline .MuiDataGrid-cell,
      .no-cell-focus-outline .MuiDataGrid-row {
        outline: none !important;
        box-shadow: none !important;
      }
    `;
    document.head.appendChild(style);
    
    // Clean up on unmount
    return () => {
      document.head.removeChild(style);
    };
  }, []);

  useEffect(() => {
    // Add custom CSS for DataGrid header styling and horizontal scroll prevention
    const style = document.createElement('style');
    style.innerHTML = `
      /* Disable browser horizontal scrolling */
      html, body {
        overflow-x: hidden !important;
        max-width: 100vw !important;
      }

      /* Ensure main container doesn't overflow */
      #root {
        overflow-x: hidden !important;
        max-width: 100vw !important;
      }

      /* DataGrid header styling */
      .super-app-theme--header {
        background-color: ${theme.palette.primary.main};
        font-weight: bold;
      }

      .MuiDataGrid-columnHeaders .MuiDataGrid-columnHeader:focus,
      .MuiDataGrid-columnHeaders .MuiDataGrid-columnHeader:focus-within {
        outline: none !important;
      }

      .MuiDataGrid-cell {
        outline: none !important;
      }

      /* Prevent any container from causing horizontal overflow */
      .MuiContainer-root,
      .MuiBox-root {
        max-width: 100% !important;
        overflow-x: hidden !important;
      }

      /* Optimized scrollbar styling for better performance */
      .MuiDataGrid-virtualScroller {
        /* Enable GPU acceleration for smooth scrolling */
        transform: translateZ(0);
        will-change: scroll-position;
        /* Use momentum scrolling on iOS */
        -webkit-overflow-scrolling: touch;
      }

      .MuiDataGrid-virtualScroller::-webkit-scrollbar {
        height: 6px;
        width: 6px;
      }

      .MuiDataGrid-virtualScroller::-webkit-scrollbar-track {
        background-color: #f1f1f1;
      }

      .MuiDataGrid-virtualScroller::-webkit-scrollbar-thumb {
        background-color: #c1c1c1;
        border-radius: 3px;
        /* Removed transition for better performance */
      }

      /* Optimize row rendering for better scroll performance */
      .MuiDataGrid-row {
        contain: layout style;
        will-change: transform;
      }

      /* Optimize cell rendering */
      .MuiDataGrid-cell {
        contain: layout style;
      }
    `;
    document.head.appendChild(style);

    return () => {
      document.head.removeChild(style);
    };
  }, [theme.palette.primary.main]);

  useEffect(() => {
    // Create a style element for date picker popups
    const style = document.createElement('style');
    style.textContent = `
      .MuiPickersPopper-root, 
      .MuiDialog-root,
      .MuiPopover-root,
      .MuiModal-root,
      .MuiPopover-paper,
      .MuiDialog-paper,
      .MuiPickersLayout-root {
        z-index: 9999 !important;
      }
      .MuiBackdrop-root {
        z-index: 9998 !important;
      }
    `;
    document.head.appendChild(style);
    
    return () => {
      document.head.removeChild(style);
    };
  }, []);

  // Add effect to handle the trackingNo URL parameter
  useEffect(() => {
    const loadManifestFromURL = async () => {
      if (trackingNo && currentUser?.token) {
        try {
          setLoading(true);
          const response = await manifestService.getManifestByTrackingNo(trackingNo, currentUser.token);
          
          if (response.success) {
            // Use the existing handleOpenDialog function to open the edit dialog with this manifest
            handleOpenDialog(undefined, response.data);
          } else {
            setError(`Failed to load manifest with tracking number ${trackingNo}: ${response.message}`);
          }
        } catch (err: any) {
          console.error('Error loading manifest from URL parameter:', err);
          setError(err.response?.data?.message || `Failed to load manifest with tracking number ${trackingNo}`);
        } finally {
          setLoading(false);
        }
      }
    };

    if (trackingNo) {
      loadManifestFromURL();
    }
  }, [trackingNo, currentUser?.token]);

  // Consolidated effect to handle authentication and data loading
  useEffect(() => {
    const loadData = async () => {
    if (isAuthenticated) {
        setLoading(true);
        try {
          // Load data in parallel for better performance
          await Promise.all([
            fetchManifests(),
            fetchClients(),
            fetchContainers(),
            fetchDrivers(),
            fetchLocationZones(),
            fetchVehicleTypes()
          ]);
        } catch (error) {
          console.error("Error loading data:", error);
        } finally {
          setLoading(false);
        }
    } else {
        // Redirect to login if not authenticated
        window.location.href = '/login';
      }
    };

    loadData();

    // Cleanup function
    return () => {
      // Cancel any pending requests or timers here
      const win = window as any;
      if (win.resizeTimeout) clearTimeout(win.resizeTimeout);
    };
  }, [isAuthenticated]);

  // Add event listener for selective pallet updates
  useEffect(() => {
    const handleSelectivePalletUpdate = (event: CustomEvent) => {
      const { type, manifestTrackingNo, palletCount, inboundedPieces } = event.detail;

      if (type === 'pallet_created') {
        console.log(`🎯 Selective update: Pallet created for manifest ${manifestTrackingNo}, new count: ${palletCount}, inbounded pieces: ${inboundedPieces}`);

        // Update only the specific manifest's pallet count and inbounded pieces without full refresh
        setManifests(prevManifests =>
          prevManifests.map(manifest =>
            manifest.trackingNo === manifestTrackingNo
              ? {
                  ...manifest,
                  actualPalletsCount: palletCount,
                  inboundPieces: inboundedPieces
                }
              : manifest
          )
        );

        // Mark that we've handled the selective update
        const marker = document.createElement('div');
        marker.setAttribute('data-selective-update-handled', 'true');
        marker.style.display = 'none';
        document.body.appendChild(marker);

        console.log('✅ Manifest pallet count and inbound pieces updated selectively without page refresh');
      }
    };

    // Add event listener
    window.addEventListener('pallet-operation-selective-update', handleSelectivePalletUpdate as EventListener);

    // Cleanup
    return () => {
      window.removeEventListener('pallet-operation-selective-update', handleSelectivePalletUpdate as EventListener);
    };
  }, []);

  // Add event listener for bulk manifest creation updates
  useEffect(() => {
    const handleBulkManifestCreation = (event: CustomEvent) => {
      const { type, manifests, count } = event.detail;

      if (type === 'bulk_created' && manifests && Array.isArray(manifests)) {
        console.log(`🎯 ManifestManagement: Received bulk manifest creation event - ${count} new manifests`);

        // Add the new manifests to the existing list without full refresh
        setManifests(prevManifests => {
          // Check for duplicates to avoid adding the same manifest twice
          const existingTrackingNos = new Set(prevManifests.map(m => m.trackingNo));
          const newManifests = manifests.filter((manifest: any) => !existingTrackingNos.has(manifest.trackingNo));

          if (newManifests.length > 0) {
            console.log(`📊 Adding ${newManifests.length} new manifests to ManifestManagement list`);

            // Combine existing and new manifests, then sort by creation date (newest first)
            const updatedManifests = [...prevManifests, ...newManifests];
            const sortedManifests = updatedManifests.sort((a, b) => {
              const dateA = new Date(a.createdDate || 0).getTime();
              const dateB = new Date(b.createdDate || 0).getTime();
              return dateB - dateA; // Newest first
            });

            console.log(`✅ ManifestManagement: Added ${newManifests.length} new manifests without page refresh`);
            return sortedManifests;
          } else {
            console.log('ℹ️ No new manifests to add to ManifestManagement (all already exist)');
            return prevManifests;
          }
        });
      }
    };

    // Add event listener
    window.addEventListener('manifests-bulk-created', handleBulkManifestCreation as EventListener);

    // Cleanup
    return () => {
      window.removeEventListener('manifests-bulk-created', handleBulkManifestCreation as EventListener);
    };
  }, []);

  const fetchManifests = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const token = currentUser?.token;
      if (!token) {
        setError('Authentication token is missing');
        setLoading(false);
        return;
      }
      
      console.log("Fetching all manifests...");
      const response = await manifestService.getAllManifests(token);
      
      if (response.success) {
        console.log(`Fetched ${response.data.length} manifests successfully`);
        
        // Debug sample of manifests to check for internalId
        if (response.data.length > 0) {
          const sampleManifests = response.data.slice(0, 3);
          console.log("DEBUG - Manifest Management - Raw manifest data:", 
            JSON.stringify(sampleManifests.map(m => ({
              trackingNo: m.trackingNo,
              internalId: m.internalId,
              container: m.container?.containerNo,
              sequenceNo: m.sequenceNo,
              actualPalletsCount: m.actualPalletsCount,
              actualPalletsCountType: typeof m.actualPalletsCount,
              cbm: m.cbm,
              cbmType: typeof m.cbm
            })), null, 2)
          );
        }
        
        setManifests(response.data);
      } else {
        setError(response.message || 'Failed to fetch manifests');
        console.error('Error response from API:', response.message);
      }
    } catch (err: any) {
      console.error('Error fetching manifests:', err);
      setError('An error occurred while fetching manifests');
    } finally {
      setLoading(false);
    }
  };

  const fetchClients = async () => {
    try {
      const token = currentUser?.token;
      if (!token) return;

      console.log('Fetching clients with token:', token);
      const response = await userService.getClients(token);
      
      if (response.success) {
        console.log('Clients fetched successfully:', response.data);
        setClients(response.data);
      } else {
        console.error('Failed to fetch clients:', response.message);
        // Set empty array to prevent UI issues
        setClients([]);
        // Don't show error to user for this peripheral feature
      }
    } catch (err: any) {
      console.error('Error fetching clients:', err);
      // Set empty array to prevent UI issues
      setClients([]);
      
      // Don't display API errors for these peripheral features
      // as they might just be permission-related
      if (err.response?.status === 401) {
        console.error("Authentication failed when fetching clients");
      }
    }
  };

  const fetchContainers = async () => {
    try {
      const token = currentUser?.token;
      if (!token) return;

      const response = await containerService.getAllContainers(token);
      
      if (response.success) {
        setContainers(response.data);
      } else {
        // Set empty array to prevent UI issues
        setContainers([]);
      }
    } catch (err: any) {
      console.error('Failed to fetch containers:', err);
      // Set empty array to prevent UI issues
      setContainers([]);
      
      if (err.response?.status === 401) {
        console.error("Authentication failed when fetching containers");
      }
    }
  };

  const fetchDrivers = async () => {
    try {
      const token = currentUser?.token;
      if (!token) return;
      
      const response = await userService.getDrivers(token);
      
      if (response.success) {
        setDrivers(response.data);
      } else {
        // Set empty array to prevent UI issues
        setDrivers([]);
      }
    } catch (err: any) {
      console.error('Failed to fetch drivers:', err);
      // Set empty array to prevent UI issues
      setDrivers([]);
      
      if (err.response?.status === 401) {
        console.error("Authentication failed when fetching drivers");
      }
    }
  };

  const fetchLocationZones = async () => {
    try {
      if (!currentUser?.token) {
        console.error('Authentication token is missing');
        return;
      }
      
      setLocationsLoading(true);
      const response = await locationService.getActiveLocationZones(currentUser.token);
      
      if (response.success && response.data) {
        console.log(`Loaded ${response.data.length} location zones`);
        setLocationZones(response.data);
      } else {
        console.error('Failed to fetch location zones:', response.message);
        toast.error('Failed to load location zones. Location selection may be unavailable.');
      }
    } catch (err: any) {
      console.error('Error fetching location zones:', err);
      toast.error('Error loading location zones. Location selection may be unavailable.');
    } finally {
      setLocationsLoading(false);
    }
  };

  const fetchVehicleTypes = async () => {
    try {
      if (!currentUser?.token) {
        console.error('Authentication token is missing');
        return;
      }
      
      setVehicleTypesLoading(true);
      const data = await vehicleService.getVehicleTypes();
      
      if (data) {
        console.log(`Loaded ${data.length} vehicle types`);
        setVehicleTypes(data);
      } else {
        console.error('Failed to fetch vehicle types');
        toast.error('Failed to load vehicle types. Vehicle type selection may be unavailable.');
      }
    } catch (err: any) {
      console.error('Error fetching vehicle types:', err);
      toast.error('Error loading vehicle types. Vehicle type selection may be unavailable.');
    } finally {
      setVehicleTypesLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
    
    // Update internal ID if sequence number is changed manually
    if (name === 'sequenceNo' && formData.container?.containerNo) {
      const newInternalId = `${formData.container.containerNo}-${value}`;
      setFormData(prev => ({ ...prev, [name]: value, internalId: newInternalId }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  const handleNumberChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    
    // For sequenceNo, update the internal ID as well
    if (name === 'sequenceNo') {
      const numValue = parseInt(value, 10) || 0;
      const newInternalId = `${formData.container.containerNo}-${numValue}`;
      setFormData(prev => ({ ...prev, [name]: numValue, internalId: newInternalId }));
    } else {
      // For other numeric fields, convert to number
      const numValue = parseFloat(value) || 0;
      setFormData(prev => ({ ...prev, [name]: numValue }));
    }
  };

  const handleClientChange = (e: SelectChangeEvent) => {
    const selectedClient = clients.find(client => client.username === e.target.value);
    
    // Reset container selection when client changes
    setFormData(prev => ({
      ...prev,
      client: selectedClient || { username: e.target.value },
      container: { containerNo: '' } // Reset container selection when client changes
    }));
  };

  const handleContainerChange = async (e: SelectChangeEvent) => {
    const containerNo = e.target.value;
    
    // Update the container in the form data
    setFormData(prev => ({
      ...prev,
      container: { containerNo }
    }));
    
    // If we're creating a new manifest (not editing) and a container was selected
    if (!selectedManifest && containerNo) {
      try {
        const token = currentUser?.token;
        if (token) {
          // Fetch the next sequence number for this container
          const response = await manifestService.getNextSequenceNumber(containerNo, token);
          if (response.success) {
            // Update the form with the auto-assigned sequence number and calculate internal ID
            const nextSequence = response.data;
            const internalId = `${containerNo}-${nextSequence}`;
            
            setFormData(prev => ({
              ...prev,
              sequenceNo: nextSequence,
              internalId: internalId
            }));
            
            console.log(`Auto-assigned sequence number ${nextSequence} for container ${containerNo}`);
            console.log(`Generated internal ID: ${internalId}`);
          }
        }
      } catch (error) {
        console.error('Error fetching next sequence number:', error);
      }
    } else if (selectedManifest) {
      // For editing, maintain the current internal ID
      if (formData.sequenceNo) {
        const internalId = `${containerNo}-${formData.sequenceNo}`;
        setFormData(prev => ({
          ...prev,
          internalId: internalId
        }));
      }
    }
  };

  const handleDriverChange = (e: SelectChangeEvent) => {
    const driverUsername = e.target.value;
    if (!driverUsername) {
      // Handle the "Not Assigned" case
      setFormData(prev => ({
        ...prev,
        driver: { username: '' }
      }));
      return;
    }
    
    const selectedDriver = drivers.find(driver => driver.username === driverUsername);
    setFormData(prev => ({
      ...prev,
      driver: { username: driverUsername }
    }));
  };

  const handleStatusChange = (e: SelectChangeEvent) => {
    const newStatus = e.target.value as ManifestStatus;
    const previousStatus = formData.status;
    
    // If changing from DELIVERED to another status, clear the delivered date
    if (previousStatus === ManifestStatus.DELIVERED && newStatus !== ManifestStatus.DELIVERED) {
      setFormData(prev => ({
        ...prev,
        status: newStatus,
        deliveredDate: null
      }));
      toast.info('Delivered date has been cleared as the status is no longer DELIVERED');
    } else {
      setFormData(prev => ({
        ...prev,
        status: newStatus
      }));
    }
  };

  const handleDateChange = (name: string, value: Date | null) => {
    // For deliveredDate, only allow changes if the manifest status is DELIVERED
    if (name === 'deliveredDate' && formData.status !== ManifestStatus.DELIVERED) {
      toast.warning('Delivered date can only be set when manifest status is DELIVERED');
      return;
    }
    
    // Update the form data with the new date value
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle quick edit for delivery date directly from the manifest list
  const handleQuickEditDeliveryDate = async (date: Date | null = tempDeliveryDate, timeSlot: string | null = tempTimeSlot) => {
    if (!editingDeliveryDate) return;
    
    setLoading(true);
    
    try {
      // Find the manifest being updated
      const manifestToUpdate = manifests.find(m => m.trackingNo === editingDeliveryDate);
      if (!manifestToUpdate) {
        toast.error('Manifest not found');
        return;
      }
      
      // Create a copy of the manifest to update
      const updatedManifest = { ...manifestToUpdate };
      
      // Update the delivery date and time slot directly
      // Convert Date object to formatted string to ensure consistent processing
      // Use formatDateOnlyLocal to avoid timezone conversion issues
      updatedManifest.deliveryDate = date ? formatDateOnlyLocal(date) : null;
      updatedManifest.timeSlot = date ? (timeSlot || 'no_time') : null; // Default to 'no_time' if no time slot but date exists
      
      // Log the update
      console.log(`Updating manifest: ${editingDeliveryDate}`, {
        oldDate: manifestToUpdate.deliveryDate,
        newDate: date ? formatDate(date) : null,
        oldTimeSlot: manifestToUpdate.timeSlot,
        newTimeSlot: updatedManifest.timeSlot,
        oldStatus: manifestToUpdate.status,
        newStatus: updatedManifest.status,
        updatedManifest
      });
      
      // Call the API to update the manifest
      const response = await manifestService.updateManifest(
        editingDeliveryDate,
        updatedManifest,
        currentUser.token
      );
      
      if (response.success) {
        toast.success(date ? 'Delivery date updated successfully' : 'Delivery date removed successfully');
        
        // Only update specific fields from the response instead of replacing the entire object
        // This preserves other UI state and position
        setManifests(prevManifests => 
          prevManifests.map(manifest => {
            if (manifest.trackingNo === editingDeliveryDate) {
              // Only update the fields that might have changed
              return {
                ...manifest, // Keep all existing fields
                deliveryDate: response.data.deliveryDate, // Update with new delivery date
                timeSlot: response.data.timeSlot, // Update with new time slot
                status: response.data.status, // Status might be updated by backend
                inboundPieces: response.data.inboundPieces || manifest.inboundPieces // Update inbound pieces if available
              };
            }
            return manifest;
          })
        );
      } else {
        toast.error(response.message || 'Failed to update delivery date');
      }
    } catch (err: any) {
      console.error('Error updating delivery date:', err);
      toast.error(err.response?.data?.message || 'An error occurred while updating delivery date');
    } finally {
      // Reset the editing state
      setDeliveryDateDialogOpen(false);
      setEditingDeliveryDate(null);
      setEditingInternalId(null);
      setTempDeliveryDate(null);
      setTempTimeSlot(null);
      setLoading(false);
    }
  };

  const handleOpenDialog = (event?: React.MouseEvent, manifest?: Manifest) => {
    if (manifest) {
      setSelectedManifest(manifest);
      try {
        // Find the full client object from the clients array
        const fullClient = manifest.client?.username ? 
          clients.find(c => c.username === manifest.client.username) || manifest.client : 
          manifest.client;
        
        // When editing, we want to make sure the current container is preserved
        const containerValue = manifest.container?.containerNo || '';
        
        // Log current container for debugging
        console.log('Setting container in edit mode:', containerValue, manifest.container);
          
        setFormData({
          trackingNo: manifest.trackingNo,
          client: fullClient || { username: manifest.client?.username || '' },
          container: { containerNo: containerValue },
          driver: { username: manifest.driver?.username || '' },
          sequenceNo: manifest.sequenceNo || 0,
          status: manifest.status || ManifestStatus.CREATED,
          customerName: manifest.customerName || '',
          phoneNo: manifest.phoneNo || '',
          address: manifest.address || '',
          postalCode: manifest.postalCode || '',
          country: manifest.country || '',
          pieces: manifest.pieces || 0,
          cbm: manifest.cbm || 0,
          actualPalletsCount: manifest.actualPalletsCount || 0,
          location: manifest.location || '',
          deliveryDate: manifest.deliveryDate ? new Date(manifest.deliveryDate) : null,
          deliveredDate: manifest.deliveredDate ? new Date(manifest.deliveredDate) : null,
          deliveryVehicle: manifest.deliveryVehicle || '',
          driverRemarks: manifest.driverRemarks || '',
          remarks: manifest.remarks || '',
          weight: manifest.weight || 0,
          internalId: manifest.internalId || '',
          createdDate: manifest.createdDate || null,
        });
      } catch (error) {
        console.error('Error setting form data:', error, manifest);
        // Show a user-friendly error and reset the form to a clean state
        setError('Could not load manifest data. Some fields may be missing.');
        setFormData({
          trackingNo: manifest.trackingNo || '',
          client: { username: '' },
          container: { containerNo: '' },
          driver: { username: '' },
          sequenceNo: 0,
          status: ManifestStatus.CREATED,
          customerName: '',
          phoneNo: '',
          address: '',
          postalCode: '',
          country: '',
          pieces: 0,
          cbm: 0,
          actualPalletsCount: 0,
          location: '',
          deliveryDate: null,
          deliveredDate: null,
          deliveryVehicle: '',
          driverRemarks: '',
          remarks: '',
          weight: 0,
          internalId: '',
          createdDate: null,
        });
      }
    } else {
      setSelectedManifest(null);
      setFormData(initialFormData);
    }
    setOpenDialog(true);
  };

  // Function to clear manifest selection
  const clearManifestSelection = () => {
    setSelectedManifests([]);

    // Clear DataGrid selection using API
    if (apiRef.current) {
      try {
        apiRef.current.setRowSelectionModel([]);
      } catch (error) {
        console.warn('Failed to clear DataGrid selection:', error);
        // Fallback to key increment if API method fails
        setDataGridKey(prev => prev + 1);
      }
    }
  };

  // Quick edit handler for remarks fields
  const handleQuickEditRemarks = async (manifest: Manifest, field: 'remarks' | 'driverRemarks', newValue: string) => {
    try {
      if (!manifest || !manifest.trackingNo) {
        throw new Error('Invalid manifest object provided');
      }

      const response = await manifestService.updateManifestRemarks(
        manifest.trackingNo,
        field,
        newValue,
        currentUser.token
      );

      if (response.success) {
        toast.success(`${field === 'driverRemarks' ? 'Driver remarks' : 'CS remarks'} updated successfully`);
        setManifests(prev =>
          prev.map(m =>
            m.trackingNo === manifest.trackingNo
              ? { ...m, [field]: newValue }
              : m
          )
        );
      } else {
        throw new Error(response.message || 'Failed to update');
      }
    } catch (error) {
      console.error(`Failed to update ${field}:`, error);
      toast.error(`Failed to update ${field === 'driverRemarks' ? 'driver remarks' : 'CS remarks'}`);
      throw error; // Re-throw to let the QuickEditCell handle the error
    }
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setError(null);
    // Clear manifest selection when dialog is closed
    clearManifestSelection();
  };

  const handleSaveManifest = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    
    try {
      setLoading(true);
      const token = currentUser?.token;
      if (!token) {
        setError("Authentication token not found");
        setTimeout(() => navigate('/login', { replace: true }), 2000);
        return;
      }

      // Ensure we have the complete client object
      const clientToSave = formData.client.username 
        ? clients.find(c => c.username === formData.client.username) || formData.client
        : formData.client;
      
      // Validate delivered date - it should only be set when status is DELIVERED
      let deliveredDate = formData.deliveredDate;
      if (formData.status !== ManifestStatus.DELIVERED && deliveredDate) {
        deliveredDate = null;
        toast.warning('Delivered date has been cleared as the status is not DELIVERED');
      }
        
      // Create a new manifest object
      const manifestToSave: Manifest = {
        trackingNo: formData.trackingNo,
        client: clientToSave,
        container: formData.container,
        driver: formData.driver.username ? formData.driver : null,
        sequenceNo: formData.sequenceNo,
        status: formData.status,
        customerName: formData.customerName,
        phoneNo: formData.phoneNo,
        address: formData.address,
        postalCode: formData.postalCode,
        country: formData.country,
        pieces: formData.pieces,
        cbm: formData.cbm,
        actualPalletsCount: formData.actualPalletsCount,
        location: formData.location, // Ensure location is included
        // Format dates using our utility function instead of using toISOString directly
        deliveryDate: formData.deliveryDate ? formatDate(formData.deliveryDate) : null,
        deliveredDate: deliveredDate ? formatDate(deliveredDate) : null,
        deliveryVehicle: formData.deliveryVehicle?.trim() || null,
        driverRemarks: formData.driverRemarks,
        remarks: formData.remarks,
        weight: formData.weight,
        internalId: formData.internalId || selectedManifest?.internalId,
      } as Manifest;

      console.log('Saving manifest with location:', manifestToSave.location);
      console.log('Date format check - deliveryDate:', manifestToSave.deliveryDate);
      console.log('Date format check - deliveredDate:', manifestToSave.deliveredDate);

      let response;
      
      if (selectedManifest) {
        response = await manifestService.updateManifest(
          selectedManifest.trackingNo,
          manifestToSave,
          token
        );
        setSuccessMessage('Manifest updated successfully');
      } else {
        response = await manifestService.createManifest(
          manifestToSave,
          token
        );
        setSuccessMessage('Manifest created successfully');
      }

      if (response.success) {
        handleCloseDialog();
        
        // Update the manifest in the local state instead of refreshing all data
        // This preserves the user's position in the UI
        if (selectedManifest) {
          // For update operations
          setManifests(prevManifests =>
            prevManifests.map(manifest =>
              manifest.trackingNo === selectedManifest.trackingNo
                ? response.data // Use response data for the updated manifest
                : manifest
            )
          );
        } else {
          // For create operations, add the new manifest to the list
          setManifests(prevManifests => [...prevManifests, response.data]);
        }
        
        // If this is an edit operation from a URL parameter (like from ContainerDetail)
        // Redirect back to the container page after successful save
        if (trackingNo && manifestToSave.container?.containerNo) {
          // Short delay to allow success message to be seen
          setTimeout(() => {
            window.location.href = `/containers/${manifestToSave.container.containerNo}`;
          }, 1500);
        }
      } else {
        setError(response.message);
      }
    } catch (err: any) {
      console.error('Error saving manifest:', err);
      setError(err.response?.data?.message || 'Failed to save manifest');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (trackingNo: string) => {
    try {
      const response = await manifestService.deleteManifest(
        trackingNo,
        currentUser?.token || ''
      );

      if (response.success) {
        // Clear selected manifests after deletion
        setSelectedManifests(prevSelected => 
          prevSelected.filter(manifest => manifest.trackingNo !== trackingNo)
        );
        
        // Remove the deleted manifest from the state instead of refreshing all data
        // This preserves the user's position in the UI
        setManifests(prevManifests => 
          prevManifests.filter(manifest => manifest.trackingNo !== trackingNo)
        );
        
        setSuccessMessage('Manifest deleted successfully');
      } else {
        setError(response.message);
      }
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to delete manifest');
    }
  };

  // Function to handle bulk deletion of manifests
  const handleBulkDelete = async () => {
    if (selectedManifests.length === 0) {
      toast.warning('Please select at least one manifest to delete');
      return;
    }

    try {
      const token = currentUser?.token || '';
      let successCount = 0;
      let failCount = 0;
      
      // Create a copy of the tracking numbers to delete
      const trackingNumbers = selectedManifests.map(m => m.trackingNo);
      const failedTrackingNumbers: string[] = [];
      
      // Show loading state
      setLoading(true);
      
      // Process each manifest sequentially to avoid overwhelming the server
      for (const trackingNo of trackingNumbers) {
        try {
          const response = await manifestService.deleteManifest(trackingNo, token);
          
          if (response.success) {
            successCount++;
          } else {
            failCount++;
            failedTrackingNumbers.push(trackingNo);
            console.error(`Failed to delete manifest ${trackingNo}: ${response.message}`);
          }
        } catch (err) {
          failCount++;
          failedTrackingNumbers.push(trackingNo);
          console.error(`Error deleting manifest ${trackingNo}:`, err);
        }
      }
      
      // Clear selected manifests
      setSelectedManifests([]);
      
      // Show success message
      if (successCount > 0) {
        setSuccessMessage(`Successfully deleted ${successCount} manifest${successCount !== 1 ? 's' : ''}`);
        
        // Remove the deleted manifests from the state instead of refreshing all data
        // This preserves the user's position in the UI
        setManifests(prevManifests => 
          prevManifests.filter(manifest => !trackingNumbers.includes(manifest.trackingNo) || 
                                          failedTrackingNumbers.includes(manifest.trackingNo))
        );
      }
      
      // Show error if some failed
      if (failCount > 0) {
        setError(`Failed to delete ${failCount} manifest${failCount !== 1 ? 's' : ''}`);
      }
    } catch (err: any) {
      setError(err.message || 'An error occurred during bulk deletion');
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateStatus = async (trackingNo: string, status: ManifestStatus) => {
    try {
      const response = await manifestService.updateManifestStatus(
        trackingNo,
        status,
        currentUser?.token || ''
      );

      if (response.success) {
        setSuccessMessage(`Manifest status updated to ${status}`);
        
        // Update only the status field in the manifest rather than refreshing all data
        // This preserves the user's position in the UI
        setManifests(prevManifests =>
          prevManifests.map(manifest =>
            manifest.trackingNo === trackingNo
              ? {
                  ...manifest, // Keep all existing fields
                  status: status // Update with new status
                }
              : manifest
          )
        );
      } else {
        setError(response.message);
      }
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || 'Failed to update manifest status';
      
      // Show specific toast notification for INBOUNDING/INBOUNDED_TO_WAREHOUSE validation errors
      if (errorMessage.includes('INBOUNDING') || errorMessage.includes('INBOUNDED_TO_WAREHOUSE')) {
        toast.error(errorMessage);
      } else {
        setError(errorMessage);
      }
    }
  };

  const handleSuccessClose = () => {
    setSuccessMessage(null);
  };

  const showConfirmDialog = (title: string, message: string, action: () => void) => {
    setConfirmTitle(title);
    setConfirmMessage(message);
    setConfirmAction(() => action);
    setConfirmDialogOpen(true);
  };

  const formatDateTime = (dateStr?: string | null) => {
    return formatDateString(dateStr);
  };

  const getStatusChipColor = (status: ManifestStatus) => {
    switch (status) {
      case ManifestStatus.CREATED:
        return { bg: '#e3f2fd', color: '#1976d2' }; // Light blue
      case ManifestStatus.ETA_TO_WAREHOUSE:
        return { bg: '#fff8e1', color: '#ff8f00' }; // Amber
      case ManifestStatus.ARRIVED:
        return { bg: '#f1f8e9', color: '#689f38' }; // Light green
      case ManifestStatus.ON_HOLD:
        return { bg: '#ffebee', color: '#d32f2f' }; // Red
      case ManifestStatus.INBOUNDING:
        return { bg: '#fff3e0', color: '#ef6c00' }; // Orange
      case ManifestStatus.INBOUNDED_TO_WAREHOUSE:
        return { bg: '#e8eaf6', color: '#3f51b5' }; // Indigo
      case ManifestStatus.READY_TO_DELIVER:
        return { bg: '#e0f7fa', color: '#00838f' }; // Cyan
      case ManifestStatus.PENDING_DELIVER:
        return { bg: '#ede7f6', color: '#673ab7' }; // Deep purple
      case ManifestStatus.DELIVERING:
        return { bg: '#e8f5e9', color: '#2e7d32' }; // Green
      case ManifestStatus.DELIVERED:
        return { bg: '#e0f2f1', color: '#00695c' }; // Teal
      case ManifestStatus.DISCREPANCY:
        return { bg: '#ffcdd2', color: '#c62828' }; // Bright Red
      default:
        return { bg: '#f5f5f5', color: '#757575' }; // Gray
    }
  };

  // Memoize columns to prevent re-creation on every render
  const columns = React.useMemo(() => [
    // 1. Actions Column - Optimized with better accessibility and performance
    {
      field: 'actions',
      headerName: 'Actions',
      type: 'actions',
      sortable: false,
      filterable: false,
      disableColumnMenu: true,
      width: 120,
      minWidth: 120,
      maxWidth: 120,
      headerClassName: 'super-app-theme--header',
      headerAlign: 'center',
      align: 'center',
      renderCell: (params: GridRenderCellParams) => {
        if (!params?.row?.trackingNo) return null;

        return (
          <Box sx={{ display: 'flex', gap: 0.5, justifyContent: 'center' }}>
            <Tooltip title="Edit manifest" placement="top">
              <IconButton
                size="small"
                onClick={(event) => {
                  event.stopPropagation();
                  handleOpenDialog(event, params.row);
                }}
                sx={{
                  color: 'primary.main',
                  bgcolor: 'primary.light',
                  '&:hover': {
                    bgcolor: 'primary.main',
                    color: 'primary.contrastText',
                    transform: 'scale(1.05)',
                  },
                  transition: 'all 0.2s ease-in-out',
                  width: 32,
                  height: 32,
                }}
                aria-label={`Edit manifest ${params.row.trackingNo}`}
              >
                <EditIcon fontSize="small" />
              </IconButton>
            </Tooltip>

            <Tooltip title="Delete manifest" placement="top">
              <IconButton
                size="small"
                onClick={(event) => {
                  event.stopPropagation();
                  showConfirmDialog(
                    'Delete Manifest',
                    `Are you sure you want to delete manifest ${params.row.trackingNo}?`,
                    () => handleDelete(params.row.trackingNo)
                  );
                }}
                sx={{
                  color: 'error.main',
                  bgcolor: 'error.light',
                  '&:hover': {
                    bgcolor: 'error.main',
                    color: 'error.contrastText',
                    transform: 'scale(1.05)',
                  },
                  transition: 'all 0.2s ease-in-out',
                  width: 32,
                  height: 32,
                }}
                aria-label={`Delete manifest ${params.row.trackingNo}`}
              >
                <DeleteIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          </Box>
        );
      }
    },
    // 2. Location Zone - Enhanced with better visual hierarchy
    {
      field: 'location',
      headerName: 'Location',
      type: 'string',
      width: 140,
      minWidth: 120,
      headerClassName: 'super-app-theme--header',
      headerAlign: 'left',
      align: 'left',
      sortable: true,
      filterable: true,
      renderCell: (params: GridRenderCellParams) => {
        const location = params.value || params.row?.location;
        const hasLocation = Boolean(location);

        return (
          <Box sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 1,
            width: '100%',
            overflow: 'hidden'
          }}>
            <LocationOnIcon
              sx={{
                color: hasLocation ? 'primary.main' : 'grey.400',
                fontSize: 18,
                flexShrink: 0
              }}
            />
            <Typography
              variant="body2"
              sx={{
                fontWeight: hasLocation ? 500 : 400,
                color: hasLocation ? 'text.primary' : 'text.secondary',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap'
              }}
              title={location || 'Not assigned'}
            >
              {location || 'Not assigned'}
            </Typography>
          </Box>
        );
      },
      valueGetter: (params) => params.row?.location || 'Not assigned'
    },
    // 3. Internal ID - Enhanced with smart sorting and visual hierarchy
    {
      field: 'internalId',
      headerName: 'Internal ID',
      type: 'string',
      width: 140,
      minWidth: 120,
      headerClassName: 'super-app-theme--header',
      headerAlign: 'left',
      align: 'left',
      sortable: true,
      filterable: true,
      sortComparator: (v1, v2, params1, params2) => {
        try {
          // Smart sorting for container-sequence format (e.g., "JY6069-1", "JY6069-2")
          const id1 = String(v1 || '');
          const id2 = String(v2 || '');

          const parts1 = id1.split('-');
          const parts2 = id2.split('-');

          // Compare container numbers first (case insensitive)
          const containerCompare = (parts1[0] || '').localeCompare(parts2[0] || '', undefined, {
            sensitivity: 'base',
            numeric: true
          });

          if (containerCompare !== 0) return containerCompare;

          // Compare sequence numbers numerically
          const seq1 = parseInt(parts1[1], 10) || 0;
          const seq2 = parseInt(parts2[1], 10) || 0;

          return seq1 - seq2;
        } catch (err) {
          console.error('Error in internalId sortComparator:', err);
          return String(v1).localeCompare(String(v2));
        }
      },
      renderCell: (params: GridRenderCellParams) => {
        const internalId = params.value || params.row?.internalId;
        const hasId = Boolean(internalId);

        return (
          <Box sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 1,
            width: '100%',
            overflow: 'hidden'
          }}>
            <TimelineIcon
              sx={{
                color: hasId ? 'primary.main' : 'grey.400',
                fontSize: 18,
                flexShrink: 0
              }}
            />
            <Typography
              variant="body2"
              sx={{
                fontWeight: hasId ? 600 : 400,
                color: hasId ? 'primary.dark' : 'text.secondary',
                fontFamily: 'monospace',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap'
              }}
              title={internalId || 'No ID assigned'}
            >
              {internalId || '-'}
            </Typography>
          </Box>
        );
      },
      valueGetter: (params) => params.row?.internalId || ''
    },
    // 4. Tracking Number - Enhanced with copy functionality and better UX
    {
      field: 'trackingNo',
      headerName: 'Tracking No',
      type: 'string',
      width: 160,
      minWidth: 140,
      headerClassName: 'super-app-theme--header',
      headerAlign: 'left',
      align: 'left',
      sortable: true,
      filterable: true,
      renderCell: (params: GridRenderCellParams) => {
        const trackingNo = params.value || params.row?.trackingNo;
        if (!trackingNo) return <Typography variant="body2" color="text.secondary">-</Typography>;

        return (
          <Box sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 1,
            width: '100%',
            overflow: 'hidden'
          }}>
            <Typography
              variant="body2"
              sx={{
                color: 'primary.main',
                fontWeight: 600,
                fontFamily: 'monospace',
                cursor: 'pointer',
                textDecoration: 'none',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
                flex: 1,
                '&:hover': {
                  color: 'primary.dark',
                  textDecoration: 'underline'
                }
              }}
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                navigate(`/manifests/${trackingNo}`);
              }}
              title={`Click to view manifest ${trackingNo}`}
            >
              {trackingNo}
            </Typography>

            <Tooltip title="Copy tracking number" placement="top">
              <IconButton
                size="small"
                onClick={(e) => {
                  e.stopPropagation();
                  navigator.clipboard.writeText(trackingNo);
                  // You could add a toast notification here
                }}
                sx={{
                  width: 24,
                  height: 24,
                  color: 'grey.500',
                  '&:hover': {
                    color: 'primary.main',
                    bgcolor: 'primary.light'
                  }
                }}
                aria-label={`Copy tracking number ${trackingNo}`}
              >
                <ContentCopyIcon sx={{ fontSize: 14 }} />
              </IconButton>
            </Tooltip>
          </Box>
        );
      },
      valueGetter: (params) => params.row?.trackingNo || ''
    },
    // 5. Customer Name
    {
      field: 'customerName',
      headerName: 'Customer Name',
      flex: 1,
      minWidth: 150,
      headerClassName: 'super-app-theme--header',
      sortable: true,
      renderCell: (params) => {
        if (!params || !params.row) return 'N/A';
        return (
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <PersonIcon sx={{ mr: 1, color: theme.palette.grey[600] }} fontSize="small" />
            <Typography variant="body2">
              {params.row.customerName || 'N/A'}
            </Typography>
          </Box>
        );
      }
    },
    // 6. Address
    {
      field: 'address',
      headerName: 'Address',
      flex: 1,
      minWidth: 200,
      headerClassName: 'super-app-theme--header',
      sortable: true,
      renderCell: (params) => (
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <HomeIcon sx={{ mr: 1, color: theme.palette.grey[600] }} fontSize="small" />
          <span>{params.value || '-'}</span>
        </Box>
      ),
    },
    // 7. Postal Code
    {
      field: 'postalCode',
      headerName: 'Postal Code',
      flex: 1,
      minWidth: 120,
      headerClassName: 'super-app-theme--header',
      sortable: true,
    },
    // 8. Delivery Date
    {
      field: 'deliveryDate',
      headerName: 'Delivery Date',
      flex: 1,
      minWidth: 120,
      headerClassName: 'super-app-theme--header',
      sortable: true,
      type: 'date',
      valueGetter: (params) => params && params.row ? params.row.deliveryDate || '' : '',
      renderCell: (params) => {
        try {
          const trackingNo = params.row?.trackingNo;

          if (params.row && params.row.deliveryDate) {
            return (
              <Box sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                width: '100%'
              }}>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <EventAvailableIcon sx={{ mr: 1, color: theme.palette.grey[600] }} fontSize="small" />
                  <span>{formatDeliveryDate(params.row.deliveryDate)}</span>
                </Box>
                <Tooltip title="Edit delivery date">
                  <IconButton
                    size="small"
                    onClick={(e) => {
                      e.stopPropagation();
                      setEditingDeliveryDate(trackingNo);
                      setEditingInternalId(params.row.internalId || null);
                      setTempDeliveryDate(params.row.deliveryDate ? parseDateString(params.row.deliveryDate) : null);
                      setTempTimeSlot(params.row.timeSlot || (params.row.deliveryDate ? getTimeSlotIdFromDate(parseDateString(params.row.deliveryDate) || new Date()) : null));
                      setDeliveryDateDialogOpen(true);
                    }}
                    sx={{
                      opacity: 0.5,
                      '&:hover': { opacity: 1 },
                      padding: '2px',
                    }}
                  >
                    <EditIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              </Box>
            );
          }
          return (
            <Box sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              width: '100%'
            }}>
              <Typography variant="body2" color="text.secondary">
                Not set
              </Typography>
              <Tooltip title="Set delivery date">
                <IconButton
                  size="small"
                  onClick={(e) => {
                    e.stopPropagation();
                    setEditingDeliveryDate(trackingNo);
                    setEditingInternalId(params.row.internalId || null);
                    setTempDeliveryDate(null);
                    setTempTimeSlot(null);
                    setDeliveryDateDialogOpen(true);
                  }}
                  sx={{
                    opacity: 0.5,
                    '&:hover': { opacity: 1 },
                    padding: '2px',
                  }}
                >
                  <AddIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Box>
          );
        } catch (error) {
          console.error('Error rendering delivery date cell:', error);
          return <Typography variant="body2" color="error">Error</Typography>;
        }
      },
    },
    // 9. Time Slot
    {
      field: 'timeSlot',
      headerName: 'Time Slot',
      flex: 1,
      minWidth: 160,
      headerClassName: 'super-app-theme--header',
      sortable: true,
      type: 'string',
      valueGetter: (params) => {
        if (!params || !params.row) return '';
        return params.row.timeSlot || getTimeSlotIdFromDate(params.row.deliveryDate);
      },
      renderCell: (params) => {
        try {
          const timeSlotId = params.row?.timeSlot || getTimeSlotIdFromDate(params.row?.deliveryDate);
          const timeSlotLabel = formatTimeSlot(timeSlotId);

          return (
            <Typography variant="body2" sx={{
              color: timeSlotId ? theme.palette.text.primary : theme.palette.text.secondary
            }}>
              {timeSlotLabel}
            </Typography>
          );
        } catch (error) {
          console.error('Error rendering time slot cell:', error);
          return <Typography variant="body2" color="error">Error</Typography>;
        }
      },
    },
    // 10. Phone Number
    {
      field: 'phoneNo',
      headerName: 'Phone Number',
      flex: 1,
      minWidth: 150,
      headerClassName: 'super-app-theme--header',
      sortable: true,
      renderCell: (params) => (
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <PhoneIcon sx={{ mr: 1, color: theme.palette.grey[600] }} fontSize="small" />
          <span>{params.value || '-'}</span>
        </Box>
      ),
    },
    // 11. Remarks (Driver)
    {
      field: 'driverRemarks',
      headerName: 'Remarks (Driver)',
      flex: 1,
      minWidth: 200,
      headerClassName: 'super-app-theme--header',
      sortable: true,
      renderCell: (params) => {
        const manifest = params.row as Manifest;
        const trackingNo = manifest.trackingNo || '';

        return (
          <QuickEditCell
            value={params.value}
            onSave={(newValue) => handleQuickEditRemarks(manifest, 'driverRemarks', newValue)}
            placeholder="Click to add driver remarks..."
            fieldName="Driver Remarks"
            maxLength={1000}
          />
        );
      },
    },
    // 12. Driver
    {
      field: 'driver',
      headerName: 'Driver',
      flex: 1,
      minWidth: 150,
      headerClassName: 'super-app-theme--header',
      valueGetter: (params) => {
        if (!params || !params.row) return 'Not Assigned';
        const driver = params.row.driver;
        return driver ? driver.username || 'Not Assigned' : 'Not Assigned';
      }
    },
    // 12. Status - Enhanced with better visual design and accessibility
    {
      field: 'status',
      headerName: 'Status',
      type: 'singleSelect',
      width: 160,
      minWidth: 140,
      headerClassName: 'super-app-theme--header',
      headerAlign: 'center',
      align: 'center',
      sortable: true,
      filterable: true,
      valueOptions: Object.values(ManifestStatus),
      valueGetter: (params) => params.row?.status || '',
      sortComparator: (v1, v2) => {
        // Smart status sorting based on workflow progression
        const statusOrder = {
          [ManifestStatus.CREATED]: 1,
          [ManifestStatus.ETA_TO_WAREHOUSE]: 2,
          [ManifestStatus.ARRIVED]: 3,
          [ManifestStatus.INBOUNDED_TO_WAREHOUSE]: 4,
          [ManifestStatus.ON_HOLD]: 5,
          [ManifestStatus.PENDING_DELIVER]: 6,
          [ManifestStatus.READY_TO_DELIVER]: 7,
          [ManifestStatus.DELIVERING]: 8,
          [ManifestStatus.DELIVERED]: 9
        };

        return (statusOrder[v1] || 999) - (statusOrder[v2] || 999);
      },
      renderCell: (params: GridRenderCellParams) => {
        const status = params.value || params.row?.status;
        if (!status) return <Typography variant="body2" color="text.secondary">-</Typography>;

        const { bg, color } = getStatusChipColor(status);
        const statusLabel = status.replace(/_/g, ' ');

        return (
          <Tooltip title={`Status: ${statusLabel}`} placement="top">
            <Chip
              label={statusLabel}
              size="small"
              sx={{
                backgroundColor: bg,
                color: color,
                fontWeight: 600,
                fontSize: '0.75rem',
                height: 28,
                borderRadius: '14px',
                border: `1px solid ${color}`,
                textTransform: 'capitalize',
                minWidth: 'fit-content',
                '& .MuiChip-label': {
                  px: 1.5,
                  py: 0.5
                },
                '&:hover': {
                  transform: 'scale(1.02)',
                  boxShadow: `0 2px 8px ${bg}40`
                },
                transition: 'all 0.2s ease-in-out'
              }}
            />
          </Tooltip>
        );
      }
    },
    // 13. Vehicle Type
    {
      field: 'deliveryVehicle',
      headerName: 'Vehicle Type',
      minWidth: 140,
      flex: 1,
      headerClassName: 'super-app-theme--header',
      renderCell: (params) => (
        <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', justifyContent: 'space-between' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', flex: 1 }}>
            <LocalShippingOutlinedIcon sx={{ mr: 1, color: theme.palette.grey[600] }} fontSize="small" />
            <Typography variant="body2" sx={{ flexGrow: 1 }}>
              {params.value || 'Auto-assign'}
            </Typography>
          </Box>
          <Tooltip title="Quick Edit Vehicle">
            <IconButton
              size="small"
              onClick={(event) => {
                event.stopPropagation();
                handleOpenQuickVehicleEdit(params.row);
              }}
              sx={{
                color: theme.palette.warning.main,
                bgcolor: 'rgba(255, 152, 0, 0.08)',
                ml: 1,
                '&:hover': {
                  bgcolor: 'rgba(255, 152, 0, 0.15)',
                }
              }}
            >
              <EditIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        </Box>
      )
    },
    // 14. Pieces
    {
      field: 'pieces',
      headerName: 'Pieces',
      flex: 1,
      minWidth: 100,
      headerClassName: 'super-app-theme--header',
      sortable: true,
      type: 'number',
    },
    // 15. Inbound Pieces
    {
      field: 'inboundPieces',
      headerName: 'Inbound Pieces',
      flex: 1,
      minWidth: 120,
      headerClassName: 'super-app-theme--header',
      sortable: true,
      type: 'number',
      renderCell: (params) => {
        const manifest = params.row;
        const inboundPieces = params.value || 0;
        const totalPieces = manifest.pieces || 0;

        // Determine the color based on inbound vs total pieces
        let color = theme.palette.text.primary;

        if (inboundPieces === 0) {
          color = theme.palette.text.secondary;
        } else if (inboundPieces < totalPieces) {
          color = theme.palette.warning.main;
        } else if (inboundPieces === totalPieces) {
          color = theme.palette.success.main;
        } else if (inboundPieces > totalPieces) {
          color = theme.palette.error.main;
        }

        return (
          <Typography variant="body2" sx={{
            color,
            fontWeight: 'medium'
          }}>
            {inboundPieces.toLocaleString()}
          </Typography>
        );
      },
    },
    // 16. Pallets
    {
      field: 'actualPalletsCount',
      headerName: 'Pallets',
      flex: 1,
      minWidth: 140, // Increased to accommodate button
      headerClassName: 'super-app-theme--header',
      sortable: true,
      type: 'number',
      headerAlign: 'left',
      renderCell: (params) => (
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            width: '100%',
            justifyContent: 'space-between'
          }}
          onClick={(e) => e.stopPropagation()} // Prevent row selection
        >
          <Typography variant="body2" sx={{ minWidth: 'fit-content' }}>
            {params.row.actualPalletsCount ?? 0}
          </Typography>

          {/* Manage Pallet Button */}
          <Tooltip title="Manage Pallets">
            <IconButton
              size="small"
              onClick={(event) => {
                try {
                  // Stop event propagation to prevent row selection
                  event.stopPropagation();

                  if (!params.row?.trackingNo) return;
                  handleOpenPalletManagementDialog(params.row);
                } catch (error) {
                  console.error('Error opening pallet management:', error);
                  setError('Failed to open pallet management. Please try again.');
                }
              }}
              sx={{
                color: theme.palette.success.main,
                bgcolor: 'rgba(76, 175, 80, 0.08)',
                '&:hover': {
                  bgcolor: 'rgba(76, 175, 80, 0.15)',
                },
                minWidth: '28px',
                height: '28px'
              }}
            >
              <ViewInArIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        </Box>
      )
    },
    // 17. CBM
    {
      field: 'cbm',
      headerName: 'CBM',
      flex: 1,
      minWidth: 100,
      headerClassName: 'super-app-theme--header',
      sortable: true,
      type: 'number',
      headerAlign: 'left',
      renderCell: (params) => (
        <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
          <span>{params.value ?? '-'}</span>
        </Box>
      )
    },
    // 18. Remarks (CS)
    {
      field: 'remarks',
      headerName: 'Remarks (CS)',
      flex: 1,
      minWidth: 200,
      headerClassName: 'super-app-theme--header',
      sortable: true,
      renderCell: (params) => {
        const manifest = params.row as Manifest;
        const trackingNo = manifest.trackingNo || '';

        return (
          <QuickEditCell
            value={params.value}
            onSave={(newValue) => handleQuickEditRemarks(manifest, 'remarks', newValue)}
            placeholder="Click to add CS remarks..."
            fieldName="CS Remarks"
            maxLength={1000}
          />
        );
      },
    },
    // 19. Weight
    {
      field: 'weight',
      headerName: 'Weight (kg)',
      minWidth: 120,
      flex: 0.7,
      type: 'number',
      headerClassName: 'super-app-theme--header',
    },
    // 20. Delivered Date
    {
      field: 'deliveredDate',
      headerName: 'Delivered On',
      flex: 1,
      minWidth: 180,
      headerClassName: 'super-app-theme--header',
      sortable: true,
      renderCell: (params) => {
        try {
          if (params.row && params.row.deliveredDate) {
            return (
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <EventAvailableIcon sx={{ mr: 1, color: theme.palette.grey[600] }} fontSize="small" />
                <span>{formatDateTime(params.row.deliveredDate)}</span>
              </Box>
            );
          }
          return <span>-</span>;
        } catch (err) {
          console.error('Error rendering deliveredDate:', err);
          return <span>-</span>;
        }
      }
    },
    // 21. Created Date
    {
      field: 'createdDate',
      headerName: 'Created On',
      flex: 1,
      minWidth: 180,
      headerClassName: 'super-app-theme--header',
      sortable: true,
      renderCell: (params) => {
        try {
          if (params.row && params.row.createdDate) {
            return (
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <CalendarTodayIcon sx={{ mr: 1, color: theme.palette.grey[600] }} fontSize="small" />
                <span>{formatDateTime(params.row.createdDate)}</span>
              </Box>
            );
          }
          return <span>-</span>;
        } catch (err) {
          console.error('Error rendering createdDate:', err);
          return <span>-</span>;
        }
      }
    },
    // 22. Country
    {
      field: 'country',
      headerName: 'Country',
      flex: 1,
      minWidth: 120,
      headerClassName: 'super-app-theme--header',
      sortable: true,
      renderCell: (params) => (
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <LocationOnIcon sx={{ mr: 1, color: theme.palette.grey[600] }} fontSize="small" />
          <span>{params.value || '-'}</span>
        </Box>
      ),
    },
    // 23. Container
    {
      field: 'container',
      headerName: 'Container',
      flex: 1,
      minWidth: 120,
      headerClassName: 'super-app-theme--header',
      sortable: true,
      valueGetter: (params) => {
        if (!params || !params.row) return '';
        const container = params.row.container;
        return container?.containerNo || '';
      },
      renderCell: (params) => {
        if (!params || !params.row) return 'N/A';

        const container = params.row.container;
        if (!container) return 'N/A';

        return (
            <Typography variant="body2">
            {container.containerNo || 'N/A'}
            </Typography>
        );
      }
    },
    // 24. Client
    {
      field: 'client',
      headerName: 'Client',
      flex: 1,
      minWidth: 150,
      headerClassName: 'super-app-theme--header',
      sortable: true,
      valueGetter: (params) => {
        if (!params || !params.row) return '';
        const client = params.row.client;
        return client?.username || '';
      },
      renderCell: (params) => {
        if (!params || !params.row) return 'N/A';

        const client = params.row.client;
        if (!client) return 'N/A';

        const username = client.username || '';
        const companyName = client.companyName || '';

          return (
            <Box>
              <Typography variant="body2" component="div">
                {username}
              </Typography>
            {companyName && (
              <Typography variant="caption" color="text.secondary">
                {companyName}
              </Typography>
            )}
            </Box>
          );
      }
    }
  ], [navigate]);

  const getFilteredManifests = () => {
    const { tab, search } = filterState;
    
    // Skip filtering if there are no manifests
    if (!manifests.length) return [];
    
    // Create a defensive copy and ensure each manifest has required fields
    let filtered = manifests
      .filter(m => m && m.trackingNo) // Filter out null/undefined manifests
      .map(m => {
        // Create the internal ID if it doesn't exist
        const internalId = m.internalId || (m.container?.containerNo && m.sequenceNo 
          ? `${m.container.containerNo}-${m.sequenceNo}` 
          : undefined);
        
        return {
          ...m,
          // Ensure required fields exist
          client: m.client || { username: 'Unknown', companyName: '' },
          container: m.container || { containerNo: 'Unknown' },
          status: m.status || ManifestStatus.CREATED,
          weight: m.weight || 0,
          location: m.location || '',
          deliveryVehicle: m.deliveryVehicle || '',
          // Always set internalId - if it doesn't exist in the backend, generate it
          internalId: internalId || `${m.container?.containerNo || 'Unknown'}-${m.sequenceNo || '0'}`
        };
      });
    
    // Apply status filter based on the selected tab
    if (tab === 1) {
      filtered = filtered.filter(m => m.status === ManifestStatus.CREATED);
    } else if (tab === 2) {
      filtered = filtered.filter(m => 
        m.status === ManifestStatus.ARRIVED || 
        m.status === ManifestStatus.INBOUNDED_TO_WAREHOUSE
      );
    } else if (tab === 3) {
      filtered = filtered.filter(m => 
        m.status === ManifestStatus.READY_TO_DELIVER || 
        m.status === ManifestStatus.PENDING_DELIVER
      );
    } else if (tab === 4) {
      filtered = filtered.filter(m => m.status === ManifestStatus.DELIVERING);
    } else if (tab === 5) { // Delivered
      filtered = filtered.filter(m => m.status === ManifestStatus.DELIVERED);
    }
    
    // Apply search filter if search text exists
    if (search.trim()) {
      const searchLower = search.toLowerCase();
      filtered = filtered.filter(m => {
        try {
          // Safe property access with optional chaining and explicit string conversion
          const clientUsername = String(m.client?.username || '').toLowerCase();
          const clientCompanyName = String(m.client?.companyName || '').toLowerCase();
          const containerNo = String(m.container?.containerNo || '').toLowerCase();
          const trackingNo = String(m.trackingNo || '').toLowerCase();
          const internalId = String(m.internalId || '').toLowerCase();
          const customerName = String(m.customerName || '').toLowerCase();
          const location = String(m.location || '').toLowerCase();
          const deliveryVehicle = String(m.deliveryVehicle || '').toLowerCase();
          
          // Check if any of the fields match the search term
          return (
            trackingNo.includes(searchLower) ||
            internalId.includes(searchLower) ||
            customerName.includes(searchLower) ||
            clientUsername.includes(searchLower) ||
            clientCompanyName.includes(searchLower) ||
            containerNo.includes(searchLower) ||
            location.includes(searchLower) ||
            deliveryVehicle.includes(searchLower)
          );
        } catch (error) {
          // If an error occurs, don't filter out this manifest
          return true;
        }
      });
    }
    
    // Return at most 1000 manifests to prevent performance issues
    if (filtered.length > 1000) {
      return filtered.slice(0, 1000);
    }
    
    return filtered;
  };

  // Calculate totals for filtered manifests
  const calculateTotals = (manifests: Manifest[]) => {
    return manifests.reduce((totals, manifest) => {
      return {
        totalCBM: totals.totalCBM + (manifest.cbm || 0),
        totalPallets: totals.totalPallets + (manifest.actualPalletsCount || 0),
        totalPieces: totals.totalPieces + (manifest.pieces || 0),
        totalInboundPieces: totals.totalInboundPieces + (manifest.inboundPieces || 0)
      };
    }, {
      totalCBM: 0,
      totalPallets: 0,
      totalPieces: 0,
      totalInboundPieces: 0
    });
  };

  // Memoize the filtered manifests to avoid unnecessary recalculations
  const filteredManifests = useMemo(() => {
    const { search, status, dateRange, location, deliveryVehicle, hasDeliveryDate } = filterState;
    
    // Create a copy of the manifests that match the current tab criteria
    let filtered = getFilteredManifests();
    
    // Apply status filter if specific statuses are selected
    if (status && status.length > 0) {
      filtered = filtered.filter(m => status.includes(m.status));
    }
    
    // Apply date range filter
    if (dateRange.start || dateRange.end) {
      filtered = filtered.filter(m => {
        if (!m.deliveryDate) return false;
        
        // Parse the delivery date from the manifest
        const deliveryDate = new Date(m.deliveryDate);
        
        // Set time to start of day for start date and end of day for end date
        if (dateRange.start && dateRange.end) {
          const startDate = new Date(dateRange.start);
          startDate.setHours(0, 0, 0, 0);
          
          const endDate = new Date(dateRange.end);
          endDate.setHours(23, 59, 59, 999);
          
          return deliveryDate >= startDate && deliveryDate <= endDate;
        } else if (dateRange.start) {
          const startDate = new Date(dateRange.start);
          startDate.setHours(0, 0, 0, 0);
          return deliveryDate >= startDate;
        } else if (dateRange.end) {
          const endDate = new Date(dateRange.end);
          endDate.setHours(23, 59, 59, 999);
          return deliveryDate <= endDate;
        }
        
        return true;
      });
    }
    
    // Apply location filter - update to exact match
    if (location) {
      filtered = filtered.filter(m => 
        m.location && m.location === location
      );
    }
    
    // Apply delivery vehicle filter
    if (deliveryVehicle) {
      const vehicleLower = deliveryVehicle.toLowerCase();
      filtered = filtered.filter(m => 
        m.deliveryVehicle && m.deliveryVehicle.toLowerCase().includes(vehicleLower)
      );
    }
    
    // Apply has delivery date filter
    if (hasDeliveryDate) {
      filtered = filtered.filter(m => m.deliveryDate !== null);
    }
    
    // Apply search filter if a search term is provided
    if (search && search.trim() !== '') {
      const searchTerm = search.toLowerCase().trim();
      filtered = filtered.filter(m => {
        return (
          (m.trackingNo && m.trackingNo.toLowerCase().includes(searchTerm)) ||
          (m.internalId && m.internalId.toLowerCase().includes(searchTerm)) ||
          (m.customerName && m.customerName.toLowerCase().includes(searchTerm)) ||
          (m.client.username && m.client.username.toLowerCase().includes(searchTerm)) ||
          (m.client.companyName && m.client.companyName.toLowerCase().includes(searchTerm)) ||
          (m.container.containerNo && m.container.containerNo.toLowerCase().includes(searchTerm)) ||
          (m.address && m.address.toLowerCase().includes(searchTerm)) ||
          (m.phoneNo && m.phoneNo.toLowerCase().includes(searchTerm)) ||
          (m.postalCode && m.postalCode.toLowerCase().includes(searchTerm))
        );
      });
    }
    
    return filtered;
  }, [manifests, filterState]);

  // Calculate totals for the filtered manifests
  const totals = useMemo(() => {
    return calculateTotals(filteredManifests);
  }, [filteredManifests]);

  // Get the count of visible columns
  const visibleColumnCount = useMemo(() => {
    return Object.values(visibleColumns).filter(Boolean).length - 1; // Subtract 1 for actions column
  }, [visibleColumns]);
  
  const totalColumnCount = Object.keys(visibleColumns).length - 1; // Subtract 1 for actions column

  // Handle search input change
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setFilterState(prev => ({ ...prev, search: event.target.value }));
  };
  
  // Handle status filter change
  const handleStatusFilterChange = (status: ManifestStatus) => {
    setFilterState(prev => {
      const currentStatuses = [...prev.status];
      const index = currentStatuses.indexOf(status);
      
      if (index === -1) {
        // Add status to filter
        return {
      ...prev,
          status: [...currentStatuses, status]
        };
      } else {
        // Remove status from filter
        currentStatuses.splice(index, 1);
        return {
          ...prev,
          status: currentStatuses
        };
      }
    });
  };
  
  // Handle date range filter change
  const handleDateRangeChange = (type: 'start' | 'end', date: Date | null) => {
    // If date is provided, ensure it has the correct time
    let adjustedDate = date;
    if (date) {
      adjustedDate = new Date(date);
      if (type === 'start') {
        adjustedDate.setHours(0, 0, 0, 0);
      } else if (type === 'end') {
        adjustedDate.setHours(23, 59, 59, 999);
      }
    }
    
    setFilterState(prev => ({
      ...prev,
      dateRange: {
        ...prev.dateRange,
        [type]: adjustedDate
      }
    }));
  };

  // Handle location filter change
  const handleLocationFilterChange = (event, newValue) => {
    setFilterState(prev => ({
      ...prev,
      location: newValue ? newValue.name : ''
    }));
  };
  
  // Handle delivery vehicle filter change
  const handleDeliveryVehicleFilterChange = (event, newValue) => {
    setFilterState(prev => ({
      ...prev,
      deliveryVehicle: newValue ? newValue.name : ''
    }));
  };
  
  // Handle has delivery date filter change
  const handleHasDeliveryDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFilterState(prev => ({
      ...prev,
      hasDeliveryDate: e.target.checked
    }));
  };
  
  // Reset all filters
  const resetFilters = () => {
    setFilterState({
      search: '',
      status: [],
      dateRange: {
        start: null,
        end: null
      },
      location: '',
      deliveryVehicle: '',
      hasDeliveryDate: false
    });
  };

  // Get filtered containers based on selected client
  const getFilteredContainers = () => {
    if (!formData.client?.username) {
      console.log('No client selected for container filtering');
      return []; // No client selected, show no containers
    }
    
    // Debug log for container filtering
    console.log('Filtering containers for client:', formData.client.username);
    console.log('Currently selected container:', formData.container.containerNo);
    
    // Find current container object
    const currentContainerObj = containers.find(c => c.containerNo === formData.container.containerNo);
    
    // Filter containers to only show those belonging to the selected client
    let filteredContainers = containers.filter(container => 
      container.client?.username === formData.client.username
    );
    
    // If we're editing a manifest and the current container doesn't belong to this client,
    // add it to the list anyway so it can still be selected
    if (selectedManifest && 
        formData.container.containerNo && 
        currentContainerObj && 
        !filteredContainers.some(c => c.containerNo === formData.container.containerNo)) {
      console.log('Adding current container to filtered list as it belongs to a different client', currentContainerObj);
      filteredContainers = [currentContainerObj, ...filteredContainers];
    }
    
    console.log('Filtered containers:', filteredContainers);
    return filteredContainers;
  };

  // Function to open the label dialog
  const handleOpenLabelDialog = (manifest: Manifest) => {
    setSelectedLabelManifest(manifest);
    setLabelDialogOpen(true);
  };

  // Function to close the label dialog
  const handleCloseLabelDialog = () => {
    setLabelDialogOpen(false);
    setSelectedLabelManifest(null);
  };

  const handleOpenPalletManagementDialog = (manifest: Manifest) => {
    setSelectedPalletManifest(manifest);
    setPalletManagementDialogOpen(true);
  };

  const handleClosePalletManagementDialog = () => {
    setPalletManagementDialogOpen(false);
    setSelectedPalletManifest(null);
  };

  const handlePalletOperationComplete = () => {
    // Traditional approach: Refresh manifests to update pallet counts
    // This will be called after selective updates have been processed
    console.log('🔄 Traditional pallet operation complete callback - checking if full refresh is needed');

    // Only do full refresh if we haven't handled it via selective update
    if (!document.querySelector('[data-selective-update-handled]')) {
      console.log('📄 No selective update detected, performing full refresh');
      fetchManifests();
    } else {
      console.log('✅ Selective update already handled, skipping full refresh');
      // Remove the marker for next time
      document.querySelector('[data-selective-update-handled]')?.remove();
    }
  };
  
  // Bulk label generation function to handle multiple manifests
  const handleBulkLabelGeneration = () => {
    console.log("handleBulkLabelGeneration called with selected manifests:", selectedManifests);
    console.log("Selected manifest count:", selectedManifests.length);
    console.log("Selected manifest tracking numbers:", selectedManifests.map(m => m.trackingNo));
    
    if (selectedManifests.length === 0) {
      console.log("No manifests selected, showing error");
      setManifestError('Please select at least one manifest to generate labels');
      toast.warning('Please select at least one manifest to generate labels');
      return;
    }
    
    if (selectedManifests.length === 1) {
      // For a single manifest, open the label dialog in single mode
      console.log("Single manifest selected, opening label dialog with:", selectedManifests[0]);
      setSelectedLabelManifest(selectedManifests[0]);
      setLabelDialogOpen(true);
      toast.info(`Generating label for manifest: ${selectedManifests[0].trackingNo}`);
    } else {
      // For multiple manifests, use batch mode
      console.log(`Multiple manifests selected (${selectedManifests.length}), opening batch label dialog`);
      // We'll still set the first manifest as selectedLabelManifest for compatibility
      setSelectedLabelManifest(selectedManifests[0]);
      setLabelDialogOpen(true);
      
      // Show informative message
      toast.info(`Preparing to print ${selectedManifests.length} manifest labels in batch mode`);
    }
  };

  // Function to generate and download Excel template
  const handleDownloadTemplate = async () => {
    try {
      // Create new workbook
      const workbook = new Excel.Workbook();
      const worksheet = workbook.addWorksheet('Manifest Template');
      
      // Define template columns with Sequence No as the first column
      const columns = [
        { header: 'Sequence No*', key: 'sequenceNo', width: 15 },
        { header: 'Tracking No*', key: 'trackingNo', width: 20 },
        { header: 'Client Username*', key: 'clientUsername', width: 20 },
        { header: 'Container No*', key: 'containerNo', width: 15 },
        { header: 'Customer Name*', key: 'customerName', width: 20 },
        { header: 'Phone No*', key: 'phoneNo', width: 15 },
        { header: 'Address*', key: 'address', width: 30 },
        { header: 'Postal Code*', key: 'postalCode', width: 12 },
        { header: 'Country*', key: 'country', width: 10 },
        { header: 'Pieces*', key: 'pieces', width: 10 },
        { header: 'CBM*', key: 'cbm', width: 10 },
        { header: 'Weight (kg)*', key: 'weight', width: 10 },
        { header: 'Delivery Date (YYYY-MM-DD)', key: 'deliveryDate', width: 25 },
        { header: 'Remarks', key: 'remarks', width: 30 },
      ];
      
      // Add columns to worksheet
      worksheet.columns = columns;
      
      // Configure data validation for the sequence number column
      // Set the first data row to start with 1
      const firstDataRow = worksheet.getRow(2);
      const firstSeqCell = firstDataRow.getCell(1);
      firstSeqCell.value = 1;
      
      // Style the first sequence cell
      firstSeqCell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'E6F7FF' } // Light blue background
      };
      firstSeqCell.font = {
        bold: true,
        color: { argb: '0066CC' } // Blue text
      };
      
      // Add formula for all subsequent rows (at least 30 rows)
      for (let i = 3; i <= 32; i++) {
        const row = worksheet.getRow(i);
        const seqCell = row.getCell(1);
        seqCell.value = { formula: `=A${i-1}+1` }; // Use specific cell reference for reliability
        
        // Apply styling
        seqCell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'E6F7FF' } // Light blue background
        };
        seqCell.font = {
          bold: true,
          color: { argb: '0066CC' } // Blue text
        };
      }
      
      // Style the header row
      worksheet.getRow(1).eachCell((cell) => {
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: '4F81BD' } // Blue background
        };
        cell.font = {
          bold: true,
          color: { argb: 'FFFFFF' } // White text
        };
        cell.alignment = {
          vertical: 'middle',
          horizontal: 'center'
        };
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        };
      });
      
      // Add one sample row to demonstrate data entry
      const sampleRow = {
        sequenceNo: 1,                  // Sequence number will be auto-filled
        trackingNo: 'TRK12345',         // Tracking number example
        clientUsername: 'client1',      // Must match an existing client username
        containerNo: 'CONT123',         // Must match an existing container number
        customerName: 'John Doe',
        phoneNo: '************',
        address: '123 Main St, Anytown',
        postalCode: '12345',
        country: 'USA',
        pieces: 5,
        cbm: 2.5,
        weight: 100,
        deliveryDate: '2023-12-25',     // Optional, format YYYY-MM-DD
        remarks: 'Handle with care'     // Optional
      };
      
      // Add the sample row data (starting at row 2 as row 1 is header)
      const row2 = worksheet.getRow(2);
      
      // Initialize other column values for the sample row
      row2.getCell(2).value = sampleRow.trackingNo;
      row2.getCell(3).value = sampleRow.clientUsername;
      row2.getCell(4).value = sampleRow.containerNo;
      row2.getCell(5).value = sampleRow.customerName;
      row2.getCell(6).value = sampleRow.phoneNo;
      row2.getCell(7).value = sampleRow.address;
      row2.getCell(8).value = sampleRow.postalCode;
      row2.getCell(9).value = sampleRow.country;
      row2.getCell(10).value = sampleRow.pieces;
      row2.getCell(11).value = sampleRow.cbm;
      row2.getCell(12).value = sampleRow.weight;
      row2.getCell(13).value = sampleRow.deliveryDate;
      row2.getCell(14).value = sampleRow.remarks;
      
      // Style the sample data cells (but not the sequence number)
      for (let col = 2; col <= 14; col++) {
        const cell = row2.getCell(col);
        cell.font = {
          italic: true,
          color: { argb: '808080' } // Gray text for example data
        };
      }
      
      // Add 20 empty rows with auto-incrementing sequence numbers
      for (let i = 0; i < 20; i++) {
        worksheet.addRow({});
      }
      
      // Generate buffer
      const buffer = await workbook.xlsx.writeBuffer();
      
      // Save file using FileSaver
      const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      saveAs(blob, 'manifest_upload_template.xlsx');
      
      toast.success('Template downloaded successfully');
    } catch (error) {
      console.error('Error generating template:', error);
      toast.error('Failed to download template');
    }
  };
  
  // Handle file selection
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0] || null;
    
    if (file) {
      // Check file type
      if (!file.name.endsWith('.xlsx') && !file.name.endsWith('.xls')) {
        toast.error('Please upload an Excel file (.xlsx or .xls)');
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
        return;
      }
      
      // Check file size (5MB limit)
      if (file.size > 5 * 1024 * 1024) {
        toast.error('File size exceeds 5MB limit');
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
        return;
      }
      
      setUploadedFile(file);
      toast.info(`File "${file.name}" selected for upload`);
    } else {
      setUploadedFile(null);
    }
  };
  
  // Handle file upload
  const handleFileUpload = async () => {
    if (!uploadedFile || !currentUser?.token) {
      toast.error('Please select a file to upload');
      return;
    }
    
    setIsUploading(true);
    setUploadProgress(0);
    setBulkUploadResults(null);
    
    try {
      const reader = new FileReader();
      
      reader.onload = async (e) => {
        try {
          const data = new Uint8Array(e.target?.result as ArrayBuffer);
          const workbook = new Excel.Workbook();
          await workbook.xlsx.load(data);
          
          const worksheet = workbook.getWorksheet(1);
          if (!worksheet) {
            throw new Error('No worksheet found in the Excel file');
          }
          
          const manifests: any[] = [];
          const errors: { row: number; error: string }[] = [];
          
          // Skip header row
          worksheet.eachRow((row, rowNumber) => {
            if (rowNumber === 1) return; // Skip header
            
            try {
              // Skip empty rows or info rows
              const trackingNo = row.getCell(2).value?.toString().trim();
              if (!trackingNo) return; // Skip empty rows (but keep rows with auto-generated sequence numbers)
              
              // Helper function to safely extract string value from Excel cell
              const getCellStringValue = (cellIndex: number, fieldName: string): string => {
                try {
                  const cellValue = row.getCell(cellIndex).value;

                  // Handle null/undefined
                  if (cellValue === null || cellValue === undefined) {
                    return '';
                  }

                  // Handle string values
                  if (typeof cellValue === 'string') {
                    return cellValue.trim();
                  }

                  // Handle number values
                  if (typeof cellValue === 'number') {
                    return cellValue.toString().trim();
                  }

                  // Handle rich text objects (common in Excel)
                  if (typeof cellValue === 'object' && cellValue.richText) {
                    // Extract text from rich text format
                    return cellValue.richText.map((rt: any) => rt.text || '').join('').trim();
                  }

                  // Handle other object types
                  if (typeof cellValue === 'object') {
                    console.warn(`Row ${rowNumber}: ${fieldName} contains object data:`, cellValue);

                    // Try to extract text property if it exists
                    if (cellValue.text) {
                      return cellValue.text.toString().trim();
                    }

                    // Try to extract result property if it exists (for formulas)
                    if (cellValue.result !== undefined) {
                      return cellValue.result.toString().trim();
                    }

                    throw new Error(`${fieldName} contains invalid data format. Expected text, got object. Please ensure the cell contains plain text.`);
                  }

                  // Fallback to string conversion
                  return cellValue.toString().trim();
                } catch (error) {
                  console.error(`Row ${rowNumber}: Error processing ${fieldName}:`, error);
                  throw new Error(`${fieldName} contains invalid data format. Please check the cell content.`);
                }
              };

              // Helper function to safely extract numeric value from Excel cell
              const getCellNumericValue = (cellIndex: number, fieldName: string): number => {
                try {
                  const cellValue = row.getCell(cellIndex).value;

                  // Handle null/undefined
                  if (cellValue === null || cellValue === undefined) {
                    return 0;
                  }

                  // Handle number values
                  if (typeof cellValue === 'number') {
                    return cellValue;
                  }

                  // Handle string values
                  if (typeof cellValue === 'string') {
                    const trimmed = cellValue.trim();
                    if (trimmed === '') return 0;
                    const parsed = parseFloat(trimmed);
                    return isNaN(parsed) ? 0 : parsed;
                  }

                  // Handle object types
                  if (typeof cellValue === 'object') {
                    console.warn(`Row ${rowNumber}: ${fieldName} contains object data:`, cellValue);

                    // Try to extract numeric properties
                    if (cellValue.result !== undefined) {
                      const parsed = parseFloat(cellValue.result.toString());
                      return isNaN(parsed) ? 0 : parsed;
                    }

                    if (cellValue.text !== undefined) {
                      const parsed = parseFloat(cellValue.text.toString());
                      return isNaN(parsed) ? 0 : parsed;
                    }

                    console.warn(`Row ${rowNumber}: ${fieldName} object cannot be converted to number, defaulting to 0`);
                    return 0;
                  }

                  // Fallback conversion
                  const parsed = parseFloat(cellValue.toString());
                  return isNaN(parsed) ? 0 : parsed;
                } catch (error) {
                  console.error(`Row ${rowNumber}: Error processing ${fieldName}:`, error);
                  return 0;
                }
              };

              // Helper function to safely extract date value from Excel cell
              const getCellDateValue = (cellIndex: number, fieldName: string): string | null => {
                try {
                  const cellValue = row.getCell(cellIndex).value;

                  if (!cellValue) return null;

                  // Handle Date objects
                  if (cellValue instanceof Date) {
                    return formatDate(cellValue);
                  }

                  // Handle string values
                  if (typeof cellValue === 'string') {
                    const trimmed = cellValue.trim();
                    if (trimmed === '') return null;
                    return formatDate(new Date(trimmed));
                  }

                  // Handle number values (Excel date serial numbers)
                  if (typeof cellValue === 'number') {
                    return formatDate(new Date(cellValue));
                  }

                  // Handle object types
                  if (typeof cellValue === 'object') {
                    console.warn(`Row ${rowNumber}: ${fieldName} contains object data:`, cellValue);

                    if (cellValue.result !== undefined) {
                      return formatDate(new Date(cellValue.result.toString()));
                    }

                    if (cellValue.text !== undefined) {
                      return formatDate(new Date(cellValue.text.toString()));
                    }

                    return null;
                  }

                  // Fallback conversion
                  return formatDate(new Date(cellValue.toString()));
                } catch (error) {
                  console.error(`Row ${rowNumber}: Error processing ${fieldName}:`, error);
                  return null;
                }
              };

              const manifest = {
                sequenceNo: getCellNumericValue(1, 'Sequence No'),
                trackingNo: trackingNo,
                client: { username: getCellStringValue(3, 'Client Username') },
                container: { containerNo: getCellStringValue(4, 'Container No') },
                customerName: getCellStringValue(5, 'Customer Name'),
                phoneNo: getCellStringValue(6, 'Phone No'),
                address: getCellStringValue(7, 'Address'),
                postalCode: getCellStringValue(8, 'Postal Code'),
                country: getCellStringValue(9, 'Country'),
                pieces: getCellNumericValue(10, 'Pieces'),
                cbm: getCellNumericValue(11, 'CBM'),
                weight: getCellNumericValue(12, 'Weight'),
                deliveryDate: getCellDateValue(13, 'Delivery Date'),
                remarks: getCellStringValue(14, 'Remarks'),
                status: ManifestStatus.CREATED
              };
              
              // Validate required fields
              const requiredFields = [
                { field: 'trackingNo', name: 'Tracking No' },
                { field: 'client.username', name: 'Client Username' },
                { field: 'container.containerNo', name: 'Container No' },
                { field: 'customerName', name: 'Customer Name' },
                { field: 'phoneNo', name: 'Phone No' },
                { field: 'address', name: 'Address' },
                { field: 'postalCode', name: 'Postal Code' },
                { field: 'country', name: 'Country' },
              ];
              
              for (const { field, name } of requiredFields) {
                const value = field.includes('.')
                  ? field.split('.').reduce((obj, key) => obj?.[key], manifest)
                  : manifest[field];
                
                if (!value) {
                  throw new Error(`Missing required field: ${name}`);
                }
              }
              
              // Additional validation for container
              if (manifest.container?.containerNo) {
                const containerExists = containers.some(
                  c => c.containerNo === manifest.container.containerNo
                );
                
                if (!containerExists) {
                  throw new Error(
                    `Container "${manifest.container.containerNo}" does not exist in the system. Please check the container number.`
                  );
                }
              }
              
              // Additional validation for client
              if (manifest.client?.username) {
                const clientExists = clients.some(
                  c => c.username === manifest.client.username
                );
                
                if (!clientExists) {
                  throw new Error(
                    `Client "${manifest.client.username}" does not exist in the system. Please check the client username.`
                  );
                }
              }
              
              manifests.push(manifest);
            } catch (err: any) {
              errors.push({ row: rowNumber, error: err.message || 'Unknown error' });
            }
          });
          
          // Process manifests
          let successCount = 0;
          // Track the total number attempted (including validation errors)
          const totalAttempted = manifests.length + errors.length;
          
          // CRITICAL: If there are ANY validation errors, do not create ANY manifests
          if (errors.length > 0) {
            console.log(`Bulk upload cancelled: ${errors.length} validation errors found. No manifests will be created.`);
            setUploadProgress(100);
          } else if (manifests.length > 0) {
            try {
              setUploadProgress(50); // Set progress to halfway point before starting creation
              
              // Use bulk creation for all-or-nothing behavior
              const response = await manifestService.createManifestsBulk(
                manifests,
                currentUser.token
              );
              
              if (response.success && response.data) {
                successCount = response.data.length;
                setUploadProgress(100);
                console.log(`Bulk creation successful: ${successCount} manifests created`);
              } else {
                // If the bulk operation failed, add the error message
                errors.push({ 
                  row: 0, // General error, not tied to a specific row
                  error: response.message || 'Bulk creation failed'
                });
                setUploadProgress(100);
              }
            } catch (err: any) {
              // If bulk creation fails, no manifests should be created
              let errorMessage = 'Bulk creation failed';
              if (err.response?.data?.message) {
                errorMessage = err.response.data.message;
              } else if (err.message) {
                errorMessage = err.message;
              }
              
              errors.push({ 
                row: 0, // General error, not tied to a specific row
                error: errorMessage
              });
              setUploadProgress(100);
              console.error('Bulk manifest creation failed:', err);
            }
          } else {
            // No valid manifests to create
            setUploadProgress(100);
          }
          
          // Set results
          console.log(`Upload results - Attempted: ${totalAttempted}, Success: ${successCount}, Errors: ${errors.length}`);
          console.log('Validation errors:', errors);
          
          setBulkUploadResults({
            total: totalAttempted,
            successful: successCount,
            failed: errors.length,
            errors
          });
          
          // Complete progress
          setUploadProgress(100);
          
          // Show success message
          if (successCount > 0) {
            toast.success(`Created ${successCount} manifests successfully`);
            // Refresh manifests list
            fetchManifests();
          }
          
          if (errors.length > 0) {
            if (successCount === 0) {
              // All manifests failed or upload was cancelled due to validation errors
              toast.error(`Upload cancelled: ${errors.length} validation error${errors.length !== 1 ? 's' : ''} found. No manifests were created.`);
            } else {
              // This should not happen with the new logic, but keeping as fallback
              toast.error(`Failed to create ${errors.length} manifests`);
            }
          }
          // Only set isUploading to false after all processing is done
          setIsUploading(false);
        } catch (err: any) {
          console.error('Error processing Excel file:', err);
          toast.error(err.message || 'Failed to process Excel file');
          setIsUploading(false);
        }
      };
      
      reader.onerror = () => {
        toast.error('Failed to read file');
        setIsUploading(false);
      };
      
      reader.readAsArrayBuffer(uploadedFile);
    } catch (err: any) {
      console.error('Upload error:', err);
      toast.error(err.message || 'Upload failed');
      setIsUploading(false);
    }
  };
  
  // Handle file drop
  const handleFileDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    
    if (event.dataTransfer.files && event.dataTransfer.files.length > 0) {
      const file = event.dataTransfer.files[0];
      
      // Check file type
      if (!file.name.endsWith('.xlsx') && !file.name.endsWith('.xls')) {
        toast.error('Please upload an Excel file (.xlsx or .xls)');
        return;
      }
      
      setUploadedFile(file);
      toast.info(`File "${file.name}" selected for upload`);
    }
  };
  
  // Prevent default behavior for drag over
  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
  };
  
  // Reset file upload state
  const handleResetFileUpload = () => {
    setUploadedFile(null);
    setUploadProgress(0);
    setBulkUploadResults(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const ConfirmDialog: React.FC<ConfirmDialogProps> = ({ open, title, message, onConfirm, onCancel }) => {
    return (
      <Dialog 
        open={open} 
        onClose={onCancel}
        PaperProps={{
          sx: {
            borderRadius: 2,
            boxShadow: 24,
          },
        }}
      >
        <DialogTitle sx={{ 
          bgcolor: theme.palette.primary.main, 
          color: 'white',
          px: 3,
          py: 2
        }}>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Typography variant="h6">
              {title}
            </Typography>
            <IconButton onClick={onCancel} size="small" sx={{ color: 'white' }}>
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent sx={{ px: 3, py: 3 }}>
          <DialogContentText>{message}</DialogContentText>
        </DialogContent>
        <DialogActions sx={{ px: 3, py: 2 }}>
          <Button onClick={onCancel} variant="outlined">
            Cancel
          </Button>
          <Button 
            onClick={() => {
              onConfirm();
              onCancel();
            }} 
            variant="contained"
            color="primary" 
            autoFocus
          >
            Confirm
          </Button>
        </DialogActions>
      </Dialog>
    );
  };

  // Function to select all columns
  const selectAllColumns = () => {
    const allSelected = { ...visibleColumns };
    Object.keys(allSelected).forEach(key => {
      if (key !== 'actions') { // Keep actions as is
        allSelected[key] = true;
      }
    });
    setVisibleColumns(allSelected);
    
    // Save to localStorage
    try {
      localStorage.setItem('manifestColumnVisibility', JSON.stringify(allSelected));
    } catch (error) {
      console.error('Failed to save column visibility to localStorage:', error);
    }
  };

  // Function to deselect all columns
  const selectNoColumns = () => {
    const noneSelected = { ...visibleColumns };
    Object.keys(noneSelected).forEach(key => {
      if (key !== 'actions') { // Keep actions as is
        noneSelected[key] = false;
      }
    });
    // Keep at least trackingNo visible
    noneSelected.trackingNo = true;
    
    setVisibleColumns(noneSelected);
    
    // Save to localStorage
    try {
      localStorage.setItem('manifestColumnVisibility', JSON.stringify(noneSelected));
    } catch (error) {
      console.error('Failed to save column visibility to localStorage:', error);
    }
  };

  // Load saved column visibility preferences from localStorage when component mounts
  useEffect(() => {
    try {
      const savedColumnVisibility = localStorage.getItem('manifestColumnVisibility');
      if (savedColumnVisibility) {
        const parsedVisibility = JSON.parse(savedColumnVisibility);
        // Ensure the actions column is always visible
        setVisibleColumns({
          ...parsedVisibility,
          actions: true
        });
      }
    } catch (error) {
      console.error('Failed to load column visibility from localStorage:', error);
    }
  }, []);

  // Simple column toggle function
  const handleColumnToggle = (column: string) => {
    // Don't allow hiding the actions column
    if (column === 'actions') return;
    
    setVisibleColumns(prev => {
      const newState = {
      ...prev,
      [column]: !prev[column]
      };
      
      // Save to localStorage
      try {
        localStorage.setItem('manifestColumnVisibility', JSON.stringify(newState));
      } catch (error) {
        console.error('Failed to save column visibility to localStorage:', error);
      }
      
      return newState;
    });
  };
  
  // Generate column visibility model for DataGrid
  const generateColumnVisibilityModel = () => {
    const model = {};
    
    Object.keys(visibleColumns).forEach((field) => {
      model[field] = visibleColumns[field];
    });
    
    return model;
  };
  
  // Function to handle auto-sizing columns using the DataGrid API
  const handleAutosizeColumns = () => {
    if (apiRef.current) {
      // Get only the visible columns
      const visibleColumnFields = columns
        .filter(col => visibleColumns[col.field])
        .map(col => col.field);
      
      // Auto-size only the visible columns with a minimum width of 100px
      apiRef.current.autosizeColumns({
        columns: visibleColumnFields,
        defaultMinWidth: 100
      });
    }
  };
  
  // Add event handler for column resize
  // const handleColumnResize = () => {
  //   // No need to do anything extra here
  // };

  // Export selected manifests to Excel
  const handleExportToExcel = async () => {
    try {
      // Determine which manifests to export
      const manifestsToExport = selectedManifests.length > 0 
        ? selectedManifests 
        : filteredManifests;
        
      if (manifestsToExport.length === 0) {
        toast.warning('No manifests to export');
        return;
      }
      
      // Create a new workbook
      const workbook = new Excel.Workbook();
      const worksheet = workbook.addWorksheet('Manifests Export');
      
      // Get visible columns
      const visibleColumnDefs = columns
        .filter(col => visibleColumns[col.field])
        .filter(col => col.field !== 'actions') // Exclude 'actions' column
        .sort((a, b) => {
          // Ensure columns are in their displayed order
          const aIndex = columns.findIndex(c => c.field === a.field);
          const bIndex = columns.findIndex(c => c.field === b.field);
          return aIndex - bIndex;
        });
        
      // Define Excel columns based on visible grid columns
      const excelColumns = visibleColumnDefs.map(col => ({
        header: col.headerName?.replace('*', '') || formatColumnName(col.field),
        key: col.field,
        width: Math.max(15, (col.headerName?.length || 10) * 1.2)
      }));
      
      // Add columns to worksheet
      worksheet.columns = excelColumns;
      
      // Style the header row
      worksheet.getRow(1).eachCell((cell) => {
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: '4F81BD' } // Blue background
        };
        cell.font = {
          bold: true,
          color: { argb: 'FFFFFF' } // White text
        };
        cell.alignment = {
          vertical: 'middle',
          horizontal: 'center'
        };
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        };
      });
      
      // Process manifest data for Excel
      const processedData = manifestsToExport.map(manifest => {
        const rowData = {};
        
        // Process each column
        visibleColumnDefs.forEach(col => {
          const field = col.field;
          
          // Handle special fields with nested objects
          if (field === 'client') {
            rowData[field] = manifest.client?.username || '';
          } 
          else if (field === 'container') {
            rowData[field] = manifest.container?.containerNo || '';
          }
          else if (field === 'driver') {
            rowData[field] = manifest.driver?.username || 'Not Assigned';
          }
          // Handle date fields
          else if (field === 'createdDate' || field === 'deliveryDate' || field === 'deliveredDate') {
            rowData[field] = manifest[field] ? formatDateTime(manifest[field]) : '';
          }
          // Handle timeSlot field - convert ID to human-readable label
          else if (field === 'timeSlot') {
            rowData[field] = formatTimeSlot(manifest.timeSlot);
          }
          // Handle status field
          else if (field === 'status') {
            rowData[field] = manifest.status?.replace(/_/g, ' ') || '';
          }
          // Handle regular fields
          else {
            rowData[field] = manifest[field] !== undefined ? manifest[field] : '';
          }
        });
        
        return rowData;
      });
      
      // Add rows to worksheet
      processedData.forEach(data => {
        worksheet.addRow(data);
      });
      
      // Format all data rows
      for (let i = 2; i <= processedData.length + 1; i++) {
        worksheet.getRow(i).eachCell((cell) => {
          cell.alignment = {
            vertical: 'middle'
          };
          cell.border = {
            top: { style: 'thin', color: { argb: 'D3D3D3' } },
            left: { style: 'thin', color: { argb: 'D3D3D3' } },
            bottom: { style: 'thin', color: { argb: 'D3D3D3' } },
            right: { style: 'thin', color: { argb: 'D3D3D3' } }
          };
        });
        
        // Set alternating row background color
        if (i % 2 === 0) {
          worksheet.getRow(i).eachCell((cell) => {
            cell.fill = {
              type: 'pattern',
              pattern: 'solid',
              fgColor: { argb: 'F5F5F5' } // Light gray for even rows
            };
          });
        }
      }
      
      // Auto-size columns
      worksheet.columns.forEach(column => {
        let maxLength = 10;
        column.eachCell({ includeEmpty: false }, (cell) => {
          const columnLength = cell.value ? cell.value.toString().length : 0;
          if (columnLength > maxLength) {
            maxLength = columnLength;
          }
        });
        column.width = Math.min(30, Math.max(12, maxLength * 1.2));
      });
      
      // Add timestamp and metadata at the bottom
      const metaRow = worksheet.addRow(['']);
      const metaCell = metaRow.getCell(1);
      metaCell.value = `Exported on ${formatDateString(new Date().toISOString())} - Total: ${processedData.length} manifests`;
      worksheet.mergeCells(metaRow.number, 1, metaRow.number, excelColumns.length);
      metaCell.font = {
        italic: true,
        color: { argb: '808080' } // Gray text
      };
      
      // Generate buffer
      const buffer = await workbook.xlsx.writeBuffer();
      
      // Generate filename
      const timestamp = formatTimestamp();
      const filename = `manifest_export_${timestamp}.xlsx`;
      
      // Save file using FileSaver
      const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      saveAs(blob, filename);
      
      // Show success message
      toast.success(`Successfully exported ${processedData.length} manifests`);
    } catch (error) {
      console.error('Error exporting manifests:', error);
      toast.error('Failed to export manifests');
    }
  };

  // Handle saving custom label count
  const handleSaveLabelCount = (trackingNo: string, count: number) => {
    manifestService.saveLabelCount(trackingNo, count);
  };

  // Add a handler for location change
  const handleLocationChange = (e: SelectChangeEvent) => {
    setFormData({ ...formData, location: e.target.value });
    console.log(`Location zone selected: ${e.target.value}`);
  };

  // After all hooks and before return:
  useEffect(() => {
    // Autosize columns after visibleColumns changes (e.g., columns selected/deselected)
    const timeout = setTimeout(() => {
      if (typeof handleAutosizeColumns === 'function') {
        handleAutosizeColumns();
      }
    }, 100); // Delay to allow DOM update
    return () => clearTimeout(timeout);
  }, [visibleColumns]);

  // Add quick edit handler for CBM and No of Pallets
  const handleQuickEditCell = async () => {
    if (!editingCell || tempCellValue === null || !currentUser?.token) {
      setEditingCell(null);
      setTempCellValue(null);
      return;
    }
    try {
      setLoading(true);
      const manifestToUpdate = manifests.find(m => m.trackingNo === editingCell.trackingNo);
      if (!manifestToUpdate) {
        toast.error('Manifest not found');
        return;
      }
      const updatedManifest = {
        ...manifestToUpdate,
        [editingCell.field]: tempCellValue
      };
      const response = await manifestService.updateManifest(
        editingCell.trackingNo,
        updatedManifest,
        currentUser.token
      );
      if (response.success) {
        toast.success(`${editingCell.field === 'cbm' ? 'CBM' : 'No of Pallets'} updated successfully`);
        setManifests(prev =>
          prev.map(m =>
            m.trackingNo === editingCell.trackingNo
              ? { ...m, [editingCell.field]: tempCellValue }
              : m
          )
        );
      } else {
        toast.error(response.message || 'Failed to update');
      }
    } catch (err) {
      toast.error(err.response?.data?.message || 'An error occurred while updating');
    } finally {
      setEditingCell(null);
      setTempCellValue(null);
      setLoading(false);
    }
  };

  // Handler to open the dialog
  const handleOpenEditValueDialog = (trackingNo: string, field: 'cbm' | 'actualPalletsCount', currentValue: number) => {
    setEditValueDialogOpen(true);
    setEditValueField(field);
    setEditValueTrackingNo(trackingNo);
    setEditValueTemp(currentValue);
  };

  // Handler to close the dialog
  const handleCloseEditValueDialog = () => {
    setEditValueDialogOpen(false);
    setEditValueField(null);
    setEditValueTrackingNo(null);
    setEditValueTemp(null);
  };

  // Handler to save the value
  const handleSaveEditValueDialog = async () => {
    if (!editValueTrackingNo || !editValueField || editValueTemp === null || !currentUser?.token) {
      handleCloseEditValueDialog();
      return;
    }
    try {
      setLoading(true);
      const manifestToUpdate = manifests.find(m => m.trackingNo === editValueTrackingNo);
      if (!manifestToUpdate) {
        toast.error('Manifest not found');
        return;
      }
      
      // Use the complete manifest object approach for consistency
      const updatedManifest = {
        ...manifestToUpdate,
        [editValueField]: editValueTemp
      };
      
      const response = await manifestService.updateManifest(
        editValueTrackingNo,
        updatedManifest,
        currentUser.token
      );
      if (response.success) {
        toast.success(`${editValueField === 'cbm' ? 'CBM' : 'No of Pallets'} updated successfully`);
        
        // Update the manifest in the local state with the complete response data
        setManifests(prev =>
          prev.map(m =>
            m.trackingNo === editValueTrackingNo
              ? response.data // Use the complete response data
              : m
          )
        );
      } else {
        toast.error(response.message || 'Failed to update');
      }
    } catch (err) {
      toast.error(err.response?.data?.message || 'An error occurred while updating');
    } finally {
      handleCloseEditValueDialog();
      setLoading(false);
    }
  };

  // Quick delivery vehicle edit handlers
  const handleOpenQuickVehicleEdit = (manifest: Manifest) => {
    setSelectedManifestForVehicleEdit(manifest);
    setQuickVehicleDialogOpen(true);
  };

  const handleCloseQuickVehicleEdit = () => {
    setQuickVehicleDialogOpen(false);
    setSelectedManifestForVehicleEdit(null);
  };

  const handleSaveQuickVehicleEdit = async (deliveryVehicle: string | null) => {
    if (!selectedManifestForVehicleEdit || !currentUser?.token) {
      throw new Error('Missing manifest or authentication token');
    }

    try {
      setLoading(true);
      
      const response = await manifestService.updateManifestDeliveryVehicle(
        selectedManifestForVehicleEdit.trackingNo,
        deliveryVehicle,
        currentUser.token
      );

      if (response.success) {
        toast.success(`Delivery vehicle updated successfully`);
        
        // Update only specific fields in the manifest rather than replacing the entire object
        // This preserves the user's position in the UI
        setManifests(prevManifests =>
          prevManifests.map(manifest =>
            manifest.trackingNo === selectedManifestForVehicleEdit.trackingNo
              ? {
                  ...manifest, // Keep all existing fields
                  deliveryVehicle: response.data.deliveryVehicle, // Update with new delivery vehicle
                  status: response.data.status // Status might be updated by backend
                }
              : manifest
          )
        );
      } else {
        toast.error(response.message || 'Failed to update delivery vehicle');
      }
    } catch (err: any) {
      console.error('Error updating delivery vehicle:', err);
      toast.error(err.response?.data?.message || 'An error occurred while updating delivery vehicle');
    } finally {
      setLoading(false);
      handleCloseQuickVehicleEdit();
    }
  };

  // Handle bulk delivery date update
  const handleBulkDeliveryDateUpdate = async (date: Date | null = bulkDeliveryDate, timeSlot: string | null = bulkTimeSlot) => {
    if (!currentUser?.token || selectedManifests.length === 0 || !date) {
      toast.warning('Please select at least one manifest and set a delivery date');
      return;
    }
    
    try {
      setLoading(true);
      
      // Format the date as it should appear in the UI (dd MMM yyyy HH:mm)
      // This is what individual updates use
      const formattedDisplayDate = formatDate(date);
      console.log(`Formatted display date: ${formattedDisplayDate}`);
      
      // For the API, format date in the exact format the backend expects: YYYY-MM-DDTHH:MM:SS
      const formattedDate = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}T00:00:00`;
      
      console.log(`Selected date: ${date.toLocaleDateString()}`);
      console.log(`Formatted for API (backend format): ${formattedDate}`);
      
      // Get tracking numbers of selected manifests
      const trackingNos = selectedManifests.map(m => m.trackingNo);
      console.log(`Selected manifest tracking numbers (${trackingNos.length}):`, trackingNos);
      
      // Log the manifests before update
      console.log('Manifests before update:', 
        selectedManifests.map(m => ({
          trackingNo: m.trackingNo,
          deliveryDate: m.deliveryDate
        }))
      );
      
      // Call the API to update delivery dates in bulk
      const response = await manifestService.updateBulkDeliveryDates(
        trackingNos,
        formattedDate,
        currentUser.token
      );
      
      if (response.success) {
        // Check if there were any failures mentioned in the message
        const failureMatch = response.message?.match(/\((\d+) failed\)/);
        const failedCount = failureMatch ? parseInt(failureMatch[1]) : 0;
        
        if (failedCount > 0) {
          console.warn(`${failedCount} manifest updates failed`);
          toast.warning(`${failedCount} out of ${trackingNos.length} manifests could not be updated`);
        } else {
          toast.success(`Delivery date updated for ${trackingNos.length} manifest(s)`);
        }
        
        console.log('API response success:', response.success);
        console.log('API response data count:', response.data?.length || 0);
        
        if (response.data && response.data.length > 0) {
          // Check what's returned from the API
          console.log('Sample API response data:', 
            response.data.slice(0, Math.min(3, response.data.length)).map(m => ({
              trackingNo: m.trackingNo,
              deliveryDate: m.deliveryDate
            }))
          );
          
          // Update the manifests in the local state with the returned data
          setManifests(prevManifests => {
            const updatedManifests = prevManifests.map(manifest => {
              const updatedManifest = response.data.find(m => m.trackingNo === manifest.trackingNo);
              return updatedManifest || manifest;
            });
            
            // Log updates
            console.log('State updated with API response data');
            return updatedManifests;
          });
        } else {
          console.log('No manifests returned from API. Manually updating the local state.');
          
          // If API doesn't return updated manifests, manually update the local state
          setManifests(prevManifests => {
            const updatedManifests = prevManifests.map(manifest => {
              // If this manifest is in the selected list, update its delivery date
              if (trackingNos.includes(manifest.trackingNo)) {
                return {
                  ...manifest,
                  // Use the pre-formatted display date that matches the individual update format
                  deliveryDate: formattedDisplayDate
                };
              }
              return manifest;
            });
            
            // Log the manually updated manifests
            const updatedSelectedManifests = updatedManifests.filter(m => 
              trackingNos.includes(m.trackingNo)
            );
            console.log('Manually updated manifests:', 
              updatedSelectedManifests.map(m => ({
                trackingNo: m.trackingNo,
                deliveryDate: m.deliveryDate
              }))
            );
            
            return updatedManifests;
          });
        }
        
        // Close the dialog and reset the date
        setBulkDeliveryDateDialogOpen(false);
        setBulkDeliveryDate(null);
        setBulkTimeSlot(null);
        
        // Clear selected manifests after successful update
        setSelectedManifests([]);
        
        // Refresh manifest list to get the updated data from the server
        fetchManifests();
      } else {
        toast.error(response.message || 'Failed to update delivery dates');
        console.error('API response indicated failure:', response.message);
      }
    } catch (err: any) {
      console.error('Error updating delivery dates in bulk:', err);
      toast.error(err.response?.data?.message || 'An error occurred while updating delivery dates');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box
      className="no-cell-focus-outline"
      sx={{
        p: { xs: 2, sm: 3 },
        height: 'auto',
        display: 'flex',
        flexDirection: 'column',
        maxWidth: '100vw', // Prevent horizontal overflow beyond viewport
        width: '100%',
        mx: 'auto',
        overflow: 'hidden', // Prevent container overflow
        boxSizing: 'border-box', // Include padding in width calculation
        // Ensure all child elements respect container bounds
        '& > *': {
          maxWidth: '100%',
          overflow: 'hidden',
          boxSizing: 'border-box'
        }
      }}
    >
      <Global styles={globalStyles} />
      <LocalizationProvider dateAdapter={AdapterDateFns}>
        <Box sx={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center', 
          mb: 2,
          flexWrap: { xs: 'wrap', sm: 'nowrap' },
          gap: 2
        }}>
          <Typography 
            variant="h4" 
            sx={{ 
              fontWeight: 'bold', 
              color: theme.palette.primary.main,
              fontSize: { xs: '1.5rem', sm: '2rem' }
            }}
          >
            Manifest Management
          </Typography>
          <Box
            sx={{
              display: 'flex',
              gap: 2,
              alignItems: 'center',
            }}
          >
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={(e) => handleOpenDialog(e)}
            sx={{
              fontWeight: 'bold',
              borderRadius: 2,
              boxShadow: 2,
              px: 3,
              py: 1,
              textTransform: 'none',
              '&:hover': {
                boxShadow: 4,
                bgcolor: theme.palette.primary.dark
              },
            }}
          >
            Create Manifest
          </Button>
            <Button
              variant="contained"
              startIcon={<CloudUploadIcon />}
              onClick={() => setBulkUploadDialogOpen(true)}
              sx={{
                ml: 2,
                fontWeight: 'bold',
                borderRadius: 2,
                boxShadow: 2,
                px: 3,
                py: 1,
                textTransform: 'none',
                bgcolor: theme.palette.success.main,
                '&:hover': {
                  boxShadow: 4,
                  bgcolor: theme.palette.success.dark
                },
              }}
            >
              Bulk Upload
          </Button>
          </Box>
        </Box>

        {/* Welcome message */}
        <Paper sx={{ 
          p: { xs: 2, sm: 3 }, 
          mb: 2, 
          borderLeft: '4px solid #1976d2', 
          bgcolor: 'rgba(25, 118, 210, 0.04)',
          display: { xs: 'none', sm: 'block' },
          borderRadius: 2
        }}>
          <Typography variant="h6" gutterBottom>
            Manifest Management Dashboard
          </Typography>
          <Typography variant="body1" color="textSecondary">
            View, create, update, and manage all delivery manifests in the system. Use the tabs below to filter by manifest status.
          </Typography>
        </Paper>

        {error && (
          <Alert 
            severity="error" 
            sx={{ 
              mb: 2,
              borderRadius: 2,
              '& .MuiAlert-icon': {
                alignItems: 'center'
              }
            }} 
            onClose={() => setError(null)}
          >
            {error}
          </Alert>
        )}

        {/* Advanced Filtering UI */}
        <Box sx={{ mb: 2 }}>
          <Paper sx={{ p: 2, borderRadius: 2, mb: 2 }}>
            <Box sx={{ mb: 3 }}>
              <Typography variant="subtitle2" sx={{ mb: 1 }}>Search</Typography>
              <TextField
                placeholder="Search by tracking no, internal ID, customer name, client, or container"
                variant="outlined"
                size="small"
                value={filterState.search}
                onChange={handleSearchChange}
                fullWidth
                sx={{ 
                  bgcolor: 'white',
                  borderRadius: 1,
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 1,
                    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                      borderColor: theme.palette.primary.main,
                      borderWidth: '1px'
                    },
                    '&:hover .MuiOutlinedInput-notchedOutline': {
                      borderColor: 'rgba(0, 0, 0, 0.42)'
                    }
                  },
                  '& .MuiInputBase-root': {
                    '&:focus, &:focus-visible, &:focus-within': {
                      outline: 'none',
                      boxShadow: 'none'
                    }
                  }
                }}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon color="action" />
                    </InputAdornment>
                  ),
                  endAdornment: filterState.search && (
                    <InputAdornment position="end">
                      <IconButton 
                        size="small" 
                        onClick={() => setFilterState({...filterState, search: ''})}
                        sx={{
                          '&:hover': {
                            bgcolor: 'rgba(0, 0, 0, 0.04)'
                          }
                        }}
                      >
                        <CloseIcon fontSize="small" />
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
              />
            </Box>

            <Box sx={{ mb: 3 }}>
              <Typography variant="subtitle2" sx={{ mb: 1 }}>Filter by Status</Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                {Object.values(ManifestStatus).map((status) => {
                  const isSelected = filterState.status.includes(status);
                  const { bg, color } = getStatusChipColor(status);
                  
                  return (
                    <Chip
                      key={status}
                      label={status.replace(/_/g, ' ')}
                      onClick={() => handleStatusFilterChange(status)}
                      sx={{
                        backgroundColor: isSelected ? bg : 'transparent',
                        color: isSelected ? color : 'text.secondary',
                        borderColor: isSelected ? 'transparent' : 'divider',
                        border: isSelected ? 'none' : '1px solid',
                        fontWeight: isSelected ? 'medium' : 'normal',
                        '&:hover': {
                          backgroundColor: isSelected ? bg : alpha(bg, 0.3),
                        }
                      }}
                      variant={isSelected ? "filled" : "outlined"}
                    />
                  );
                })}
              </Box>
            </Box>
            
            <Box sx={{ mb: 3 }}>
              <Typography variant="subtitle2" sx={{ mb: 1 }}>Delivery Date Range</Typography>
                  <Box sx={{ display: 'flex', gap: 2 }}>
                    <LocalizationProvider dateAdapter={AdapterDateFns}>
                  <Box sx={{ width: '50%' }}>
                      <DatePicker
                        label="From"
                        value={filterState.dateRange.start}
                        onChange={(date) => handleDateRangeChange('start', date)}
                        format="dd MMM yyyy"
                        slotProps={{ 
                          textField: { 
                            fullWidth: true,
                            size: "small",
                            variant: "outlined",
                            InputProps: {
                              endAdornment: filterState.dateRange.start && (
                                <InputAdornment position="end">
                                  <IconButton
                                    edge="end"
                                    size="small"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleDateRangeChange('start', null);
                                    }}
                                    title="Clear date"
                                  >
                                    <ClearIcon fontSize="small" />
                                  </IconButton>
                                </InputAdornment>
                              )
                            }
                          }
                        }}
                      />
                  </Box>
                    
                  <Box sx={{ width: '50%' }}>
                      <DatePicker
                        label="To"
                        value={filterState.dateRange.end}
                        onChange={(date) => handleDateRangeChange('end', date)}
                        format="dd MMM yyyy"
                        slotProps={{ 
                          textField: { 
                            fullWidth: true,
                            size: "small",
                            variant: "outlined",
                            InputProps: {
                              endAdornment: filterState.dateRange.end && (
                                <InputAdornment position="end">
                                  <IconButton
                                    edge="end"
                                    size="small"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleDateRangeChange('end', null);
                                    }}
                                    title="Clear date"
                                  >
                                    <ClearIcon fontSize="small" />
                                  </IconButton>
                                </InputAdornment>
                              )
                            }
                          }
                        }}
                      />
                  </Box>
                    </LocalizationProvider>
                  </Box>
            </Box>
            
            <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
              <Box sx={{ width: '50%' }}>
                <Autocomplete
                  options={locationZones}
                  getOptionLabel={(option) => option.name || ''}
                  value={locationZones.find(loc => loc.name === filterState.location) || null}
                  onChange={handleLocationFilterChange}
                  loading={locationsLoading}
                  loadingText="Loading location zones..."
                  noOptionsText="No location zones available"
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label="Location Zone"
                      fullWidth
                      size="small"
                      variant="outlined"
                      InputProps={{
                        ...params.InputProps,
                        startAdornment: (
                          <>
                            <InputAdornment position="start">
                              <LocationOnIcon color="action" fontSize="small" />
                            </InputAdornment>
                            {params.InputProps.startAdornment}
                          </>
                        ),
                        endAdornment: (
                          <>
                            {locationsLoading ? <CircularProgress color="inherit" size={20} /> : null}
                            {params.InputProps.endAdornment}
                          </>
                        )
                      }}
                    />
                  )}
                />
              </Box>
              
              <Box sx={{ width: '50%' }}>
                <Autocomplete
                  options={vehicleTypes}
                  getOptionLabel={(option) => option.name || ''}
                  value={vehicleTypes.find(vehicle => vehicle.name === filterState.deliveryVehicle) || null}
                  onChange={(event, newValue) => handleDeliveryVehicleFilterChange(event, newValue)}
                  loading={vehicleTypesLoading}
                  loadingText="Loading vehicle types..."
                  noOptionsText="No vehicle types available"
                  componentsProps={{
                    popper: {
                      sx: {
                        zIndex: 10001
                      }
                    }
                  }}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label="Delivery Vehicle"
                      fullWidth
                      size="small"
                      variant="outlined"
                      InputProps={{
                        ...params.InputProps,
                        startAdornment: (
                          <>
                          <InputAdornment position="start">
                              <LocalShippingOutlinedIcon color="action" fontSize="small" />
                          </InputAdornment>
                            {params.InputProps.startAdornment}
                          </>
                        ),
                        endAdornment: (
                          <>
                            {vehicleTypesLoading ? <CircularProgress color="inherit" size={20} /> : null}
                            {params.InputProps.endAdornment}
                          </>
                        )
                      }}
                    />
                  )}
                />
              </Box>
            </Box>

            {/* Compact Totals Display */}
            <Box sx={{ mt: 2 }}>
              <Typography variant="body2" sx={{
                color: theme.palette.text.secondary,
                fontWeight: 'medium'
              }}>
                <strong>Total CBM:</strong> {totals.totalCBM.toFixed(2)} &nbsp;&nbsp;
                <strong>Total Pallets:</strong> {totals.totalPallets.toLocaleString()} &nbsp;&nbsp;
                <strong>Total Pieces:</strong> {totals.totalPieces.toLocaleString()} &nbsp;&nbsp;
                <strong>Total Inbound Pieces:</strong> {totals.totalInboundPieces.toLocaleString()}
              </Typography>
            </Box>

            <FormControlLabel
              control={
                <Checkbox
                  checked={filterState.hasDeliveryDate}
                  onChange={handleHasDeliveryDateChange}
                  color="primary"
                />
              }
              label="Show only manifests with delivery date"
            />
            
            {(filterState.status.length > 0 || filterState.dateRange.start || filterState.dateRange.end || 
              filterState.location || filterState.deliveryVehicle || filterState.hasDeliveryDate) && (
              <Box sx={{ mt: 2, display: 'flex', justifyContent: 'flex-end' }}>
                <Button
                  variant="outlined"
                  size="small"
                  onClick={resetFilters}
                  startIcon={<ClearAllIcon />}
                  color="error"
                  sx={{ borderRadius: 2, px: 2 }}
                >
                  Clear All Filters
                </Button>
              </Box>
            )}
          </Paper>
        </Box>

        <Paper sx={{
          width: '100%',
          maxWidth: '100%', // Prevent overflow beyond container
          borderRadius: 2,
          boxShadow: 2,
          flex: 'none',
          display: 'flex',
          overflow: 'hidden', // Contain all internal overflow
          boxSizing: 'border-box', // Include borders in width calculation
          flexDirection: 'column',
          overflow: 'visible', // Keep visible to allow content to expand
          height: 'auto',
          minHeight: '200px',
          position: 'relative', // Add positioning context
          maxWidth: '100%', // Ensure paper respects parent width 
        }}>
          <Toolbar
            sx={{
              pl: { sm: 2 },
              pr: { xs: 1, sm: 1 },
              borderBottom: '1px solid rgba(224, 224, 224, 1)'
              // Removed background color comment
            }}
          >
            <Typography
              sx={{ flex: '1 1 100%' }}
              variant="h6"
              component="div"
            >
              Manifest List
              {selectedManifests.length > 0 && (
                <Typography variant="caption" sx={{ ml: 2 }}>
                  {selectedManifests.length} manifests selected
            </Typography>
              )}
            </Typography>
            <Tooltip title={selectedManifests.length === 0 ? "Select manifests to generate labels" : `Generate labels for ${selectedManifests.length} selected manifest(s)`}>
              <span>
                <Button
                  variant={selectedManifests.length > 0 ? "contained" : "outlined"}
                  color="primary"
                  startIcon={<PrintIcon />}
                  size="small"
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log("Generate Labels clicked, selected manifests:", selectedManifests.length);
                    if (selectedManifests.length === 0) {
                      console.warn("No manifests selected for label generation");
                      clearManifestSelection(); // Ensure selection is cleared
                      return;
                    }
                    handleBulkLabelGeneration();
                  }}
                  disabled={selectedManifests.length === 0}
                  sx={{ 
                    mr: 1,
                    borderRadius: 2,
                    minWidth: '160px',
                    px: 2,
                    whiteSpace: 'nowrap',
                    fontWeight: selectedManifests.length > 0 ? 'bold' : 'normal',
                    boxShadow: selectedManifests.length > 0 ? 2 : 0,
                  }}
                >
                  Generate Labels {selectedManifests.length > 0 ? `(${selectedManifests.length})` : ''}
                </Button>
              </span>
            </Tooltip>
            <Tooltip title={selectedManifests.length === 0 ? "Select manifests to delete" : `Delete ${selectedManifests.length} selected manifest(s)`}>
              <span>
                <Button
                  variant={selectedManifests.length > 0 ? "contained" : "outlined"}
                  color="error"
                  startIcon={<DeleteIcon />}
                  size="small"
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    if (selectedManifests.length === 0) {
                      console.warn("No manifests selected for deletion");
                      clearManifestSelection(); // Ensure selection is cleared
                      return;
                    }
                    showConfirmDialog(
                      'Bulk Delete Manifests',
                      `Are you sure you want to delete ${selectedManifests.length} selected manifest${selectedManifests.length !== 1 ? 's' : ''}? This action cannot be undone.`,
                      handleBulkDelete
                    );
                  }}
                  disabled={selectedManifests.length === 0}
                  sx={{ 
                    mr: 1,
                    borderRadius: 2,
                    minWidth: '160px',
                    px: 2,
                    whiteSpace: 'nowrap',
                    fontWeight: selectedManifests.length > 0 ? 'bold' : 'normal',
                    boxShadow: selectedManifests.length > 0 ? 2 : 0,
                  }}
                >
                  Delete Selected {selectedManifests.length > 0 ? `(${selectedManifests.length})` : ''}
                </Button>
              </span>
            </Tooltip>
            
            {/* Bulk Delivery Date Update button */}
            <Tooltip title={selectedManifests.length === 0 ? "Select manifests to update delivery date" : `Update delivery date for ${selectedManifests.length} selected manifest(s)`}>
              <span>
                <Button
                  variant={selectedManifests.length > 0 ? "contained" : "outlined"}
                  color="secondary"
                  startIcon={<EventAvailableIcon />}
                  size="small"
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    if (selectedManifests.length === 0) {
                      console.warn("No manifests selected for delivery date update");
                      clearManifestSelection(); // Ensure selection is cleared
                      return;
                    }
                    setBulkDeliveryDateDialogOpen(true);
                  }}
                  disabled={selectedManifests.length === 0}
                  sx={{ 
                    mr: 1,
                    borderRadius: 2,
                    minWidth: '160px',
                    px: 2,
                    whiteSpace: 'nowrap',
                    fontWeight: selectedManifests.length > 0 ? 'bold' : 'normal',
                    boxShadow: selectedManifests.length > 0 ? 2 : 0,
                  }}
                >
                  Update Delivery Date {selectedManifests.length > 0 ? `(${selectedManifests.length})` : ''}
                </Button>
              </span>
            </Tooltip>
            
            <Button
              variant="outlined"
              color="info"
              startIcon={<GetAppIcon />}
              size="small"
              onClick={handleExportToExcel}
              sx={{
                mr: 1,
                borderRadius: 2,
                minWidth: '160px',
                px: 2,
                whiteSpace: 'nowrap',
                backgroundColor: theme.palette.info.main,
                color: theme.palette.common.white,
                '&:hover': {
                  backgroundColor: theme.palette.info.dark,
                },
              }}
            >
              Export {selectedManifests.length > 0 ? `(${selectedManifests.length})` : ''}
            </Button>
            <Tooltip title="Auto-resize columns">
              <IconButton 
                size="small" 
                onClick={handleAutosizeColumns}
                sx={{
                  color: theme.palette.primary.main,
                  '&:hover': {
                    bgcolor: 'rgba(25, 118, 210, 0.08)',
                  }
                }}
              >
                <AutorenewIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          </Toolbar>

          {/* Column visibility toggle buttons - in sequence */}
          <Box sx={{ 
            p: 1.5,
            borderBottom: '1px solid rgba(224, 224, 224, 1)'
            // Removed background color
            // bgcolor: 'background.paper'
          }}>
            <Box sx={{ 
              display: 'flex', 
              justifyContent: 'space-between', 
              alignItems: 'center',
              mb: 1.5
            }}>
              <Typography variant="subtitle2" sx={{ fontWeight: 'medium' }}>
                Toggle Column Visibility
                <Typography component="span" variant="caption" sx={{ ml: 1, color: 'text.secondary' }}>
                  ({visibleColumnCount} of {totalColumnCount} visible)
                </Typography>
              </Typography>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Button 
                  size="small" 
                  variant="outlined"
                  onClick={selectAllColumns}
                  sx={{ 
                    borderRadius: 10,
                    fontSize: '0.75rem',
                    textTransform: 'none'
                  }}
                >
                  Select All
                </Button>
                <Button 
                  size="small" 
                  variant="outlined"
                  onClick={selectNoColumns}
                  sx={{ 
                    borderRadius: 10,
                    fontSize: '0.75rem',
                    textTransform: 'none'
                  }}
                >
                  Select None
                </Button>
                <Button 
                  size="small"
                  variant="outlined"
                  color="primary"
                  onClick={resetColumnVisibility}
                  sx={{ 
                    borderRadius: 10,
                    fontSize: '0.75rem',
                    textTransform: 'none'
                  }}
                >
                  Reset to Default
                </Button>
              </Box>
            </Box>
            
            <Box sx={{
              display: 'flex',
              flexWrap: 'wrap',
              gap: 1,
              overflowX: 'auto', // Allow internal horizontal scrolling for buttons
              overflowY: 'hidden',
              maxWidth: '100%',
              '&::-webkit-scrollbar': {
                height: 6,
              },
              '&::-webkit-scrollbar-track': {
                backgroundColor: theme.palette.grey[100],
                borderRadius: 3,
              },
              '&::-webkit-scrollbar-thumb': {
                backgroundColor: theme.palette.grey[400],
                borderRadius: 3,
                '&:hover': {
                  backgroundColor: theme.palette.grey[600],
                },
              },
            }}>
              {/* Display buttons in the exact order of columns sequence */}
              {[
                'location', 'internalId', 'trackingNo', 'customerName', 'address', 'postalCode',
                'deliveryDate', 'timeSlot', 'phoneNo', 'driverRemarks', 'driver', 'status', 'deliveryVehicle',
                'pieces', 'inboundPieces', 'actualPalletsCount', 'cbm', 'remarks', 'weight',
                'deliveredDate', 'createdDate', 'country', 'container', 'client'
              ].map((column) => {
                // Only show toggle for columns that exist in visibleColumns
                if (!(column in visibleColumns)) return null;

                const isVisible = visibleColumns[column];
                
                const columnLabel = column
                  .charAt(0).toUpperCase() 
                  + column.slice(1).replace(/([A-Z])/g, ' $1').trim();
                
                return (
                  <Button
                  key={column}
                      size="small"
                    variant={isVisible ? "contained" : "outlined"}
                    color={isVisible ? "primary" : "inherit"}
                    onClick={() => handleColumnToggle(column)}
                  sx={{
                      borderRadius: 10,
                      px: 1.5,
                      py: 0.5,
                      minWidth: 'fit-content',
                      fontSize: '0.75rem',
                      textTransform: 'none',
                      boxShadow: isVisible ? 1 : 0
                    }}
                  >
                    {columnLabel}
                  </Button>
                );
              })}
            </Box>
          </Box>

          {loading ? (
            <Box sx={{ 
              display: 'flex', 
              justifyContent: 'center', 
              alignItems: 'center', 
              height: '100%', 
              minHeight: '200px' 
            }}>
              <CircularProgress />
            </Box>
          ) : error ? (
            <ErrorFallback error={new Error(error)} />
          ) : filteredManifests.length === 0 ? (
            <Alert 
              severity="info" 
              sx={{ 
                m: 2,
                borderRadius: 2
              }}
            >
              No manifests found. Try changing the filter or add a new manifest.
            </Alert>
          ) : (
            <DataGrid
              key={dataGridKey}
              apiRef={apiRef}
              rows={filteredManifests}
              columns={columns.filter(col => visibleColumns[col.field])}
              getRowId={(row) => row.trackingNo || `row-${Math.random().toString(36).substr(2, 9)}`}

              // Performance optimizations for smooth scrolling
              disableVirtualization={false}
              rowBuffer={5} // Reduced buffer for better performance
              columnBuffer={1} // Reduced column buffer

              // Scrolling performance improvements
              scrollbarSize={8}

              // Disable experimental features that may cause lag
              // experimentalFeatures={{ ariaV7: true }}

              // Column management
              columnVisibilityModel={generateColumnVisibilityModel()}
              onColumnVisibilityModelChange={(newModel) => {
                const newVisibleColumns = { ...visibleColumns };
                Object.keys(newModel).forEach((field) => {
                  if (field in newVisibleColumns) {
                    newVisibleColumns[field] = newModel[field];
                  }
                });
                setVisibleColumns(newVisibleColumns);

                // Persist column visibility preferences
                try {
                  localStorage.setItem('manifestColumnVisibility', JSON.stringify(newVisibleColumns));
                } catch (error) {
                  console.error('Failed to save column visibility to localStorage:', error);
                }
              }}

              // Initial configuration optimized for performance
              initialState={{
                pagination: {
                  paginationModel: { pageSize: 25 }, // Reduced for better initial performance
                },
                sorting: {
                  sortModel: [{ field: 'internalId', sort: 'asc' }],
                },
                columns: {
                  columnVisibilityModel: generateColumnVisibilityModel(),
                },
              }}

              // Pagination settings optimized for performance
              pageSizeOptions={[25, 50, 100]}
              paginationMode="client"

              // Performance optimizations
              disableColumnResize={false}
              disableColumnReorder={false}
              disableMultipleSelection={false}

              // Throttle updates for better performance
              throttleRowsMs={100}

              // Row settings optimized for performance
              keepNonExistentRowsSelected
              density="compact" // Use compact density for better performance
              rowHeight={60} // Fixed height for better virtualization
              headerHeight={56}

              // Disable auto-sizing for better performance
              autoHeight={false}
              getEstimatedRowHeight={() => 60} // Fixed estimated height
              // Remove getRowHeight for better virtualization performance

              sx={{
                // Container styling with horizontal scroll containment
                width: '100%',
                height: 'auto',
                minHeight: 400,
                maxHeight: '80vh',
                border: '1px solid',
                borderColor: 'divider',
                borderRadius: 2,
                bgcolor: 'background.paper',
                boxShadow: 1,
                overflow: 'hidden', // Prevent container overflow

                // Root element with internal scrolling
                '& .MuiDataGrid-root': {
                  border: 'none',
                  fontFamily: theme.typography.fontFamily,
                  width: '100%',
                  overflow: 'hidden', // Prevent root overflow
                },

                // Virtual scroller optimized for performance
                '& .MuiDataGrid-virtualScroller': {
                  overflowX: 'auto',
                  overflowY: 'auto',
                  // Use will-change for better scroll performance
                  willChange: 'scroll-position',
                  // Simplified scrollbar styling for better performance
                  '&::-webkit-scrollbar': {
                    height: 6,
                    width: 6,
                  },
                  '&::-webkit-scrollbar-track': {
                    backgroundColor: '#f1f1f1',
                  },
                  '&::-webkit-scrollbar-thumb': {
                    backgroundColor: '#c1c1c1',
                    borderRadius: 3,
                  },
                  // Remove hover effects on scrollbar for better performance
                },

                // Main container with proper overflow handling
                '& .MuiDataGrid-main': {
                  overflow: 'hidden', // Prevent main container overflow
                  width: '100%',
                },

                // Viewport with controlled scrolling
                '& .MuiDataGrid-viewport': {
                  overflow: 'hidden', // Let virtualScroller handle scrolling
                  width: '100%',
                },
                // Header styling
                '& .MuiDataGrid-columnHeaders': {
                  backgroundColor: theme.palette.grey[50],
                  borderBottom: `2px solid ${theme.palette.primary.main}`,
                  borderRadius: '8px 8px 0 0',
                  '& .MuiDataGrid-columnHeader': {
                    backgroundColor: 'transparent',
                    '&:focus, &:focus-within': {
                      outline: `2px solid ${theme.palette.primary.main}`,
                      outlineOffset: -2,
                    },
                  },
                },

                '& .MuiDataGrid-columnHeader': {
                  padding: '0 16px',
                  fontSize: '0.875rem',
                  fontWeight: 600,
                  color: theme.palette.text.primary,
                  '& .MuiDataGrid-columnHeaderTitle': {
                    fontWeight: 600,
                    fontSize: '0.875rem',
                  },
                },

                '& .MuiDataGrid-columnSeparator': {
                  color: theme.palette.divider,
                  '&:hover': {
                    color: theme.palette.primary.main,
                  },
                },
                // Row styling optimized for performance (removed expensive animations)
                '& .MuiDataGrid-row': {
                  cursor: 'pointer',
                  // Removed transition for better scroll performance
                  borderRadius: 0,
                  '&:hover': {
                    backgroundColor: alpha(theme.palette.primary.main, 0.04),
                    // Removed transform and box-shadow for better performance
                  },
                  '&:nth-of-type(even)': {
                    backgroundColor: alpha(theme.palette.grey[100], 0.3),
                  },
                  '&.Mui-selected': {
                    backgroundColor: alpha(theme.palette.primary.main, 0.08),
                    '&:hover': {
                      backgroundColor: alpha(theme.palette.primary.main, 0.12),
                    },
                  },
                  // Simplified focus styles
                  '&:focus, &:focus-within': {
                    outline: `1px solid ${theme.palette.primary.main}`,
                  },
                },

                // Cell styling optimized for performance
                '& .MuiDataGrid-cell': {
                  padding: '8px 12px', // Reduced padding for better performance
                  borderBottom: `1px solid ${theme.palette.divider}`,
                  fontSize: '0.875rem',
                  lineHeight: 1.43, // Standard line height
                  color: theme.palette.text.primary,
                  display: 'flex',
                  alignItems: 'center',
                  whiteSpace: 'nowrap',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  // Simplified focus styles for better performance
                  '&:focus, &:focus-within': {
                    outline: `1px solid ${theme.palette.primary.main}`,
                  },
                },
                '& .MuiDataGrid-columnHeader': {
                  padding: '0 16px', // Increased padding from 12px to 16px
                  height: '56px', // Increased from 48px to 56px
                  display: 'flex',
                  alignItems: 'center',
                  '& .MuiDataGrid-columnHeaderTitleContainer': {
                    padding: '0 0',
                  },
                  '& .MuiDataGrid-columnHeaderTitle': {
                    fontWeight: 600, // Make column headers bolder
                    fontSize: '0.95rem', // Slightly larger text
                  },
                },
                '& .MuiDataGrid-cell--withRenderer': {
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'flex-start',
                },
                '& .MuiDataGrid-cellContent': {
                  textAlign: 'left',
                  display: 'flex',
                  alignItems: 'center',
                },
                '& .MuiDataGrid-footerContainer': {
                  minHeight: '56px',
                  maxHeight: '56px',
                  borderTop: '1px solid rgba(224, 224, 224, 1)',
                  overflow: 'hidden', // Changed from 'visible' to 'hidden'
                  padding: '0 12px',
                  display: 'flex',
                  alignItems: 'center',
                },
                '& .MuiTablePagination-root': {
                  width: '100%',
                  display: 'flex',
                  flexDirection: 'row',
                  justifyContent: 'flex-end',
                },
                '& .MuiTablePagination-selectLabel, & .MuiTablePagination-displayedRows': {
                  fontSize: '0.75rem',
                  margin: 0,
                  overflow: 'visible',
                },
                '& .MuiTablePagination-select': {
                  fontSize: '0.75rem',
                  marginLeft: '4px',
                  marginRight: '4px',
                  overflow: 'visible',
                },
                '& .MuiTablePagination-actions': {
                  marginLeft: 0,
                  display: 'flex',
                  alignItems: 'center',
                  overflow: 'visible',
                },
                '& .MuiDataGrid-columnHeaderCheckbox': {
                  padding: '0',
                  height: '48px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                },
                '& .MuiDataGrid-cellCheckbox': {
                  padding: '0',
                  height: '48px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                },
                '& .MuiCheckbox-root': {
                  padding: '8px',
                  margin: '0 auto',
                },
                '& .MuiDataGrid-columnHeaderDraggableContainer, & .MuiDataGrid-columnHeader': {
                  height: '48px',
                },
                '& .MuiDataGrid-columnSeparator': {
                  visibility: 'visible',
                },
                // Specific fix for checkbox alignment
                '& .MuiDataGrid-columnHeaders .MuiDataGrid-iconButtonContainer': {
                  visibility: 'visible',
                  width: '48px',
                  alignItems: 'center',
                  justifyContent: 'center',
                  display: 'flex',
                },
                '& .MuiDataGrid-iconButtonContainer .MuiCheckbox-root': {
                  position: 'static',
                  transform: 'none',
                },
              }}
              checkboxSelection={true}
              onRowSelectionModelChange={(selectionModel) => {
                try {
                  // Log the model type and value
                  console.log("Selection model type:", typeof selectionModel);
                  console.log("Selection model value:", selectionModel);
                  
                  // Extract IDs from the selection model
                  let selectedIds = [];
                  
                  if (Array.isArray(selectionModel)) {
                    // If it's already an array, use it directly
                    selectedIds = selectionModel;
                  } else if (selectionModel && typeof selectionModel === 'object') {
                    // Handle the new selection model format with type and ids fields
                    if (selectionModel.type === 'include' && selectionModel.ids instanceof Set) {
                      // Convert the Set to an array
                      selectedIds = Array.from(selectionModel.ids);
                    } else if (selectionModel.ids && Array.isArray(selectionModel.ids)) {
                      // If ids is already an array
                      selectedIds = selectionModel.ids;
                    } else {
                      try {
                        // Try to convert to array if it's an iterable
                        selectedIds = Array.from(selectionModel);
                      } catch (e) {
                        console.error("Failed to convert selection model to array:", e);
                        // If conversion fails, try to use as is
                        selectedIds = [selectionModel];
                      }
                    }
                  }
                  
                  console.log("Processed selection IDs:", selectedIds);
                  
                  // Find manifests with the selected IDs
                  const selectedItems = manifests.filter(manifest => {
                    const isSelected = selectedIds.indexOf(manifest.trackingNo) !== -1;
                    if (isSelected) {
                      console.log("Selected manifest:", manifest.trackingNo);
                    }
                    return isSelected;
                  });
                  
                  console.log("Selected manifests:", selectedItems.length, selectedItems);

                  // Update the state
                  setSelectedManifests(selectedItems);
                } catch (error) {
                  console.error('Error handling selection change:', error);
                  setSelectedManifests([]);
                }
              }}
              components={{
                NoRowsOverlay: () => (
                  <Box sx={{ 
                    display: 'flex', 
                    justifyContent: 'center', 
                    alignItems: 'center', 
                    height: '100%', 
                    minHeight: '200px' 
                  }}>
                    <Typography>No manifests found</Typography>
                  </Box>
                ),
                ErrorOverlay: (props) => (
                  <Box sx={{ 
                    display: 'flex', 
                    justifyContent: 'center', 
                    alignItems: 'center', 
                    height: '100%', 
                    p: 2 
                  }}>
                    <Alert severity="error">
                      An error occurred while loading data. Please try refreshing the page.
                    </Alert>
                  </Box>
                ),
              }}
            />
          )}
        </Paper>

        {/* Manifest Create/Edit Dialog */}
        <Dialog 
          open={openDialog} 
          onClose={handleCloseDialog}
          maxWidth="md"
          fullWidth
          PaperProps={{
            sx: {
              borderRadius: 2,
              boxShadow: 24,
              maxHeight: '90vh',
              overflowY: 'auto'
            },
          }}
        >
          <DialogTitle sx={{ 
            bgcolor: theme.palette.primary.main, 
            color: 'white',
            px: 3,
            py: 2
          }}>
            <Box display="flex" justifyContent="space-between" alignItems="center">
              <Typography variant="h6">
                {selectedManifest ? 'Edit Manifest' : 'Create Manifest'}
              </Typography>
              <IconButton
                aria-label="close"
                onClick={handleCloseDialog}
                sx={{
                  color: 'white',
                }}
              >
                <CloseIcon />
              </IconButton>
            </Box>
          </DialogTitle>
          <DialogContent dividers sx={{ px: 3, py: 3, '&::-webkit-scrollbar': { width: 8 }, '&::-webkit-scrollbar-thumb': { backgroundColor: 'rgba(0,0,0,0.2)', borderRadius: 4 } }}>
            {error && (
              <Alert severity="error" sx={{ mb: 2 }}>
                {error}
              </Alert>
            )}
            <form onSubmit={handleSaveManifest}>
              {/* Identification Section */}
              <Typography variant="h6" color="primary" sx={{ mb: 2, fontWeight: 'bold' }}>
                Identification
              </Typography>
              <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', md: '1fr 1fr' }, gap: 2, mb: 3 }}>
                {selectedManifest ? (
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
                    <Typography variant="caption" color="text.secondary">
                      Tracking Number
                    </Typography>
                    <Typography variant="body1" sx={{ fontWeight: 'medium', display: 'flex', alignItems: 'center' }}>
                      <Box component="span" sx={{ 
                        color: theme.palette.primary.main, 
                        display: 'inline-flex',
                        alignItems: 'center',
                        '& svg': { mr: 1 } 
                      }}>
                        <TimelineIcon fontSize="small" />
                      </Box>
                      {formData.trackingNo}
                    </Typography>
                  </Box>
                ) : (
                  <TextField
                    required
                    name="trackingNo"
                    label="Tracking No"
                    value={formData.trackingNo}
                    onChange={handleInputChange}
                    fullWidth
                    margin="normal"
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 1,
                        '&:hover .MuiOutlinedInput-notchedOutline': {
                          borderColor: theme.palette.primary.main,
                        },
                      },
                    }}
                  />
                )}
                
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
                  <Typography variant="caption" color="text.secondary">
                    Internal ID
                  </Typography>
                  <Typography variant="body1" sx={{ fontWeight: 'medium', display: 'flex', alignItems: 'center' }}>
                    <Box component="span" sx={{ 
                      color: theme.palette.primary.main, 
                      display: 'inline-flex',
                      alignItems: 'center',
                      '& svg': { mr: 1 } 
                    }}>
                      <TimelineIcon fontSize="small" />
                    </Box>
                    {formData.container?.containerNo && formData.sequenceNo 
                      ? `${formData.container.containerNo}-${formData.sequenceNo}` 
                      : (formData.internalId || 'Auto-generated from container number and sequence')}
                  </Typography>
                </Box>
                
                {selectedManifest && (
                  <FormControl fullWidth margin="normal">
                    <InputLabel id="status-label">Status</InputLabel>
                    <Select
                      labelId="status-label"
                      name="status"
                      value={formData.status}
                      onChange={handleStatusChange}
                      label="Status"
                      MenuProps={{
                        sx: {
                          zIndex: 10001,
                          '& .MuiPaper-root': {
                            zIndex: 10001
                          }
                        }
                      }}
                      required
                      sx={{
                        borderRadius: 1,
                        '&:hover .MuiOutlinedInput-notchedOutline': {
                          borderColor: theme.palette.primary.main,
                        },
                      }}
                    >
                      {Object.values(ManifestStatus).map((status) => (
                        <MenuItem key={status} value={status}>
                          {status.replace(/_/g, ' ')}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                )}
                
                
              </Box>

              {/* Client Information Section */}
              <Typography variant="h6" color="primary" sx={{ mb: 2, fontWeight: 'bold' }}>
                Client Information
              </Typography>
              <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', md: '1fr 1fr' }, gap: 2, mb: 3 }}>
                {selectedManifest ? (
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5, mt: 2 }}>
                    <Typography variant="caption" color="text.secondary">
                      Client
                    </Typography>
                    <Typography variant="body1" sx={{ fontWeight: 'medium', display: 'flex', alignItems: 'center' }}>
                      <Box component="span" sx={{ 
                        color: theme.palette.grey[600], 
                        display: 'inline-flex',
                        alignItems: 'center',
                        '& svg': { mr: 1 } 
                      }}>
                        <PersonIcon fontSize="small" />
                      </Box>
                      {formData.client?.username ? 
                        (clients.find(c => c.username === formData.client.username)?.companyName || formData.client.username) : 
                        'No client selected'}
                    </Typography>
                  </Box>
                ) : (
                  <FormControl fullWidth margin="normal">
                    <InputLabel id="client-label">Client</InputLabel>
                    <Select
                      labelId="client-label"
                      name="client"
                      value={formData.client.username}
                      onChange={handleClientChange}
                      label="Client"
                      MenuProps={{
                        sx: {
                          zIndex: 10001,
                          '& .MuiPaper-root': {
                            zIndex: 10001
                          }
                        }
                      }}
                      required
                      sx={{
                        borderRadius: 1,
                        '&:hover .MuiOutlinedInput-notchedOutline': {
                          borderColor: theme.palette.primary.main,
                        },
                      }}
                    >
                      {clients && clients.length > 0 ? (
                        clients.map((client) => (
                          <MenuItem key={client.username} value={client.username}>
                            {`${client.username} - ${client.companyName || 'No Company'}`}
                          </MenuItem>
                        ))
                      ) : (
                        <MenuItem disabled value="">
                          {currentUser?.role === 'MANAGER' ? 
                            'Manager access does not have client list permissions' : 
                            'No clients available'}
                        </MenuItem>
                      )}
                    </Select>
                  </FormControl>
                )}
                
                {selectedManifest ? (
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5, mt: 2 }}>
                    <Typography variant="caption" color="text.secondary">
                      Container
                    </Typography>
                    <Typography variant="body1" sx={{ fontWeight: 'medium', display: 'flex', alignItems: 'center' }}>
                      <Box component="span" sx={{ 
                        color: theme.palette.grey[600], 
                        display: 'inline-flex',
                        alignItems: 'center',
                        '& svg': { mr: 1 } 
                      }}>
                        <LocalShippingOutlinedIcon fontSize="small" />
                      </Box>
                      {formData.container?.containerNo || 'No container selected'}
                    </Typography>
                  </Box>
                ) : (
                  <FormControl fullWidth margin="normal">
                    <InputLabel id="container-label">Container</InputLabel>
                    <Select
                      labelId="container-label"
                      name="container"
                      value={formData.container.containerNo}
                      onChange={handleContainerChange}
                      label="Container"
                      MenuProps={{
                        sx: {
                          zIndex: 10001,
                          '& .MuiPaper-root': {
                            zIndex: 10001
                          }
                        }
                      }}
                      required
                      sx={{
                        borderRadius: 1,
                        '&:hover .MuiOutlinedInput-notchedOutline': {
                          borderColor: theme.palette.primary.main,
                        },
                      }}
                    >
                      <MenuItem disabled value="">
                        <em>Select a container</em>
                      </MenuItem>
                      {formData.client?.username ? (
                        getFilteredContainers().length > 0 ? (
                          getFilteredContainers().map((container) => (
                            <MenuItem key={container.containerNo} value={container.containerNo}>
                              {`${container.containerNo} - ${container.client?.companyName || 'No Client'}`}
                            </MenuItem>
                          ))
                        ) : (
                          <MenuItem disabled value="">
                            No containers available for this client
                          </MenuItem>
                        )
                      ) : (
                        <MenuItem disabled value="">
                          Select a client first
                        </MenuItem>
                      )}
                    </Select>
                  </FormControl>
                )}
              </Box>
              
              {/* Package Details Section */}
              <Typography variant="h6" color="primary" sx={{ mb: 2, fontWeight: 'bold' }}>
                Package Details
              </Typography>
              <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', md: '1fr 1fr' }, gap: 2, mb: 3 }}>
                <TextField
                  required
                  name="pieces"
                  label="Pieces"
                  type="number"
                  value={formData.pieces || ''}
                  onChange={handleNumberChange}
                  fullWidth
                  margin="normal"
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 1,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: theme.palette.primary.main,
                      },
                    },
                  }}
                />
                <TextField
                  required
                  name="cbm"
                  label="CBM"
                  type="number"
                  value={formData.cbm || ''}
                  onChange={handleNumberChange}
                  fullWidth
                  margin="normal"
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 1,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: theme.palette.primary.main,
                      },
                    },
                  }}
                />
                <Box sx={{ mt: 2, mb: 2 }}>
                  <Typography variant="body2" color="text.secondary">Number of Pallets</Typography>
                  <Typography variant="body1" sx={{ fontWeight: 500, mt: 1 }}>
                    {selectedManifest ? (selectedManifest.actualPalletsCount ?? 0) : 0}
                    <Typography variant="caption" color="text.secondary" display="block" sx={{ mt: 0.5 }}>
                      The number of pallets is automatically calculated based on created pallets
                    </Typography>
                  </Typography>
                </Box>
                <FormControl 
                  fullWidth 
                  margin="normal"
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 1,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: theme.palette.primary.main,
                      },
                    },
                  }}
                >
                  <InputLabel id="location-label">Location</InputLabel>
                  <Select
                    labelId="location-label"
                    id="location"
                    name="location"
                    value={formData.location || ''}
                    onChange={handleLocationChange}
                    label="Location"
                    startAdornment={
                      <InputAdornment position="start">
                        <LocationOnIcon color="action" />
                      </InputAdornment>
                    }
                  >
                    <MenuItem value="">
                      <em>None</em>
                    </MenuItem>
                    {locationZones.map((zone) => (
                      <MenuItem key={zone.id} value={zone.name}>
                        {zone.name}
                      </MenuItem>
                    ))}
                  </Select>
                  <FormHelperText>Select a location zone</FormHelperText>
                </FormControl>
                <FormControl fullWidth margin="normal">
                  <InputLabel id="driver-label">Driver</InputLabel>
                  <Select
                    labelId="driver-label"
                    name="driver"
                    value={formData.driver.username}
                    onChange={handleDriverChange}
                    label="Driver"
                    MenuProps={{
                      sx: {
                        zIndex: 10001,
                        '& .MuiPaper-root': {
                          zIndex: 10001
                        }
                      }
                    }}
                    sx={{
                      borderRadius: 1,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: theme.palette.primary.main,
                      },
                    }}
                  >
                    <MenuItem value="">
                      <em>Not Assigned</em>
                    </MenuItem>
                    {drivers && drivers.length > 0 ? (
                      drivers.map((driver) => (
                        <MenuItem key={driver.username} value={driver.username}>
                          {`${driver.username} - ${driver.firstName || ''} ${driver.lastName || ''}`.trim()}
                        </MenuItem>
                      ))
                    ) : (
                      <MenuItem disabled value="">
                        {currentUser?.role === 'MANAGER' ? 
                          'Manager access does not have driver list permissions' : 
                          'No drivers available'}
                      </MenuItem>
                    )}
                  </Select>
                  <FormHelperText>Driver selection is optional</FormHelperText>
                </FormControl>
                
                
                <FormControl fullWidth margin="normal">
                  <InputLabel id="delivery-vehicle-label">Delivery Vehicle</InputLabel>
                  <Select
                    labelId="delivery-vehicle-label"
                    id="deliveryVehicle"
                    name="deliveryVehicle"
                    value={vehicleTypes.some(vt => vt.name === formData.deliveryVehicle) ? formData.deliveryVehicle : ''}
                    onChange={(e) => handleInputChange({
                      target: {
                        name: 'deliveryVehicle',
                        value: e.target.value
                      }
                    } as React.ChangeEvent<HTMLInputElement>)}
                    label="Delivery Vehicle"
                    startAdornment={
                      <InputAdornment position="start">
                        <LocalShippingOutlinedIcon color="action" />
                      </InputAdornment>
                    }
                    sx={{
                      borderRadius: 1,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: theme.palette.primary.main,
                      },
                    }}
                  >
                    <MenuItem value="">
                      <em>Auto-assign</em>
                    </MenuItem>
                    {vehicleTypes.map((vehicleType) => (
                      <MenuItem key={vehicleType.id} value={vehicleType.name}>
                        {vehicleType.name}
                      </MenuItem>
                    ))}
                  </Select>
                  <FormHelperText sx={{ display: 'flex', alignItems: 'center' }}>
                    Will be auto-assigned based on weight, CBM, and pieces if left empty
                    <Tooltip title="The system will automatically determine the appropriate vehicle type based on the manifest's weight, CBM, and number of pieces. You can manually select a different vehicle type if needed." arrow>
                      <IconButton size="small" sx={{ ml: 0.5, p: 0 }}>
                        <InfoIcon fontSize="small" color="action" />
                      </IconButton>
                    </Tooltip>
                  </FormHelperText>
                </FormControl>
                <TextField
                  name="weight"
                  label="Weight (kg)"
                  type="number"
                  value={formData.weight || ''}
                  onChange={handleNumberChange}
                  fullWidth
                  margin="normal"
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 1,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: theme.palette.primary.main,
                      },
                    },
                  }}
                />
                {selectedManifest && formData.createdDate && (
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5, mt: 2 }}>
                    <Typography variant="caption" color="text.secondary">
                      Created Date
                    </Typography>
                    <Typography variant="body1" sx={{ fontWeight: 'medium', display: 'flex', alignItems: 'center' }}>
                      <Box component="span" sx={{ 
                        color: theme.palette.grey[600], 
                        display: 'inline-flex',
                        alignItems: 'center',
                        '& svg': { mr: 1 } 
                      }}>
                        <CalendarTodayIcon fontSize="small" />
                      </Box>
                      {formatDateTime(formData.createdDate)}
                    </Typography>
                  </Box>
                )}
                <Box position="relative" zIndex={1300} width="100%">
                  <SplitDeliveryDatePicker
                    label="Delivery Date & Time Slot"
                    value={formData.deliveryDate}
                    onChange={(newValue) => handleDateChange('deliveryDate', newValue)}
                    fullWidth
                    slotProps={{
                      datePicker: {
                      popper: {
                          sx: { zIndex: 9999 }
                        }
                      } 
                    }}
                  />
                </Box>
                <Box position="relative" zIndex={1300} width="100%">
                  {formData.status === ManifestStatus.DELIVERED ? (
                    <LocalizationProvider dateAdapter={AdapterDateFns}>
                    <DateTimePicker
                      label="Delivered Date"
                      value={formData.deliveredDate}
                      onChange={(newValue) => handleDateChange('deliveredDate', newValue)}
                      format="dd MMM yyyy HH:mm"
                      ampm={false}
                      slotProps={{
                        textField: { 
                          fullWidth: true, 
                          margin: "normal",
                          InputProps: {
                            startAdornment: (
                              <InputAdornment position="start">
                                <EventAvailableIcon color="action" />
                              </InputAdornment>
                            ),
                          },
                          sx: {
                            '& .MuiOutlinedInput-root': {
                              borderRadius: 1,
                              '&:hover .MuiOutlinedInput-notchedOutline': {
                                borderColor: theme.palette.primary.main,
                              },
                            },
                          } 
                        },
                        popper: {
                          sx: {
                            zIndex: 9999
                          }
                        }
                      }}
                    />
                    </LocalizationProvider>
                  ) : (
                    <TextField
                      label="Delivered Date"
                      value="Not Delivered"
                      fullWidth
                      margin="normal"
                      disabled
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <EventAvailableIcon color="action" />
                          </InputAdornment>
                        ),
                        readOnly: true
                      }}
                      helperText="Manifest must be in DELIVERED status to set delivered date"
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          borderRadius: 1,
                          backgroundColor: 'rgba(0, 0, 0, 0.04)',
                        },
                      }}
                    />
                  )}
                </Box>
              </Box>
              
              {/* Shipping Information Section */}
              <Typography variant="h6" color="primary" sx={{ mb: 2, fontWeight: 'bold' }}>
                Shipping Information
              </Typography>
              <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', md: '1fr 1fr' }, gap: 2, mb: 3 }}>
                <TextField
                  required
                  name="customerName"
                  label="Customer Name"
                  value={formData.customerName}
                  onChange={handleInputChange}
                  fullWidth
                  margin="normal"
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <PersonIcon color="action" />
                      </InputAdornment>
                    ),
                  }}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 1,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: theme.palette.primary.main,
                      },
                    },
                  }}
                />
                <TextField
                  required
                  name="phoneNo"
                  label="Phone Number"
                  value={formData.phoneNo}
                  onChange={handleInputChange}
                  fullWidth
                  margin="normal"
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <PhoneIcon color="action" />
                      </InputAdornment>
                    ),
                  }}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 1,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: theme.palette.primary.main,
                      },
                    },
                  }}
                />
                <TextField
                  required
                  name="address"
                  label="Address"
                  value={formData.address}
                  onChange={handleInputChange}
                  fullWidth
                  margin="normal"
                  sx={{ 
                    gridColumn: '1 / -1',
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 1,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: theme.palette.primary.main,
                      },
                    },
                  }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <HomeIcon color="action" />
                      </InputAdornment>
                    ),
                  }}
                />
                <TextField
                  required
                  name="postalCode"
                  label="Postal Code"
                  value={formData.postalCode}
                  onChange={handleInputChange}
                  fullWidth
                  margin="normal"
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 1,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: theme.palette.primary.main,
                      },
                    },
                  }}
                />
                <TextField
                  required
                  name="country"
                  label="Country"
                  value={formData.country}
                  onChange={handleInputChange}
                  fullWidth
                  margin="normal"
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <LocationOnIcon color="action" />
                      </InputAdornment>
                    ),
                  }}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 1,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: theme.palette.primary.main,
                      },
                    },
                  }}
                />
              </Box>
              
              {/* Remarks (CS) Section */}
              <Typography variant="h6" color="primary" sx={{ mb: 2, fontWeight: 'bold' }}>
                Remarks (CS)
              </Typography>
              <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr' }, gap: 2 }}>
                <TextField
                  name="remarks"
                  label="Remarks (CS)"
                  value={formData.remarks || ''}
                  onChange={handleInputChange}
                  fullWidth
                  multiline
                  rows={3}
                  margin="normal"
                  sx={{ 
                    gridColumn: '1 / -1',
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 1,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: theme.palette.primary.main,
                      },
                    },
                  }}
                />
              </Box>
            </form>
          </DialogContent>
          <DialogActions sx={{ px: 3, py: 2 }}>
            <Button onClick={handleCloseDialog} variant="outlined">Cancel</Button>
            <Button onClick={handleSaveManifest} variant="contained" color="primary">
              {selectedManifest ? 'Update' : 'Create'}
            </Button>
          </DialogActions>
        </Dialog>

        {/* Confirmation Dialog */}
        <ConfirmDialog
          open={confirmDialogOpen}
          title={confirmTitle}
          message={confirmMessage}
          onConfirm={confirmAction}
          onCancel={() => setConfirmDialogOpen(false)}
        />

        {/* Success Snackbar */}
        <Snackbar
          open={!!successMessage}
          autoHideDuration={6000}
          onClose={handleSuccessClose}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
          sx={{ 
            '& .MuiAlert-root': {
              width: '100%',
              borderRadius: 2,
              boxShadow: 2,
            }
          }}
        >
          <Alert 
            onClose={handleSuccessClose} 
            severity="success" 
            elevation={6}
            variant="filled"
            sx={{ width: '100%' }}
          >
            {successMessage}
          </Alert>
        </Snackbar>

        {/* Label Dialog */}
        <ManifestLabelDialog
          open={labelDialogOpen}
          onClose={handleCloseLabelDialog}
          manifest={selectedLabelManifest}
          manifests={selectedManifests.length > 1 ? selectedManifests : []}
          initialLabelCount={selectedLabelManifest ? (selectedLabelManifest.actualPalletsCount && selectedLabelManifest.actualPalletsCount > 0 ? selectedLabelManifest.actualPalletsCount : 1) : undefined}
          onSaveLabelCount={handleSaveLabelCount}
          batchMode={selectedManifests.length > 1}
          getLabelCount={(trackingNo) => {
            const manifest = manifests.find(m => m.trackingNo === trackingNo);
            if (!manifest) return 1;
            return manifest.actualPalletsCount && manifest.actualPalletsCount > 0 ? manifest.actualPalletsCount : 1;
          }}
        />

        {/* Pallet Management Dialog */}
        <PalletManagementDialog
          open={palletManagementDialogOpen}
          onClose={handleClosePalletManagementDialog}
          manifest={selectedPalletManifest}
          onPalletOperationComplete={handlePalletOperationComplete}
        />

        {/* Bulk Upload Dialog */}
        <Dialog
          open={bulkUploadDialogOpen}
          onClose={() => {
            if (!isUploading) {
              setBulkUploadDialogOpen(false);
              handleResetFileUpload();
            }
          }}
          maxWidth="md"
          fullWidth
          keepMounted={false}
          disableRestoreFocus={false}
          PaperProps={{
            sx: {
              borderRadius: 2,
              boxShadow: 24,
              maxHeight: '90vh',
              overflowY: 'auto'
            },
          }}
        >
          <DialogTitle sx={{ 
            bgcolor: theme.palette.primary.main, 
            color: 'white',
            px: 3,
            py: 2,
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center'
          }}>
            <Box display="flex" alignItems="center">
              <CloudUploadIcon sx={{ mr: 2 }} />
              Bulk Import Manifests
            </Box>
            <IconButton
              aria-label="close"
              onClick={() => {
                if (!isUploading) {
                  setBulkUploadDialogOpen(false);
                  handleResetFileUpload();
                }
              }}
              sx={{
                color: 'white',
              }}
              disabled={isUploading}
            >
              <CloseIcon />
            </IconButton>
          </DialogTitle>
          <DialogContent sx={{ px: 3, py: 3 }}>
            <Box sx={{ mb: 3 }}>
              <Typography variant="h6" gutterBottom>Upload Excel File</Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                Upload an Excel file containing manifest details. Download the template below to ensure your data is properly formatted.
              </Typography>
              <Button
                variant="outlined"
                color="primary"
                startIcon={<FileDownloadIcon />}
                onClick={handleDownloadTemplate}
                sx={{ borderRadius: 2, mr: 2 }}
              >
                Download Template
              </Button>
            </Box>
            
            {!uploadedFile && !isUploading && !bulkUploadResults && (
              <UploadArea
                onDrop={handleFileDrop}
                onDragOver={handleDragOver}
                onClick={() => fileInputRef.current?.click()}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    fileInputRef.current?.click();
                  }
                }}
                role="button"
                tabIndex={0}
                aria-label="Upload Excel file"
                sx={{ 
                  height: 200,
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center',
                  alignItems: 'center'
                }}
              >
                <input
                  type="file"
                  ref={fileInputRef}
                  onChange={handleFileChange}
                  style={{ display: 'none' }}
                  accept=".xlsx,.xls"
                />
                <UploadFileIcon sx={{ fontSize: 48, color: theme.palette.primary.main, mb: 2 }} />
                <Typography variant="body1" gutterBottom>
                  Drag and drop an Excel file here, or click to browse
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  Supports .xlsx and .xls files up to 5MB
                </Typography>
              </UploadArea>
            )}
            
            {uploadedFile && !isUploading && !bulkUploadResults && (
              <Box>
                <Paper
                  elevation={0}
                  sx={{
                    p: 2,
                    border: '1px solid',
                    borderColor: 'divider',
                    borderRadius: 2,
                    bgcolor: alpha(theme.palette.primary.main, 0.04),
                    display: 'flex',
                    alignItems: 'center',
                    mb: 2
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1 }}>
                    <UploadFileIcon sx={{ color: theme.palette.primary.main, mr: 2 }} />
                    <Box>
                      <Typography variant="body1">{uploadedFile.name}</Typography>
                      <Typography variant="caption" color="text.secondary">
                        {(uploadedFile.size / 1024).toFixed(2)} KB
                      </Typography>
                    </Box>
                  </Box>
                  <IconButton 
                    onClick={handleResetFileUpload}
                    color="error"
                  >
                    <CloseIcon />
                  </IconButton>
                </Paper>
                
                <Button
                  variant="contained"
                  color="primary"
                  startIcon={<CloudUploadIcon />}
                  onClick={handleFileUpload}
                  fullWidth
                  sx={{ 
                    mt: 2,
                    py: 1.5,
                    borderRadius: 2
                  }}
                >
                  Upload and Process
                </Button>
              </Box>
            )}
            
            {isUploading && (
              <Box sx={{ textAlign: 'center', py: 4 }}>
                <CircularProgress size={60} sx={{ mb: 2 }} />
                <Typography variant="h6">Processing Upload</Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                  Please wait while your file is being processed...
                </Typography>
                <Box sx={{ width: '100%', mt: 3 }}>
                  <LinearProgress 
                    variant="determinate" 
                    value={uploadProgress} 
                    sx={{ height: 10, borderRadius: 5 }}
                  />
                  <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                    {uploadProgress}% Complete
                  </Typography>
                </Box>
              </Box>
            )}
            
            {bulkUploadResults && (
              <Box sx={{ mt: 2 }}>
                <Paper
                  elevation={0}
                  sx={{
                    p: 3,
                    borderRadius: 2,
                    bgcolor: theme.palette.background.paper,
                    border: '1px solid',
                    borderColor: 'divider',
                    mb: 3
                  }}
                >
                  <Typography variant="h6" gutterBottom>Upload Results</Typography>
                  
                  <Grid spacing={2} sx={{ mb: 3, display: 'flex' }}>
                    <Grid sx={{ width: '33.33%' }}>
                      <Paper
                        elevation={0}
                        sx={{ 
                          p: 2, 
                          textAlign: 'center',
                          borderRadius: 2,
                          bgcolor: alpha(theme.palette.info.main, 0.1)
                        }}
                      >
                        <Typography variant="h4" color="info.main">
                          {bulkUploadResults.total}
                        </Typography>
                        <Typography variant="body2">Total Manifests</Typography>
                      </Paper>
                    </Grid>
                    <Grid sx={{ width: '33.33%' }}>
                      <Paper
                        elevation={0}
                        sx={{ 
                          p: 2, 
                          textAlign: 'center',
                          borderRadius: 2,
                          bgcolor: alpha(theme.palette.success.main, 0.1)
                        }}
                      >
                        <Typography variant="h4" color="success.main">
                          {bulkUploadResults.successful}
                        </Typography>
                        <Typography variant="body2">Successfully Created</Typography>
                      </Paper>
                    </Grid>
                    <Grid sx={{ width: '33.33%' }}>
                      <Paper
                        elevation={0}
                        sx={{ 
                          p: 2, 
                          textAlign: 'center',
                          borderRadius: 2,
                          bgcolor: alpha(theme.palette.error.main, 0.1)
                        }}
                      >
                        <Typography variant="h4" color="error.main">
                          {bulkUploadResults.failed}
                        </Typography>
                        <Typography variant="body2">Failed</Typography>
                      </Paper>
                    </Grid>
                  </Grid>
                  
                  {bulkUploadResults.errors.length > 0 && (
                    <Box>
                      <Typography variant="subtitle1" gutterBottom>
                        Errors ({bulkUploadResults.errors.length})
                      </Typography>
                      <Paper
                        variant="outlined"
                        sx={{ 
                          maxHeight: 200, 
                          overflow: 'auto',
                          '&::-webkit-scrollbar': {
                            width: '8px',
                          },
                          '&::-webkit-scrollbar-thumb': {
                            backgroundColor: 'rgba(0,0,0,0.2)',
                            borderRadius: '4px',
                          }
                        }}
                      >
                        <Table size="small">
                          <TableHead>
                            <TableRow>
                              <TableCell>Row</TableCell>
                              <TableCell>Error</TableCell>
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            {bulkUploadResults.errors.map((error, index) => (
                              <TableRow key={index}>
                                <TableCell>{error.row}</TableCell>
                                <TableCell>{error.error}</TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </Paper>
                    </Box>
                  )}
                </Paper>
                
                <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
                  <Button
                    variant="outlined"
                    onClick={handleResetFileUpload}
                  >
                    Upload Another File
                  </Button>
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={() => {
                      setBulkUploadDialogOpen(false);
                      handleResetFileUpload();
                    }}
                  >
                    Done
                  </Button>
                </Box>
              </Box>
            )}
          </DialogContent>
        </Dialog>
      </LocalizationProvider>

      {/* Delivery Date Edit Dialog */}
      <DeliveryDateDialog
        open={deliveryDateDialogOpen}
        onClose={() => {
          setDeliveryDateDialogOpen(false);
          setEditingDeliveryDate(null);
          setEditingInternalId(null);
          setTempDeliveryDate(null);
          setTempTimeSlot(null);
        }}
        onSave={handleQuickEditDeliveryDate}
        trackingNo={editingDeliveryDate || ''}
        internalId={editingInternalId || undefined}
        deliveryDate={tempDeliveryDate}
        timeSlot={tempTimeSlot}
        onDateChange={(newValue) => setTempDeliveryDate(newValue)}
        onTimeSlotChange={(newValue) => setTempTimeSlot(newValue)}
        isEdit={true}
      />

      {/* Edit Value Dialog */}
      <Dialog
        open={editValueDialogOpen}
        onClose={handleCloseEditValueDialog}
        maxWidth="xs"
        fullWidth
        PaperProps={{
          elevation: 3,
          sx: { borderRadius: 2, p: 1 }
        }}
      >
        <DialogTitle sx={{ 
          borderBottom: '1px solid', 
          borderColor: 'divider',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        }}>
          <Box display="flex" alignItems="center">
            <EditIcon sx={{ mr: 1, color: theme.palette.primary.main }} />
            {editValueField === 'cbm' ? 'Edit CBM' : 'Edit Number of Pallets'}
          </Box>
          <IconButton
            size="small"
            onClick={handleCloseEditValueDialog}
            sx={{ color: 'text.secondary' }}
          >
            <CloseIcon fontSize="small" />
          </IconButton>
        </DialogTitle>
        <DialogContent sx={{ pt: 3, pb: 2 }}>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            {editValueField === 'cbm' ? 'Edit the CBM value for this manifest:' : 'Edit the number of pallets for this manifest:'}
          </Typography>
          <TextField
            label={editValueField === 'cbm' ? 'CBM' : 'Number of Pallets'}
            type="number"
            value={editValueTemp ?? ''}
            onChange={e => setEditValueTemp(Number(e.target.value))}
            fullWidth
            autoFocus
            sx={{ my: 1 }}
            onKeyDown={e => {
              if (e.key === 'Enter') handleSaveEditValueDialog();
              if (e.key === 'Escape') handleCloseEditValueDialog();
            }}
          />
          <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 1 }}>
            Enter a new value and press Enter or click Update to save.
          </Typography>
        </DialogContent>
        <DialogActions sx={{ px: 3, py: 2, borderTop: '1px solid', borderColor: 'divider' }}>
          <Button onClick={handleCloseEditValueDialog} variant="outlined">Cancel</Button>
          <Button onClick={handleSaveEditValueDialog} variant="contained" color="primary" disabled={editValueTemp === null}>
            Update
          </Button>
        </DialogActions>
      </Dialog>

      {/* Quick Delivery Vehicle Dialog */}
      <QuickDeliveryVehicleDialog
        open={quickVehicleDialogOpen}
        onClose={handleCloseQuickVehicleEdit}
        onSave={handleSaveQuickVehicleEdit}
        trackingNo={selectedManifestForVehicleEdit?.trackingNo || ''}
        currentDeliveryVehicle={selectedManifestForVehicleEdit?.deliveryVehicle || null}
        manifestWeight={selectedManifestForVehicleEdit?.weight}
        manifestCbm={selectedManifestForVehicleEdit?.cbm}
      />

      {/* Bulk Delivery Date Dialog */}
      <DeliveryDateDialog
        open={bulkDeliveryDateDialogOpen}
        onClose={() => {
          setBulkDeliveryDateDialogOpen(false);
          setBulkDeliveryDate(null);
          setBulkTimeSlot(null);
        }}
        onSave={handleBulkDeliveryDateUpdate}
        deliveryDate={bulkDeliveryDate}
        timeSlot={bulkTimeSlot}
        onDateChange={(date) => setBulkDeliveryDate(date)}
        onTimeSlotChange={(timeSlot) => setBulkTimeSlot(timeSlot)}
        isEdit={false}
      />
    </Box>
  );
}

export default ManifestManagement; 