import { useEffect } from 'react';
import { useToast } from '../contexts/ToastContext';
import { SESSION_EXPIRED_EVENT } from '../contexts/AuthContext';

/**
 * Component that listens for session expiration events and shows toast notifications.
 * This component doesn't render anything visible.
 */
const SessionExpirationHandler = () => {
  const toast = useToast();

  useEffect(() => {
    const handleSessionExpired = (event: Event) => {
      const customEvent = event as CustomEvent;
      const message = customEvent.detail?.message || 'Your session has expired. Please log in again.';
      toast.warning(message);
    };

    // Add event listener for session expiration
    window.addEventListener(SESSION_EXPIRED_EVENT, handleSessionExpired);

    // Clean up the event listener when component unmounts
    return () => {
      window.removeEventListener(SESSION_EXPIRED_EVENT, handleSessionExpired);
    };
  }, [toast]);

  // This component doesn't render anything
  return null;
};

export default SessionExpirationHandler; 