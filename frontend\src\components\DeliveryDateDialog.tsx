import React, { useEffect, useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  IconButton,
  useTheme,
} from '@mui/material';
import EventAvailableIcon from '@mui/icons-material/EventAvailable';
import CloseIcon from '@mui/icons-material/Close';
import SplitDeliveryDatePicker from './SplitDeliveryDatePicker';

interface DeliveryDateDialogProps {
  open: boolean;
  onClose: () => void;
  onSave: (date: Date | null, timeSlot: string | null) => void;
  trackingNo?: string;
  internalId?: string;
  deliveryDate: Date | null;
  timeSlot: string | null;
  onDateChange: (date: Date | null) => void;
  onTimeSlotChange: (timeSlot: string | null) => void;
  isEdit: boolean;
}

const DeliveryDateDialog: React.FC<DeliveryDateDialogProps> = ({
  open,
  onClose,
  onSave,
  trackingNo,
  internalId,
  deliveryDate,
  timeSlot,
  onDateChange,
  onTimeSlotChange,
  isEdit
}) => {
  const theme = useTheme();
  const [localDate, setLocalDate] = useState<Date | null>(deliveryDate);
  const [localTimeSlot, setLocalTimeSlot] = useState<string | null>(timeSlot);
  
  // Update local state when props change
  useEffect(() => {
    setLocalDate(deliveryDate);
    setLocalTimeSlot(timeSlot);
  }, [deliveryDate, timeSlot]);
  
  // Handle date change
  const handleDateChange = (newValue: Date | null) => {
    setLocalDate(newValue);
    onDateChange(newValue);
  };

  // Handle time slot change
  const handleTimeSlotChange = (newTimeSlot: string | null) => {
    setLocalTimeSlot(newTimeSlot);
    onTimeSlotChange(newTimeSlot);
  };

  const handleSave = () => {
    onSave(localDate, localTimeSlot);
  };

  const handleClear = () => {
    setLocalDate(null);
    setLocalTimeSlot(null);
    onDateChange(null);
    onTimeSlotChange(null);
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      scroll="paper" // Change to "paper" to work with custom backdrop scrolling
      PaperProps={{
        sx: {
          borderRadius: 2,
          boxShadow: 24,
          overflow: 'visible', // Allow content to overflow
          zIndex: 1400,
          position: 'relative', // Add position relative
          maxHeight: 'none' // Remove height restriction
        },
      }}
      sx={{
        '& .MuiBackdrop-root': {
          zIndex: 1300,
          backgroundColor: 'rgba(0, 0, 0, 0.5)', // Explicitly set backdrop color
          position: 'fixed', // Fix position to viewport
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          overflowY: 'auto' // Allow vertical scrolling
        },
        '& .MuiDialog-container': {
          overflow: 'auto', // Keep container scrollable
          alignItems: 'flex-start', // Align to top
          paddingTop: '10vh', // Add padding at top
          paddingBottom: '10vh', // Add padding at bottom
          minHeight: '100%' // Ensure container takes full height
        },
        '& .MuiDialog-paper': {
          overflow: 'visible', // Allow paper to overflow
          margin: '0 auto' // Center horizontally
        }
      }}
    >
      <DialogTitle sx={{ 
        bgcolor: theme.palette.primary.main, 
        color: 'white',
        px: 3,
        py: 2,
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <EventAvailableIcon sx={{ mr: 1 }} />
          <Typography variant="h6">
            {trackingNo ? 
              (localDate ? 'Edit Delivery Date' : 'Set Delivery Date') :
              'Update Delivery Dates'
          }
          </Typography>
        </Box>
        <IconButton onClick={onClose} size="small" sx={{ color: 'white' }}>
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      
      <DialogContent sx={{
        pt: 3,
        pb: 2,
        mb: 1,
        mt: 1,
        overflow: 'visible' // Allow content to overflow
      }}>
        {/* Display Internal ID and Tracking Number for single manifest */}
        {trackingNo && (
          <Box sx={{
            
            display: 'flex',
            gap: 3,
            flexWrap: 'wrap'
          }}>
            {internalId && (
              <Typography variant="body2" color="text.primary">
                <strong>Internal ID:</strong> {internalId}
              </Typography>
            )}
            <Typography variant="body2" color="text.primary">
              <strong>Tracking No:</strong> {trackingNo}
            </Typography>
          </Box>
        )}

        <Typography variant="body2" color="text.secondary" sx={{ }}>
          {trackingNo ?
            (localDate ?
            'Edit the delivery date and time slot for this manifest:' :
              'Set a delivery date and time slot for this manifest:') :
            'Set a delivery date and time slot for all selected manifests:'
          }
        </Typography>

        <SplitDeliveryDatePicker
          label="Delivery Date & Time Slot"
          value={localDate}
          onChange={handleDateChange}
          timeSlotValue={localTimeSlot}
          onTimeSlotChange={handleTimeSlotChange}
          fullWidth
          helperText="The manifest status will be automatically updated based on the delivery date."
          slotProps={{
            datePicker: {
              popper: {
                sx: {
                  zIndex: 10000,
                  '& .MuiPaper-root': {
                    maxWidth: '260px',
                    maxHeight: '280px',
                    overflow: 'auto'
                  }
                },
                disablePortal: false,
                placement: 'bottom-start'
              }
            }
          }}
        />

        {!trackingNo && (
          <Typography variant="caption" color="info.main" sx={{ display: 'block', mt: 2 }}>
            Note: The selected time slot will be applied to all selected manifests.
          </Typography>
        )}
      </DialogContent>
      
      <DialogActions sx={{ px: 3, py: 2, borderTop: '1px solid', borderColor: 'divider' }}>
        <Button 
          onClick={handleClear}
          variant="outlined"
          color="secondary"
          disabled={!localDate}
        >
          Clear Date
        </Button>
        <Button 
          onClick={onClose}
          variant="outlined"
        >
          Cancel
        </Button>
        <Button 
          onClick={handleSave}
          variant="contained" 
          color="primary"
          startIcon={<EventAvailableIcon />}
        >
          {trackingNo ? 
            (localDate ? 'Update Delivery Date' : 'Remove Delivery Date') :
            'Update Delivery Dates'
          }
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default DeliveryDateDialog; 