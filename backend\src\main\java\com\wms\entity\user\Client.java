package com.wms.entity.user;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.DiscriminatorValue;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
@Entity
@Table(name = "clients")
@Data
@EqualsAndHashCode(callSuper = true)
@DiscriminatorValue("CLIENT")
public class Client extends User {

    @NotBlank
    @Column(name = "company_name")
    private String companyName;
    
    @NotBlank
    @Size(max = 50)
    @Column(name = "designation")
    private String designation;

    @Size(max = 50)
    @Column(name = "alt_designation")
    private String altDesignation;

    @Size(max = 20)
    @Column(name = "alt_contact")
    private String altContact;
}