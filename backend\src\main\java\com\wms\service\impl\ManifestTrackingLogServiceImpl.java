package com.wms.service.impl;

import com.wms.entity.ManifestTrackingLog;
import com.wms.entity.Manifest;
import com.wms.entity.ManifestStatus;
import com.wms.repository.ManifestTrackingLogRepository;
import com.wms.service.ManifestTrackingLogService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.annotation.Propagation;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
@Transactional
public class ManifestTrackingLogServiceImpl implements ManifestTrackingLogService {
    
    private static final Logger logger = LoggerFactory.getLogger(ManifestTrackingLogServiceImpl.class);
    
    private final ManifestTrackingLogRepository trackingLogRepository;
    private static volatile long lastTimestampNanos = 0;
    private static volatile long sequenceCounter = 0;

    @Autowired
    public ManifestTrackingLogServiceImpl(ManifestTrackingLogRepository trackingLogRepository) {
        this.trackingLogRepository = trackingLogRepository;
    }

    /**
     * Generates a unique timestamp that ensures chronological ordering
     * Each call guarantees a timestamp that is at least 1 millisecond after the previous call
     */
    private synchronized LocalDateTime getUniqueTimestamp() {
        long currentTimeMillis = System.currentTimeMillis();
        long currentNanos = System.nanoTime();
        
        // Ensure we always get a timestamp that's at least 1 millisecond after the last one
        if (currentTimeMillis * 1_000_000 <= lastTimestampNanos) {
            // If current time is not later, increment by 1 millisecond
            lastTimestampNanos = lastTimestampNanos + 1_000_000;
        } else {
            lastTimestampNanos = currentTimeMillis * 1_000_000;
        }
        
        // Add sequence counter as nanoseconds to ensure uniqueness within the same millisecond
        sequenceCounter = (sequenceCounter + 1) % 1_000_000; // Reset every million operations
        long finalNanos = lastTimestampNanos + sequenceCounter;
        
        // Convert back to LocalDateTime
        long epochMillis = finalNanos / 1_000_000;
        int nanoOfSecond = (int)((finalNanos % 1_000_000_000));
        
        return LocalDateTime.ofInstant(
            java.time.Instant.ofEpochMilli(epochMillis),
            java.time.ZoneId.systemDefault()
        ).withNano(nanoOfSecond);
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public ManifestTrackingLog createTrackingLog(ManifestTrackingLog trackingLog) {
        if (trackingLog.getUpdatedAt() == null) {
            trackingLog.setUpdatedAt(getUniqueTimestamp());
        }
        
        ManifestTrackingLog savedLog = trackingLogRepository.save(trackingLog);
        logger.info("Created tracking log {} for manifest {}: {} -> {}", 
                   savedLog.getId(), trackingLog.getTrackingNo(), 
                   trackingLog.getPreviousStatus(), trackingLog.getNewStatus());
        return savedLog;
    }
    
    @Override
    public List<ManifestTrackingLog> getTrackingLogsByManifest(String trackingNo) {
        return trackingLogRepository.findByTrackingNoOrderByUpdatedAtDescIdDesc(trackingNo);
    }
    
    @Override
    public List<ManifestTrackingLog> getTrackingLogsByManifestAndDateRange(
            String trackingNo, LocalDateTime startDate, LocalDateTime endDate) {
        return trackingLogRepository.findByTrackingNoAndDateRange(trackingNo, startDate, endDate);
    }
    
    @Override
    public List<ManifestTrackingLog> getTrackingLogsByActionType(ManifestTrackingLog.ActionType actionType) {
        return trackingLogRepository.findByActionTypeOrderByUpdatedAtDescIdDesc(actionType);
    }
    
    @Override
    public List<ManifestTrackingLog> getTrackingLogsByUser(String username) {
        return trackingLogRepository.findByUpdatedByOrderByUpdatedAtDescIdDesc(username);
    }
    
    @Override
    public Optional<ManifestTrackingLog> getLatestTrackingLog(String trackingNo) {
        ManifestTrackingLog latestLog = trackingLogRepository.findLatestByTrackingNo(trackingNo);
        return Optional.ofNullable(latestLog);
    }
    
    @Override
    public List<ManifestTrackingLog> getStatusChangeHistory(String trackingNo) {
        return trackingLogRepository.findStatusChangesByTrackingNo(trackingNo);
    }
    
    @Override
    public ManifestTrackingLog logStatusChange(String trackingNo, ManifestStatus previousStatus, 
                                             ManifestStatus newStatus, String updatedBy, String remarks) {
        ManifestTrackingLog trackingLog = ManifestTrackingLog.builder()
                .trackingNo(trackingNo)
                .previousStatus(previousStatus != null ? previousStatus.toString() : null)
                .newStatus(newStatus.toString())
                .actionType(ManifestTrackingLog.ActionType.STATUS_CHANGED)
                .updatedBy(updatedBy)
                .updatedAt(getUniqueTimestamp())
                .remarks(remarks)
                .build();
        
        return createTrackingLog(trackingLog);
    }
    
    @Override
    public ManifestTrackingLog logFieldUpdate(String trackingNo, String fieldName, String oldValue, 
                                            String newValue, String updatedBy, String remarks) {
        ManifestTrackingLog trackingLog = ManifestTrackingLog.builder()
                .trackingNo(trackingNo)
                .fieldName(fieldName)
                .oldValue(oldValue)
                .newValue(newValue)
                .actionType(ManifestTrackingLog.ActionType.UPDATED)
                .updatedBy(updatedBy)
                .updatedAt(getUniqueTimestamp())
                .remarks(remarks)
                .build();
        
        return createTrackingLog(trackingLog);
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public ManifestTrackingLog logManifestCreation(Manifest manifest, String createdBy, String remarks) {
        ManifestTrackingLog trackingLog = ManifestTrackingLog.builder()
                .trackingNo(manifest.getTrackingNo())
                .newStatus(manifest.getStatus().toString())
                .actionType(ManifestTrackingLog.ActionType.CREATED)
                .updatedBy(createdBy)
                .updatedAt(getUniqueTimestamp())
                .remarks(remarks != null ? remarks : "Manifest created")
                .build();
        
        return createTrackingLog(trackingLog);
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public ManifestTrackingLog logManifestDeletion(String trackingNo, String deletedBy, String remarks) {
        ManifestTrackingLog trackingLog = ManifestTrackingLog.builder()
                .trackingNo(trackingNo)
                .actionType(ManifestTrackingLog.ActionType.DELETED)
                .updatedBy(deletedBy)
                .updatedAt(getUniqueTimestamp())
                .remarks(remarks != null ? remarks : "Manifest deleted")
                .build();
        
        return createTrackingLog(trackingLog);
    }
    
    @Override
    public List<ManifestTrackingLog> logManifestUpdate(Manifest oldManifest, Manifest newManifest, 
                                                     String updatedBy, String remarks) {
        List<ManifestTrackingLog> logs = new ArrayList<>();
        
        // Check for status change
        if (!oldManifest.getStatus().equals(newManifest.getStatus())) {
            logs.add(logStatusChange(newManifest.getTrackingNo(), 
                                   oldManifest.getStatus(), newManifest.getStatus(), 
                                   updatedBy, "Status updated: " + remarks));
        }
        
        // Check for other field changes
        if (!safeEquals(oldManifest.getCustomerName(), newManifest.getCustomerName())) {
            logs.add(logFieldUpdate(newManifest.getTrackingNo(), "customerName", 
                                  oldManifest.getCustomerName(), newManifest.getCustomerName(), 
                                  updatedBy, "Customer name updated"));
        }
        
        if (!safeEquals(oldManifest.getAddress(), newManifest.getAddress())) {
            logs.add(logFieldUpdate(newManifest.getTrackingNo(), "address", 
                                  oldManifest.getAddress(), newManifest.getAddress(), 
                                  updatedBy, "Address updated"));
        }
        
        if (!safeEquals(oldManifest.getPhoneNo(), newManifest.getPhoneNo())) {
            logs.add(logFieldUpdate(newManifest.getTrackingNo(), "phoneNo", 
                                  oldManifest.getPhoneNo(), newManifest.getPhoneNo(), 
                                  updatedBy, "Phone number updated"));
        }
        
        if (!safeEquals(oldManifest.getLocation(), newManifest.getLocation())) {
            logs.add(logFieldUpdate(newManifest.getTrackingNo(), "location", 
                                  oldManifest.getLocation(), newManifest.getLocation(), 
                                  updatedBy, "Location updated"));
        }
        
        if (!safeEquals(oldManifest.getDeliveryVehicle(), newManifest.getDeliveryVehicle())) {
            logs.add(logFieldUpdate(newManifest.getTrackingNo(), "deliveryVehicle", 
                                  oldManifest.getDeliveryVehicle(), newManifest.getDeliveryVehicle(), 
                                  updatedBy, "Delivery vehicle updated"));
        }
        
        if (!safeEquals(oldManifest.getDeliveryDate(), newManifest.getDeliveryDate())) {
            logs.add(logFieldUpdate(newManifest.getTrackingNo(), "deliveryDate", 
                                  oldManifest.getDeliveryDate() != null ? oldManifest.getDeliveryDate().toString() : null,
                                  newManifest.getDeliveryDate() != null ? newManifest.getDeliveryDate().toString() : null, 
                                  updatedBy, "Delivery date updated"));
        }
        
        if (!safeEquals(oldManifest.getDeliveredDate(), newManifest.getDeliveredDate())) {
            logs.add(logFieldUpdate(newManifest.getTrackingNo(), "deliveredDate", 
                                  oldManifest.getDeliveredDate() != null ? oldManifest.getDeliveredDate().toString() : null,
                                  newManifest.getDeliveredDate() != null ? newManifest.getDeliveredDate().toString() : null, 
                                  updatedBy, "Delivered date updated"));
        }
        
        if (!safeEquals(oldManifest.getDriver(), newManifest.getDriver())) {
            String oldDriver = oldManifest.getDriver() != null ? oldManifest.getDriver().getUsername() : null;
            String newDriver = newManifest.getDriver() != null ? newManifest.getDriver().getUsername() : null;
            logs.add(logFieldUpdate(newManifest.getTrackingNo(), "driver", 
                                  oldDriver, newDriver, updatedBy, "Driver assigned/updated"));
        }
        
        if (!safeEquals(oldManifest.getRemarks(), newManifest.getRemarks())) {
            logs.add(logFieldUpdate(newManifest.getTrackingNo(), "remarks", 
                                  oldManifest.getRemarks(), newManifest.getRemarks(), 
                                  updatedBy, "Remarks updated"));
        }
        
        // Check numeric fields
        if (!safeEquals(oldManifest.getPieces(), newManifest.getPieces())) {
            logs.add(logFieldUpdate(newManifest.getTrackingNo(), "pieces", 
                                  oldManifest.getPieces().toString(), newManifest.getPieces().toString(), 
                                  updatedBy, "Number of pieces updated"));
        }
        
        if (!safeEquals(oldManifest.getCbm(), newManifest.getCbm())) {
            logs.add(logFieldUpdate(newManifest.getTrackingNo(), "cbm", 
                                  oldManifest.getCbm().toString(), newManifest.getCbm().toString(), 
                                  updatedBy, "CBM updated"));
        }
        
        if (!safeEquals(oldManifest.getWeight(), newManifest.getWeight())) {
            logs.add(logFieldUpdate(newManifest.getTrackingNo(), "weight", 
                                  oldManifest.getWeight().toString(), newManifest.getWeight().toString(), 
                                  updatedBy, "Weight updated"));
        }
        
        // Note: Actual pallet count changes are logged separately by pallet operations
        // and should not be logged here to avoid duplicate/incorrect logging
        
        return logs;
    }
    
    @Override
    public Long countTrackingLogs(String trackingNo) {
        return trackingLogRepository.countByTrackingNo(trackingNo);
    }
    
    @Override
    public List<ManifestTrackingLog> getTrackingLogsInDateRange(LocalDateTime startDate, LocalDateTime endDate) {
        return trackingLogRepository.findByDateRangeOrderByUpdatedAtDesc(startDate, endDate);
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public ManifestTrackingLog logPalletAddition(String trackingNo, String palletId, String addedBy, String remarks) {
        logger.info("logPalletAddition called: trackingNo={}, palletId={}, addedBy={}, remarks={}", 
                   trackingNo, palletId, addedBy, remarks);
        
        ManifestTrackingLog trackingLog = ManifestTrackingLog.builder()
                .trackingNo(trackingNo)
                .fieldName("pallet")
                .newValue(palletId)
                .actionType(ManifestTrackingLog.ActionType.PALLET_ADDED)
                .updatedBy(addedBy)
                .updatedAt(getUniqueTimestamp())
                .remarks(remarks != null ? remarks : ("Pallet " + palletId + " added to manifest"))
                .build();
        
        logger.info("Built tracking log for pallet addition: {}", trackingLog);
        
        ManifestTrackingLog savedLog = createTrackingLog(trackingLog);
        logger.info("Successfully saved pallet addition tracking log: id={}, trackingNo={}, palletId={}", 
                   savedLog.getId(), trackingNo, palletId);
        
        return savedLog;
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public ManifestTrackingLog logPalletDeletion(String trackingNo, String palletId, String deletedBy, String remarks) {
        logger.info("logPalletDeletion called: trackingNo={}, palletId={}, deletedBy={}, remarks={}", 
                   trackingNo, palletId, deletedBy, remarks);
        
        ManifestTrackingLog trackingLog = ManifestTrackingLog.builder()
                .trackingNo(trackingNo)
                .fieldName("pallet")
                .oldValue(palletId)
                .actionType(ManifestTrackingLog.ActionType.PALLET_DELETED)
                .updatedBy(deletedBy)
                .updatedAt(getUniqueTimestamp())
                .remarks(remarks != null ? remarks : ("Pallet " + palletId + " removed from manifest"))
                .build();
        
        logger.info("Built tracking log for pallet deletion: {}", trackingLog);
        
        ManifestTrackingLog savedLog = createTrackingLog(trackingLog);
        logger.info("Successfully saved pallet deletion tracking log: id={}, trackingNo={}, palletId={}", 
                   savedLog.getId(), trackingNo, palletId);
        
        return savedLog;
    }
    
    @Override
    @Transactional
    public void deleteTrackingLog(Long logId) {
        logger.info("Deleting tracking log with ID: {}", logId);
        
        if (!trackingLogRepository.existsById(logId)) {
            throw new IllegalArgumentException("Tracking log not found with ID: " + logId);
        }
        
        trackingLogRepository.deleteById(logId);
        logger.info("Successfully deleted tracking log with ID: {}", logId);
    }
    
    @Override
    @Transactional
    public void deleteAllTrackingLogsForManifest(String trackingNo) {
        logger.info("Deleting all tracking logs for manifest: {}", trackingNo);
        
        List<ManifestTrackingLog> logs = trackingLogRepository.findByTrackingNoOrderByUpdatedAtDescIdDesc(trackingNo);
        if (logs.isEmpty()) {
            logger.warn("No tracking logs found for manifest: {}", trackingNo);
            return;
        }
        
        trackingLogRepository.deleteAll(logs);
        logger.info("Successfully deleted {} tracking logs for manifest: {}", logs.size(), trackingNo);
    }
    
    private boolean safeEquals(Object obj1, Object obj2) {
        if (obj1 == null && obj2 == null) return true;
        if (obj1 == null || obj2 == null) return false;
        return obj1.equals(obj2);
    }
} 