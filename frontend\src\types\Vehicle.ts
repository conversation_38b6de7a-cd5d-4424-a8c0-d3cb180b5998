export interface VehicleType {
  id: number | null;
  name: string;
  abbreviation?: string;
  description: string;
  minWeight?: number | null;
  maxWeight?: number | null;
  minCbm?: number | null;
  maxCbm?: number | null;
  minCbmPerPiece?: number | null;
  maxCbmPerPiece?: number | null;
  isActive?: boolean;
}

export enum VehicleStatus {
  AVAILABLE = 'AVAILABLE',
  IN_USE = 'IN_USE',
  MAINTENANCE = 'MAINTENANCE',
  OUT_OF_SERVICE = 'OUT_OF_SERVICE'
}

export interface Vehicle {
  id: number | null;
  licensePlate: string;
  vehicleTypeId: number;
  vehicleTypeName: string;
  status: VehicleStatus;
  assignedDriverUsernames: string[];
}

export interface Driver {
  username: string;
  fullName: string;
  email: string;
  contact: string;
  licenseNumber: string;
  active: boolean;
  assignedVehicles?: Vehicle[];
} 