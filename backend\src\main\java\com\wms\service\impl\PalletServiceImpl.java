package com.wms.service.impl;

import com.wms.entity.Manifest;
import com.wms.entity.ManifestStatus;
import com.wms.entity.ManifestTrackingLog;
import com.wms.entity.Pallet;
import com.wms.exception.ResourceNotFoundException;
import com.wms.repository.ManifestRepository;
import com.wms.repository.PalletRepository;
import com.wms.service.PalletService;
import com.wms.service.ManifestTrackingLogService;
import com.wms.service.ManifestStatusService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
public class PalletServiceImpl implements PalletService {

    private final PalletRepository palletRepository;
    private final ManifestRepository manifestRepository;
    private final ManifestTrackingLogService manifestTrackingLogService;
    private final ManifestStatusService manifestStatusService;
    private static final Logger logger = LoggerFactory.getLogger(PalletServiceImpl.class);

    @Autowired
    public PalletServiceImpl(PalletRepository palletRepository, ManifestRepository manifestRepository, 
                            ManifestTrackingLogService manifestTrackingLogService, ManifestStatusService manifestStatusService) {
        this.palletRepository = palletRepository;
        this.manifestRepository = manifestRepository;
        this.manifestTrackingLogService = manifestTrackingLogService;
        this.manifestStatusService = manifestStatusService;
        
        logger.info("PalletServiceImpl initialized with tracking service: {}", 
                   manifestTrackingLogService != null ? "SUCCESS" : "FAILED");
    }

    @Override
    public List<Pallet> getAllPallets() {
        return palletRepository.findAll();
    }

    @Override
    public Optional<Pallet> getPalletByPalletNo(String palletNo) {
        return palletRepository.findById(palletNo);
    }

    @Override
    public List<Pallet> getPalletsByManifestTrackingNo(String manifestTrackingNo) {
        return palletRepository.findByManifestTrackingNo(manifestTrackingNo);
    }

    @Override
    @Transactional
    public Pallet createPallet(Pallet pallet) {
        // Validate manifest exists
        if (pallet.getManifest() == null || pallet.getManifest().getTrackingNo() == null) {
            throw new IllegalArgumentException("Manifest is required for pallet creation");
        }
        
        // Get manifest with pallets collection to properly manage the relationship
        Manifest manifest = manifestRepository.findByIdWithPallets(pallet.getManifest().getTrackingNo())
                .orElseThrow(() -> new ResourceNotFoundException("Manifest not found with tracking number: " + pallet.getManifest().getTrackingNo()));
        
        // Set the manifest reference
        pallet.setManifest(manifest);
        
        // Auto-assign sequence number if not provided
        if (pallet.getPalletSequence() == null) {
            Integer maxSequence = palletRepository.findMaxPalletSequenceByManifestTrackingNo(manifest.getTrackingNo());
            pallet.setPalletSequence(maxSequence + 1);
        }
        
        // Generate pallet number
        pallet.generatePalletNo();
        
        logger.info("Creating pallet {} for manifest {}", pallet.getPalletNo(), manifest.getTrackingNo());
        
        // Get the current pallet count BEFORE adding the new pallet
        int oldPalletCount = manifest.getNoOfPalletsFromObjects();
        
        Pallet savedPallet = palletRepository.save(pallet);
        logger.info("Successfully saved pallet: {}", savedPallet.getPalletNo());
        
        // Add the pallet to the manifest's collection to maintain consistency
        manifest.addPallet(savedPallet);
        logger.info("Added pallet to manifest collection: {}", savedPallet.getPalletNo());
        
        // Save the manifest to persist the collection change
        manifestRepository.save(manifest);
        logger.info("Saved manifest after adding pallet to collection");
        
        // Force flush the creation to ensure it's committed before logging
        palletRepository.flush();
        logger.info("Flushed pallet creation to database: {}", savedPallet.getPalletNo());
        
        // Get updated pallet count for logging
        int newPalletCount = oldPalletCount + 1; // We know it increased by 1
        
        // Log pallet addition FIRST to preserve chronological order
        try {
            String currentUser = getCurrentUser();
            logger.info("Attempting to log pallet addition: pallet={}, manifest={}, user={}", 
                       savedPallet.getPalletNo(), manifest.getTrackingNo(), currentUser);
            
            ManifestTrackingLog trackingLog = manifestTrackingLogService.logPalletAddition(
                manifest.getTrackingNo(), 
                savedPallet.getPalletNo(), 
                currentUser, 
                String.format("Pallet %s added with %d pieces", savedPallet.getPalletNo(), savedPallet.getNoOfPieces())
            );
            
            logger.info("Successfully logged pallet addition: trackingLogId={}, pallet={}, manifest={}", 
                       trackingLog.getId(), savedPallet.getPalletNo(), manifest.getTrackingNo());
            
            // Log the actual pallet count change immediately after pallet logging
            manifestTrackingLogService.logFieldUpdate(
                manifest.getTrackingNo(),
                "actualPallets",
                String.valueOf(oldPalletCount),
                String.valueOf(newPalletCount),
                currentUser,
                "Actual pallet count updated due to pallet addition"
            );
            
        } catch (Exception e) {
            logger.error("Failed to log pallet addition to tracking history: pallet={}, manifest={}, error={}", 
                        savedPallet.getPalletNo(), manifest.getTrackingNo(), e.getMessage(), e);
        }
        
        // Check for discrepancy LAST to preserve chronological order
        // This will log any status changes AFTER the pallet operations are logged
        // Refresh the manifest to get the most current state
        Manifest refreshedManifest = manifestRepository.findById(manifest.getTrackingNo()).orElse(manifest);
        manifestStatusService.checkAndUpdateManifestStatus(refreshedManifest);
        
        return savedPallet;
    }

    @Override
    @Transactional
    public Pallet updatePallet(String palletNo, Pallet palletDetails) {
        Pallet pallet = palletRepository.findById(palletNo)
                .orElseThrow(() -> new ResourceNotFoundException("Pallet not found with number: " + palletNo));
        
        // Update pallet details
        pallet.setNoOfPieces(palletDetails.getNoOfPieces());
        pallet.setTotalPiecesReference(palletDetails.getTotalPiecesReference());
        
        logger.debug("Updating pallet {}", palletNo);
        
        Pallet updatedPallet = palletRepository.save(pallet);
        
        // Check for discrepancy after updating a pallet
        manifestStatusService.checkAndUpdateManifestStatus(updatedPallet.getManifest());
        
        return updatedPallet;
    }

    @Override
    @Transactional
    public void deletePallet(String palletNo) {
        logger.info("=== PALLET DELETE CALLED === palletNo: {}", palletNo);
        logger.info("Attempting to delete pallet: {}", palletNo);
        
        // Get pallet details before deletion for logging
        Pallet pallet = palletRepository.findById(palletNo)
                .orElseThrow(() -> new ResourceNotFoundException("Pallet not found with number: " + palletNo));
        
        String manifestTrackingNo = pallet.getManifest().getTrackingNo();
        int pieces = pallet.getNoOfPieces();
        String currentUser = getCurrentUser();
        
        // Get manifest with pallets collection to properly manage the relationship
        Manifest manifest = manifestRepository.findByIdWithPallets(manifestTrackingNo)
                .orElseThrow(() -> new ResourceNotFoundException("Manifest not found with tracking number: " + manifestTrackingNo));
        
        // Get the pallet count before deletion for logging
        int oldPalletCount = manifest.getNoOfPalletsFromObjects();
        
        logger.info("Found pallet to delete: palletNo={}, manifest={}, pieces={}, user={}", 
                   palletNo, manifestTrackingNo, pieces, currentUser);
        
        // IMPORTANT: Remove the pallet from the manifest's collection first
        // This prevents JPA orphanRemoval conflicts
        manifest.removePallet(pallet);
        logger.info("Removed pallet from manifest collection: {}", palletNo);
        
        // Save the manifest to persist the collection change
        manifestRepository.save(manifest);
        logger.info("Saved manifest after removing pallet from collection");
        
        // Now delete the pallet entity
        palletRepository.deleteById(palletNo);
        logger.info("Successfully deleted pallet from database: {}", palletNo);
        
        // Force flush the deletion to ensure it's committed before logging
        palletRepository.flush();
        logger.info("Flushed pallet deletion to database: {}", palletNo);
        
        // Get updated pallet count for logging
        int newPalletCount = oldPalletCount - 1; // We know it decreased by 1
        
        // Log pallet deletion FIRST to preserve chronological order
        try {
            logger.info("Attempting to log pallet deletion: pallet={}, manifest={}, user={}", 
                       palletNo, manifestTrackingNo, currentUser);
            
            ManifestTrackingLog trackingLog = manifestTrackingLogService.logPalletDeletion(
                manifestTrackingNo, 
                palletNo, 
                currentUser, 
                String.format("Pallet %s deleted (contained %d pieces)", palletNo, pieces)
            );
            
            logger.info("Successfully logged pallet deletion: trackingLogId={}, pallet={}, manifest={}", 
                       trackingLog.getId(), palletNo, manifestTrackingNo);
            
            // Log the actual pallet count change immediately after pallet logging
            manifestTrackingLogService.logFieldUpdate(
                manifestTrackingNo,
                "actualPallets",
                String.valueOf(oldPalletCount),
                String.valueOf(newPalletCount),
                currentUser,
                "Actual pallet count updated due to pallet deletion"
            );
            
        } catch (Exception e) {
            logger.error("Failed to log pallet deletion to tracking history: pallet={}, manifest={}, error={}", 
                        palletNo, manifestTrackingNo, e.getMessage(), e);
            // Don't rethrow - allow the pallet deletion to succeed even if logging fails
        }
        
        // Check for discrepancy LAST to preserve chronological order
        // This will log any status changes AFTER the pallet operations are logged
        // Refresh the manifest to get the most current state
        Manifest refreshedManifest = manifestRepository.findById(manifestTrackingNo).orElse(manifest);
        manifestStatusService.checkAndUpdateManifestStatus(refreshedManifest);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void deletePalletsByManifestTrackingNo(String manifestTrackingNo) {
        List<Pallet> pallets = palletRepository.findByManifestTrackingNo(manifestTrackingNo);
        if (pallets.isEmpty()) {
            logger.debug("No pallets found for manifest {}", manifestTrackingNo);
            return;
        }
        
        logger.info("Deleting {} pallets for manifest {}", pallets.size(), manifestTrackingNo);
        palletRepository.deleteByManifestTrackingNo(manifestTrackingNo);
        logger.info("Successfully deleted all pallets for manifest {}", manifestTrackingNo);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public List<Pallet> createPalletsForManifest(String manifestTrackingNo, List<Pallet> pallets) {
        Manifest manifest = manifestRepository.findById(manifestTrackingNo)
                .orElseThrow(() -> new ResourceNotFoundException("Manifest not found with tracking number: " + manifestTrackingNo));
        
        List<Pallet> createdPallets = new ArrayList<>();
        String currentUser = getCurrentUser();
        
        for (int i = 0; i < pallets.size(); i++) {
            Pallet pallet = pallets.get(i);
            pallet.setManifest(manifest);
            
            // Auto-assign sequence number
            Integer maxSequence = palletRepository.findMaxPalletSequenceByManifestTrackingNo(manifestTrackingNo);
            pallet.setPalletSequence(maxSequence + i + 1);
            
            // Generate pallet number
            pallet.generatePalletNo();
            
            Pallet savedPallet = palletRepository.save(pallet);
            createdPallets.add(savedPallet);
            
            // Log each pallet addition to tracking history IMMEDIATELY after saving
            try {
                manifestTrackingLogService.logPalletAddition(
                    manifestTrackingNo, 
                    savedPallet.getPalletNo(), 
                    currentUser, 
                    String.format("Pallet %s added with %d pieces (bulk creation)", savedPallet.getPalletNo(), savedPallet.getNoOfPieces())
                );
            } catch (Exception e) {
                logger.warn("Failed to log pallet addition: {}", e.getMessage());
                // Continue with the next pallet even if logging fails
            }
        }
        
        // Check for discrepancy after adding ALL pallets (this will log status changes if needed)
        manifestStatusService.checkAndUpdateManifestStatus(manifest);
        
        return createdPallets;
    }

    @Override
    public Long countPalletsByManifestTrackingNo(String manifestTrackingNo) {
        return palletRepository.countByManifestTrackingNo(manifestTrackingNo);
    }
    
    @Override
    public int getTotalPiecesInPallets(String manifestTrackingNo) {
        List<Pallet> pallets = palletRepository.findByManifestTrackingNo(manifestTrackingNo);
        return pallets.stream()
                .mapToInt(Pallet::getNoOfPieces)
                .sum();
    }
    
    @Override
    public boolean isPalletPiecesMatchingManifest(String manifestTrackingNo) {
        Optional<Manifest> manifestOpt = manifestRepository.findById(manifestTrackingNo);
        if (manifestOpt.isEmpty()) {
            return false;
        }
        
        Manifest manifest = manifestOpt.get();
        int totalPiecesInPallets = getTotalPiecesInPallets(manifestTrackingNo);
        
        return totalPiecesInPallets == manifest.getPieces();
    }
    
    /**
     * Helper method to get the current authenticated user
     */
    private String getCurrentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.isAuthenticated() && 
            !authentication.getName().equals("anonymousUser")) {
            return authentication.getName();
        }
        return "system"; // Fallback for system operations
    }
} 