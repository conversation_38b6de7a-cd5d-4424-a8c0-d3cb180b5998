import React from 'react';
import { Box } from '@mui/material';

interface LayoutContainerProps {
  children: React.ReactNode;
}

const LayoutContainer: React.FC<LayoutContainerProps> = ({ children }) => {
  return (
    <Box sx={{
      display: 'flex',
      minHeight: '100vh',
      minWidth: '100vw',
      position: 'relative',
      boxSizing: 'border-box',
      margin: 0,
      padding: 0,
      overflow: 'hidden',
    }}>
      {children}
    </Box>
  );
};

export default LayoutContainer; 