package com.wms.repository.UserRepo;

import com.wms.entity.user.Admin;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface AdminRepository extends JpaRepository<Admin, String> {
    @Query("SELECT a FROM Admin a LEFT JOIN FETCH a.roles WHERE a.email = :email")
    Optional<Admin> findByEmail(String email);

    @Query("SELECT a FROM Admin a LEFT JOIN FETCH a.roles WHERE a.username = :username")
    Optional<Admin> findByUsername(String username);

    @Query("SELECT COUNT(a) > 0 FROM Admin a WHERE a.email = :email")
    boolean existsByEmail(String email);

    @Query("SELECT COUNT(a) > 0 FROM Admin a WHERE a.username = :username")
    boolean existsByUsername(String username);
}
