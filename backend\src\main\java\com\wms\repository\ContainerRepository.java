package com.wms.repository;

import com.wms.entity.Container;
import com.wms.entity.ContainerStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface ContainerRepository extends JpaRepository<Container, String> {
    List<Container> findByClientUsername(String username);
    List<Container> findByStatus(ContainerStatus status);
    List<Container> findByVesselVoyageNo(String vesselVoyageNo);
} 