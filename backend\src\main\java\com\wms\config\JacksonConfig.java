package com.wms.config;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.time.format.DateTimeParseException;
import java.util.Locale;
import java.util.TimeZone;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Configuration
public class JacksonConfig {

    @Bean
    @Primary
    public ObjectMapper objectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();
        
        // Set timezone to Singapore
        objectMapper.setTimeZone(TimeZone.getTimeZone("Asia/Singapore"));
        
        // Register JavaTimeModule to properly handle Java 8 date/time types
        objectMapper.registerModule(new JavaTimeModule());
        
        // Register custom module for handling date formats
        SimpleModule customDateModule = new SimpleModule();
        customDateModule.addDeserializer(LocalDateTime.class, new CustomLocalDateTimeDeserializer());
        customDateModule.addDeserializer(LocalDate.class, new CustomLocalDateDeserializer());
        objectMapper.registerModule(customDateModule);
        
        // Disable writing dates as timestamps
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        
        return objectMapper;
    }
    
    /**
     * Custom deserializer for LocalDateTime that handles multiple formats:
     * - ISO format: "2025-06-04T16:00:00"
     * - Custom format: "04 Jun 2025, 16:00"
     * - Custom format: "04 Jun 2025 16:00"
     */
    public static class CustomLocalDateTimeDeserializer extends JsonDeserializer<LocalDateTime> {
        private static final DateTimeFormatter DEFAULT_FORMATTER = DateTimeFormatter.ISO_LOCAL_DATE_TIME;
        
        // Define formatters for various date formats
        private static final DateTimeFormatter[] FORMATTERS = {
            DEFAULT_FORMATTER,
            DateTimeFormatter.ofPattern("dd MMM yyyy, HH:mm", Locale.ENGLISH),
            DateTimeFormatter.ofPattern("dd MMM yyyy HH:mm", Locale.ENGLISH),
            new DateTimeFormatterBuilder()
                .parseCaseInsensitive()
                .appendPattern("dd MMM yyyy")
                .appendLiteral(' ')
                .appendPattern("HH:mm")
                .toFormatter(Locale.ENGLISH)
        };
        
        // Date-only formatter (for handling date without time)
        private static final DateTimeFormatter DATE_ONLY_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd", Locale.ENGLISH);
        
        @Override
        public LocalDateTime deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
            String dateStr = p.getText().trim();
            
            if (dateStr == null || dateStr.isEmpty()) {
                return null;
            }
            
            // Check if it's a date-only string (no time component)
            if (dateStr.matches("\\d{4}-\\d{2}-\\d{2}")) {
                try {
                    // Parse as LocalDate and convert to LocalDateTime at midnight
                    return java.time.LocalDate.parse(dateStr, DATE_ONLY_FORMATTER).atStartOfDay();
                } catch (DateTimeParseException e) {
                    // Continue to other formatters if this fails
                }
            }
            
            // Try each formatter in sequence
            for (DateTimeFormatter formatter : FORMATTERS) {
                try {
                    return LocalDateTime.parse(dateStr, formatter);
                } catch (DateTimeParseException e) {
                    // Try the next formatter
                }
            }
            
            // If all formatters fail, throw an exception with helpful message
            throw new IOException("Cannot parse date '" + dateStr + 
                "'. Expected formats: ISO date-time, 'dd MMM yyyy, HH:mm', 'dd MMM yyyy HH:mm', or 'yyyy-MM-dd'");
        }
    }
    
    /**
     * Custom deserializer for LocalDate that handles multiple formats:
     * - ISO format: "2025-06-04"
     * - Custom format: "04 Jun 2025"
     */
    public static class CustomLocalDateDeserializer extends JsonDeserializer<LocalDate> {
        // Define formatters for various date formats
        private static final DateTimeFormatter[] DATE_FORMATTERS = {
            DateTimeFormatter.ISO_LOCAL_DATE,
            DateTimeFormatter.ofPattern("dd MMM yyyy", Locale.ENGLISH),
            DateTimeFormatter.ofPattern("yyyy-MM-dd", Locale.ENGLISH),
            new DateTimeFormatterBuilder()
                .parseCaseInsensitive()
                .appendPattern("dd MMM yyyy")
                .toFormatter(Locale.ENGLISH)
        };
        
        private static final Logger logger = LoggerFactory.getLogger(CustomLocalDateDeserializer.class);
        
        @Override
        public LocalDate deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
            String dateStr = p.getText().trim();
            
            if (dateStr == null || dateStr.isEmpty()) {
                return null;
            }
            
            logger.debug("Deserializing date string: '{}'", dateStr);
            
            // If the string contains a time component (T or space followed by digits), extract just the date part
            if (dateStr.contains("T")) {
                String originalDateStr = dateStr;
                dateStr = dateStr.split("T")[0];
                logger.debug("Extracted date part '{}' from datetime '{}'", dateStr, originalDateStr);
            } else if (dateStr.matches(".*\\d{2}:\\d{2}.*")) {
                // If it has a time pattern like "04 Jun 2025 16:00", extract just the date part
                String originalDateStr = dateStr;
                dateStr = dateStr.replaceAll("\\s\\d{2}:\\d{2}.*$", "");
                logger.debug("Extracted date part '{}' from datetime with time '{}'", dateStr, originalDateStr);
            }
            
            // Try each formatter in sequence
            for (DateTimeFormatter formatter : DATE_FORMATTERS) {
                try {
                    LocalDate result = LocalDate.parse(dateStr, formatter);
                    logger.debug("Successfully parsed '{}' to LocalDate: {}", dateStr, result);
                    return result;
                } catch (DateTimeParseException e) {
                    // Try the next formatter
                    logger.trace("Failed to parse '{}' with formatter {}: {}", dateStr, formatter, e.getMessage());
                }
            }
            
            // If all formatters fail, log the error and throw an exception
            logger.error("Failed to parse date '{}' with any formatter", dateStr);
            logger.error("Available formatters: {}", (Object) DATE_FORMATTERS);
            
            // Try one more approach - direct parsing
            try {
                LocalDate result = LocalDate.parse(dateStr);
                logger.debug("Successfully parsed '{}' using LocalDate.parse() directly", dateStr);
                return result;
            } catch (DateTimeParseException e) {
                logger.error("Direct parsing also failed: {}", e.getMessage());
            }
            
            throw new IOException("Cannot parse date '" + dateStr + 
                "'. Expected formats: 'yyyy-MM-dd' or 'dd MMM yyyy'");
        }
    }
} 