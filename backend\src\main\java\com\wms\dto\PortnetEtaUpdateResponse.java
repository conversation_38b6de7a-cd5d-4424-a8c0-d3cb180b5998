package com.wms.dto;

import com.wms.entity.Container;
import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class PortnetEtaUpdateResponse {
    private String vesselVoyageNo;
    private LocalDateTime portnetEta;
    private int containerCount;
    private List<String> containerNumbers;
    
    public static PortnetEtaUpdateResponse fromContainers(List<Container> containers, LocalDateTime portnetEta) {
        PortnetEtaUpdateResponse response = new PortnetEtaUpdateResponse();
        
        if (!containers.isEmpty()) {
            response.setVesselVoyageNo(containers.get(0).getVesselVoyageNo());
        }
        
        response.setPortnetEta(portnetEta);
        response.setContainerCount(containers.size());
        response.setContainerNumbers(containers.stream()
            .map(Container::getContainerNo)
            .toList());
        
        return response;
    }
} 