# JWT Configuration
wms.app.jwtSecret=404E635266556A586E3272357538782F413F4428472B4B6250645367566B5970
wms.app.jwtExpirationMs=86400000

# Database Configuration
spring.datasource.url=${DATABASE_URL}
spring.datasource.username=${DATABASE_USERNAME}
spring.datasource.password=${DATABASE_PASSWORD}
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# JPA and Hibernate Configuration
spring.jpa.hibernate.ddl-auto=none
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL8Dialect

# Enhanced SQL monitoring
spring.jpa.properties.hibernate.generate_statistics=true
spring.jpa.properties.hibernate.session.events.log.LOG_QUERIES_SLOWER_THAN_MS=25
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE
logging.level.org.hibernate.stat=DEBUG

# HikariCP connection pool settings
spring.datasource.hikari.maximum-pool-size=10
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.idle-timeout=600000
spring.datasource.hikari.max-lifetime=300000
spring.datasource.hikari.connection-test-query=SELECT 1
spring.datasource.hikari.validation-timeout=1000
spring.datasource.hikari.auto-commit=true
spring.datasource.hikari.leak-detection-threshold=30000

# Data initialization
spring.sql.init.mode=always
spring.jpa.defer-datasource-initialization=true
spring.sql.init.platform=mysql
spring.sql.init.schema-locations=classpath:schema.sql
spring.sql.init.data-locations=classpath:data-prod.sql
spring.sql.init.continue-on-error=true
spring.sql.init.separator=;

# Additional logging for SQL initialization
logging.level.org.springframework.jdbc.datasource.init=DEBUG

# JPA Transaction and Cache Settings
spring.jpa.properties.hibernate.connection.isolation=2
spring.jpa.properties.hibernate.flushMode=AUTO
spring.jpa.properties.hibernate.current_session_context_class=thread
spring.jpa.properties.hibernate.jdbc.batch_size=20
spring.jpa.properties.hibernate.default_batch_fetch_size=20
spring.jpa.properties.hibernate.cache.use_second_level_cache=false
spring.jpa.properties.hibernate.cache.use_query_cache=false

# Server Configuration
server.port=${PORT:8080}
spring.jackson.time-zone=Asia/Singapore

# CORS Configuration
spring.web.cors.allowed-origins=https://wmsfuku.vercel.app,https://*.vercel.app
spring.web.cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
spring.web.cors.allowed-headers=*
spring.web.cors.allow-credentials=true
spring.web.cors.max-age=3600

# Security Configuration
spring.security.filter.order=10

# Logging Configuration
logging.level.root=INFO
logging.level.org.springframework.web=INFO
logging.level.org.springframework.transaction=DEBUG
logging.level.org.hibernate.engine.transaction.internal.TransactionImpl=DEBUG
logging.level.org.springframework.orm.jpa=DEBUG
logging.level.org.springframework.jdbc.core=DEBUG
logging.level.org.springframework.security=INFO 