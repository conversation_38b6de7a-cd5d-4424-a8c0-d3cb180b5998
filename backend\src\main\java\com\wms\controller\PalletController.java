package com.wms.controller;

import com.wms.dto.ApiResponse;
import com.wms.entity.Pallet;
import com.wms.service.PalletService;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

@RestController
@CrossOrigin(origins = "*")
@PreAuthorize("hasAnyRole('ADMIN', 'MANAGER', 'CLIENT')")
public class PalletController {

    private final PalletService palletService;
    private static final Logger logger = LoggerFactory.getLogger(PalletController.class);

    @Autowired
    public PalletController(PalletService palletService) {
        this.palletService = palletService;
    }

    @GetMapping({"/api/pallets", "/pallets"})
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER')")
    public ResponseEntity<ApiResponse<List<Pallet>>> getAllPallets() {
        List<Pallet> pallets = palletService.getAllPallets();
        return ResponseEntity.ok(ApiResponse.success(pallets));
    }

    @GetMapping({"/api/pallets/{palletNo}", "/pallets/{palletNo}"})
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER', 'CLIENT')")
    public ResponseEntity<ApiResponse<Pallet>> getPalletByPalletNo(@PathVariable String palletNo) {
        Optional<Pallet> pallet = palletService.getPalletByPalletNo(palletNo);
        if (pallet.isPresent()) {
            return ResponseEntity.ok(ApiResponse.success(pallet.get()));
        } else {
            return ResponseEntity.notFound().build();
        }
    }

    @GetMapping({"/api/manifests/{manifestTrackingNo}/pallets", "/manifests/{manifestTrackingNo}/pallets"})
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER', 'CLIENT')")
    public ResponseEntity<ApiResponse<List<Pallet>>> getPalletsByManifestTrackingNo(@PathVariable String manifestTrackingNo) {
        List<Pallet> pallets = palletService.getPalletsByManifestTrackingNo(manifestTrackingNo);
        return ResponseEntity.ok(ApiResponse.success(pallets));
    }

    @PostMapping({"/api/pallets", "/pallets"})
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER', 'CLIENT')")
    public ResponseEntity<ApiResponse<Pallet>> createPallet(@Valid @RequestBody Pallet pallet) {
        try {
            Pallet createdPallet = palletService.createPallet(pallet);
            return ResponseEntity.ok(ApiResponse.success("Pallet created successfully", createdPallet));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    @PostMapping({"/api/manifests/{manifestTrackingNo}/pallets", "/manifests/{manifestTrackingNo}/pallets"})
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER', 'CLIENT')")
    public ResponseEntity<ApiResponse<List<Pallet>>> createPalletsForManifest(
            @PathVariable String manifestTrackingNo,
            @Valid @RequestBody List<Pallet> pallets) {
        try {
            List<Pallet> createdPallets = palletService.createPalletsForManifest(manifestTrackingNo, pallets);
            return ResponseEntity.ok(ApiResponse.success("Pallets created successfully", createdPallets));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    @PutMapping({"/api/pallets/{palletNo}", "/pallets/{palletNo}"})
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER', 'CLIENT')")
    public ResponseEntity<ApiResponse<Pallet>> updatePallet(
            @PathVariable String palletNo,
            @Valid @RequestBody Pallet pallet) {
        try {
            Pallet updatedPallet = palletService.updatePallet(palletNo, pallet);
            return ResponseEntity.ok(ApiResponse.success("Pallet updated successfully", updatedPallet));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    @DeleteMapping({"/api/pallets/{palletNo}", "/pallets/{palletNo}"})
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER')")
    public ResponseEntity<ApiResponse<Void>> deletePallet(@PathVariable String palletNo) {
        try {
            palletService.deletePallet(palletNo);
            return ResponseEntity.ok(ApiResponse.success("Pallet deleted successfully", null));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    @DeleteMapping({"/api/manifests/{manifestTrackingNo}/pallets", "/manifests/{manifestTrackingNo}/pallets"})
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER')")
    public ResponseEntity<ApiResponse<Void>> deletePalletsByManifestTrackingNo(@PathVariable String manifestTrackingNo) {
        try {
            palletService.deletePalletsByManifestTrackingNo(manifestTrackingNo);
            return ResponseEntity.ok(ApiResponse.success("Pallets deleted successfully", null));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    @GetMapping({"/api/manifests/{manifestTrackingNo}/pallets/count", "/manifests/{manifestTrackingNo}/pallets/count"})
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER', 'CLIENT')")
    public ResponseEntity<ApiResponse<Long>> countPalletsByManifestTrackingNo(@PathVariable String manifestTrackingNo) {
        Long count = palletService.countPalletsByManifestTrackingNo(manifestTrackingNo);
        return ResponseEntity.ok(ApiResponse.success(count));
    }
    
    @GetMapping({"/api/manifests/{manifestTrackingNo}/pallets/total-pieces", "/manifests/{manifestTrackingNo}/pallets/total-pieces"})
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER', 'CLIENT')")
    public ResponseEntity<ApiResponse<Integer>> getTotalPiecesInPallets(@PathVariable String manifestTrackingNo) {
        int totalPieces = palletService.getTotalPiecesInPallets(manifestTrackingNo);
        return ResponseEntity.ok(ApiResponse.success(totalPieces));
    }
    
    @GetMapping({"/api/manifests/{manifestTrackingNo}/pallets/is-matching", "/manifests/{manifestTrackingNo}/pallets/is-matching"})
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER', 'CLIENT')")
    public ResponseEntity<ApiResponse<Boolean>> isPalletPiecesMatchingManifest(@PathVariable String manifestTrackingNo) {
        boolean isMatching = palletService.isPalletPiecesMatchingManifest(manifestTrackingNo);
        return ResponseEntity.ok(ApiResponse.success(isMatching));
    }
} 