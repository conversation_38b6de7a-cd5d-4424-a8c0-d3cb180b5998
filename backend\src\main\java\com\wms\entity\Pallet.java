package com.wms.entity;

import com.fasterxml.jackson.annotation.JsonBackReference;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@Entity
@Table(name = "pallets")
@EqualsAndHashCode(exclude = "manifest")
@ToString(exclude = "manifest")
public class Pallet {
    
    @Id
    @Column(name = "pallet_no", unique = true)
    private String palletNo;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "manifest_tracking_no", nullable = false)
    @JsonBackReference
    private Manifest manifest;
    
    @NotNull(message = "Number of pieces is required")
    @Column(name = "no_of_pieces", nullable = false)
    private Integer noOfPieces;
    
    @NotNull(message = "Total pieces reference is required")
    @Column(name = "total_pieces_reference", nullable = false)
    private Integer totalPiecesReference;
    
    @Column(name = "pallet_sequence")
    private Integer palletSequence;
    
    @PrePersist
    @PreUpdate
    public void generatePalletNo() {
        if (manifest != null && manifest.getInternalId() != null && palletSequence != null) {
            this.palletNo = manifest.getInternalId() + "-P" + palletSequence;
        }
    }
    
    // Constructor
    public Pallet() {}
    
    public Pallet(Manifest manifest, Integer palletSequence, Integer noOfPieces, Integer totalPiecesReference) {
        this.manifest = manifest;
        this.palletSequence = palletSequence;
        this.noOfPieces = noOfPieces;
        this.totalPiecesReference = totalPiecesReference;
        generatePalletNo();
    }
} 