package com.wms.service.impl;

import com.wms.entity.LocationZone;
import com.wms.exception.ResourceNotFoundException;
import com.wms.repository.LocationZoneRepository;
import com.wms.service.LocationZoneService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Service
public class LocationZoneServiceImpl implements LocationZoneService {

    private final LocationZoneRepository locationZoneRepository;
    private static final Logger logger = LoggerFactory.getLogger(LocationZoneServiceImpl.class);

    @Autowired
    public LocationZoneServiceImpl(LocationZoneRepository locationZoneRepository) {
        this.locationZoneRepository = locationZoneRepository;
    }

    @Override
    public List<LocationZone> getAllLocationZones() {
        return locationZoneRepository.findAll();
    }

    @Override
    public List<LocationZone> getAllActiveLocationZones() {
        return locationZoneRepository.findAllActiveZones();
    }

    @Override
    public Optional<LocationZone> getLocationZoneById(Long id) {
        return locationZoneRepository.findById(id);
    }

    @Override
    public Optional<LocationZone> getLocationZoneByName(String name) {
        return locationZoneRepository.findByName(name);
    }

    @Override
    @Transactional
    public LocationZone createLocationZone(LocationZone locationZone) {
        // Validate that the postal code range is valid
        validatePostalCodeRange(locationZone);
        
        // Check for duplicate name
        if (locationZoneRepository.existsByName(locationZone.getName())) {
            throw new IllegalArgumentException("Location zone with name " + locationZone.getName() + " already exists");
        }
        
        return locationZoneRepository.save(locationZone);
    }

    @Override
    @Transactional
    public LocationZone updateLocationZone(Long id, LocationZone locationZoneDetails) {
        LocationZone locationZone = locationZoneRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Location zone not found with id: " + id));
        
        // Validate that the postal code range is valid
        validatePostalCodeRange(locationZoneDetails);
        
        // Check for duplicate name if name is changed
        if (!locationZone.getName().equals(locationZoneDetails.getName()) && 
            locationZoneRepository.existsByName(locationZoneDetails.getName())) {
            throw new IllegalArgumentException("Location zone with name " + locationZoneDetails.getName() + " already exists");
        }
        
        // Update fields
        locationZone.setName(locationZoneDetails.getName());
        locationZone.setAbbreviation(locationZoneDetails.getAbbreviation());
        locationZone.setStartPostalCode(locationZoneDetails.getStartPostalCode());
        locationZone.setEndPostalCode(locationZoneDetails.getEndPostalCode());
        locationZone.setSpecialCases(locationZoneDetails.getSpecialCases());
        locationZone.setDescription(locationZoneDetails.getDescription());
        locationZone.setIsActive(locationZoneDetails.getIsActive());
        
        return locationZoneRepository.save(locationZone);
    }

    @Override
    @Transactional
    public void deleteLocationZone(Long id) {
        LocationZone locationZone = locationZoneRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Location zone not found with id: " + id));
        
        // Instead of hard delete, just mark as inactive
        locationZone.setIsActive(false);
        locationZoneRepository.save(locationZone);
    }

    @Override
    public LocationZone determineLocationZoneByPostalCode(String postalCode) {
        if (postalCode == null || postalCode.length() < 2) {
            throw new IllegalArgumentException("Invalid postal code format");
        }

        try {
            // Extract the first two digits of the postal code
            String firstTwoDigits = postalCode.substring(0, 2);
            Integer code = Integer.parseInt(firstTwoDigits);
            
            // First check if the postal code falls within the main range of any zone
            List<LocationZone> matchingZones = locationZoneRepository.findByPostalCode(code);
            if (!matchingZones.isEmpty()) {
                return matchingZones.get(0);
            }
            
            // If not found in main ranges, check special cases for all active zones
            List<LocationZone> allActiveZones = locationZoneRepository.findAllActiveZones();
            for (LocationZone zone : allActiveZones) {
                if (isPostalCodeInSpecialCases(code, zone.getSpecialCases())) {
                    return zone;
                }
            }
            
            // If no matching zone found, create a default "UNKNOWN" zone
            LocationZone defaultZone = new LocationZone();
            defaultZone.setId(0L);
            defaultZone.setName("UNKNOWN");
            defaultZone.setDescription("Default zone for unmatched postal codes");
            defaultZone.setIsActive(true);
            
            logger.warn("No location zone found for postal code: {}", postalCode);
            return defaultZone;
            
        } catch (NumberFormatException e) {
            logger.error("Error parsing postal code: {}", postalCode, e);
            throw new IllegalArgumentException("Invalid postal code format: " + postalCode);
        }
    }

    /**
     * Check if a postal code is included in the special cases string.
     * Special cases can be comma-separated values or ranges with hyphens.
     * For example: "12,53,55" or "13-32,54,56-57"
     */
    private boolean isPostalCodeInSpecialCases(Integer postalCode, String specialCases) {
        if (specialCases == null || specialCases.trim().isEmpty()) {
            return false;
        }
        
        String[] parts = specialCases.split(",");
        for (String part : parts) {
            part = part.trim();
            if (part.contains("-")) {
                // Handle range
                String[] range = part.split("-");
                if (range.length == 2) {
                    try {
                        int start = Integer.parseInt(range[0]);
                        int end = Integer.parseInt(range[1]);
                        if (postalCode >= start && postalCode <= end) {
                            return true;
                        }
                    } catch (NumberFormatException e) {
                        logger.warn("Invalid range format in special cases: {}", part);
                    }
                }
            } else {
                // Handle single value
                try {
                    if (postalCode == Integer.parseInt(part)) {
                        return true;
                    }
                } catch (NumberFormatException e) {
                    logger.warn("Invalid number format in special cases: {}", part);
                }
            }
        }
        
        return false;
    }

    @Override
    public boolean existsByName(String name) {
        return locationZoneRepository.existsByName(name);
    }

    @Override
    public String getLocationAbbreviation(String locationName) {
        if (locationName == null || locationName.trim().isEmpty()) {
            return "N"; // Default fallback
        }

        // Try to find the location zone by name first
        Optional<LocationZone> locationZone = getLocationZoneByName(locationName.trim());
        
        if (locationZone.isPresent() && locationZone.get().getAbbreviation() != null 
            && !locationZone.get().getAbbreviation().trim().isEmpty()) {
            return locationZone.get().getAbbreviation().toUpperCase();
        }

        // Fallback: generate abbreviation from location name
        return generateAbbreviationFromName(locationName);
    }

    /**
     * Generates a fallback abbreviation from the location name
     */
    private String generateAbbreviationFromName(String locationName) {
        if (locationName == null || locationName.trim().isEmpty()) {
            return "N";
        }

        String loc = locationName.toUpperCase().trim();
        
        // Check for compound directions first (most specific first)
        if (loc.contains("NORTH_EAST") || loc.contains("NORTHEAST")) {
            return "NE";
        }
        if (loc.contains("NORTH_WEST") || loc.contains("NORTHWEST")) {
            return "NW";
        }
        if (loc.contains("SOUTH_EAST") || loc.contains("SOUTHEAST")) {
            return "SE";
        }
        if (loc.contains("SOUTH_WEST") || loc.contains("SOUTHWEST")) {
            return "SW";
        }
        
        // Then check for simple directions
        if (loc.contains("NORTH")) {
            return "N";
        }
        if (loc.contains("SOUTH")) {
            return "S";
        }
        if (loc.contains("EAST")) {
            return "E";
        }
        if (loc.contains("WEST")) {
            return "W";
        }
        if (loc.contains("CENTRAL")) {
            return "C";
        }
        
        // Default to first letter of location
        return loc.substring(0, 1);
    }
    
    private void validatePostalCodeRange(LocationZone locationZone) {
        // Ensure start code is less than or equal to end code
        if (locationZone.getStartPostalCode() != null && locationZone.getEndPostalCode() != null &&
            locationZone.getStartPostalCode() > locationZone.getEndPostalCode()) {
            throw new IllegalArgumentException("Start postal code must be less than or equal to end postal code");
        }
    }
} 