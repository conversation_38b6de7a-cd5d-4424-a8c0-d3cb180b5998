package com.wms.repository;

import com.wms.entity.LocationZone;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import java.util.List;
import java.util.Optional;

@Repository
public interface LocationZoneRepository extends JpaRepository<LocationZone, Long> {
    Optional<LocationZone> findByName(String name);
    
    @Query("SELECT lz FROM LocationZone lz WHERE lz.isActive = true ORDER BY lz.name")
    List<LocationZone> findAllActiveZones();
    
    @Query("SELECT lz FROM LocationZone lz WHERE " +
           "(:postalCode >= lz.startPostalCode AND :postalCode <= lz.endPostalCode) AND " +
           "lz.isActive = true")
    List<LocationZone> findByPostalCode(@Param("postalCode") Integer postalCode);
    
    boolean existsByName(String name);
} 