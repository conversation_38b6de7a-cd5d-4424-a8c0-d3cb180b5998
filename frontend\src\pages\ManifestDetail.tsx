// @ts-nocheck - TODO: Fix TypeScript errors properly in a follow-up task
import React, { useState, useEffect, useMemo, useRef } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { useTheme } from '@mui/material/styles';
import {
  Box,
  Button,
  Typography,
  Alert,
  Paper,
  CircularProgress,
  Divider,
  Stack,
  Chip,
  Tooltip,
  IconButton,
  Breadcrumbs,
  Link,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  SelectChangeEvent,
  FormHelperText,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  InputAdornment,
  Grid,
} from '@mui/material';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider, DateTimePicker } from '@mui/x-date-pickers';
import {
  ArrowBack as ArrowBackIcon,
  Edit as EditIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
  LocalShipping as LocalShippingIcon,
  LocalShippingOutlined as LocalShippingOutlinedIcon,
  Person as PersonIcon,
  Phone as PhoneIcon,
  Home as HomeIcon,
  CalendarToday as CalendarTodayIcon,
  EventAvailable as EventAvailableIcon,
  CheckCircle as CheckCircleIcon,
  Inventory as InventoryIcon,
  Info as InfoIcon,
  Label as LabelIcon,
  Timeline as TimelineIcon,
  Warehouse as WarehouseIcon,
  LocationOn as LocationOnIcon,
  History as HistoryIcon,
  ErrorOutline as ErrorOutlineIcon,
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import { useToast } from '../contexts/ToastContext';
import { usePageTitle } from '../hooks/usePageTitle';
import manifestService from '../services/manifest.service';
import containerService from '../services/container.service';
import userService from '../services/user.service';
import locationService from '../services/location.service';
import { getVehicleTypes } from '../services/vehicle.service';
import { Manifest, ManifestStatus } from '../types/manifest';
import { format } from 'date-fns';
import { alpha } from '@mui/material/styles';
import ManifestLabelDialog from '../components/ManifestLabelDialog';
import DeliveryDateDialog from '../components/DeliveryDateDialog';
import { LocationZone } from '../types/LocationZone';
import { normalizeDate, getDaysDifference, formatDateString, formatDate } from '../utils/dateUtils';
import PalletManagement from '../components/PalletManagement';
import ManifestTrackingHistory, { ManifestTrackingHistoryRef } from '../components/ManifestTrackingHistory';
import { VehicleType } from '../types/Vehicle';
import SplitDeliveryDatePicker from '../components/SplitDeliveryDatePicker';
import { formatDateTime, formatDeliveryDate, formatTimeSlot, getTimeSlotIdFromDate } from '../utils/dateUtils';

// Add a helper to handle safe navigation
const safeNavigate = (navigate: any, path: string, state?: any) => {
  // Use replace instead of push to prevent the browser's back button from getting to invalid URLs
  navigate(path, { replace: true, state });
};

const ManifestDetail: React.FC = () => {
  const { trackingNo } = useParams<{ trackingNo: string }>();
  const navigate = useNavigate();
  const location = useLocation();
  const { currentUser } = useAuth();
  const theme = useTheme();
  const toast = useToast();
  
  // Set page title with tracking number
  usePageTitle(trackingNo ? `Manifest ${trackingNo}` : 'Manifest Detail');
  
  // Extract state from navigation
  const fromContainer = location.state?.fromContainer;
  const containerNo = location.state?.containerNo;
  
  const [manifest, setManifest] = useState<Manifest | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [editMode, setEditMode] = useState(false);
  const [editedManifest, setEditedManifest] = useState<Manifest | null>(null);
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [statusDialogOpen, setStatusDialogOpen] = useState(false);
  const [newStatus, setNewStatus] = useState<ManifestStatus | null>(null);
  const [labelDialogOpen, setLabelDialogOpen] = useState(false);
  const [drivers, setDrivers] = useState<any[]>([]);
  const [locationZones, setLocationZones] = useState<LocationZone[]>([]);
  const [vehicleTypes, setVehicleTypes] = useState<VehicleType[]>([]);
  
  const trackingHistoryRef = useRef<ManifestTrackingHistoryRef | null>(null);
  
  // Add event listener for manifest status updates
  useEffect(() => {
    // Event handler for manifest status updates from PalletManagement
    const handleManifestStatusUpdate = (event) => {
      const { manifest: updatedManifest } = event.detail;
      if (updatedManifest && updatedManifest.trackingNo === trackingNo) {
        console.log('Received manifest status update:', updatedManifest.status);
        setManifest(updatedManifest);
        
        // Refresh tracking history
        if (trackingHistoryRef.current) {
          trackingHistoryRef.current.refreshTrackingHistory();
        }
      }
    };

    // Add event listener
    window.addEventListener('manifest-status-updated', handleManifestStatusUpdate);

    // Clean up
    return () => {
      window.removeEventListener('manifest-status-updated', handleManifestStatusUpdate);
    };
  }, [trackingNo]);
  
  useEffect(() => {
    if (!trackingNo) {
      setError('Tracking number is required');
      setLoading(false);
      return;
    }
    
    // Reset states when tracking number changes
    setManifest(null);
    setError(null);
    setLoading(true);
    
    // Load data in parallel
    const loadData = async () => {
      try {
        await Promise.all([
          fetchManifestDetail(),
          fetchLocationZones(),
          fetchVehicleTypes()
        ]);
      } catch (err) {
        console.error('Error loading data:', err);
        setError('Failed to load data. Please try again.');
      } finally {
        setLoading(false);
      }
    };
    
    loadData();
  }, [trackingNo, currentUser?.token]);
  
  // Add a side effect to update document title and log navigation source
  useEffect(() => {
    // Log whether we came from container page for debugging
    if (fromContainer && containerNo) {
      console.log(`Navigated from container ${containerNo} page`);
    }
  }, [trackingNo, fromContainer, containerNo]);
  
  // Add effect to fetch drivers for the edit form
  useEffect(() => {
    const fetchDrivers = async () => {
      if (currentUser?.token && editMode) {
        try {
          const response = await userService.getDrivers(currentUser.token);
          if (response.success) {
            setDrivers(response.data);
          } else {
            console.error('Failed to fetch drivers:', response.message);
            setDrivers([]);
          }
        } catch (err) {
          console.error('Error fetching drivers:', err);
          setDrivers([]);
        }
      }
    };

    fetchDrivers();
  }, [editMode, currentUser?.token]);
  
  // Add effect to handle direct navigation via URL without state
  useEffect(() => {
    // If someone tries to access the page directly via URL (without state from navigation)
    // Make sure we don't get source file access errors when they hit back
    if (!location.state) {
      // Update history with the current URL but with proper state
      window.history.replaceState(
        { from: 'direct' }, // Add minimal state to indicate direct access
        '', // Title (unused)
        window.location.pathname // Keep current path
      );
    }
  }, [location]);
  
  const fetchManifestDetail = async () => {
    if (!currentUser?.token || !trackingNo) {
      setError('Authentication required');
      return;
    }
    
    try {
      console.log(`Fetching details for manifest: ${trackingNo}`);
      setError(null);
      
      // Fetch manifest details
      const manifestResponse = await manifestService.getManifestByTrackingNo(trackingNo, currentUser.token);
      
      if (manifestResponse.success) {
        console.log("Manifest details fetched successfully:", manifestResponse.data);
        
        // Log internal ID for debugging
        const manifest = manifestResponse.data;
        const internalId = manifest.internalId || 
          (manifest.container?.containerNo && manifest.sequenceNo 
            ? `${manifest.container.containerNo}-${manifest.sequenceNo}` 
            : undefined);
        
        console.log("Manifest internal ID:", internalId);
        
        setManifest(manifestResponse.data);
      } else {
        console.error("Failed to fetch manifest details:", manifestResponse.message);
        setError(manifestResponse.message || 'Failed to fetch manifest details');
      }
    } catch (err) {
      console.error('Error fetching manifest details:', err);
      setError(err.response?.data?.message || 'An error occurred while fetching data');
    }
  };
  
  const fetchLocationZones = async () => {
    try {
      if (!currentUser?.token) {
        console.error('Authentication token is missing');
        return;
      }
      
      console.log('Fetching location zones...');
      const response = await locationService.getActiveLocationZones(currentUser.token);
      
      if (response.success && response.data) {
        console.log(`Successfully loaded ${response.data.length} location zones`);
        console.log('Location zones:', response.data.map(z => `${z.id}: ${z.name}`).join(', '));
        setLocationZones(response.data);
      } else {
        console.error('Failed to fetch location zones:', response.message);
        toast.warning('Failed to load location zones. Location selection may be unavailable.', {
          position: 'bottom-right'
        });
      }
    } catch (err: any) {
      console.error('Error fetching location zones:', err);
      toast.warning('Error loading location zones. Please try refreshing the page.', {
        position: 'bottom-right'
      });
    }
  };
  
  const fetchVehicleTypes = async () => {
    try {
      if (!currentUser?.token) {
        console.error('Authentication token is missing');
        return;
      }
      
      console.log('Fetching vehicle types...');
      const data = await getVehicleTypes();
      
      if (data) {
        console.log(`Successfully loaded ${data.length} vehicle types`);
        setVehicleTypes(data);
      } else {
        console.error('Failed to fetch vehicle types');
        toast.warning('Failed to load vehicle types. Vehicle selection may be unavailable.', {
          position: 'bottom-right'
        });
      }
    } catch (err: any) {
      console.error('Error fetching vehicle types:', err);
      toast.warning('Error loading vehicle types. Please try refreshing the page.', {
        position: 'bottom-right'
      });
    }
  };
  
  const formatDateTime = (dateStr?: string | null) => {
    if (!dateStr) return '-';
    try {
      // Use our utility function for consistent date formatting
      return formatDateString(dateStr, '-');
    } catch (e) {
      console.error('Error formatting date:', e, dateStr);
      return dateStr as string;
    }
  };
  
  const getStatusChipColor = (status: ManifestStatus) => {
    const defaultColors = { bg: 'rgba(158, 158, 158, 0.1)', color: 'text.secondary' };
    
    switch (status) {
      case ManifestStatus.CREATED:
        return { bg: 'rgba(255, 167, 38, 0.1)', color: 'warning.main' };
      case ManifestStatus.ETA_TO_WAREHOUSE:
        return { bg: 'rgba(33, 150, 243, 0.1)', color: 'info.main' };
      case ManifestStatus.ARRIVED:
        return { bg: 'rgba(106, 27, 154, 0.1)', color: 'secondary.main' };
      case ManifestStatus.INBOUNDING:
        return { bg: 'rgba(239, 108, 0, 0.1)', color: 'warning.dark' };
      case ManifestStatus.INBOUNDED_TO_WAREHOUSE:
        return { bg: 'rgba(0, 200, 83, 0.1)', color: 'success.main' };
      case ManifestStatus.READY_TO_DELIVER:
        return { bg: 'rgba(255, 193, 7, 0.1)', color: 'warning.main' };
      case ManifestStatus.DELIVERING:
        return { bg: 'rgba(0, 200, 83, 0.1)', color: 'success.main' };
      case ManifestStatus.DELIVERED:
        return { bg: 'rgba(76, 175, 80, 0.1)', color: 'success.dark' };
      case ManifestStatus.ON_HOLD:
        return { bg: 'rgba(244, 67, 54, 0.1)', color: 'error.main' };
      case ManifestStatus.DISCREPANCY:
        return { bg: 'rgba(244, 67, 54, 0.2)', color: 'error.main' };
      default:
        return defaultColors;
    }
  };
  
  // Add function to get status icon based on manifest status
  const getStatusIcon = (status: ManifestStatus) => {
    switch (status) {
      case ManifestStatus.CREATED:
        return <CalendarTodayIcon sx={{ color: theme.palette.warning.main }} />;
      case ManifestStatus.ETA_TO_WAREHOUSE:
        return <TimelineIcon sx={{ color: theme.palette.info.main }} />;
      case ManifestStatus.ARRIVED:
        return <LocalShippingIcon sx={{ color: theme.palette.secondary.main }} />;
      case ManifestStatus.INBOUNDING:
        return <WarehouseIcon sx={{ color: theme.palette.warning.dark }} />;
      case ManifestStatus.INBOUNDED_TO_WAREHOUSE:
        return <WarehouseIcon sx={{ color: theme.palette.success.main }} />;
      case ManifestStatus.READY_TO_DELIVER:
        return <CheckCircleIcon sx={{ color: theme.palette.warning.main }} />;
      case ManifestStatus.DELIVERING:
        return <LocalShippingOutlinedIcon sx={{ color: theme.palette.success.main }} />;
      case ManifestStatus.DELIVERED:
        return <CheckCircleIcon sx={{ color: theme.palette.success.dark }} />;
      case ManifestStatus.ON_HOLD:
        return <TimelineIcon sx={{ color: theme.palette.error.main }} />;
      case ManifestStatus.DISCREPANCY:
        return <ErrorOutlineIcon sx={{ color: theme.palette.error.main }} />;
      default:
        return <TimelineIcon sx={{ color: theme.palette.grey[500] }} />;
    }
  };
  
  // Handle edit manifest
  const handleEditManifest = () => {
    if (manifest) {
      // Ensure location zones are loaded
      if (locationZones.length === 0) {
        fetchLocationZones();
      }
      
      // Ensure vehicle types are loaded
      if (vehicleTypes.length === 0) {
        fetchVehicleTypes();
      }
      
      // Prepare the manifest for editing
      const editableManifest = {
        ...manifest,
        // Keep delivery date as formatted string for consistency with the rest of the app
        deliveryDate: manifest.deliveryDate,
        deliveredDate: manifest.deliveredDate,
        // Ensure timeSlot is included - extract from deliveryDate if not present
        timeSlot: manifest.timeSlot || getTimeSlotIdFromDate(manifest.deliveryDate),
        // Keep original location - will be recalculated on save based on postal code
        location: manifest.location || '', 
        // Ensure internal ID is included
        internalId: manifest.internalId || (manifest.container?.containerNo && manifest.sequenceNo 
          ? `${manifest.container.containerNo}-${manifest.sequenceNo}` : undefined)
      };
      
      console.log('Starting edit mode with location:', editableManifest.location);
      console.log('Available location zones:', locationZones.map(z => z.name));
      console.log('Delivery date for editing:', editableManifest.deliveryDate);
      console.log('Time slot for editing:', editableManifest.timeSlot);
      console.log('NOTE: Location is automatically determined based on postal code and cannot be directly edited');
      
      setEditedManifest(editableManifest);
      setEditMode(true);
    }
  };
  
  // Handle cancel edit
  const handleCancelEdit = () => {
    setEditMode(false);
    setFormErrors({});
  };
  
  // Handle location change
  const handleLocationChange = (e: SelectChangeEvent<string>) => {
    if (!editedManifest) return;
    const newLocation = e.target.value;
    console.log(`Location zone selected: '${newLocation}'`);
    
    // Create a new object to ensure React detects the state change
    const updatedManifest = { 
      ...editedManifest, 
      location: newLocation 
    };
    
    setEditedManifest(updatedManifest);
    console.log('Updated editedManifest with location:', updatedManifest.location);
  };
  
  // Update the handleSaveEdit function to ensure location is properly handled
  const handleSaveEdit = async () => {
    if (!editedManifest || !currentUser?.token) return;
    
    // Basic validation
    const errors: Record<string, string> = {};
    if (!editedManifest.customerName) errors.customerName = 'Customer Name is required';
    if (!editedManifest.address) errors.address = 'Address is required';
    
    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }
    
    try {
      setLoading(true);
      
      // Create a clean version of the manifest without computed properties
      // Keep actualPalletsCount locally for UI but don't send to API
      const { noOfPalletsFromObjects, ...cleanManifest } = editedManifest;
      
      // Store actualPalletsCount for later use
      const actualPalletsCount = cleanManifest.actualPalletsCount;
      
      // Preserve the manually selected location
      const manifestToSave = {
        ...cleanManifest,
        // Use empty string for UI display but null for API
        location: cleanManifest.location === '' ? null : cleanManifest.location,
        // Convert empty delivery vehicle to null for auto-assignment
        deliveryVehicle: cleanManifest.deliveryVehicle?.trim() || null
      };
      
      console.log('Saving manifest with data:', {
        trackingNo: manifestToSave.trackingNo,
        postalCode: manifestToSave.postalCode,
        location: manifestToSave.location, // Will be preserved if manually set
        customerName: manifestToSave.customerName,
        weight: manifestToSave.weight,
        cbm: manifestToSave.cbm,
        deliveryDate: manifestToSave.deliveryDate,
        timeSlot: manifestToSave.timeSlot, // Include time slot in the log
        deliveredDate: manifestToSave.deliveredDate,
        deliveryVehicle: manifestToSave.deliveryVehicle
      });
      
      const response = await manifestService.updateManifest(
        editedManifest.trackingNo,
        manifestToSave,
        currentUser.token
      );
      
      if (response.success) {
        console.log('Manifest updated successfully. New data:', {
          postalCode: response.data.postalCode,
          location: response.data.location
        });
        
        // Restore actualPalletsCount in the response data
        const updatedManifest = {
          ...response.data,
          actualPalletsCount: actualPalletsCount
        };
        
        setManifest(updatedManifest);
        setEditMode(false);
        setFormErrors({});
        toast.success('Manifest updated successfully');
      } else {
        setError(response.message || 'Failed to update manifest');
      }
    } catch (err) {
      console.error('Error updating manifest:', err);
      setError(err.response?.data?.message || 'An error occurred while updating manifest');
    } finally {
      setLoading(false);
    }
  };
  
  // Handle status update
  const handleSaveStatus = async () => {
    if (!newStatus || !currentUser?.token || !trackingNo) return;
    
    try {
      setLoading(true);
      
      const response = await manifestService.updateManifestStatus(
        trackingNo,
        newStatus as ManifestStatus,
        currentUser.token
      );
      
      if (response.success) {
        setManifest(response.data);
        setStatusDialogOpen(false);
        toast.success(`Status updated to ${newStatus}`);
      } else {
        setError(response.message || 'Failed to update status');
        // Keep dialog open when there's an error
      }
    } catch (err) {
      console.error('Error updating status:', err);
      const errorMessage = err.response?.data?.message || 'An error occurred while updating status';
      
      // Show specific error message for INBOUNDING/INBOUNDED_TO_WAREHOUSE status changes
      if (errorMessage.includes('INBOUNDING') || errorMessage.includes('INBOUNDED_TO_WAREHOUSE')) {
        toast.error(errorMessage);
      } else {
        setError(errorMessage);
      }
      
      // Keep dialog open when there's an error
    } finally {
      setLoading(false);
    }
  };
  
  // Handle date change
  const handleDateChange = (field: string, newValue: Date | null) => {
    if (!editedManifest) return;
    
    const updatedManifest = { ...editedManifest };
    
    if (newValue) {
      // Format the date using our utility function
      const formattedDate = formatDate(newValue);
      
      console.log(`Setting ${field} to formatted date:`, formattedDate);
      console.log(`Original date value:`, newValue);
      
      updatedManifest[field] = formattedDate;
      
      // If this is a delivery date change, also extract and set the time slot
      if (field === 'deliveryDate') {
        const timeSlotId = getTimeSlotIdFromDate(newValue);
        updatedManifest.timeSlot = timeSlotId;
        console.log(`Setting timeSlot to:`, timeSlotId);
      }
    } else {
      console.log(`Clearing ${field}`);
      updatedManifest[field] = null;
      
      // If clearing delivery date, also clear time slot
      if (field === 'deliveryDate') {
        updatedManifest.timeSlot = null;
        console.log(`Clearing timeSlot`);
      }
    }
    
    setEditedManifest(updatedManifest);
  };

  // Handle time slot change separately
  const handleTimeSlotChange = (timeSlotId: string | null) => {
    if (!editedManifest) return;
    
    const updatedManifest = { ...editedManifest };
    updatedManifest.timeSlot = timeSlotId;
    
    console.log(`Setting timeSlot to:`, timeSlotId);
    setEditedManifest(updatedManifest);
  };
  
  // Handle label dialog
  const handleOpenLabelDialog = () => {
    setLabelDialogOpen(true);
  };
  
  const handleCloseLabelDialog = () => {
    setLabelDialogOpen(false);
  };

  // Handle saving custom label count
  const handleSaveLabelCount = (trackingNo: string, count: number) => {
    // This will be handled by the dialog component
  };

  // Callback to refresh tracking history when pallets are modified
  const handlePalletOperationComplete = () => {
    console.log('Pallet operation completed, refreshing manifest data...');
    // Add a small delay to ensure backend tracking log is created
    setTimeout(async () => {
      if (trackingHistoryRef.current) {
        console.log('Refreshing tracking history...');
        trackingHistoryRef.current.refreshTrackingHistory();
      }
      
      // Fetch the latest manifest data to reflect any status changes
      if (currentUser?.token && trackingNo) {
        try {
          console.log('Fetching latest manifest data...');
          const manifestResponse = await manifestService.getManifestByTrackingNo(trackingNo, currentUser.token);
          if (manifestResponse.success) {
            console.log('Manifest data updated:', manifestResponse.data.status);
            setManifest(manifestResponse.data);
          }
        } catch (error) {
          console.error("Error refreshing manifest data:", error);
        }
      }
    }, 200); // Reduced from 500ms to 200ms for better responsiveness
  };

  // Add a function to get the container number from either state or manifest
  const getContainerNo = () => {
    // First try to get from location state (if navigated from container page)
    if (fromContainer && containerNo) {
      return containerNo;
    }
    
    // Otherwise try to get from the manifest data
    if (manifest && manifest.container && manifest.container.containerNo) {
      return manifest.container.containerNo;
    }
    
    // Default fallback
    return null;
  };

  // Determine if we should display the container in breadcrumbs/back button
  // Only if we explicitly came from a container page
  const showContainerNav = fromContainer && containerNo;

  // Use the safe navigation helper for all navigate calls
  const handleBack = () => {
    if (showContainerNav) {
      console.log(`Navigating back to container ${getContainerNo()}`);
      safeNavigate(navigate, `/containers/${getContainerNo()}`);
    } else {
      console.log('Navigating back to manifests list');
      safeNavigate(navigate, '/manifests');
    }
  };

  return (
    <Box 
      sx={{ 
        p: 3, 
        width: '100%',
        maxWidth: '100%',
        overflowX: 'auto'
      }}
    >
      {/* Breadcrumbs and back button */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Breadcrumbs separator="›" aria-label="breadcrumb">
            {(() => {
              const breadcrumbs = [];
              
              if (showContainerNav) {
                breadcrumbs.push(
                  <Link
                    key="containers"
                    underline="hover"
                    color="inherit"
                    onClick={() => safeNavigate(navigate, '/containers')}
                    sx={{ cursor: 'pointer' }}
                  >
                    Containers
                  </Link>
                );
                
                breadcrumbs.push(
                  <Link
                    key="container-detail"
                    underline="hover"
                    color="inherit"
                    onClick={() => safeNavigate(navigate, `/containers/${getContainerNo()}`)}
                    sx={{ cursor: 'pointer' }}
                  >
                    {getContainerNo()}
                  </Link>
                );
              } else {
                breadcrumbs.push(
                  <Link
                    key="manifests"
                    underline="hover"
                    color="inherit"
                    onClick={() => safeNavigate(navigate, '/manifests')}
                    sx={{ cursor: 'pointer' }}
                  >
                    Manifests
                  </Link>
                );
              }
              
              breadcrumbs.push(
                <Typography key="current" color="text.primary">
                  {trackingNo}
                </Typography>
              );
              
              return breadcrumbs;
            })()}
          </Breadcrumbs>
          <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
            <Typography variant="h4" sx={{ fontWeight: 'bold', color: theme.palette.primary.main }}>
              Manifest {trackingNo} Details
            </Typography>
          </Box>
        </Box>
        <Button
          variant="outlined"
          startIcon={<ArrowBackIcon />}
          onClick={handleBack}
          sx={{ borderRadius: 2 }}
        >
          {showContainerNav ? `Back to Container ${getContainerNo()}` : 'Back to Manifests'}
        </Button>
      </Box>
      
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}
      
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 5 }}>
          <CircularProgress />
        </Box>
      ) : manifest ? (
        <>
          {/* Main Content with Two-Column Layout */}
          <Box sx={{ display: 'flex', gap: 3, alignItems: 'flex-start' }}>
            {/* Left Column - Manifest Details */}
            <Box sx={{ flex: '1 1 65%', minWidth: 0 }}>
              {/* Manifest Information Section with Inline Edit */}
              <Paper
                elevation={1}
                sx={{ 
                  p: { xs: 2, md: 4 }, 
                  mb: 4, 
                  borderRadius: 2, 
                  border: '1px solid rgba(0,0,0,0.08)',
                  maxWidth: '100%',
                  overflowX: 'auto',
                  boxShadow: '0 4px 12px rgba(0,0,0,0.05)'
                }}
              >
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 3 }}>
                  <Typography variant="h5" sx={{ fontWeight: 'bold' }}>Manifest Information</Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    {!editMode ? (
                      <Box>
                        <Button 
                          variant="outlined" 
                          color="primary" 
                          size="small" 
                          startIcon={<EditIcon />} 
                          onClick={handleEditManifest}
                          sx={{ ml: 1 }}
                        >
                          Edit
                        </Button>
                        <Button 
                          variant="outlined" 
                          color="primary" 
                          size="small" 
                          startIcon={<InventoryIcon />} 
                          onClick={handleOpenLabelDialog}
                          sx={{ ml: 1 }}
                        >
                          Generate Label
                        </Button>
                      </Box>
                    ) : (
                      <>
                        <Button 
                          variant="outlined" 
                          color="primary" 
                          size="small" 
                          onClick={handleSaveEdit}
                          sx={{ ml: 1 }}
                        >
                          Save
                        </Button>
                        <Button 
                          variant="outlined" 
                          color="inherit" 
                          size="small" 
                          onClick={handleCancelEdit}
                          sx={{ ml: 1 }}
                        >
                          Cancel
                        </Button>
                      </>
                    )}
                  </Box>
                </Box>
                <Divider sx={{ mb: 4 }} />
                
                {/* Manifest Details Grid */}
                <Box sx={{ 
                  display: 'grid', 
                  gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr', md: '1fr 1fr 1fr' }, 
                  gap: { xs: 2, md: 4 }
                }}>
                  {/* Identification Section */}
                  <Box sx={{ gridColumn: { xs: '1', sm: '1 / span 2', md: '1 / span 3' }, mb: 3 }}>
                    <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 'bold', color: theme.palette.primary.main }}>
                      Identification
                    </Typography>
                    <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr', md: '1fr 1fr 1fr' }, gap: 2 }}>
                      <Box>
                        <Typography variant="body2" color="text.secondary">Tracking Number</Typography>
                        <Typography variant="body1" sx={{ fontWeight: 500, display: 'flex', alignItems: 'center', mt: 1 }}>
                          <TimelineIcon sx={{ mr: 1, color: theme.palette.primary.main }} />
                          {manifest.trackingNo}
                        </Typography>
                      </Box>
                      
                      <Box>
                        <Typography variant="body2" color="text.secondary">Internal ID</Typography>
                        {editMode ? (
                          <TextField
                            fullWidth
                            size="small"
                            value={editedManifest?.internalId || ''}
                            onChange={(e) => setEditedManifest({...editedManifest!, internalId: e.target.value})}
                            InputProps={{ readOnly: true }}
                            helperText="Auto-generated from container number and sequence number"
                            sx={{ mt: 1 }}
                          />
                        ) : (
                          <Typography variant="body1" sx={{ fontWeight: 500, display: 'flex', alignItems: 'center', mt: 1 }}>
                            <TimelineIcon sx={{ mr: 1, color: theme.palette.primary.main }} />
                            {manifest.internalId || (manifest.container?.containerNo && manifest.sequenceNo ? 
                              `${manifest.container.containerNo}-${manifest.sequenceNo}` : '-')}
                          </Typography>
                        )}
                      </Box>

                      <Box>
                        <Typography variant="body2" color="text.secondary">Status</Typography>
                        <Typography 
                          variant="body1" 
                          component="div" 
                          sx={{ display: 'flex', alignItems: 'center', mt: 1 }}
                        >
                          <Tooltip title="Click to update manifest status">
                            <Chip 
                              icon={getStatusIcon(manifest.status)}
                              label={manifest.status.replace(/_/g, ' ')}
                              size="small"
                              sx={{ 
                                ...getStatusChipColor(manifest.status),
                                fontWeight: 'medium',
                                fontSize: '0.775rem',
                                cursor: 'pointer',
                                height: 28,
                                '& .MuiChip-label': { px: 1.5 }
                              }}
                              onClick={() => {
                                setNewStatus(manifest?.status || null);
                                setStatusDialogOpen(true);
                              }}
                            />
                          </Tooltip>
                        </Typography>
                      </Box>
                    </Box>
                  </Box>
                  
                  {/* Customer Information Section */}
                  <Box sx={{ gridColumn: { xs: '1', sm: '1 / span 2', md: '1 / span 3' }, mb: 3 }}>
                    <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 'bold', color: theme.palette.primary.main }}>
                      Client Information
                    </Typography>
                    <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr' }, gap: 2 }}>
                      <Box>
                        <Typography variant="body2" color="text.secondary">Client</Typography>
                        <Typography variant="body1" sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                          <PersonIcon sx={{ mr: 1, color: theme.palette.grey[600], fontSize: 'small' }} />
                          {manifest.client?.username || '-'}
                        </Typography>
                      </Box>
                      
                      <Box>
                        <Typography variant="body2" color="text.secondary">Company</Typography>
                        <Typography variant="body1" sx={{ mt: 1 }}>
                          {manifest.client?.companyName || '-'}
                        </Typography>
                      </Box>
                      
                      <Box>
                        <Typography variant="body2" color="text.secondary">Container</Typography>
                        <Typography
                          variant="body1"
                          sx={{
                            fontWeight: 500,
                            display: 'flex',
                            alignItems: 'center',
                            mt: 1,
                            cursor: 'pointer',
                            '&:hover': { textDecoration: 'underline' },
                          }}
                          onClick={() => {
                            const containerNo = manifest.container?.containerNo;
                            if (containerNo) {
                              safeNavigate(navigate, `/containers/${containerNo}`);
                            }
                          }}
                        >
                          <LocalShippingOutlinedIcon sx={{ mr: 1, color: theme.palette.grey[600], fontSize: 'small' }} />
                          {manifest.container?.containerNo || '-'}
                        </Typography>
                      </Box>
                    </Box>
                  </Box>
                  
                  {/* Package Details Section */}
                  <Box sx={{ gridColumn: { xs: '1', sm: '1 / span 2', md: '1 / span 3' }, mb: 3 }}>
                    <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 'bold', color: theme.palette.primary.main }}>
                      Package Details
                    </Typography>
                    <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr', md: '1fr 1fr 1fr' }, gap: 2 }}>
                      <Box>
                        <Typography variant="body2" color="text.secondary">Weight</Typography>
                        {editMode ? (
                          <TextField
                            fullWidth
                            size="small"
                            type="number"
                            value={editedManifest?.weight || 0}
                            onChange={(e) => setEditedManifest({...editedManifest!, weight: parseFloat(e.target.value) || 0})}
                            InputProps={{ inputProps: { min: 0, step: 0.1 } }}
                            sx={{ mt: 1 }}
                          />
                        ) : (
                          <Typography variant="body1" sx={{ mt: 1 }}>
                            {manifest.weight} kg
                          </Typography>
                        )}
                      </Box>
                      
                      <Box>
                        <Typography variant="body2" color="text.secondary">CBM</Typography>
                        {editMode ? (
                          <TextField
                            fullWidth
                            size="small"
                            type="number"
                            value={editedManifest?.cbm || 0}
                            onChange={(e) => setEditedManifest({...editedManifest!, cbm: parseFloat(e.target.value) || 0})}
                            InputProps={{ inputProps: { min: 0, step: 0.01 } }}
                            sx={{ mt: 1 }}
                          />
                        ) : (
                          <Typography variant="body1" sx={{ mt: 1 }}>
                            {manifest.cbm} m³
                          </Typography>
                        )}
                      </Box>
                      
                      <Box>
                        <Typography variant="body2" color="text.secondary">Pieces</Typography>
                        {editMode ? (
                          <TextField
                            fullWidth
                            size="small"
                            type="number"
                            value={editedManifest?.pieces || 0}
                            onChange={(e) => setEditedManifest({...editedManifest!, pieces: parseInt(e.target.value) || 0})}
                            InputProps={{ inputProps: { min: 0 } }}
                            sx={{ mt: 1 }}
                          />
                        ) : (
                          <Typography variant="body1" sx={{ mt: 1 }}>
                            {manifest.pieces}
                          </Typography>
                        )}
                      </Box>
                      
                      <Box>
                        <Typography variant="body2" color="text.secondary">Number of Pallets</Typography>
                        {editMode ? (
                          <Box sx={{ mt: 2, mb: 1 }}>
                            <Typography variant="body1" sx={{ fontWeight: 500 }}>
                              {manifest.actualPalletsCount ?? 0}
                            </Typography>
                            {manifest.actualPalletsCount !== manifest.noOfPallets && manifest.noOfPallets && (
                              <Typography variant="caption" color="text.secondary">
                                (Target was: {manifest.noOfPallets})
                              </Typography>
                            )}
                            <Typography variant="caption" color="text.secondary" display="block" sx={{ mt: 0.5 }}>
                              The number of pallets is automatically calculated based on created pallets
                            </Typography>
                          </Box>
                        ) : (
                          <Typography variant="body1" sx={{ mt: 1 }}>
                            {manifest.actualPalletsCount ?? 0}
                            {manifest.actualPalletsCount !== manifest.noOfPallets && manifest.noOfPallets && (
                              <Typography component="span" variant="caption" color="text.secondary" sx={{ ml: 1 }}>
                                (Target: {manifest.noOfPallets})
                              </Typography>
                            )}
                          </Typography>
                        )}
                      </Box>
                      
                      <Box>
                        <Typography variant="body2" color="text.secondary" display="flex" alignItems="center">
                          <LocationOnIcon fontSize="small" sx={{ mr: 0.5 }} />
                          Location Zone
                        </Typography>
                        {editMode ? (
                          <FormControl 
                            fullWidth 
                            sx={{ mt: 1 }} 
                            size="small"
                          >
                            <InputLabel id="location-label">Location Zone</InputLabel>
                            <Select
                              labelId="location-label"
                              id="location-select"
                              value={editedManifest?.location || ''}
                              onChange={handleLocationChange}
                              label="Location Zone"
                              MenuProps={{
                                sx: {
                                  zIndex: 10001,
                                  '& .MuiPaper-root': {
                                    zIndex: 10001
                                  }
                                }
                              }}
                            >
                              <MenuItem value="">
                                <em>None</em>
                              </MenuItem>
                              {locationZones.map(zone => (
                                <MenuItem key={zone.id} value={zone.name}>
                                  {zone.name}
                                </MenuItem>
                              ))}
                            </Select>
                            <FormHelperText>
                              Manual selection available, but normally determined by postal code
                            </FormHelperText>
                          </FormControl>
                        ) : (
                          <Stack direction="row" spacing={1} alignItems="center" sx={{ mt: 1 }}>
                            <Typography variant="body1">
                              {manifest.location || 'N/A'}
                            </Typography>
                            <Tooltip title="Location can be manually selected or determined automatically based on postal code">
                              <IconButton size="small">
                                <InfoIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                          </Stack>
                        )}
                      </Box>
                      
                      <Box>
                        <Typography variant="body2" color="text.secondary">Driver</Typography>
                        {editMode ? (
                          <FormControl fullWidth size="small" sx={{ mt: 1 }}>
                            <InputLabel id="driver-label">Driver</InputLabel>
                            <Select
                              labelId="driver-label"
                              id="driver-select"
                              label="Driver"
                              value={editedManifest?.driver?.username || ''}
                              onChange={(e) => {
                                const value = e.target.value;
                                setEditedManifest({
                                  ...editedManifest!,
                                  driver: value ? { username: value } : null
                                });
                              }}
                              MenuProps={{
                                sx: {
                                  zIndex: 10001,
                                  '& .MuiPaper-root': {
                                    zIndex: 10001
                                  }
                                }
                              }}
                            >
                              <MenuItem value="">
                                <em>Not Assigned</em>
                              </MenuItem>
                              {drivers.map((driver) => (
                                <MenuItem key={driver.username} value={driver.username}>
                                  {driver.username}
                                </MenuItem>
                              ))}
                            </Select>
                          </FormControl>
                        ) : (
                          <Typography variant="body1" sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                            <PersonIcon sx={{ mr: 1, color: theme.palette.grey[600], fontSize: 'small' }} />
                            {manifest.driver?.username || '-'}
                          </Typography>
                        )}
                      </Box>
                      
                      <Box>
                        <Typography variant="body2" color="text.secondary">Delivery Vehicle</Typography>
                        {editMode ? (
                          <FormControl fullWidth size="small" sx={{ mt: 1 }}>
                            <Select
                              value={vehicleTypes.some(vt => vt.name === editedManifest?.deliveryVehicle) ? editedManifest?.deliveryVehicle : ''}
                              onChange={(e) => setEditedManifest({...editedManifest!, deliveryVehicle: e.target.value})}
                              displayEmpty
                            >
                              <MenuItem value="">
                                <em>Auto-assign</em>
                              </MenuItem>
                              {vehicleTypes.map((vehicleType) => (
                                <MenuItem key={vehicleType.id} value={vehicleType.name}>
                                  {vehicleType.name}
                                </MenuItem>
                              ))}
                            </Select>
                            <Typography variant="caption" color="text.secondary" sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                              Will be auto-assigned based on weight, CBM, and pieces if left empty
                              <Tooltip title="The system will automatically determine the appropriate vehicle type based on the manifest's weight, CBM, and number of pieces. You can manually select a different vehicle type if needed." arrow>
                                <IconButton size="small" sx={{ ml: 0.5, p: 0 }}>
                                  <InfoIcon fontSize="small" color="action" />
                                </IconButton>
                              </Tooltip>
                            </Typography>
                          </FormControl>
                        ) : (
                          <Typography variant="body1" sx={{ mt: 1 }}>
                            {manifest.deliveryVehicle || 'Auto-assigned'}
                          </Typography>
                        )}
                      </Box>

                      <Box>
                        <Typography variant="body2" color="text.secondary">Created Date</Typography>
                        <Typography variant="body1" sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                          <CalendarTodayIcon sx={{ mr: 1, color: theme.palette.grey[600], fontSize: 'small' }} />
                          {formatDateTime(manifest.createdDate)}
                        </Typography>
                      </Box>
                      
                      <Box>
                        <Typography variant="body2" color="text.secondary">Delivery Date</Typography>
                        {editMode ? (
                          <SplitDeliveryDatePicker
                            label=""
                              value={editedManifest.deliveryDate ? new Date(editedManifest.deliveryDate) : null}
                              onChange={(newValue) => handleDateChange('deliveryDate', newValue)}
                            onTimeSlotChange={handleTimeSlotChange}
                            timeSlotValue={editedManifest.timeSlot || getTimeSlotIdFromDate(editedManifest.deliveryDate)}
                            fullWidth
                              slotProps={{ 
                              datePicker: {
                                textField: { size: "small" },
                                popper: { sx: { zIndex: 10001 } }
                              }
                              }}
                            sx={{ mt: 1 }}
                            />
                        ) : (
                          <Box sx={{ mt: 1 }}>
                            <Typography variant="body1" sx={{ display: 'flex', alignItems: 'center' }}>
                            <EventAvailableIcon sx={{ mr: 1, color: theme.palette.grey[600], fontSize: 'small' }} />
                              {formatDeliveryDate(manifest.deliveryDate)}
                          </Typography>
                            <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5, ml: 3 }}>
                              Time Slot: {formatTimeSlot(manifest.timeSlot || getTimeSlotIdFromDate(manifest.deliveryDate))}
                            </Typography>
                          </Box>
                        )}
                      </Box>
                      
                      <Box>
                        <Typography variant="body2" color="text.secondary">Delivered Date</Typography>
                        {editMode ? (
                          <LocalizationProvider dateAdapter={AdapterDateFns}>
                            <DateTimePicker
                              label="Delivered Date"
                              value={editedManifest?.deliveredDate ? new Date(editedManifest.deliveredDate) : null}
                              onChange={(newValue) => handleDateChange('deliveredDate', newValue)}
                              format="dd MMM yyyy HH:mm"
                              ampm={false}
                              slotProps={{ 
                                textField: { 
                                  fullWidth: true, 
                                  size: "small", 
                                  sx: { mt: 1 }
                                },
                                popper: { sx: { zIndex: 10001 } }
                              }}
                            />
                          </LocalizationProvider>
                        ) : (
                          <Typography variant="body1" sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                            <CheckCircleIcon sx={{ mr: 1, color: theme.palette.grey[600], fontSize: 'small' }} />
                            {formatDateTime(manifest.deliveredDate)}
                          </Typography>
                        )}
                      </Box>
                    </Box>
                  </Box>
                  
                  {/* Shipping Information Section */}
                  <Box sx={{ gridColumn: { xs: '1', sm: '1 / span 2', md: '1 / span 3' }, mb: 3 }}>
                    <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 'bold', color: theme.palette.primary.main }}>
                      Shipping Information
                    </Typography>
                    <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr' }, gap: 2 }}>
                      <Box>
                        <Typography variant="body2" color="text.secondary">Customer Name</Typography>
                        {editMode ? (
                          <TextField
                            fullWidth
                            size="small"
                            value={editedManifest?.customerName || ''}
                            onChange={(e) => setEditedManifest({...editedManifest!, customerName: e.target.value})}
                            error={!!formErrors.customerName}
                            helperText={formErrors.customerName}
                            sx={{ mt: 1 }}
                          />
                        ) : (
                          <Typography variant="body1" sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                            <PersonIcon sx={{ mr: 1, color: theme.palette.grey[600], fontSize: 'small' }} />
                            {manifest.customerName}
                          </Typography>
                        )}
                      </Box>
                      
                      <Box>
                        <Typography variant="body2" color="text.secondary">Phone Number</Typography>
                        {editMode ? (
                          <TextField
                            fullWidth
                            size="small"
                            value={editedManifest?.phoneNo || ''}
                            onChange={(e) => setEditedManifest({...editedManifest!, phoneNo: e.target.value})}
                            sx={{ mt: 1 }}
                          />
                        ) : (
                          <Typography variant="body1" sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                            <PhoneIcon sx={{ mr: 1, color: theme.palette.grey[600], fontSize: 'small' }} />
                            {manifest.phoneNo || '-'}
                          </Typography>
                        )}
                      </Box>

                      <Box sx={{ gridColumn: '1 / span 2' }}>
                        <Typography variant="body2" color="text.secondary">Address</Typography>
                        {editMode ? (
                          <TextField
                            fullWidth
                            size="small"
                            multiline
                            rows={2}
                            value={editedManifest?.address || ''}
                            onChange={(e) => setEditedManifest({...editedManifest!, address: e.target.value})}
                            error={!!formErrors.address}
                            helperText={formErrors.address}
                            sx={{ mt: 1 }}
                          />
                        ) : (
                          <Typography variant="body1" sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                            <HomeIcon sx={{ mr: 1, color: theme.palette.grey[600], fontSize: 'small' }} />
                            {manifest.address}
                          </Typography>
                        )}
                      </Box>

                      <Box>
                        <Typography variant="body2" color="text.secondary">Postal Code</Typography>
                        {editMode ? (
                          <TextField
                            fullWidth
                            size="small"
                            value={editedManifest?.postalCode || ''}
                            onChange={(e) => setEditedManifest({...editedManifest!, postalCode: e.target.value})}
                            sx={{ mt: 1 }}
                            helperText="Changing postal code will update the location zone automatically"
                          />
                        ) : (
                          <Typography variant="body1" sx={{ mt: 1 }}>
                            {manifest.postalCode || '-'}
                          </Typography>
                        )}
                      </Box>
                      
                      <Box>
                        <Typography variant="body2" color="text.secondary">Country</Typography>
                        {editMode ? (
                          <TextField
                            fullWidth
                            size="small"
                            value={editedManifest?.country || ''}
                            onChange={(e) => setEditedManifest({...editedManifest!, country: e.target.value})}
                            sx={{ mt: 1 }}
                          />
                        ) : (
                          <Typography variant="body1" sx={{ mt: 1 }}>
                            {manifest.country || '-'}
                          </Typography>
                        )}
                      </Box>
                    </Box>
                  </Box>

                  {/* Remarks (CS) Section */}
                  <Box sx={{ gridColumn: { xs: '1', sm: '1 / span 2', md: '1 / span 3' } }}>
                    <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 'bold', color: theme.palette.primary.main }}>
                      Remarks (CS)
                    </Typography>
                    {editMode ? (
                      <TextField
                        fullWidth
                        multiline
                        rows={3}
                        size="small"
                        value={editedManifest?.remarks || ''}
                        onChange={(e) => setEditedManifest({...editedManifest!, remarks: e.target.value})}
                        placeholder="Add any additional notes or remarks here"
                      />
                    ) : (
                      <Typography variant="body1" sx={{ 
                        mt: 1, 
                        p: 2, 
                        bgcolor: 'grey.50',
                        borderRadius: 1,
                        minHeight: '60px'
                      }}>
                        {manifest.remarks || 'No remarks'}
                      </Typography>
                    )}
                  </Box>
                </Box>
              </Paper>
              
              {/* Pallet Management Section */}
              {manifest && (
                <Paper
                  elevation={1}
                  sx={{ 
                    p: { xs: 2, md: 4 }, 
                    mb: 4, 
                    borderRadius: 2, 
                    border: '1px solid rgba(0,0,0,0.08)',
                    maxWidth: '100%',
                    overflowX: 'auto',
                    boxShadow: '0 4px 12px rgba(0,0,0,0.05)'
                  }}
                >
                  <PalletManagement 
                    manifestTrackingNo={manifest.trackingNo}
                    manifestPieces={manifest.pieces}
                    manifest={manifest}
                    readonly={editMode} // Disable pallet editing when manifest is in edit mode
                    onPalletsChange={(palletCount) => {
                      // Update the manifest's actualPalletsCount property
                      setManifest(prevManifest => {
                        if (!prevManifest) return prevManifest;
                        return {
                          ...prevManifest,
                          actualPalletsCount: palletCount
                        };
                      });
                    }}
                    onPalletOperationComplete={handlePalletOperationComplete}
                  />
                </Paper>
              )}
            </Box>
            
            {/* Right Column - Tracking History */}
            <Box sx={{ flex: '1 1 35%', minWidth: '350px', maxWidth: '450px' }}>
              <Paper
                elevation={1}
                sx={{ 
                  borderRadius: 2, 
                  border: '1px solid rgba(0,0,0,0.08)',
                  boxShadow: '0 2px 8px rgba(0,0,0,0.04)',
                  position: 'sticky',
                  top: 20
                }}
              >
                <ManifestTrackingHistory
                  trackingNo={manifest.trackingNo}
                  ref={trackingHistoryRef}
                />
              </Paper>
            </Box>
          </Box>
          
          {/* Status Update Dialog */}
          <Dialog 
            open={statusDialogOpen} 
            onClose={() => setStatusDialogOpen(false)}
            maxWidth="sm"
            fullWidth
            PaperProps={{
              sx: { p: 1 }
            }}
          >
            <DialogTitle sx={{ 
              borderBottom: 1, 
              borderColor: 'divider',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between'
            }}>
              <Box display="flex" alignItems="center">
                <TimelineIcon sx={{ mr: 1 }} />
                Update Manifest Status
              </Box>
              {manifest && (
                <Chip 
                  label={manifest.status}
                  size="small"
                  icon={getStatusIcon(manifest.status)}
                  sx={{ 
                    ...getStatusChipColor(manifest.status),
                    fontWeight: 'medium',
                    fontSize: '0.75rem'
                  }}
                />
              )}
            </DialogTitle>
            <DialogContent sx={{ py: 3 }}>
              {manifest && (
                <>
                  <Box sx={{ mb: 3 }}>
                    <Typography variant="subtitle2" gutterBottom>
                      Manifest: {manifest.trackingNo}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Select the new status for this manifest:
                    </Typography>
                  </Box>
                  
                  <Box sx={{ 
                    display: 'grid', 
                    gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr' },
                    gap: 2 
                  }}>
                    {Object.values(ManifestStatus).map((status) => {
                      const isSelected = newStatus === status;
                      const isCurrentStatus = manifest.status === status;
                      const statusColors = getStatusChipColor(status);
                      
                      return (
                        <Paper
                          key={status}
                          elevation={isSelected ? 4 : 1}
                          sx={{
                            p: 2,
                            borderRadius: 2,
                            border: isSelected ? `1px solid ${theme.palette.primary.main}` : '1px solid transparent',
                            cursor: isCurrentStatus ? 'default' : 'pointer',
                            backgroundColor: isSelected ? alpha(statusColors.bg, 0.5) : 'background.paper',
                            opacity: isCurrentStatus ? 0.7 : 1,
                            transition: 'all 0.2s',
                            '&:hover': {
                              backgroundColor: isCurrentStatus ? 'inherit' : alpha(statusColors.bg, 0.3),
                              transform: isCurrentStatus ? 'none' : 'translateY(-2px)',
                            },
                          }}
                          onClick={() => {
                            if (!isCurrentStatus) {
                              setNewStatus(status);
                            }
                          }}
                        >
                          <Box sx={{ 
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'space-between'
                          }}>
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              {getStatusIcon(status)}
                              <Typography sx={{ ml: 1, fontWeight: isSelected ? 'bold' : 'normal' }}>
                                {status.replace(/_/g, ' ')}
                              </Typography>
                            </Box>
                            {isCurrentStatus && (
                              <Chip size="small" label="Current" color="primary" sx={{ height: 20 }} />
                            )}
                          </Box>
                          
                          {isSelected && (
                            <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                              {isCurrentStatus 
                                ? "This is the current status"
                                : `Change status from ${manifest.status.replace(/_/g, ' ')} to ${status.replace(/_/g, ' ')}`
                              }
                            </Typography>
                          )}
                        </Paper>
                      );
                    })}
                  </Box>
                </>
              )}
            </DialogContent>
            <DialogActions sx={{ px: 3, py: 2, borderTop: 1, borderColor: 'divider' }}>
              <Button onClick={() => setStatusDialogOpen(false)}>Cancel</Button>
              <Button 
                onClick={handleSaveStatus} 
                variant="contained" 
                color="primary"
                disabled={!newStatus || (manifest && newStatus === manifest.status)}
                startIcon={<CheckCircleIcon />}
              >
                Update Status
              </Button>
            </DialogActions>
          </Dialog>
          
          {/* Label Dialog */}
          {manifest && (
            <ManifestLabelDialog
              open={labelDialogOpen}
              onClose={handleCloseLabelDialog}
              manifest={manifest}
              initialLabelCount={manifestService.getLabelCount(manifest.trackingNo) || manifest.pieces}
              onSaveLabelCount={handleSaveLabelCount}
            />
          )}
        </>
      ) : (
        <Alert severity="info">Manifest not found.</Alert>
      )}
    </Box>
  );
};

export default ManifestDetail; 