import { useState, useMemo } from 'react';
import { Manifest, ManifestStatus } from '../../../types/manifest';

interface FilterState {
  search: string;
  status: ManifestStatus[];
  dateRange: {
    start: Date | null;
    end: Date | null;
  };
  dateFieldType: 'createdDate' | 'deliveryDate' | 'deliveredDate';
  location: string;
  deliveryVehicle: string;
  hasDeliveryDate: boolean;
}

export function useManifestFilters(manifests: Manifest[]) {
  const [filters, setFilters] = useState<FilterState>({
    search: '',
    status: [],
    dateRange: {
      start: null,
      end: null
    },
    dateFieldType: 'createdDate',
    location: '',
    deliveryVehicle: '',
    hasDeliveryDate: false
  });
  
  // Apply filters to get filtered manifests
  const filteredManifests = useMemo(() => {
    return manifests.filter(manifest => {
      // Search filter
      if (filters.search) {
        const searchLower = filters.search.toLowerCase();
        const matchesSearch = 
          (manifest.trackingNo || '').toLowerCase().includes(searchLower) ||
          (manifest.customerName || '').toLowerCase().includes(searchLower) ||
          (manifest.internalId || '').toLowerCase().includes(searchLower);
          
        if (!matchesSearch) return false;
      }
      
      // Status filter
      if (filters.status.length > 0 && !filters.status.includes(manifest.status)) {
        return false;
      }
      
      // Date range filter
      if (filters.dateRange.start || filters.dateRange.end) {
        // Get the date based on the selected date field type
        let dateToFilter: Date | null = null;
        
        switch (filters.dateFieldType) {
          case 'createdDate':
            dateToFilter = manifest.createdDate ? new Date(manifest.createdDate) : null;
            break;
          case 'deliveryDate':
            dateToFilter = manifest.deliveryDate ? new Date(manifest.deliveryDate) : null;
            break;
          case 'deliveredDate':
            dateToFilter = manifest.deliveredDate ? new Date(manifest.deliveredDate) : null;
            break;
          default:
            dateToFilter = manifest.createdDate ? new Date(manifest.createdDate) : null;
        }
        
        if (!dateToFilter) return false;
        
        if (filters.dateRange.start && dateToFilter < filters.dateRange.start) {
          return false;
        }
        
        if (filters.dateRange.end) {
          // Set end date to end of day
          const endDate = new Date(filters.dateRange.end);
          endDate.setHours(23, 59, 59, 999);
          
          if (dateToFilter > endDate) {
            return false;
          }
        }
      }
      
      // Location filter
      if (filters.location && manifest.location !== filters.location) {
        return false;
      }
      
      // Delivery vehicle filter
      if (filters.deliveryVehicle && manifest.deliveryVehicle !== filters.deliveryVehicle) {
        return false;
      }
      
      // Has delivery date filter
      if (filters.hasDeliveryDate && !manifest.deliveryDate) {
        return false;
      }
      
      return true;
    }).sort((a, b) => {
      // Sort by internalId - first by letter prefix, then by numbers
      const aId = a.internalId || '';
      const bId = b.internalId || '';
      
      // Extract letter prefix and numbers
      const aPrefix = aId.match(/^[A-Za-z]+/)?.[0] || '';
      const bPrefix = bId.match(/^[A-Za-z]+/)?.[0] || '';
      
      // If prefixes are different, sort by prefix
      if (aPrefix !== bPrefix) {
        return aPrefix.localeCompare(bPrefix);
      }
      
      // If prefixes are the same, extract and compare the numbers
      const aNum = parseInt(aId.replace(/^[A-Za-z]+\-?/, '')) || 0;
      const bNum = parseInt(bId.replace(/^[A-Za-z]+\-?/, '')) || 0;
      
      return aNum - bNum;
    });
  }, [manifests, filters]);
  
  const handleFilterChange = (field: string, value: any) => {
    setFilters(prev => {
      // Handle nested fields like dateRange.start
      if (field.includes('.')) {
        const [parent, child] = field.split('.');
        if (parent === 'dateRange') {
          return {
            ...prev,
            dateRange: {
              ...prev.dateRange,
              [child]: value
            }
          };
        }
      }
      
      return {
        ...prev,
        [field]: value
      };
    });
  };
  
  const resetFilters = () => {
    setFilters({
      search: '',
      status: [],
      dateRange: {
        start: null,
        end: null
      },
      dateFieldType: 'createdDate',
      location: '',
      deliveryVehicle: '',
      hasDeliveryDate: false
    });
  };
  
  return {
    filters,
    filteredManifests,
    handleFilterChange,
    resetFilters
  };
} 