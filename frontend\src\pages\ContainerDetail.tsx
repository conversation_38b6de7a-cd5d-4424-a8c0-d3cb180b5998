// @ts-nocheck - TODO: Fix TypeScript errors properly in a follow-up task
import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Button,
  Typography,
  Alert,
  Paper,
  CircularProgress,
  Divider,
  Stack,
  Chip,
  Tooltip,
  IconButton,
  useTheme,
  Toolbar,
  Breadcrumbs,
  Link,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  FormHelperText,
  InputAdornment,
  LinearProgress,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Menu,
  ListItemIcon,
  ListItemText,
  Autocomplete,
  Checkbox,
  FormControlLabel,
  Snackbar,
  Grid,
  Tabs,
  Tab,
  Collapse,
  SelectChangeEvent,
  DialogContentText,
} from '@mui/material';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import EditIcon from '@mui/icons-material/Edit';
import EventAvailableIcon from '@mui/icons-material/EventAvailable';
import LocalShippingIcon from '@mui/icons-material/LocalShipping';
import LocalShippingOutlinedIcon from '@mui/icons-material/LocalShippingOutlined';
import DirectionsBoatOutlinedIcon from '@mui/icons-material/DirectionsBoatOutlined';
import PersonIcon from '@mui/icons-material/Person';
import TimelineIcon from '@mui/icons-material/Timeline';
import WarehouseIcon from '@mui/icons-material/Warehouse';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import DirectionsBoatIcon from '@mui/icons-material/DirectionsBoat';
import ConstructionIcon from '@mui/icons-material/Construction';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import RefreshIcon from '@mui/icons-material/Refresh';
import ViewColumnIcon from '@mui/icons-material/ViewColumn';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import PrintIcon from '@mui/icons-material/Print';
import AddIcon from '@mui/icons-material/Add';
import VisibilityIcon from '@mui/icons-material/Visibility';
import DeleteIcon from '@mui/icons-material/Delete';
import LabelIcon from '@mui/icons-material/Label';
import AutorenewIcon from '@mui/icons-material/Autorenew';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import ViewInArIcon from '@mui/icons-material/ViewInAr';
import { Info as InfoIcon } from '@mui/icons-material';
import { DataGrid } from '@mui/x-data-grid';
import type { GridColDef, GridRenderCellParams, GridApiRef } from '@mui/x-data-grid';
import { useAuth } from '../contexts/AuthContext';
import usePageTitle from '../hooks/usePageTitle';
import containerService from '../services/container.service';
import manifestService from '../services/manifest.service';
import locationService from '../services/location.service'; // Import location service
import userService from '../services/user.service'; // Import user service for clients and drivers
import * as vehicleService from '../services/vehicle.service'; // Import vehicle service
import { Container, ContainerStatus } from '../types/Container';
import { Manifest, ManifestStatus } from '../types/manifest';
import { LocationZone } from '../types/location'; // Import LocationZone type
import { Client, Driver } from '../types/User'; // Import Client and Driver types
import { VehicleType } from '../types/Vehicle'; // Import VehicleType interface
import { format } from 'date-fns';
import { alpha } from '@mui/material/styles';
import { useToast } from '../contexts/ToastContext';
import ManifestLabelDialog from '../components/ManifestLabelDialog';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import CloudDownloadIcon from '@mui/icons-material/CloudDownload';
import FileDownloadIcon from '@mui/icons-material/FileDownload';
import UploadFileIcon from '@mui/icons-material/UploadFile';
import CloseIcon from '@mui/icons-material/Close';
import Excel from 'exceljs';
import { saveAs } from 'file-saver';
import BusinessIcon from '@mui/icons-material/Business';
import FormatListNumberedIcon from '@mui/icons-material/FormatListNumbered';
import GetAppIcon from '@mui/icons-material/GetApp';
import SearchIcon from '@mui/icons-material/Search';
import ClearIcon from '@mui/icons-material/Clear';
import FilterListIcon from '@mui/icons-material/FilterList';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import ClearAllIcon from '@mui/icons-material/ClearAll';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import PhoneIcon from '@mui/icons-material/Phone';
import HomeIcon from '@mui/icons-material/Home';
import SaveIcon from '@mui/icons-material/Save';
import { normalizeDate, getDaysDifference, formatDateString, formatDate, formatTimestamp, toSingaporeTime, formatDateOnly } from '../utils/dateUtils';
import PalletManagementDialog from '../components/PalletManagementDialog';
import DeliveryDateDialog from '../components/DeliveryDateDialog';
import QuickEditCell from '../components/QuickEditCell';
import QuickDeliveryVehicleDialog from '../components/QuickDeliveryVehicleDialog';
import SplitDeliveryDatePicker from '../components/SplitDeliveryDatePicker';
import { formatDateTime, formatDeliveryDate, formatTimeSlot, getTimeSlotIdFromDate, formatDateForExport, parseDateString } from '../utils/dateUtils';

const ContainerDetail: React.FC = () => {
  const { containerNo } = useParams<{ containerNo: string }>();
  const navigate = useNavigate();
  const { currentUser } = useAuth();
  const theme = useTheme();
  const toast = useToast();
  
  // Set page title with container number
  usePageTitle(containerNo ? `Container ${containerNo}` : 'Container Detail');
  
  const [container, setContainer] = useState<Container | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [editedContainer, setEditedContainer] = useState<Container | null>(null);
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [statusDialogOpen, setStatusDialogOpen] = useState(false);
  const [newStatus, setNewStatus] = useState<ContainerStatus | null>(null);
  const [editMode, setEditMode] = useState(false);
  
  // Manifest list related states
  const [manifests, setManifests] = useState<Manifest[]>([]);
  const [manifestLoading, setManifestLoading] = useState(false);
  const [manifestError, setManifestError] = useState<string | null>(null);
  const [selectedManifests, setSelectedManifests] = useState<Manifest[]>([]);
  const [dataGridKey, setDataGridKey] = useState<number>(0);

  // Edit manifest related states
  const [editManifestDialogOpen, setEditManifestDialogOpen] = useState(false);
  const [selectedManifestForEdit, setSelectedManifestForEdit] = useState<Manifest | null>(null);
  
  // Filter states
  const [filters, setFilters] = useState({
    search: '',
    status: [] as ManifestStatus[],
    dateRange: {
      start: null as Date | null,
      end: null as Date | null
    },
    location: '',
    deliveryVehicle: '',
    hasDeliveryDate: false,
    advancedFiltersOpen: true // This will remain true by default and we won't toggle it anymore
  });
  
  const [visibleColumns, setVisibleColumns] = useState<Record<string, boolean>>(() => {
    // Try to load saved column preferences from localStorage
    try {
      const saved = localStorage.getItem('manifestColumnVisibility');
      if (saved) {
        return JSON.parse(saved);
      }
    } catch (error) {
      console.error('Failed to load column visibility from localStorage:', error);
    }
    
    // Default visible columns
    return {
    trackingNo: true,
    internalId: true,
    customerName: true,
    status: true,
      pieces: true,
      inboundPieces: true, // Show inbound pieces by default
    weight: true,
    location: true,
      createdDate: true,
      deliveryDate: true,
      timeSlot: true, // Show time slot by default
    actions: true,
      client: false,
      container: false,
      driverRemarks: false,
      driver: false,
      phoneNo: false,
      address: false,
      postalCode: false,
      country: false,
      cbm: false,
      actualPalletsCount: true,
      deliveryVehicle: false,
      deliveredDate: false,
      remarks: false, // Add remarks column
    };
  });
  
  // Reference to DataGrid API for custom operations
  const apiRef = useRef<GridApiRef>();
  
  const [labelDialogOpen, setLabelDialogOpen] = useState(false);
  const [selectedLabelManifest, setSelectedLabelManifest] = useState<Manifest | null>(null);
  
  // Pallet management dialog states
  const [palletManagementDialogOpen, setPalletManagementDialogOpen] = useState(false);
  const [selectedPalletManifest, setSelectedPalletManifest] = useState<Manifest | null>(null);
  
  // Bulk upload states
  const [bulkUploadDialogOpen, setBulkUploadDialogOpen] = useState(false);
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  const [bulkUploadResults, setBulkUploadResults] = useState<{
    total: number;
    successful: number;
    failed: number;
    errors: Array<{ row: number; error: string }>;
  } | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  // Add new state variables for manifest creation dialog
  const [openManifestDialog, setOpenManifestDialog] = useState(false);
  const [manifestFormData, setManifestFormData] = useState<any>({
    trackingNo: '',
    client: { username: '' },
    container: { containerNo: '' },
    driver: { username: '' },
    sequenceNo: 0,
    status: ManifestStatus.CREATED,
    customerName: '',
    phoneNo: '',
    address: '',
    postalCode: '',
    country: '',
    pieces: 0,
    cbm: 0,
    actualPalletsCount: 0,
    location: '',
    deliveryDate: null,
    deliveredDate: null,
    deliveryVehicle: '',
    driverRemarks: '',
    remarks: '',
    weight: 0,
    createdDate: null,
  });
  const [manifestFormError, setManifestFormError] = useState<string | null>(null);
  const [manifestFormLoading, setManifestFormLoading] = useState(false);
  const [clients, setClients] = useState<Client[]>([]);
  const [drivers, setDrivers] = useState<Driver[]>([]);
  
  // Add state for location zones
  const [locationZones, setLocationZones] = useState<LocationZone[]>([]);
  const [locationsLoading, setLocationsLoading] = useState(false);
  
  // Add state for vehicle types
  const [vehicleTypes, setVehicleTypes] = useState<VehicleType[]>([]);
  const [vehicleTypesLoading, setVehicleTypesLoading] = useState(false);
  
  // Add a new state for tracking which manifest is being edited
  const [editingDeliveryDate, setEditingDeliveryDate] = useState<string | null>(null);
  const [tempDeliveryDate, setTempDeliveryDate] = useState<Date | null>(null);
  
  // First, add a new state for the dialog
  const [deliveryDateDialogOpen, setDeliveryDateDialogOpen] = useState(false);
  
  // States for confirmation dialog
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [confirmAction, setConfirmAction] = useState<() => void>(() => {});
  const [confirmTitle, setConfirmTitle] = useState('');
  const [confirmMessage, setConfirmMessage] = useState('');
  
  // States for quick edit dialog
  const [editValueDialogOpen, setEditValueDialogOpen] = useState(false);
  const [editValueField, setEditValueField] = useState<'cbm' | 'actualPalletsCount'>('cbm');
  const [editValueTrackingNo, setEditValueTrackingNo] = useState<string | null>(null);
  const [editValueTemp, setEditValueTemp] = useState<number | null>(null);
  
  // States for quick delivery vehicle edit
  const [quickVehicleDialogOpen, setQuickVehicleDialogOpen] = useState(false);
  const [selectedManifestForVehicleEdit, setSelectedManifestForVehicleEdit] = useState<Manifest | null>(null);
  
  // States for bulk delivery date update
  const [bulkDeliveryDateDialogOpen, setBulkDeliveryDateDialogOpen] = useState(false);
  const [bulkDeliveryDate, setBulkDeliveryDate] = useState<Date | null>(null);
  
  useEffect(() => {
    if (!containerNo) {
      setError('Container number is required');
      setLoading(false);
      return;
    }
    
    // Reset states when container number changes
    setContainer(null);
    setError(null);
    setLoading(true);
    
    fetchContainerDetail();
    fetchAllManifests();
  }, [containerNo, currentUser?.token]);
  
  // Add a new useEffect to watch for container status changes
  useEffect(() => {
    // If container data is loaded and status changes, refresh manifest list
    if (container && container.status) {
      console.log(`Container status detected: ${container.status} - refreshing manifest list`);
      fetchAllManifests();
    }
  }, [container?.status]);
  
  // Add useEffect to fetch location zones
  useEffect(() => {
    fetchLocationZones();
    fetchVehicleTypes();
  }, [currentUser?.token]);

  // Add event listener for selective pallet updates
  useEffect(() => {
    const handleSelectivePalletUpdate = (event: CustomEvent) => {
      const { type, manifestTrackingNo, palletCount, inboundedPieces } = event.detail;

      if (type === 'pallet_created') {
        console.log(`🎯 Selective update: Pallet created for manifest ${manifestTrackingNo}, new count: ${palletCount}, inbounded pieces: ${inboundedPieces}`);

        // Update only the specific manifest's pallet count and inbounded pieces without full refresh
        setManifests(prevManifests =>
          prevManifests.map(manifest =>
            manifest.trackingNo === manifestTrackingNo
              ? {
                  ...manifest,
                  actualPalletsCount: palletCount,
                  inboundPieces: inboundedPieces
                }
              : manifest
          )
        );

        // Mark that we've handled the selective update
        const marker = document.createElement('div');
        marker.setAttribute('data-selective-update-handled', 'true');
        marker.style.display = 'none';
        document.body.appendChild(marker);

        console.log('✅ Manifest pallet count and inbound pieces updated selectively without page refresh');
      }
    };

    // Add event listener
    window.addEventListener('pallet-operation-selective-update', handleSelectivePalletUpdate as EventListener);

    // Cleanup
    return () => {
      window.removeEventListener('pallet-operation-selective-update', handleSelectivePalletUpdate as EventListener);
    };
  }, []);

  // Add event listener for bulk manifest creation updates
  useEffect(() => {
    const handleBulkManifestCreation = (event: CustomEvent) => {
      const { type, manifests, count } = event.detail;

      if (type === 'bulk_created' && manifests && Array.isArray(manifests)) {
        console.log(`🎯 ContainerDetail: Received bulk manifest creation event - ${count} new manifests`);

        // Add the new manifests to the existing list without full refresh
        setManifests(prevManifests => {
          // Check for duplicates to avoid adding the same manifest twice
          const existingTrackingNos = new Set(prevManifests.map(m => m.trackingNo));
          const newManifests = manifests.filter((manifest: any) => !existingTrackingNos.has(manifest.trackingNo));

          if (newManifests.length > 0) {
            console.log(`📊 Adding ${newManifests.length} new manifests to ContainerDetail list`);

            // Combine existing and new manifests, then sort by creation date (newest first)
            const updatedManifests = [...prevManifests, ...newManifests];
            const sortedManifests = updatedManifests.sort((a, b) => {
              const dateA = new Date(a.createdDate || 0).getTime();
              const dateB = new Date(b.createdDate || 0).getTime();
              return dateB - dateA; // Newest first
            });

            console.log(`✅ ContainerDetail: Added ${newManifests.length} new manifests without page refresh`);
            return sortedManifests;
          } else {
            console.log('ℹ️ No new manifests to add to ContainerDetail (all already exist)');
            return prevManifests;
          }
        });
      }
    };

    // Add event listener
    window.addEventListener('manifests-bulk-created', handleBulkManifestCreation as EventListener);

    // Cleanup
    return () => {
      window.removeEventListener('manifests-bulk-created', handleBulkManifestCreation as EventListener);
    };
  }, []);
  
  // Function to fetch location zones
  const fetchLocationZones = async () => {
    if (!currentUser?.token) {
      return;
    }
    
    try {
      setLocationsLoading(true);
      const response = await locationService.getActiveLocationZones(currentUser.token);
      
      if (response.success) {
        console.log("Location zones fetched successfully:", response.data.length);
        setLocationZones(response.data);
      } else {
        console.error("Failed to fetch location zones:", response.message);
      }
    } catch (err) {
      console.error('Error fetching location zones:', err);
    } finally {
      setLocationsLoading(false);
    }
  };
  
  // Function to fetch vehicle types
  const fetchVehicleTypes = async () => {
    if (!currentUser?.token) {
      return;
    }
    
    try {
      setVehicleTypesLoading(true);
      const data = await vehicleService.getVehicleTypes();
      console.log("Vehicle types fetched successfully:", data.length);
      setVehicleTypes(data);
    } catch (err) {
      console.error('Error fetching vehicle types:', err);
    } finally {
      setVehicleTypesLoading(false);
    }
  };
  
  const fetchContainerDetail = async () => {
    if (!currentUser?.token || !containerNo) {
      setError('Authentication required');
      setLoading(false);
      return;
    }
    
    try {
      console.log(`Fetching details for container: ${containerNo}`);
      setLoading(true);
      setError(null);
      
      // Fetch container details
      const containerResponse = await containerService.getContainerByNo(containerNo, currentUser.token);
      
      if (containerResponse.success) {
        console.log("Container details fetched successfully:", containerResponse.data);
        setContainer(containerResponse.data);
      } else {
        console.error("Failed to fetch container details:", containerResponse.message);
        setError(containerResponse.message || 'Failed to fetch container details');
      }
    } catch (err: any) {
      console.error('Error fetching container details:', err);
      setError(err.response?.data?.message || 'An error occurred while fetching data');
    } finally {
      setLoading(false);
    }
  };
  
  // Function to fetch all manifests (from manifest management page)
  const fetchAllManifests = async () => {
    if (!currentUser?.token || !containerNo) {
      setManifestError('Authentication required');
      return;
    }
    
    try {
      console.log(`Starting manifest refresh for container ${containerNo}...`);
      setManifestLoading(true);
      setManifestError(null);
      
      // Use the service method instead of direct fetch to ensure correct API URL
      const response = await manifestService.getManifestsByContainer(containerNo, currentUser.token);
      
      if (response.success) {
        console.log(`Successfully fetched ${response.data.length} manifests for container ${containerNo}`);
        
        // Debug the first few manifests to check status values
        if (response.data.length > 0) {
          const sampleManifests = response.data.slice(0, Math.min(5, response.data.length));
          console.log("Manifest status check:", 
            sampleManifests.map(m => ({
              trackingNo: m.trackingNo,
              status: m.status,
              container: m.container?.containerNo
            }))
          );
        } else {
          console.log("No manifests found for this container");
        }
        
        setManifests(response.data);
      } else {
        console.error("Failed to fetch manifests:", response.message);
        setManifestError(response.message || 'Failed to fetch manifests');
      }
    } catch (err: any) {
      console.error('Error fetching manifests:', err);
      setManifestError(err.message || 'An error occurred while fetching manifests');
    } finally {
      setManifestLoading(false);
    }
  };
  
  // Function to handle manifest deletion
  const handleDeleteManifest = async (trackingNo: string) => {
    if (!currentUser?.token || !trackingNo) {
      setManifestError('Authentication required or invalid tracking number');
      return;
    }
    
    try {
      setManifestLoading(true);
      
      const response = await manifestService.deleteManifest(trackingNo, currentUser.token);
      
      if (response.success) {
        toast.success(`Manifest ${trackingNo} has been deleted successfully`);
        
        // Remove the deleted manifest from the state
        setManifests(prev => prev.filter(m => m.trackingNo !== trackingNo));
        
        // Also remove from selected manifests if it was selected
        setSelectedManifests(prev => prev.filter(m => m.trackingNo !== trackingNo));
      } else {
        toast.error(response.message || 'Failed to delete manifest');
      }
    } catch (err: any) {
      console.error('Error deleting manifest:', err);
      toast.error(err.response?.data?.message || 'An error occurred while deleting the manifest');
    } finally {
      setManifestLoading(false);
    }
  };
  
  // Function to handle bulk deletion of manifests
  const handleBulkDeleteManifests = async () => {
    if (!currentUser?.token || selectedManifests.length === 0) {
      toast.warning('Please select at least one manifest to delete');
      return;
    }
    
    try {
      setManifestLoading(true);
      
      let successCount = 0;
      let failCount = 0;
      
      // Create a copy of the tracking numbers to delete
      const trackingNumbers = selectedManifests.map(m => m.trackingNo);
      
      // Process each manifest sequentially
      for (const trackingNo of trackingNumbers) {
        try {
          const response = await manifestService.deleteManifest(trackingNo, currentUser.token);
          
          if (response.success) {
            successCount++;
            // Remove from state
            setManifests(prev => prev.filter(m => m.trackingNo !== trackingNo));
          } else {
            failCount++;
            console.error(`Failed to delete manifest ${trackingNo}: ${response.message}`);
          }
        } catch (err: any) {
          failCount++;
          console.error(`Error deleting manifest ${trackingNo}:`, err);
        }
      }
      
      // Clear selected manifests
      setSelectedManifests([]);
      
      // Show appropriate messages
      if (successCount > 0) {
        toast.success(`Successfully deleted ${successCount} manifest${successCount !== 1 ? 's' : ''}`);
      }
      
      if (failCount > 0) {
        toast.error(`Failed to delete ${failCount} manifest${failCount !== 1 ? 's' : ''}`);
      }
      
    } catch (err: any) {
      console.error('Error in bulk deletion:', err);
      toast.error(err.message || 'An error occurred during bulk deletion');
    } finally {
      setManifestLoading(false);
    }
  };
  
  // Filter manifests based on the current container
  const getFilteredManifests = useMemo(() => {
    if (!manifests.length || !containerNo) {
      console.log('No manifests to filter or missing containerNo');
      return [];
    }
    
    console.log(`Filtering ${manifests.length} manifests for container: ${containerNo}`);
    
    // Create a defensive copy and ensure each manifest has required fields
    let filtered = manifests
      .filter(m => m && m.trackingNo) // Filter out null/undefined manifests
      .map(m => {
        // Create the internal ID if it doesn't exist
        const internalId = m.internalId || (m.container?.containerNo && m.sequenceNo 
          ? `${m.container.containerNo}-${m.sequenceNo}` 
          : undefined);
        
        // For debugging status
        console.log(`Manifest ${m.trackingNo} has status: ${m.status}`);
        // For debugging pallet count
        console.log(`Manifest ${m.trackingNo} - actualPalletsCount:`, m.actualPalletsCount, 'type:', typeof m.actualPalletsCount);
        
        return {
          ...m,
          // Ensure required fields exist
          client: m.client || { username: 'Unknown', companyName: '' },
          container: m.container || { containerNo: 'Unknown' },
          status: m.status || ManifestStatus.CREATED,
          weight: m.weight || 0,
          location: m.location || '',
          deliveryVehicle: m.deliveryVehicle || '',
          // Always set internalId - if it doesn't exist in the backend, generate it
          internalId: internalId || `${containerNo}-${m.sequenceNo || '0'}`
        };
      });
    
    // Filter by the current container number
    filtered = filtered.filter(m => 
      m.container && 
      m.container.containerNo && 
      m.container.containerNo.toLowerCase() === containerNo.toLowerCase()
    );
    
    console.log(`Found ${filtered.length} manifests for container ${containerNo}`);
    
    // Apply search filters
    if (filters.search) {
      const searchTerm = filters.search.toLowerCase();
      filtered = filtered.filter(manifest => {
        return (
          (manifest.trackingNo && manifest.trackingNo.toLowerCase().includes(searchTerm)) ||
          (manifest.internalId && manifest.internalId.toLowerCase().includes(searchTerm)) ||
          (manifest.customerName && manifest.customerName.toLowerCase().includes(searchTerm)) ||
          (manifest.address && manifest.address.toLowerCase().includes(searchTerm)) ||
          (manifest.phoneNo && manifest.phoneNo.toLowerCase().includes(searchTerm))
        );
      });
    }
    
    // Apply status filters
    if (filters.status.length > 0) {
      filtered = filtered.filter(manifest => 
        filters.status.includes(manifest.status)
      );
    }
    
    // Apply date range filters
    if (filters.dateRange.start || filters.dateRange.end) {
      filtered = filtered.filter(manifest => {
        if (!manifest.deliveryDate) return false;
        
        const deliveryDate = new Date(manifest.deliveryDate);
        
        if (filters.dateRange.start && filters.dateRange.end) {
          return deliveryDate >= filters.dateRange.start && deliveryDate <= filters.dateRange.end;
        } else if (filters.dateRange.start) {
          return deliveryDate >= filters.dateRange.start;
        } else if (filters.dateRange.end) {
          return deliveryDate <= filters.dateRange.end;
        }
        
        return true;
      });
    }
    
    // Apply location filter
    if (filters.location) {
      filtered = filtered.filter(manifest => 
        manifest.location === filters.location
      );
    }
    
    // Apply delivery vehicle filter
    if (filters.deliveryVehicle) {
      filtered = filtered.filter(manifest => 
        manifest.deliveryVehicle && manifest.deliveryVehicle.toLowerCase().includes(filters.deliveryVehicle.toLowerCase())
      );
    }
    
    // Apply delivery date filter
    if (filters.hasDeliveryDate) {
      filtered = filtered.filter(manifest => 
        !!manifest.deliveryDate
      );
    }
    
    return filtered;
  }, [manifests, containerNo, filters]);

  // Calculate totals for filtered manifests
  const calculateTotals = (manifests: Manifest[]) => {
    return manifests.reduce((totals, manifest) => {
      return {
        totalCBM: totals.totalCBM + (manifest.cbm || 0),
        totalPallets: totals.totalPallets + (manifest.actualPalletsCount || 0),
        totalPieces: totals.totalPieces + (manifest.pieces || 0),
        totalInboundPieces: totals.totalInboundPieces + (manifest.inboundPieces || 0)
      };
    }, {
      totalCBM: 0,
      totalPallets: 0,
      totalPieces: 0,
      totalInboundPieces: 0
    });
  };

  // Calculate totals for the filtered manifests
  const totals = useMemo(() => {
    return calculateTotals(getFilteredManifests);
  }, [getFilteredManifests]);

  const formatDateTime = (dateStr?: string | null) => {
    return formatDateString(dateStr);
  };
  
  const getStatusChipColor = (status: ContainerStatus) => {
    const defaultColors = { bg: 'rgba(158, 158, 158, 0.1)', color: 'text.secondary' };
    
      const containerStatus = status as ContainerStatus;
      switch (containerStatus) {
        case ContainerStatus.CREATED:
          return { bg: 'rgba(255, 167, 38, 0.1)', color: 'warning.main' };
        case ContainerStatus.CONFIRMED:
          return { bg: 'rgba(33, 150, 243, 0.1)', color: 'info.main' };
        case ContainerStatus.ARRIVED:
          return { bg: 'rgba(0, 200, 83, 0.1)', color: 'success.main' };
        case ContainerStatus.UNSTUFFING:
          return { bg: 'rgba(255, 193, 7, 0.1)', color: 'warning.main' };
        case ContainerStatus.UNSTUFF_COMPLETED:
          return { bg: 'rgba(0, 200, 83, 0.1)', color: 'success.main' };
        case ContainerStatus.READY_TO_PULL_OUT:
          return { bg: 'rgba(0, 200, 83, 0.1)', color: 'success.main' };
        case ContainerStatus.PULLED_OUT:
          return { bg: 'rgba(76, 175, 80, 0.1)', color: 'success.dark' };
        default:
          return defaultColors;
      }
  };
  
  // Add function to get colors for manifest status chips
  const getManifestStatusChipColor = (status: ManifestStatus) => {
    switch (status) {
      case ManifestStatus.CREATED:
        return { bg: '#e3f2fd', color: '#1976d2' }; // Light blue
      case ManifestStatus.ETA_TO_WAREHOUSE:
        return { bg: '#fff8e1', color: '#ff8f00' }; // Amber
      case ManifestStatus.ARRIVED:
        return { bg: '#f1f8e9', color: '#689f38' }; // Light green
      case ManifestStatus.ON_HOLD:
        return { bg: '#ffebee', color: '#d32f2f' }; // Red
      case ManifestStatus.INBOUNDED_TO_WAREHOUSE:
        return { bg: '#e8eaf6', color: '#3f51b5' }; // Indigo
      case ManifestStatus.READY_TO_DELIVER:
        return { bg: '#e0f7fa', color: '#00838f' }; // Cyan
      case ManifestStatus.PENDING_DELIVER:
        return { bg: '#ede7f6', color: '#673ab7' }; // Deep purple
      case ManifestStatus.DELIVERING:
        return { bg: '#e8f5e9', color: '#2e7d32' }; // Green
      case ManifestStatus.DELIVERED:
        return { bg: '#e0f2f1', color: '#00695c' }; // Teal
      default:
        return { bg: '#f5f5f5', color: '#757575' }; // Gray
    }
  };
  
  // Function to handle column visibility toggle
  const handleColumnToggle = (column: string) => {
    // Don't allow hiding the actions column
    if (column === 'actions') return;
    
    setVisibleColumns(prev => {
      const newState = {
        ...prev,
        [column]: !prev[column]
      };
      
      // Save to localStorage
      try {
        localStorage.setItem('manifestColumnVisibility', JSON.stringify(newState));
      } catch (error) {
        console.error('Failed to save column visibility to localStorage:', error);
      }
      
      // Auto-size columns after a short delay to ensure the DOM has updated
      setTimeout(() => {
        handleAutosizeColumns();
      }, 100);
      
      return newState;
    });
  };
  
  // Generate column visibility model for DataGrid
  const generateColumnVisibilityModel = () => {
    const model = {};
    
    Object.keys(visibleColumns).forEach((field) => {
      model[field] = visibleColumns[field];
    });
    
    return model;
  };

  // Modify handleEditContainer to toggle edit mode instead of opening a dialog
  const handleEditContainer = () => {
    if (container) {
      setEditedContainer({...container});
      setEditMode(true);
    }
  };

  // Add function to cancel edit mode
  const handleCancelEdit = () => {
    setEditMode(false);
    setFormErrors({});
  };

  // Modify handleSaveEdit to work with inline editing
  const handleSaveEdit = async () => {
    if (!editedContainer || !currentUser?.token) return;
    
    // Basic validation
    const errors: Record<string, string> = {};
    if (!editedContainer.truckNo) errors.truckNo = 'Truck No is required';
    if (!editedContainer.vesselVoyageNo) errors.vesselVoyageNo = 'Vessel Voyage No is required';
    
    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }
    
    try {
      setLoading(true);
      
      const response = await containerService.updateContainer(
        editedContainer.containerNo,
        editedContainer,
        currentUser.token
      );
      
      if (response.success) {
        setContainer(response.data);
        setEditMode(false);
        setFormErrors({});
      } else {
        setError(response.message || 'Failed to update container');
      }
    } catch (err: any) {
      console.error('Error updating container:', err);
      setError(err.response?.data?.message || 'An error occurred while updating container');
    } finally {
      setLoading(false);
    }
  };

  // Add handler for saving status update
  const handleSaveStatus = async () => {
    if (!container || !newStatus || !currentUser?.token) {
      return;
    }

    try {
      console.log(`Updating container ${container.containerNo} status to ${newStatus}`);
      
      // First update the container status
      const response = await containerService.updateContainerStatus(
        container.containerNo, 
        newStatus,
        currentUser.token
      );
      
      if (response.success) {
        toast.success(`Container status updated to ${newStatus}`);
        
        // Close the dialog
        setStatusDialogOpen(false);
        
        // Clear the new status
        setNewStatus(null);
        
        // Force refresh both container and manifest data
        await fetchContainerDetail();
        await fetchAllManifests();
      } else {
        toast.error(response.message || 'Failed to update status');
      }
    } catch (err: any) {
      console.error('Error updating container status:', err);
      toast.error(err.response?.data?.message || 'An error occurred while updating status');
    }
  };
  
  // Fix the type error by changing how we handle the date
  const handleDateChange = (newValue: Date | null) => {
    if (!editedContainer) return;
    
    const updatedContainer = { ...editedContainer };
    // Use our formatDate utility instead of toISOString
    updatedContainer.etaRequestedDate = newValue ? formatDate(newValue) : '';
    
    setEditedContainer(updatedContainer);
  };
  
  // Generic handler for any date field in the container
  const handleContainerDateChange = (field: string, newValue: Date | null) => {
    if (!editedContainer) return;
    
    setEditedContainer({
      ...editedContainer,
      [field]: newValue ? formatDate(newValue) : ''
    });
  };
  
  // Add function to get container status icon
  const getContainerStatusIcon = (status: ContainerStatus) => {
    switch (status) {
      case ContainerStatus.CREATED:
        return <CalendarTodayIcon sx={{ color: theme.palette.warning.main }} />;
      case ContainerStatus.CONFIRMED:
        return <CheckCircleIcon sx={{ color: theme.palette.info.main }} />;
      case ContainerStatus.ARRIVED:
        return <DirectionsBoatIcon sx={{ color: theme.palette.success.main }} />;
      case ContainerStatus.UNSTUFFING:
        return <ConstructionIcon sx={{ color: theme.palette.warning.main }} />;
      case ContainerStatus.UNSTUFF_COMPLETED:
        return <CheckCircleIcon sx={{ color: theme.palette.success.main }} />;
      case ContainerStatus.READY_TO_PULL_OUT:
        return <LocalShippingOutlinedIcon sx={{ color: theme.palette.success.main }} />;
      case ContainerStatus.PULLED_OUT:
        return <LocalShippingIcon sx={{ color: theme.palette.success.dark }} />;
      default:
        return <TimelineIcon sx={{ color: theme.palette.grey[500] }} />;
    }
  };

  // Function to open the label dialog
  const handleOpenLabelDialog = (manifest: Manifest) => {
    setSelectedLabelManifest(manifest);
    setLabelDialogOpen(true);
  };
  
  // Function to close the label dialog
  const handleCloseLabelDialog = () => {
    setLabelDialogOpen(false);
    setSelectedLabelManifest(null);
  };
  
  // Handle saving custom label count
  const handleSaveLabelCount = (trackingNo: string, count: number) => {
    manifestService.saveLabelCount(trackingNo, count);
  };
  
  // Bulk label generation function to handle multiple manifests
  const handleBulkLabelGeneration = () => {
    console.log("handleBulkLabelGeneration called with selected manifests:", selectedManifests);
    console.log("Selected manifest count:", selectedManifests.length);
    console.log("Selected manifest tracking numbers:", selectedManifests.map(m => m.trackingNo));
    
    if (selectedManifests.length === 0) {
      console.log("No manifests selected, showing error");
      setManifestError('Please select at least one manifest to generate labels');
      toast.warning('Please select at least one manifest to generate labels');
      return;
    }
    
    if (selectedManifests.length === 1) {
      // For a single manifest, open the label dialog in single mode
      console.log("Single manifest selected, opening label dialog with:", selectedManifests[0]);
      setSelectedLabelManifest(selectedManifests[0]);
      setLabelDialogOpen(true);
      toast.info(`Generating label for manifest: ${selectedManifests[0].trackingNo}`);
    } else {
      // For multiple manifests, use batch mode
      console.log(`Multiple manifests selected (${selectedManifests.length}), opening batch label dialog`);
      // We'll still set the first manifest as selectedLabelManifest for compatibility
      setSelectedLabelManifest(selectedManifests[0]);
      setLabelDialogOpen(true);
      
      // Show informative message
      toast.info(`Preparing to print ${selectedManifests.length} manifest labels in batch mode`);
    }
  };

  // Function to generate and download Excel template
  const handleDownloadTemplate = async () => {
    try {
      // Create new workbook
      const workbook = new Excel.Workbook();
      const worksheet = workbook.addWorksheet('Manifest Template');
      
      // Define template columns - removed sequenceNo column as it will be auto-generated from row numbers
      const columns = [
        { header: 'Tracking No', key: 'trackingNo', width: 20 },
        { header: 'Customer Name*', key: 'customerName', width: 20 },
        { header: 'Phone No*', key: 'phoneNo', width: 15 },
        { header: 'Address*', key: 'address', width: 30 },
        { header: 'Postal Code*', key: 'postalCode', width: 12 },
        { header: 'Country*', key: 'country', width: 10 },
        { header: 'Pieces*', key: 'pieces', width: 10 },
        { header: 'CBM*', key: 'cbm', width: 10 },
        { header: 'Weight (kg)*', key: 'weight', width: 10 },
        { header: 'Delivery Date (YYYY-MM-DD)', key: 'deliveryDate', width: 25 },
        { header: 'Remarks', key: 'remarks', width: 30 },
      ];
      
      // Add columns to worksheet
      worksheet.columns = columns;
      
      // Style the header row
      worksheet.getRow(1).eachCell((cell) => {
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: '4F81BD' } // Blue background
        };
        cell.font = {
          bold: true,
          color: { argb: 'FFFFFF' } // White text
        };
        cell.alignment = {
          vertical: 'middle',
          horizontal: 'center'
        };
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        };
      });
      
      // Add one sample row to demonstrate data entry
      const sampleRow = {
        trackingNo: 'TRK12345',
        customerName: 'John Doe',
        phoneNo: '************',
        address: '123 Main St, Anytown',
        postalCode: '12345',
        country: 'USA',
        pieces: 5,
        cbm: 2.5,
        weight: 100,
        deliveryDate: '2023-12-25',
        remarks: 'Handle with care'
      };
      
      // Apply the sample data to the first data row (row 2)
      const firstDataRow = worksheet.getRow(2);
      firstDataRow.getCell(1).value = sampleRow.trackingNo;
      firstDataRow.getCell(2).value = sampleRow.customerName;
      firstDataRow.getCell(3).value = sampleRow.phoneNo;
      firstDataRow.getCell(4).value = sampleRow.address;
      firstDataRow.getCell(5).value = sampleRow.postalCode;
      firstDataRow.getCell(6).value = sampleRow.country;
      firstDataRow.getCell(7).value = sampleRow.pieces;
      firstDataRow.getCell(8).value = sampleRow.cbm;
      firstDataRow.getCell(9).value = sampleRow.weight;
      firstDataRow.getCell(10).value = sampleRow.deliveryDate;
      firstDataRow.getCell(11).value = sampleRow.remarks;
      
      // Style the sample data cells
      for (let col = 1; col <= 11; col++) {
        const cell = firstDataRow.getCell(col);
        cell.font = {
          italic: true,
          color: { argb: '808080' } // Gray text for example data
        };
      }
      
      // Add instructions
      worksheet.addRow([]);
      worksheet.addRow(['Instructions:']);
      worksheet.addRow([`1. Fields marked with * are required`]);
      worksheet.addRow([`2. Client will be automatically set to: ${container?.client?.companyName || container?.client?.username || 'Current Client'}`]);
      worksheet.addRow([`3. Container will be automatically set to: ${containerNo}`]);
      worksheet.addRow([`4. Sequence numbers will be automatically generated based on row order (1, 2, 3...)`]);
      worksheet.addRow([`5. Delete the sample row before uploading your data`]);
      
      // Generate timestamp for filename
      const timestamp = formatTimestamp();
      const filename = `manifest_template_${containerNo}_${timestamp}.xlsx`;
      
      // Generate buffer and save
      const buffer = await workbook.xlsx.writeBuffer();
      const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      saveAs(blob, filename);
      
      toast.success('Template downloaded successfully');
    } catch (error) {
      console.error('Error generating template:', error);
      toast.error('Failed to generate template');
    }
  };
  
  // Handle file selection
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0] || null;
    
    if (file) {
      // Check file type
      if (!file.name.endsWith('.xlsx') && !file.name.endsWith('.xls')) {
        toast.error('Please upload an Excel file (.xlsx or .xls)');
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
        return;
      }
      
      // Check file size (5MB limit)
      if (file.size > 5 * 1024 * 1024) {
        toast.error('File size exceeds 5MB limit');
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
        return;
      }
      
      setUploadedFile(file);
      toast.info(`File "${file.name}" selected for upload`);
    } else {
      setUploadedFile(null);
    }
  };
  
  // Handle file drop
  const handleFileDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    
    if (event.dataTransfer.files && event.dataTransfer.files.length > 0) {
      const file = event.dataTransfer.files[0];
      
      // Check file type
      if (!file.name.endsWith('.xlsx') && !file.name.endsWith('.xls')) {
        toast.error('Please upload an Excel file (.xlsx or .xls)');
        return;
      }
      
      setUploadedFile(file);
      toast.info(`File "${file.name}" selected for upload`);
    }
  };
  
  // Prevent default behavior for drag over
  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
  };
  
  // Reset file upload state
  const handleResetFileUpload = () => {
    setUploadedFile(null);
    setUploadProgress(0);
    setBulkUploadResults(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };
  
  // Handle file upload
  const handleFileUpload = async () => {
    if (!uploadedFile || !currentUser?.token) {
      toast.error('Please select a file to upload');
      return;
    }
    
    setIsUploading(true);
    setUploadProgress(0);
    setBulkUploadResults(null);
    
    try {
      const reader = new FileReader();
      
      reader.onload = async (e) => {
        try {
          const data = new Uint8Array(e.target?.result as ArrayBuffer);
          const workbook = new Excel.Workbook();
          await workbook.xlsx.load(data);
          
          const worksheet = workbook.getWorksheet(1);
          if (!worksheet) {
            throw new Error('No worksheet found in the Excel file');
          }
          
          const manifests: any[] = [];
          const errors: { row: number; error: string }[] = [];
          
          // Skip header row
          worksheet.eachRow((row, rowNumber) => {
            if (rowNumber === 1) return; // Skip header
            
            try {
              // Skip empty rows or info rows
              const trackingNo = row.getCell(1).value?.toString().trim();
              if (!trackingNo) return; // Skip empty rows
              
              // Calculate sequence number based on row position (rowNumber - 1 because header is row 1)
              const sequenceNo = rowNumber - 1;
              
              // Helper function to safely extract string value from Excel cell
              const getCellStringValue = (cellIndex: number, fieldName: string): string => {
                try {
                  const cellValue = row.getCell(cellIndex).value;

                  // Handle null/undefined
                  if (cellValue === null || cellValue === undefined) {
                    return '';
                  }

                  // Handle string values
                  if (typeof cellValue === 'string') {
                    return cellValue.trim();
                  }

                  // Handle number values
                  if (typeof cellValue === 'number') {
                    return cellValue.toString().trim();
                  }

                  // Handle rich text objects (common in Excel)
                  if (typeof cellValue === 'object' && cellValue.richText) {
                    // Extract text from rich text format
                    return cellValue.richText.map((rt: any) => rt.text || '').join('').trim();
                  }

                  // Handle other object types
                  if (typeof cellValue === 'object') {
                    console.warn(`Row ${rowNumber}: ${fieldName} contains object data:`, cellValue);

                    // Try to extract text property if it exists
                    if (cellValue.text) {
                      return cellValue.text.toString().trim();
                    }

                    // Try to extract result property if it exists (for formulas)
                    if (cellValue.result !== undefined) {
                      return cellValue.result.toString().trim();
                    }

                    throw new Error(`${fieldName} contains invalid data format. Expected text, got object. Please ensure the cell contains plain text.`);
                  }

                  // Fallback to string conversion
                  return cellValue.toString().trim();
                } catch (error) {
                  console.error(`Row ${rowNumber}: Error processing ${fieldName}:`, error);
                  throw new Error(`${fieldName} contains invalid data format. Please check the cell content.`);
                }
              };

              // Helper function to safely extract numeric value from Excel cell
              const getCellNumericValue = (cellIndex: number, fieldName: string): number => {
                try {
                  const cellValue = row.getCell(cellIndex).value;

                  // Handle null/undefined
                  if (cellValue === null || cellValue === undefined) {
                    return 0;
                  }

                  // Handle number values
                  if (typeof cellValue === 'number') {
                    return cellValue;
                  }

                  // Handle string values
                  if (typeof cellValue === 'string') {
                    const trimmed = cellValue.trim();
                    if (trimmed === '') return 0;
                    const parsed = parseFloat(trimmed);
                    return isNaN(parsed) ? 0 : parsed;
                  }

                  // Handle object types
                  if (typeof cellValue === 'object') {
                    console.warn(`Row ${rowNumber}: ${fieldName} contains object data:`, cellValue);

                    // Try to extract numeric properties
                    if (cellValue.result !== undefined) {
                      const parsed = parseFloat(cellValue.result.toString());
                      return isNaN(parsed) ? 0 : parsed;
                    }

                    if (cellValue.text !== undefined) {
                      const parsed = parseFloat(cellValue.text.toString());
                      return isNaN(parsed) ? 0 : parsed;
                    }

                    console.warn(`Row ${rowNumber}: ${fieldName} object cannot be converted to number, defaulting to 0`);
                    return 0;
                  }

                  // Fallback conversion
                  const parsed = parseFloat(cellValue.toString());
                  return isNaN(parsed) ? 0 : parsed;
                } catch (error) {
                  console.error(`Row ${rowNumber}: Error processing ${fieldName}:`, error);
                  return 0;
                }
              };

              // Helper function to safely extract date value from Excel cell
              const getCellDateValue = (cellIndex: number, fieldName: string): string | null => {
                try {
                  const cellValue = row.getCell(cellIndex).value;

                  if (!cellValue) return null;

                  // Handle Date objects
                  if (cellValue instanceof Date) {
                    return formatDate(cellValue);
                  }

                  // Handle string values
                  if (typeof cellValue === 'string') {
                    const trimmed = cellValue.trim();
                    if (trimmed === '') return null;
                    return formatDate(new Date(trimmed));
                  }

                  // Handle number values (Excel date serial numbers)
                  if (typeof cellValue === 'number') {
                    return formatDate(new Date(cellValue));
                  }

                  // Handle object types
                  if (typeof cellValue === 'object') {
                    console.warn(`Row ${rowNumber}: ${fieldName} contains object data:`, cellValue);

                    if (cellValue.result !== undefined) {
                      return formatDate(new Date(cellValue.result.toString()));
                    }

                    if (cellValue.text !== undefined) {
                      return formatDate(new Date(cellValue.text.toString()));
                    }

                    return null;
                  }

                  // Fallback conversion
                  return formatDate(new Date(cellValue.toString()));
                } catch (error) {
                  console.error(`Row ${rowNumber}: Error processing ${fieldName}:`, error);
                  return null;
                }
              };

              const manifest = {
                sequenceNo: sequenceNo,
                trackingNo: trackingNo,
                // Automatically set client and container from the current container context
                client: { username: container?.client?.username || '' },
                container: { containerNo: containerNo || '' },
                customerName: getCellStringValue(2, 'Customer Name'),
                phoneNo: getCellStringValue(3, 'Phone No'),
                address: getCellStringValue(4, 'Address'),
                postalCode: getCellStringValue(5, 'Postal Code'),
                country: getCellStringValue(6, 'Country'),
                pieces: getCellNumericValue(7, 'Pieces'),
                cbm: getCellNumericValue(8, 'CBM'),
                weight: getCellNumericValue(9, 'Weight'),
                deliveryDate: getCellDateValue(10, 'Delivery Date'),
                remarks: getCellStringValue(11, 'Remarks'),
                status: ManifestStatus.CREATED
              };
              
              // Validate required fields
              const requiredFields = [
                { field: 'trackingNo', name: 'Tracking No' },
                { field: 'customerName', name: 'Customer Name' },
                { field: 'phoneNo', name: 'Phone No' },
                { field: 'address', name: 'Address' },
                { field: 'postalCode', name: 'Postal Code' },
                { field: 'country', name: 'Country' },
              ];
              
              for (const { field, name } of requiredFields) {
                const value = field.includes('.')
                  ? field.split('.').reduce((obj, key) => obj?.[key], manifest)
                  : manifest[field];
                
                if (!value) {
                  throw new Error(`Missing required field: ${name}`);
                }
              }
              
              // Validate that we have container context (should always be true)
              if (!containerNo) {
                throw new Error('Container context is missing. Please refresh the page and try again.');
              }
              
              // Validate that we have client context
              if (!container?.client?.username) {
                throw new Error('Client information is missing from container. Please ensure the container has a valid client assigned.');
              }
              
              manifests.push(manifest);
            } catch (err: any) {
              errors.push({ row: rowNumber, error: err.message || 'Unknown error' });
            }
          });
          
          // Process manifests
          let successCount = 0;
          // Track the total number attempted (including validation errors)
          const totalAttempted = manifests.length + errors.length;
          
          // CRITICAL: If there are ANY validation errors, do not create ANY manifests
          if (errors.length > 0) {
            console.log(`Bulk upload cancelled: ${errors.length} validation errors found. No manifests will be created.`);
            setUploadProgress(100);
          } else if (manifests.length > 0) {
            try {
              setUploadProgress(50); // Set progress to halfway point before starting creation
              
              // Use bulk creation for all-or-nothing behavior
              const response = await manifestService.createManifestsBulk(
                manifests,
                currentUser.token
              );
              
              if (response.success && response.data) {
                successCount = response.data.length;
                setUploadProgress(100);
                console.log(`Bulk creation successful: ${successCount} manifests created`);
              } else {
                // If the bulk operation failed, add the error message
                errors.push({ 
                  row: 0, // General error, not tied to a specific row
                  error: response.message || 'Bulk creation failed'
                });
                setUploadProgress(100);
              }
            } catch (err: any) {
              // If bulk creation fails, no manifests should be created
              let errorMessage = 'Bulk creation failed';
              if (err.response?.data?.message) {
                errorMessage = err.response.data.message;
              } else if (err.message) {
                errorMessage = err.message;
              }
              
              errors.push({ 
                row: 0, // General error, not tied to a specific row
                error: errorMessage
              });
              setUploadProgress(100);
              console.error('Bulk manifest creation failed:', err);
            }
          } else {
            // No valid manifests to create
            setUploadProgress(100);
          }
          
          // Set results
          console.log(`Upload results - Attempted: ${totalAttempted}, Success: ${successCount}, Errors: ${errors.length}`);
          console.log('Validation errors:', errors);
          
          setBulkUploadResults({
            total: totalAttempted,
            successful: successCount,
            failed: errors.length,
            errors
          });
          
          // Complete progress
          setUploadProgress(100);
          
          // Show success message
          if (successCount > 0) {
            toast.success(`Created ${successCount} manifests successfully`);
            // Refresh manifests list
            fetchAllManifests();
          }
          
          if (errors.length > 0) {
            if (successCount === 0) {
              // All manifests failed or upload was cancelled due to validation errors
              toast.error(`Upload cancelled: ${errors.length} validation error${errors.length !== 1 ? 's' : ''} found. No manifests were created.`);
            } else {
              // This should not happen with the new logic, but keeping as fallback
              toast.error(`Failed to create ${errors.length} manifests`);
            }
          }
          
          // Set isUploading to false when upload is complete
          setIsUploading(false);
        } catch (err: any) {
          console.error('Error processing Excel file:', err);
          toast.error(err.message || 'Failed to process Excel file');
          setIsUploading(false);
        }
      };
      
      reader.onerror = () => {
        toast.error('Failed to read file');
        setIsUploading(false);
      };
      
      reader.readAsArrayBuffer(uploadedFile);
    } catch (err: any) {
      console.error('Upload error:', err);
      toast.error(err.message || 'Upload failed');
      setIsUploading(false);
    }
  };

  // Define DataGrid columns for manifests
  const columns: GridColDef[] = useMemo(() => [
    // 1. Actions (moved to first position)
    {
      field: 'actions',
      headerName: 'Actions',
      sortable: false,
      flex: 1,
      minWidth: 220,
      headerClassName: 'super-app-theme--header',
      renderCell: (params) => {
        if (!params || !params.row) return null;

        return (
          <Stack direction="row" spacing={1}>
            <Tooltip title="Edit">
              <IconButton
                size="small"
                onClick={(event) => {
                  // Stop event propagation to prevent row selection
                  event.stopPropagation();

                  if (!params.row?.trackingNo) return;

                  // Open the edit dialog
                  handleOpenEditManifest(params.row);
                }}
                sx={{
                  color: theme.palette.primary.main,
                  bgcolor: 'rgba(33, 150, 243, 0.08)',
                  '&:hover': {
                    bgcolor: 'rgba(33, 150, 243, 0.15)',
                  }
                }}
              >
                <EditIcon fontSize="small" />
              </IconButton>
            </Tooltip>
            <Tooltip title="Delete">
              <IconButton
                size="small"
                onClick={(event) => {
                  // Stop event propagation to prevent row selection
                  event.stopPropagation();

                  if (!params.row?.trackingNo) return;

                  // Confirmation before deleting
                  if (window.confirm(`Are you sure you want to delete manifest ${params.row.trackingNo}?`)) {
                    // Handle manifest deletion
                    handleDeleteManifest(params.row.trackingNo);
                  }
                }}
                sx={{
                  color: theme.palette.error.main,
                  bgcolor: 'rgba(211, 47, 47, 0.08)',
                  '&:hover': {
                    bgcolor: 'rgba(211, 47, 47, 0.15)',
                  }
                }}
              >
                <DeleteIcon fontSize="small" />
              </IconButton>
            </Tooltip>


          </Stack>
        );
      },
    },
    // 2. Location Zone
    {
      field: 'location',
      headerName: 'Location Zone',
      minWidth: 150,
      flex: 1,
      headerClassName: 'super-app-theme--header',
    },
    // 3. Internal ID
    {
      field: 'internalId',
      headerName: 'Internal ID',
      flex: 1,
      minWidth: 150,
      headerClassName: 'super-app-theme--header',
      valueFormatter: (params) => {
        return params.value || '-';
      },
      sortable: true,
      sortComparator: (v1, v2, params1, params2) => {
        try {
          // Get the internal IDs from the row data
          const id1 = params1.api.getCellValue(params1.id, 'internalId') || '';
          const id2 = params2.api.getCellValue(params2.id, 'internalId') || '';

          // Split by hyphen to get container number and sequence number
          const parts1 = id1.split('-');
          const parts2 = id2.split('-');

          // Compare container numbers first (case insensitive)
          const container1 = parts1[0] || '';
          const container2 = parts2[0] || '';

          const containerCompare = container1.localeCompare(container2, undefined, {sensitivity: 'base'});
          if (containerCompare !== 0) {
            return containerCompare;
          }

          // If container numbers are the same, compare sequence numbers numerically
          const seq1 = parseInt(parts1[1], 10) || 0;
          const seq2 = parseInt(parts2[1], 10) || 0;

          return seq1 - seq2;
        } catch (err) {
          console.error('Error in internalId sortComparator:', err);
          // Fall back to basic string comparison
          return String(v1).localeCompare(String(v2));
        }
      },
      renderCell: (params) => (
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <TimelineIcon sx={{ mr: 1, color: theme.palette.primary.main }} fontSize="small" />
          <Typography variant="body2" sx={{ fontWeight: 'medium', color: theme.palette.primary.dark }}>
            {params.value || '-'}
          </Typography>
        </Box>
      ),
    },
    // 4. Tracking No
    {
      field: 'trackingNo',
      headerName: 'Tracking No',
      flex: 1,
      minWidth: 150,
      headerClassName: 'super-app-theme--header',
      renderCell: (params: GridRenderCellParams) => (
          <Typography
          variant="body2"
            sx={{
            color: theme.palette.primary.main,
            textDecoration: 'underline',
              cursor: 'pointer',
            fontWeight: 'medium',
            '&:hover': {
              color: theme.palette.primary.dark,
              fontWeight: 'bold'
            }
          }}
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            if (!params.row?.trackingNo) return;
            // Navigate to manifest details in the same tab with container context
            navigate(`/manifests/${params.row.trackingNo}`, {
              state: {
                fromContainer: true,
                containerNo: containerNo
              }
            });
          }}
          >
            {params.value}
          </Typography>
      ),
    },
    // 5. Customer Name
    {
      field: 'customerName',
      headerName: 'Customer Name',
      flex: 1,
      minWidth: 150,
      headerClassName: 'super-app-theme--header',
      sortable: true,
      renderCell: (params) => {
        if (!params || !params.row) return 'N/A';
        return (
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <PersonIcon sx={{ mr: 1, color: theme.palette.grey[600] }} fontSize="small" />
            <Typography variant="body2">
              {params.row.customerName || 'N/A'}
            </Typography>
        </Box>
        );
      }
    },
    // 6. Address
    {
      field: 'address',
      headerName: 'Address',
      flex: 1,
      minWidth: 200,
      headerClassName: 'super-app-theme--header',
      sortable: true,
    },
    // 7. Postal Code
    {
      field: 'postalCode',
      headerName: 'Postal Code',
      flex: 1,
      minWidth: 120,
      headerClassName: 'super-app-theme--header',
      sortable: true,
    },
    // 8. Delivery Date
    {
      field: 'deliveryDate',
      headerName: 'Delivery Date',
      flex: 1,
      minWidth: 120,
      headerClassName: 'super-app-theme--header',
      sortable: true,
      type: 'date',
      valueGetter: (params) => params && params.row ? params.row.deliveryDate || '' : '',
      renderCell: (params) => {
        try {
          const trackingNo = params.row?.trackingNo;

          if (params.row && params.row.deliveryDate) {
            return (
              <Box sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                width: '100%'
              }}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <EventAvailableIcon sx={{ mr: 1, color: theme.palette.grey[600] }} fontSize="small" />
                <span>{formatDeliveryDate(params.row.deliveryDate)}</span>
                </Box>
                <Tooltip title="Edit delivery date">
                  <IconButton
                    size="small"
                    onClick={(e) => {
                      e.stopPropagation();
                      setEditingDeliveryDate(trackingNo);
                      setTempDeliveryDate(params.row.deliveryDate ? parseDateString(params.row.deliveryDate) : null);
                      setDeliveryDateDialogOpen(true);
                    }}
                    sx={{
                      opacity: 0.5,
                      '&:hover': { opacity: 1 },
                      padding: '2px',
                    }}
                  >
                    <EditIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              </Box>
            );
          }
          return (
            <Box sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              width: '100%'
            }}>
              <Typography variant="body2" color="text.secondary">
                Not set
              </Typography>
              <Tooltip title="Set delivery date">
                <IconButton
                  size="small"
                  onClick={(e) => {
                    e.stopPropagation();
                    setEditingDeliveryDate(trackingNo);
                    setTempDeliveryDate(null);
                    setDeliveryDateDialogOpen(true);
                  }}
                  sx={{
                    opacity: 0.5,
                    '&:hover': { opacity: 1 },
                    padding: '2px',
                  }}
                >
                  <AddIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Box>
          );
        } catch (error) {
          console.error('Error rendering delivery date cell:', error);
          return <Typography variant="body2" color="error">Error</Typography>;
        }
      },
    },
    // 9. Time Slot
    {
      field: 'timeSlot',
      headerName: 'Time Slot',
      flex: 1,
      minWidth: 160,
      headerClassName: 'super-app-theme--header',
      sortable: true,
      type: 'string',
      valueGetter: (params) => {
        if (!params || !params.row) return '';
        return params.row.timeSlot || getTimeSlotIdFromDate(params.row.deliveryDate);
      },
      renderCell: (params) => {
        try {
          const timeSlotId = params.row?.timeSlot || getTimeSlotIdFromDate(params.row?.deliveryDate);
          const timeSlotLabel = formatTimeSlot(timeSlotId);

          return (
            <Typography variant="body2" sx={{
              color: timeSlotId ? theme.palette.text.primary : theme.palette.text.secondary
            }}>
              {timeSlotLabel}
            </Typography>
          );
        } catch (error) {
          console.error('Error rendering time slot cell:', error);
          return <Typography variant="body2" color="error">Error</Typography>;
        }
      },
    },
    // 10. Phone Number
    {
      field: 'phoneNo',
      headerName: 'Phone Number',
      flex: 1,
      minWidth: 150,
      headerClassName: 'super-app-theme--header',
      sortable: true,
    },
    // 11. Remarks (Driver)
    {
      field: 'driverRemarks',
      headerName: 'Remarks (Driver)',
      flex: 1,
      minWidth: 200,
      headerClassName: 'super-app-theme--header',
      sortable: true,
      renderCell: (params) => {
        const manifest = params.row as Manifest;
        const trackingNo = manifest.trackingNo || '';

        return (
          <QuickEditCell
            value={params.value}
            onSave={(newValue) => handleQuickEditRemarks(manifest, 'driverRemarks', newValue)}
            placeholder="Click to add driver remarks..."
            fieldName="Driver Remarks"
            maxLength={1000}
          />
        );
      },
    },
    // 12. Driver
    {
      field: 'driver',
      headerName: 'Driver',
      flex: 1,
      minWidth: 150,
      headerClassName: 'super-app-theme--header',
      valueGetter: (params) => {
        if (!params || !params.row) return 'Not Assigned';
        const driver = params.row.driver;
        return driver ? driver.username || 'Not Assigned' : 'Not Assigned';
      }
    },
    // 12. Status
    {
      field: 'status',
      headerName: 'Status',
      flex: 1,
      minWidth: 170,
      headerClassName: 'super-app-theme--header',
      valueGetter: (params) => {
        if (!params || !params.row) return '';
        return params.row.status || '';
      },
      // Add a custom sort comparator that sorts based on the logical order of statuses
      sortComparator: (v1, v2, params1, params2) => {
        // Define the logical order of statuses
        const statusOrder = {
          [ManifestStatus.CREATED]: 1,
          [ManifestStatus.ETA_TO_WAREHOUSE]: 2,
          [ManifestStatus.ARRIVED]: 3,
          [ManifestStatus.INBOUNDED_TO_WAREHOUSE]: 4,
          [ManifestStatus.ON_HOLD]: 5,
          [ManifestStatus.PENDING_DELIVER]: 6,
          [ManifestStatus.READY_TO_DELIVER]: 7,
          [ManifestStatus.DELIVERING]: 8,
          [ManifestStatus.DELIVERED]: 9
        };

        // Get the status values from the rows
        const status1 = params1.api.getCellValue(params1.id, 'status') || '';
        const status2 = params2.api.getCellValue(params2.id, 'status') || '';

        // Compare based on the defined order
        return (statusOrder[status1] || 999) - (statusOrder[status2] || 999);
      },
      renderCell: (params) => {
        if (!params || !params.row) return null;
        const status = params.row.status;
        const { bg, color } = getManifestStatusChipColor(status);

        return (
          <Chip
            label={status.replace(/_/g, ' ')}
            sx={{
              backgroundColor: bg,
              color: color,
              fontWeight: 'medium',
              fontSize: '0.75rem',
              height: '24px',
              borderRadius: '12px',
              '& .MuiChip-label': {
                px: 1.5
              }
            }}
          />
        );
      }
    },
    // 13. Vehicle Type
    {
      field: 'deliveryVehicle',
      headerName: 'Vehicle Type',
      minWidth: 140,
      flex: 1,
      headerClassName: 'super-app-theme--header',
      renderCell: (params) => (
        <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', justifyContent: 'space-between' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', flex: 1 }}>
            <LocalShippingOutlinedIcon sx={{ mr: 1, color: theme.palette.grey[600] }} fontSize="small" />
            <Typography variant="body2" sx={{ flexGrow: 1 }}>
              {params.value || 'Auto-assign'}
            </Typography>
          </Box>
          <Tooltip title="Quick Edit Vehicle">
            <IconButton
              size="small"
              onClick={(event) => {
                event.stopPropagation();
                handleOpenQuickVehicleEdit(params.row);
              }}
              sx={{
                color: theme.palette.warning.main,
                bgcolor: 'rgba(255, 152, 0, 0.08)',
                ml: 1,
                '&:hover': {
                  bgcolor: 'rgba(255, 152, 0, 0.15)',
                }
              }}
            >
              <EditIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        </Box>
      )
    },
    // 14. Pieces
    {
      field: 'pieces',
      headerName: 'Pieces',
      flex: 1,
      minWidth: 100,
      headerClassName: 'super-app-theme--header',
      sortable: true,
      type: 'number',
    },
    // 15. Inbound Pieces
    {
      field: 'inboundPieces',
      headerName: 'Inbound Pieces',
      flex: 1,
      minWidth: 120,
      headerClassName: 'super-app-theme--header',
      sortable: true,
      type: 'number',
      renderCell: (params) => {
        const manifest = params.row;
        const inboundPieces = params.value || 0;
        const totalPieces = manifest.pieces || 0;

        // Determine the color based on inbound vs total pieces
        let color = theme.palette.text.primary;

        if (inboundPieces === 0) {
          color = theme.palette.text.secondary;
        } else if (inboundPieces < totalPieces) {
          color = theme.palette.warning.main;
        } else if (inboundPieces === totalPieces) {
          color = theme.palette.success.main;
        } else if (inboundPieces > totalPieces) {
          color = theme.palette.error.main;
        }

        return (
          <Typography variant="body2" sx={{
            color,
            fontWeight: 'medium'
          }}>
            {inboundPieces.toLocaleString()}
          </Typography>
        );
      },
    },
    // 16. Pallets
    {
      field: 'actualPalletsCount',
      headerName: 'Pallets',
      flex: 1,
      minWidth: 140, // Increased to accommodate button
      headerClassName: 'super-app-theme--header',
      sortable: true,
      type: 'number',
      headerAlign: 'left',
      renderCell: (params) => {
        const trackingNo = params.row?.trackingNo;

        return (
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              width: '100%',
              justifyContent: 'space-between'
            }}
            onClick={(e) => e.stopPropagation()} // Prevent row selection
          >
            <Typography variant="body2" sx={{ minWidth: 'fit-content' }}>
              {params.row.actualPalletsCount ?? 0}
            </Typography>

            {/* Manage Pallet Button */}
            <Tooltip title="Manage Pallets">
              <IconButton
                size="small"
                onClick={(event) => {
                  // Stop event propagation to prevent row selection
                  event.stopPropagation();

                  if (!params.row?.trackingNo) return;
                  handleOpenPalletManagementDialog(params.row);
                }}
                sx={{
                  color: theme.palette.success.main,
                  bgcolor: 'rgba(76, 175, 80, 0.08)',
                  '&:hover': {
                    bgcolor: 'rgba(76, 175, 80, 0.15)',
                  },
                  minWidth: '28px',
                  height: '28px'
                }}
              >
                <ViewInArIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          </Box>
        );
      },
    },
    // 17. CBM
    {
      field: 'cbm',
      headerName: 'CBM',
      flex: 1,
      minWidth: 100,
      headerClassName: 'super-app-theme--header',
      sortable: true,
      type: 'number',
      headerAlign: 'left',
      renderCell: (params) => (
        <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
          <span>{params.value ?? '-'}</span>
        </Box>
      )
    },
    // 18. Remarks (CS)
    {
      field: 'remarks',
      headerName: 'Remarks (CS)',
      flex: 1,
      minWidth: 200,
      headerClassName: 'super-app-theme--header',
      sortable: true,
      renderCell: (params) => {
        const manifest = params.row as Manifest;
        const trackingNo = manifest.trackingNo || '';

        return (
          <QuickEditCell
            value={params.value}
            onSave={(newValue) => handleQuickEditRemarks(manifest, 'remarks', newValue)}
            placeholder="Click to add CS remarks..."
            fieldName="CS Remarks"
            maxLength={1000}
          />
        );
      },
    },
    // 19. Weight
    {
      field: 'weight',
      headerName: 'Weight (kg)',
      minWidth: 120,
      flex: 0.7,
      type: 'number',
      headerClassName: 'super-app-theme--header',
    },
    // 20. Delivered Date
    {
      field: 'deliveredDate',
      headerName: 'Delivered On',
      flex: 1,
      minWidth: 180,
      headerClassName: 'super-app-theme--header',
      sortable: true,
      valueGetter: (params) => {
        if (!params || !params.row || !params.row.deliveredDate) return null;
        try {
          return new Date(params.row.deliveredDate).getTime();
        } catch (err) {
          console.error('Error parsing deliveredDate:', err);
          return null;
        }
      },
      renderCell: (params) => {
        try {
          if (params.row && params.row.deliveredDate) {
            return (
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <EventAvailableIcon sx={{ mr: 1, color: theme.palette.grey[600] }} fontSize="small" />
                <span>{formatDateTime(params.row.deliveredDate)}</span>
              </Box>
            );
          }
          return <span>-</span>;
        } catch (err) {
          console.error('Error rendering deliveredDate:', err);
          return <span>-</span>;
        }
      }
    },
    // 21. Created Date
    {
      field: 'createdDate',
      headerName: 'Created On',
      flex: 1,
      minWidth: 180,
      headerClassName: 'super-app-theme--header',
      sortable: true,
      valueGetter: (params) => {
        if (!params || !params.row || !params.row.createdDate) return null;
        try {
          return new Date(params.row.createdDate).getTime();
        } catch (err) {
          console.error('Error parsing createdDate:', err);
          return null;
        }
      },
      renderCell: (params) => {
        try {
          if (params.row && params.row.createdDate) {
        return (
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <CalendarTodayIcon sx={{ mr: 1, color: theme.palette.grey[600] }} fontSize="small" />
                <span>{formatDateTime(params.row.createdDate)}</span>
          </Box>
        );
          }
          return <span>-</span>;
        } catch (err) {
          console.error('Error rendering createdDate:', err);
          return <span>-</span>;
        }
      }
    },
    // 22. Country
    {
      field: 'country',
      headerName: 'Country',
      flex: 1,
      minWidth: 120,
      headerClassName: 'super-app-theme--header',
      sortable: true,
      renderCell: (params) => (
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <LocationOnIcon sx={{ mr: 1, color: theme.palette.grey[600] }} fontSize="small" />
          <span>{params.value || '-'}</span>
        </Box>
      ),
    },
    // 23. Container
    {
      field: 'container',
      headerName: 'Container',
      flex: 1,
      minWidth: 120,
      headerClassName: 'super-app-theme--header',
      sortable: true,
      valueGetter: (params) => {
        if (!params || !params.row) return '';
        const container = params.row.container;
        return container?.containerNo || '';
      },
      renderCell: (params) => {
        if (!params || !params.row) return 'N/A';

        const container = params.row.container;
        if (!container) return 'N/A';

        return (
          <Typography variant="body2">
            {container.containerNo || 'N/A'}
          </Typography>
        );
      }
    },
    // 24. Client
    {
      field: 'client',
      headerName: 'Client',
      flex: 1,
      minWidth: 150,
      headerClassName: 'super-app-theme--header',
      sortable: true,
      valueGetter: (params) => {
        if (!params || !params.row) return '';
        const client = params.row.client;
        return client?.companyName || client?.username || '';
      },
      renderCell: (params) => {
        if (!params || !params.row) return 'N/A';

        const client = params.row.client;
        if (!client) return 'N/A';

        const username = client.username || '';
        const companyName = client.companyName || '';

        return (
          <Box>
            <Typography variant="body2" component="div">
              {companyName || username}
            </Typography>
            {companyName && username && (
              <Typography variant="caption" color="text.secondary">
                {username}
              </Typography>
            )}
          </Box>
        );
      }
    },
  ], [navigate]);

  // New functions for manifest dialog
  const fetchClientsAndDrivers = async () => {
      const token = currentUser?.token || '';
      
    try {
      // Fetch clients
      try {
        console.log('Fetching clients...');
        const clientsResponse = await userService.getClients(token);
      if (clientsResponse.success) {
          console.log(`Successfully fetched ${clientsResponse.data.length} clients`);
        setClients(clientsResponse.data);
        } else {
          console.error('Failed to fetch clients:', clientsResponse.message);
        }
      } catch (clientErr) {
        console.error('Error fetching clients:', clientErr);
      }
      
      // Fetch drivers
      try {
        console.log('Fetching drivers...');
        const driversResponse = await userService.getDrivers(token);
      if (driversResponse.success) {
          console.log(`Successfully fetched ${driversResponse.data.length} drivers`);
        setDrivers(driversResponse.data);
        } else {
          console.error('Failed to fetch drivers:', driversResponse.message);
        }
      } catch (driverErr) {
        console.error('Error fetching drivers:', driverErr);
      }
    } catch (error) {
      console.error('Error fetching clients and drivers:', error);
    }
  };

  // Handle form field changes
  const handleManifestFormChange = (field: string, value: any) => {
    setManifestFormData({
      ...manifestFormData,
      [field]: value
    });
  };

  // Open manifest creation dialog
  const handleOpenManifestDialog = async () => {
    // Make sure we have the container information
    if (container) {
      setManifestFormLoading(true);
      
      // Reset form data with only container and client information
      setManifestFormData({
        trackingNo: '',
        client: container.client || { username: '' },
        container: { containerNo: containerNo || '' },
        driver: { username: '' },
        sequenceNo: 0,
        status: ManifestStatus.CREATED,
        customerName: '',
        phoneNo: '',
        address: '',
        postalCode: '',
        country: '',
        pieces: 0,
        cbm: 0,
        actualPalletsCount: 0,
        location: '',
        deliveryDate: null,
        deliveredDate: null,
        deliveryVehicle: '',
        driverRemarks: '',
        remarks: '',
        weight: 0,
        createdDate: new Date().toISOString()
      });
      
      // Fetch clients and drivers if needed
      if (clients.length === 0 || drivers.length === 0) {
        await fetchClientsAndDrivers();
      }
      
      try {
        // Fetch the next sequence number for the container
        if (currentUser?.token) {
          const response = await manifestService.getNextSequenceNumber(containerNo || '', currentUser.token);
          if (response.success) {
            // Update the form data with the next sequence number
            setManifestFormData(prevData => ({
              ...prevData,
              sequenceNo: response.data
            }));
            
            console.log(`Auto-assigned sequence number ${response.data} for new manifest`);
          }
        }
      } catch (error) {
        console.error('Error fetching next sequence number:', error);
      } finally {
        setManifestFormLoading(false);
        setOpenManifestDialog(true);
      }
    }
  };

  // Function to clear manifest selection
  const clearManifestSelection = () => {
    setSelectedManifests([]);

    // Clear DataGrid selection using API
    if (apiRef.current) {
      try {
        apiRef.current.setRowSelectionModel([]);
      } catch (error) {
        console.warn('Failed to clear DataGrid selection:', error);
        // Fallback to key increment if API method fails
        setDataGridKey(prev => prev + 1);
      }
    }
  };

  // Quick edit handler for remarks fields
  const handleQuickEditRemarks = async (manifest: Manifest, field: 'remarks' | 'driverRemarks', newValue: string) => {
    try {
      if (!manifest || !manifest.trackingNo) {
        throw new Error('Invalid manifest object provided');
      }

      const response = await manifestService.updateManifestRemarks(
        manifest.trackingNo,
        field,
        newValue,
        currentUser.token
      );

      if (response.success) {
        toast.success(`${field === 'driverRemarks' ? 'Driver remarks' : 'CS remarks'} updated successfully`);
        setManifests(prev =>
          prev.map(m =>
            m.trackingNo === manifest.trackingNo
              ? { ...m, [field]: newValue }
              : m
          )
        );
      } else {
        throw new Error(response.message || 'Failed to update');
      }
    } catch (error) {
      console.error(`Failed to update ${field}:`, error);
      toast.error(`Failed to update ${field === 'driverRemarks' ? 'driver remarks' : 'CS remarks'}`);
      throw error; // Re-throw to let the QuickEditCell handle the error
    }
  };

  // Close manifest dialog
  const handleCloseManifestDialog = () => {
    setOpenManifestDialog(false);
    setManifestFormError(null);
    // Clear manifest selection when dialog is closed
    clearManifestSelection();
  };

  // Open manifest edit dialog
  const handleOpenEditManifest = (manifest: Manifest) => {
    setSelectedManifestForEdit(manifest);
    
    // Clone the manifest data to the form
    const manifestData = {
      trackingNo: manifest.trackingNo,
      client: manifest.client || { username: container?.client?.username || '' },
      container: manifest.container || { containerNo: containerNo || '' },
      driver: manifest.driver || { username: '' },
      sequenceNo: manifest.sequenceNo,
      status: manifest.status || ManifestStatus.CREATED,
      customerName: manifest.customerName || '',
      phoneNo: manifest.phoneNo || '',
      address: manifest.address || '',
      postalCode: manifest.postalCode || '',
      country: manifest.country || '',
      pieces: manifest.pieces || 0,
      cbm: manifest.cbm || 0,
      actualPalletsCount: manifest.actualPalletsCount || 0,
      location: manifest.location || '',
      deliveryDate: manifest.deliveryDate ? new Date(manifest.deliveryDate) : null,
      deliveredDate: manifest.deliveredDate ? new Date(manifest.deliveredDate) : null,
      deliveryVehicle: manifest.deliveryVehicle || '',
      driverRemarks: manifest.driverRemarks || '',
      remarks: manifest.remarks || '',
      weight: manifest.weight || 0,
      internalId: manifest.internalId,
      createdDate: manifest.createdDate || null,
    };
    
    // Check if the delivery vehicle exists in the vehicle types list
    if (manifestData.deliveryVehicle && !vehicleTypes.some(vt => vt.name === manifestData.deliveryVehicle)) {
      console.log(`Delivery vehicle "${manifestData.deliveryVehicle}" not found in vehicle types list. Setting to empty.`);
      manifestData.deliveryVehicle = '';
    }
    
    setManifestFormData(manifestData);
    
    // Fetch clients and drivers if needed
    if (clients.length === 0 || drivers.length === 0) {
      fetchClientsAndDrivers();
    }
    
    setEditManifestDialogOpen(true);
  };
  
  // Close edit manifest dialog
  const handleCloseEditManifest = () => {
    setEditManifestDialogOpen(false);
    setSelectedManifestForEdit(null);
    setManifestFormError(null);
    // Clear manifest selection when dialog is closed
    clearManifestSelection();
  };
  
  // Handle saving edited manifest
  const handleSaveEditManifest = async () => {
    try {
      setManifestFormLoading(true);
      setManifestFormError(null);
      
      // Basic validation
      if (!manifestFormData.customerName) {
        setManifestFormError('Customer name is required');
        setManifestFormLoading(false);
        return;
      }
      
      if (!manifestFormData.address) {
        setManifestFormError('Address is required');
        setManifestFormLoading(false);
        return;
      }
      
      // Make sure container is set to the current container
      const manifestToUpdate = {
        ...manifestFormData,
        container: { containerNo: containerNo },
        // Ensure client is sent as only { username }
        client: { username: (manifestFormData.client?.username) },
        // Convert empty delivery vehicle to null for auto-assignment
        deliveryVehicle: manifestFormData.deliveryVehicle?.trim() || null
      };
      
      const token = currentUser?.token;
      if (!token) {
        setManifestFormError('Authentication token not found');
        setManifestFormLoading(false);
        return;
      }
      
      const response = await manifestService.updateManifest(
        selectedManifestForEdit?.trackingNo || '',
        manifestToUpdate,
        token
      );
      
      if (response.success) {
        toast.success('Manifest updated successfully');
        handleCloseEditManifest();
        // Refresh manifest list
        fetchAllManifests();
      } else {
        setManifestFormError(response.message || 'Failed to update manifest');
      }
    } catch (error) {
      console.error('Error updating manifest:', error);
      setManifestFormError('An error occurred while updating the manifest');
    } finally {
      setManifestFormLoading(false);
    }
  };

  // Update the handleSaveManifest function to receive an event
  const handleSaveManifest = async () => {
    try {
      setManifestFormLoading(true);
      setManifestFormError(null);
      
      // Basic validation
      if (!manifestFormData.customerName) {
        setManifestFormError('Customer name is required');
        setManifestFormLoading(false);
        return;
      }
      
      if (!manifestFormData.address) {
        setManifestFormError('Address is required');
        setManifestFormLoading(false);
        return;
      }
      
      if (!manifestFormData.postalCode) {
        setManifestFormError('Postal code is required');
        setManifestFormLoading(false);
        return;
      }
      
      if (!manifestFormData.phoneNo) {
        setManifestFormError('Phone number is required');
        setManifestFormLoading(false);
        return;
      }
      
      // Additional validation for required numeric fields
      if (!manifestFormData.pieces || manifestFormData.pieces <= 0) {
        setManifestFormError('Number of pieces must be greater than 0');
        setManifestFormLoading(false);
        return;
      }
      
      // Validation for tracking number - required field
      if (!manifestFormData.trackingNo || manifestFormData.trackingNo.trim() === '') {
        setManifestFormError('Tracking number is required');
        setManifestFormLoading(false);
        return;
      }
      
      // Make sure container is set to the current container
      // Create a clean manifest object with only the fields the backend expects
      const manifestToSave = {
        // Use the tracking number from the form (no auto-generation)
        trackingNo: manifestFormData.trackingNo.trim(),
        container: { containerNo: containerNo },
        // Ensure client is sent as only { username }
        client: { username: (container?.client?.username || manifestFormData.client?.username) },
        driver: manifestFormData.driver?.username ? { username: manifestFormData.driver.username } : null,
        status: manifestFormData.status || ManifestStatus.CREATED,
        customerName: manifestFormData.customerName?.trim(),
        phoneNo: manifestFormData.phoneNo?.trim(),
        address: manifestFormData.address?.trim(),
        postalCode: manifestFormData.postalCode?.trim(),
        country: manifestFormData.country?.trim() || 'Singapore',
        pieces: Number(manifestFormData.pieces),
        cbm: Number(manifestFormData.cbm || 0),
        weight: Number(manifestFormData.weight || 0),
        actualPalletsCount: manifestFormData.actualPalletsCount ? Number(manifestFormData.actualPalletsCount) : 0,
        location: manifestFormData.location?.trim() || '',
        deliveryDate: manifestFormData.deliveryDate ? formatDateOnly(manifestFormData.deliveryDate) : null,
        deliveredDate: manifestFormData.deliveredDate ? formatDateOnly(manifestFormData.deliveredDate) : null,
        deliveryVehicle: manifestFormData.deliveryVehicle?.trim() || null,
        driverRemarks: manifestFormData.driverRemarks?.trim() || '',
        remarks: manifestFormData.remarks?.trim() || ''
      };
      
      const token = currentUser?.token;
      if (!token) {
        setManifestFormError('Authentication token not found');
        setManifestFormLoading(false);
        return;
      }
      
      console.log('Attempting to create manifest with data:', manifestToSave);
      
      const response = await manifestService.createManifest(manifestToSave, token);
      
      if (response.success) {
        toast.success('Manifest created successfully');
        handleCloseManifestDialog();
        // Refresh manifest list
        fetchAllManifests();
      } else {
        setManifestFormError(response.message || 'Failed to create manifest');
      }
    } catch (error: any) {
      console.error('Error creating manifest:', error);
      const errorMsg = error.response?.data?.message || 
                      (error.message === 'Request failed with status code 500' ? 
                       'Server error: There may be an issue with the data format. Please check all fields.' : 
                       error.message) ||
                      'An unexpected error occurred';
      
      setManifestFormError(errorMsg);
      
      // If it's a 500 error, log more details to help debug
      if (error.response?.status === 500) {
        console.error('Detailed error info:', {
          status: error.response.status,
          statusText: error.response.statusText,
          headers: error.response.headers,
          data: error.response.data
        });
      }
    } finally {
      setManifestFormLoading(false);
    }
  };

  // Add useEffect to fetch clients and drivers when needed
  useEffect(() => {
    if (openManifestDialog && (clients.length === 0 || drivers.length === 0)) {
      fetchClientsAndDrivers();
    }
  }, [openManifestDialog]);

  // Function to handle auto-sizing columns using the DataGrid API
  const handleAutosizeColumns = () => {
    if (apiRef.current) {
      // Get only the visible columns
      const visibleColumnFields = columns
        .filter(col => visibleColumns[col.field])
        .map(col => col.field);
      
      // Auto-size only the visible columns with a minimum width of 100px
      apiRef.current.autosizeColumns({
        columns: visibleColumnFields,
        defaultMinWidth: 100
      });
    }
  };

  // Export selected manifests to Excel
  const handleExportToExcel = async () => {
    try {
      // Determine which manifests to export
      const manifestsToExport = selectedManifests.length > 0 
        ? selectedManifests 
        : getFilteredManifests;
        
      if (manifestsToExport.length === 0) {
        toast.warning('No manifests to export');
        return;
      }
      
      // Create a new workbook
      const workbook = new Excel.Workbook();
      const worksheet = workbook.addWorksheet('Manifests Export');
      
      // Get visible columns
      const visibleColumnDefs = columns
        .filter(col => visibleColumns[col.field])
        .filter(col => col.field !== 'actions') // Exclude 'actions' column
        .sort((a, b) => {
          // Ensure columns are in their displayed order
          const aIndex = columns.findIndex(c => c.field === a.field);
          const bIndex = columns.findIndex(c => c.field === b.field);
          return aIndex - bIndex;
        });
        
      // Define Excel columns based on visible grid columns
      const excelColumns = visibleColumnDefs.map(col => ({
        header: col.headerName?.replace('*', '') || col.field.charAt(0).toUpperCase() + col.field.slice(1).replace(/([A-Z])/g, ' $1').trim(),
        key: col.field,
        width: Math.max(15, (col.headerName?.length || 10) * 1.2)
      }));
      
      // Add columns to worksheet
      worksheet.columns = excelColumns;
      
      // Style the header row
      worksheet.getRow(1).eachCell((cell) => {
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: '4F81BD' } // Blue background
        };
        cell.font = {
          bold: true,
          color: { argb: 'FFFFFF' } // White text
        };
        cell.alignment = {
          vertical: 'middle',
          horizontal: 'center'
        };
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        };
      });
      
      // Process manifest data for Excel
      const processedData = manifestsToExport.map(manifest => {
        const rowData = {};
        
        // Process each column
        visibleColumnDefs.forEach(col => {
          const field = col.field;
          
          // Handle special fields with nested objects
          if (field === 'client') {
            rowData[field] = manifest.client?.username || '';
          } 
          else if (field === 'container') {
            rowData[field] = manifest.container?.containerNo || '';
          }
          else if (field === 'driver') {
            rowData[field] = manifest.driver?.username || 'Not Assigned';
          }
          // Handle date fields
          else if (field === 'createdDate' || field === 'deliveryDate' || field === 'deliveredDate') {
            rowData[field] = manifest[field] ? formatDateForExport(manifest[field]) : '';
          }
          // Handle timeSlot field - convert ID to human-readable label
          else if (field === 'timeSlot') {
            const timeSlotId = manifest.timeSlot || (manifest.deliveryDate ? getTimeSlotIdFromDate(manifest.deliveryDate) : null);
            rowData[field] = formatTimeSlot(timeSlotId);
          }
          // Handle status field
          else if (field === 'status') {
            rowData[field] = manifest.status?.replace(/_/g, ' ') || '';
          }
          // Handle regular fields
          else {
            rowData[field] = manifest[field] !== undefined ? manifest[field] : '';
          }
        });
        
        return rowData;
      });
      
      // Add rows to worksheet
      processedData.forEach(data => {
        worksheet.addRow(data);
      });
      
      // Format all data rows
      for (let i = 2; i <= processedData.length + 1; i++) {
        worksheet.getRow(i).eachCell((cell) => {
          cell.alignment = {
            vertical: 'middle'
          };
          cell.border = {
            top: { style: 'thin', color: { argb: 'D3D3D3' } },
            left: { style: 'thin', color: { argb: 'D3D3D3' } },
            bottom: { style: 'thin', color: { argb: 'D3D3D3' } },
            right: { style: 'thin', color: { argb: 'D3D3D3' } }
          };
        });
        
        // Set alternating row background color
        if (i % 2 === 0) {
          worksheet.getRow(i).eachCell((cell) => {
            cell.fill = {
              type: 'pattern',
              pattern: 'solid',
              fgColor: { argb: 'F5F5F5' } // Light gray for even rows
            };
          });
        }
      }
      
      // Auto-size columns
      worksheet.columns.forEach(column => {
        let maxLength = 10;
        column.eachCell({ includeEmpty: false }, (cell) => {
          const columnLength = cell.value ? cell.value.toString().length : 0;
          if (columnLength > maxLength) {
            maxLength = columnLength;
          }
        });
        column.width = Math.min(30, Math.max(12, maxLength * 1.2));
      });
      
      // Add container info and timestamp at the bottom
      const metaRow = worksheet.addRow(['']);
      const metaCell = metaRow.getCell(1);
      metaCell.value = `Container: ${containerNo} - Exported on ${formatDateString(new Date().toISOString())} - Total: ${processedData.length} manifests`;
      worksheet.mergeCells(metaRow.number, 1, metaRow.number, excelColumns.length);
      metaCell.font = {
        italic: true,
        color: { argb: '808080' } // Gray text
      };
      
      // Generate buffer
      const buffer = await workbook.xlsx.writeBuffer();
      
      // Generate filename
      const timestamp = formatTimestamp();
      const filename = `container_${containerNo}_manifests_${timestamp}.xlsx`;
      
      // Save file using FileSaver
      const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      saveAs(blob, filename);
      
      // Show success message
      toast.success(`Successfully exported ${processedData.length} manifests`);
    } catch (error) {
      console.error('Error exporting manifests:', error);
      toast.error('Failed to export manifests');
    }
  };

  // Reset column visibility to defaults
  const resetColumnVisibility = () => {
    // Default visible columns
    const defaultColumns = {
      trackingNo: true,
      internalId: true,
      customerName: true,
      status: true,
      pieces: true,
      inboundPieces: true, // Show inbound pieces by default
      weight: true,
      location: true,
      createdDate: true,
      deliveryDate: true,
      timeSlot: true, // Show time slot by default
      actions: true,
      client: false,
      container: false,
      driverRemarks: false,
      driver: false,
      phoneNo: false,
      address: false,
      postalCode: false,
      country: false,
      cbm: false,
      actualPalletsCount: true,
      deliveryVehicle: false,
      deliveredDate: false,
      remarks: false, // Add remarks column
    };
    
    setVisibleColumns(defaultColumns);
    
    // Save default settings to localStorage
    try {
      localStorage.setItem('manifestColumnVisibility', JSON.stringify(defaultColumns));
    } catch (error) {
      console.error('Failed to save default column visibility to localStorage:', error);
    }
    
    // Auto-size columns after a short delay to ensure the DOM has updated
    setTimeout(() => {
      handleAutosizeColumns();
    }, 100);
  };

  // ... existing code ...

  // Add useEffect for consistent styling
  useEffect(() => {
    // Create a style element for consistent styling
    const style = document.createElement('style');
    style.innerHTML = `
      .no-cell-focus-outline *:focus,
      .no-cell-focus-outline *:focus-visible,
      .no-cell-focus-outline *:focus-within,
      .no-cell-focus-outline .MuiDataGrid-root *,
      .no-cell-focus-outline .MuiDataGrid-cell,
      .no-cell-focus-outline .MuiDataGrid-row {
        outline: none !important;
        box-shadow: none !important;
      }
    `;
    document.head.appendChild(style);
    
    // Clean up on unmount
    return () => {
      document.head.removeChild(style);
    };
  }, []);

  useEffect(() => {
    // Add custom CSS for DataGrid header styling
    const style = document.createElement('style');
    style.innerHTML = `
      .super-app-theme--header {
        background-color: ${theme.palette.primary.main};
        font-weight: bold;
      }
      
      .MuiDataGrid-columnHeaders .MuiDataGrid-columnHeader:focus,
      .MuiDataGrid-columnHeaders .MuiDataGrid-columnHeader:focus-within {
        outline: none !important;
      }
      
      .MuiDataGrid-cell {
        outline: none !important;
      }
    `;
    document.head.appendChild(style);
    
    return () => {
      document.head.removeChild(style);
    };
  }, [theme.palette.primary.main]);

  // ... existing code ...

  // Handle search filter change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFilters(prev => ({
      ...prev,
      search: e.target.value
    }));
  };
  
  // Handle status filter change
  const handleStatusFilterChange = (status: ManifestStatus) => {
    setFilters(prev => {
      const currentStatuses = [...prev.status];
      const index = currentStatuses.indexOf(status);
      
      if (index === -1) {
        // Add status to filter
        return {
          ...prev,
          status: [...currentStatuses, status]
        };
      } else {
        // Remove status from filter
        currentStatuses.splice(index, 1);
        return {
          ...prev,
          status: currentStatuses
        };
      }
    });
  };
  
  // Handle date range filter change
  const handleDateRangeChange = (type: 'start' | 'end', date: Date | null) => {
    // If date is provided, ensure it has the correct time
    let adjustedDate = date;
    if (date) {
      adjustedDate = new Date(date);
      if (type === 'start') {
        adjustedDate.setHours(0, 0, 0, 0);
      } else if (type === 'end') {
        adjustedDate.setHours(23, 59, 59, 999);
      }
    }
    
    setFilters(prev => ({
      ...prev,
      dateRange: {
        ...prev.dateRange,
        [type]: adjustedDate
      }
    }));
  };
  
  // Handle location filter change - update to handle Autocomplete selection
  const handleLocationFilterChange = (event, newValue) => {
    setFilters(prev => ({
      ...prev,
      location: newValue ? newValue.name : ''
    }));
  };
  
  // Handle delivery vehicle filter change
  const handleDeliveryVehicleFilterChange = (event, newValue) => {
    setFilters(prev => ({
      ...prev,
      deliveryVehicle: newValue ? newValue.name : ''
    }));
  };
  
  // Handle has delivery date filter change
  const handleHasDeliveryDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFilters(prev => ({
      ...prev,
      hasDeliveryDate: e.target.checked
    }));
  };
  
  // Reset all filters
  const resetFilters = () => {
    setFilters({
      search: '',
      status: [],
      dateRange: {
        start: null,
        end: null
      },
      location: '',
      deliveryVehicle: '',
      hasDeliveryDate: false,
      advancedFiltersOpen: true // Keep this true
    });
  };

  // Add a function to handle quick edit of delivery date
  const handleQuickEditDeliveryDate = async (trackingNo: string, newDate: Date | null) => {
    if (!trackingNo || !currentUser?.token) {
      return;
    }
    
    try {
      setManifestLoading(true);
      
      // Find the manifest to update
      const manifestToUpdate = manifests.find(m => m.trackingNo === trackingNo);
      if (!manifestToUpdate) {
        toast.error('Manifest not found');
        return;
      }
      
      // Create a user-friendly name for toast messages
      const manifestName = `Manifest ${trackingNo}`;
      
      // Create updated manifest object
      const updatedManifest = {
        ...manifestToUpdate,
        deliveryDate: newDate ? formatDateOnly(newDate) : null,
        // Set or clear time slot based on the date
        timeSlot: newDate ? getTimeSlotIdFromDate(newDate) : null
      };
      
      // Call the API to update the manifest
      const response = await manifestService.updateManifest(
        trackingNo,
        updatedManifest,
        currentUser?.token || ''
      );
      
      if (response.success) {
        toast.success(newDate ? `Delivery date for ${manifestName} updated successfully` : `Delivery date for ${manifestName} removed successfully`);
        
        // Update the manifest in the local state
        setManifests(prevManifests => 
          prevManifests.map(manifest => 
            manifest.trackingNo === trackingNo
              ? response.data // Use the complete response data which includes inbound pieces
              : manifest
          )
        );
      } else {
        toast.error(response.message || `Failed to update delivery date for ${manifestName}`);
      }
    } catch (err: any) {
      console.error('Error updating delivery date:', err);
      toast.error(err.response?.data?.message || 'An error occurred while updating delivery date');
    } finally {
      setManifestLoading(false);
    }
  };

  // Function to show confirmation dialog
  const showConfirmDialog = (title: string, message: string, action: () => void) => {
    setConfirmTitle(title);
    setConfirmMessage(message);
    setConfirmAction(() => action);
    setConfirmDialogOpen(true);
  };

  // Add a function to refresh all data
  const refreshAllData = async () => {
    console.log('Refreshing all container and manifest data...');
    
    // Set loading states
    setLoading(true);
    setManifestLoading(true);
    
    try {
      // Run both fetch operations in parallel
      await Promise.all([
        fetchContainerDetail(),
        fetchAllManifests()
      ]);
      
      toast.success('Data refreshed successfully');
    } catch (err) {
      console.error('Error refreshing data:', err);
      toast.error('Failed to refresh data');
    } finally {
      // Make sure loading states are cleared even if there was an error
      setLoading(false);
      setManifestLoading(false);
    }
  };

  // Add quick edit handler for CBM and No of Pallets
  const handleOpenEditValueDialog = (trackingNo: string, field: 'cbm' | 'actualPalletsCount', currentValue: number | null) => {
    setEditValueTrackingNo(trackingNo);
    setEditValueField(field);
    setEditValueTemp(currentValue ?? 0);
    setEditValueDialogOpen(true);
  };

  const handleCloseEditValueDialog = () => {
    setEditValueDialogOpen(false);
    setEditValueField('cbm');
    setEditValueTrackingNo(null);
    setEditValueTemp(null);
  };

  const handleSaveEditValueDialog = async () => {
    if (!editValueTrackingNo || !currentUser?.token) {
      handleCloseEditValueDialog();
      return;
    }
    
    try {
      setManifestLoading(true);
      
      // Find the manifest to update
      const manifestToUpdate = manifests.find(m => m.trackingNo === editValueTrackingNo);
      if (!manifestToUpdate) {
        toast.error('Manifest not found');
        return;
      }
      
      // Create updated manifest object
      const updatedManifest = {
        ...manifestToUpdate,
        [editValueField]: editValueTemp
      };
      
      // Update the manifest via the API
      const response = await manifestService.updateManifest(
        editValueTrackingNo,
        updatedManifest,
        currentUser.token
      );
      
      if (response.success) {
        toast.success(`${editValueField === 'cbm' ? 'CBM' : 'Number of Pallets'} updated successfully`);
        
        // Update local state with the complete response data
        setManifests(prev =>
          prev.map(m =>
            m.trackingNo === editValueTrackingNo ? response.data : m
          )
        );
        
        handleCloseEditValueDialog();
      } else {
        toast.error(response.message || 'Failed to update');
      }
    } catch (err: any) {
      console.error('Error updating manifest:', err);
      toast.error(err.response?.data?.message || 'An error occurred while updating');
    } finally {
      setManifestLoading(false);
    }
  };

  const handleOpenPalletManagementDialog = (manifest: Manifest) => {
    setSelectedPalletManifest(manifest);
    setPalletManagementDialogOpen(true);
  };

  const handleClosePalletManagementDialog = () => {
    setPalletManagementDialogOpen(false);
    setSelectedPalletManifest(null);
  };

  const handlePalletOperationComplete = () => {
    // Traditional approach: Refresh manifests to update pallet counts
    // This will be called after selective updates have been processed
    console.log('🔄 Traditional pallet operation complete callback - checking if full refresh is needed');

    // Only do full refresh if we haven't handled it via selective update
    if (!document.querySelector('[data-selective-update-handled]')) {
      console.log('📄 No selective update detected, performing full refresh');
      fetchAllManifests();
    } else {
      console.log('✅ Selective update already handled, skipping full refresh');
      // Remove the marker for next time
      document.querySelector('[data-selective-update-handled]')?.remove();
    }
  };

  // Quick delivery vehicle edit handlers
  const handleOpenQuickVehicleEdit = (manifest: Manifest) => {
    setSelectedManifestForVehicleEdit(manifest);
    setQuickVehicleDialogOpen(true);
  };

  const handleCloseQuickVehicleEdit = () => {
    setQuickVehicleDialogOpen(false);
    setSelectedManifestForVehicleEdit(null);
  };

  const handleSaveQuickVehicleEdit = async (deliveryVehicle: string | null) => {
    if (!selectedManifestForVehicleEdit || !currentUser?.token) {
      throw new Error('Missing manifest or authentication token');
    }

    try {
      setManifestLoading(true);
      
      const response = await manifestService.updateManifestDeliveryVehicle(
        selectedManifestForVehicleEdit.trackingNo,
        deliveryVehicle,
        currentUser.token
      );

      if (response.success) {
        toast.success(`Delivery vehicle updated successfully`);
        
        // Update the manifest in the local state
        setManifests(prevManifests =>
          prevManifests.map(manifest =>
            manifest.trackingNo === selectedManifestForVehicleEdit.trackingNo
              ? response.data
              : manifest
          )
        );
      } else {
        toast.error(response.message || 'Failed to update delivery vehicle');
      }
    } catch (err: any) {
      console.error('Error updating delivery vehicle:', err);
      toast.error(err.response?.data?.message || 'An error occurred while updating delivery vehicle');
    } finally {
      setManifestLoading(false);
      handleCloseQuickVehicleEdit();
    }
  };
  
  // Handle bulk delivery date update
  const handleBulkDeliveryDateUpdate = async () => {
    if (!currentUser?.token || selectedManifests.length === 0) {
      toast.warning('Please select at least one manifest');
      return;
    }
    
    try {
      setManifestLoading(true);
      
      let formattedDate = null;
      let formattedDisplayDate = null;
      
      if (bulkDeliveryDate) {
      // Create a date object set to midnight for consistent handling
      const userDate = new Date(bulkDeliveryDate);
      userDate.setHours(0, 0, 0, 0);
      
      // Format the date as it should appear in the UI (dd MMM yyyy)
      // This is what individual updates use
      formattedDisplayDate = formatDateOnly(userDate);
      console.log(`Formatted display date: ${formattedDisplayDate}`);
      
      // For the API, format date in the exact format the backend expects: YYYY-MM-DDTHH:MM:SS
      // We'll use the same approach as individual updates - use formatDate first
      // This ensures the date is properly formatted for the backend
        formattedDate = `${userDate.getFullYear()}-${String(userDate.getMonth() + 1).padStart(2, '0')}-${String(userDate.getDate()).padStart(2, '0')}T00:00:00`;
      
      console.log(`Selected date: ${userDate.toLocaleDateString()}`);
      console.log(`Formatted for API (backend format): ${formattedDate}`);
      } else {
        console.log('Clearing delivery dates for selected manifests');
      }
      
      // Get tracking numbers of selected manifests
      const trackingNos = selectedManifests.map(m => m.trackingNo);
      console.log(`Selected manifest tracking numbers (${trackingNos.length}):`, trackingNos);
      
      // Log the manifests before update
      console.log('Manifests before update:', 
        selectedManifests.map(m => ({
          trackingNo: m.trackingNo,
          deliveryDate: m.deliveryDate
        }))
      );
      
      // Call the API to update delivery dates in bulk
      const response = await manifestService.updateBulkDeliveryDates(
        trackingNos,
        formattedDate,
        currentUser.token
      );
      
      if (response.success) {
        // Check if there were any failures mentioned in the message
        const failureMatch = response.message?.match(/\((\d+) failed\)/);
        const failedCount = failureMatch ? parseInt(failureMatch[1]) : 0;
        
        if (failedCount > 0) {
          console.warn(`${failedCount} manifest updates failed`);
          toast.warning(`${failedCount} out of ${trackingNos.length} manifests could not be updated`);
        } else {
          const actionMessage = formattedDate ? 'updated' : 'cleared';
          toast.success(`Delivery date ${actionMessage} for ${trackingNos.length} manifest(s)`);
        }
        
        console.log('API response success:', response.success);
        console.log('API response data count:', response.data?.length || 0);
        
        if (response.data && response.data.length > 0) {
          // Check what's returned from the API
          console.log('Sample API response data:', 
            response.data.slice(0, Math.min(3, response.data.length)).map(m => ({
              trackingNo: m.trackingNo,
              deliveryDate: m.deliveryDate
            }))
          );
          
          // Update the manifests in the local state with the returned data
          setManifests(prevManifests => {
            const updatedManifests = prevManifests.map(manifest => {
              const updatedManifest = response.data.find(m => m.trackingNo === manifest.trackingNo);
              return updatedManifest || manifest;
            });
            
            // Log updates
            console.log('State updated with API response data');
            return updatedManifests;
          });
        } else {
          console.log('No manifests returned from API. Manually updating the local state.');
          
          // If API doesn't return updated manifests, manually update the local state
          setManifests(prevManifests => {
            const updatedManifests = prevManifests.map(manifest => {
              // If this manifest is in the selected list, update its delivery date
              if (trackingNos.includes(manifest.trackingNo)) {
                return {
                  ...manifest,
                  // Use the pre-formatted display date that matches the individual update format
                  deliveryDate: formattedDisplayDate
                };
              }
              return manifest;
            });
            
            // Log the manually updated manifests
            const updatedSelectedManifests = updatedManifests.filter(m => 
              trackingNos.includes(m.trackingNo)
            );
            console.log('Manually updated manifests:', 
              updatedSelectedManifests.map(m => ({
                trackingNo: m.trackingNo,
                deliveryDate: m.deliveryDate
              }))
            );
            
            return updatedManifests;
          });
        }
        
        // Close the dialog and reset the date
        setBulkDeliveryDateDialogOpen(false);
        setBulkDeliveryDate(null);
        
        // Refresh the manifest list to get the updated data from the server
        fetchAllManifests();
      } else {
        toast.error(response.message || 'Failed to update delivery dates');
        console.error('API response indicated failure:', response.message);
      }
    } catch (err: any) {
      console.error('Error updating delivery dates in bulk:', err);
      toast.error(err.response?.data?.message || 'An error occurred while updating delivery dates');
    } finally {
      setManifestLoading(false);
    }
  };
  
  // Helper function to get timezone offset string in format +HH:MM or -HH:MM
  const getTimezoneOffsetString = () => {
    const offset = new Date().getTimezoneOffset();
    const sign = offset <= 0 ? '+' : '-';
    const absOffset = Math.abs(offset);
    const hours = Math.floor(absOffset / 60).toString().padStart(2, '0');
    const minutes = (absOffset % 60).toString().padStart(2, '0');
    return `${sign}${hours}:${minutes}`;
  };

  return (
    <Box 
      sx={{ 
        p: 3, 
        width: '100%',
        maxWidth: '100%',
        overflowX: 'auto'
      }}
    >
      {/* Global Progress Bar for Bulk Creation */}
      {isUploading && (
        <Box sx={{ position: 'fixed', top: 0, left: 0, width: '100%', zIndex: 2000 }}>
          <LinearProgress 
            variant="determinate" 
            value={uploadProgress} 
            sx={{ height: 8, borderRadius: 0 }}
          />
          <Box sx={{ position: 'absolute', top: 0, left: 0, width: '100%', textAlign: 'center', color: 'white', fontWeight: 'bold', fontSize: '1rem', mt: 0.5 }}>
            {uploadProgress}% complete
          </Box>
        </Box>
      )}
      {/* Breadcrumbs and back button */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Breadcrumbs separator="›" aria-label="breadcrumb">
            <Link
              underline="hover"
              color="inherit"
              onClick={() => navigate('/containers')}
              sx={{ cursor: 'pointer' }}
            >
              Containers
            </Link>
            <Typography color="text.primary">
              {containerNo}
            </Typography>
          </Breadcrumbs>
          <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
            <Typography variant="h4" sx={{ fontWeight: 'bold', color: theme.palette.primary.main }}>
              Container {containerNo} Details
            </Typography>
          </Box>
        </Box>
        <Button
          variant="outlined"
          startIcon={<ArrowBackIcon />}
          onClick={() => navigate('/containers')}
          sx={{ borderRadius: 2 }}
        >
          Back to Containers
        </Button>
      </Box>
      
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}
      
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 5 }}>
          <CircularProgress />
        </Box>
      ) : container ? (
        <>
          {/* Container Information Section with Inline Edit */}
          <Paper
            elevation={1}
            sx={{ p: 3, mb: 4, borderRadius: 2, border: '1px solid rgba(0,0,0,0.08)' }}
          >
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
              <Typography variant="h6">Container Information</Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Tooltip title="Refresh container data">
                  <IconButton 
                    onClick={refreshAllData} 
                    size="small"
                    sx={{ 
                      color: theme.palette.primary.main,
                      '&:hover': {
                        bgcolor: 'rgba(25, 118, 210, 0.08)',
                      }
                    }}
                  >
                    <RefreshIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
                {!editMode ? (
                  <Box>
                    <Button 
                      variant="outlined" 
                      color="primary" 
                      size="small" 
                      startIcon={<EditIcon />} 
                      onClick={handleEditContainer}
                      sx={{ ml: 1 }}
                    >
                      Edit
                    </Button>
                  </Box>
                ) : (
                  <>
                    <Button 
                      variant="outlined" 
                      color="primary" 
                      size="small" 
                      onClick={handleSaveEdit}
                      sx={{ ml: 1 }}
                    >
                      Save
                    </Button>
                    <Button 
                      variant="outlined" 
                      color="inherit" 
                      size="small" 
                      onClick={handleCancelEdit}
                      sx={{ ml: 1 }}
                    >
                      Cancel
                    </Button>
                  </>
                )}
              </Box>
            </Box>
            <Divider sx={{ mb: 3 }} />
            
            <Box sx={{ 
              display: 'grid', 
              gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr', md: '1fr 1fr 1fr' }, 
              gap: 3 
            }}>
              {/* Basic Container Details (first row) */}
              <Box>
                <Typography variant="body2" color="text.secondary">Container No</Typography>
                <Typography variant="body1" sx={{ fontWeight: 500, display: 'flex', alignItems: 'center', mt: 0.5 }}>
                  <LocalShippingOutlinedIcon sx={{ mr: 1, color: theme.palette.primary.main }} />
                  {container.containerNo}
                </Typography>
              </Box>
              
              <Box>
                <Typography variant="body2" color="text.secondary">Client</Typography>
                <Typography variant="body1" sx={{ fontWeight: 500, display: 'flex', alignItems: 'center', mt: 0.5 }}>
                  <PersonIcon sx={{ mr: 1, color: theme.palette.grey[600], fontSize: 'small' }} />
                  {container.client?.companyName || '-'}
                </Typography>
              </Box>

              <Box>
                <Typography variant="body2" color="text.secondary">Status</Typography>
                <Typography 
                  variant="body1" 
                  component="div" 
                  sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}
                >
                  <Tooltip title="Click to update container status">
                    <Chip 
                      icon={getContainerStatusIcon(container.status)}
                      label={container.status}
                      size="small"
                      sx={{ 
                        ...getStatusChipColor(container.status),
                        fontWeight: 'medium',
                        fontSize: '0.75rem',
                        cursor: 'pointer'
                      }}
                      onClick={() => {
                        setNewStatus(container?.status || null);
                        setStatusDialogOpen(true);
                      }}
                    />
                  </Tooltip>
                </Typography>
              </Box>

              {/* Transport Information (second row) */}
              <Box>
                <Typography variant="body2" color="text.secondary">Truck No</Typography>
                {editMode ? (
                  <TextField
                    fullWidth
                    size="small"
                    value={editedContainer?.truckNo || ''}
                    onChange={(e) => setEditedContainer({...editedContainer!, truckNo: e.target.value})}
                    error={!!formErrors.truckNo}
                    helperText={formErrors.truckNo}
                    sx={{ mt: 0.5 }}
                  />
                ) : (
                  <Typography variant="body1" sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                    <LocalShippingIcon sx={{ mr: 1, color: theme.palette.grey[600], fontSize: 'small' }} />
                    {container.truckNo || '-'}
                  </Typography>
                )}
              </Box>
              
              <Box>
                <Typography variant="body2" color="text.secondary">Vessel Voyage No</Typography>
                {editMode ? (
                  <TextField
                    fullWidth
                    size="small"
                    value={editedContainer?.vesselVoyageNo || ''}
                    onChange={(e) => setEditedContainer({...editedContainer!, vesselVoyageNo: e.target.value})}
                    error={!!formErrors.vesselVoyageNo}
                    helperText={formErrors.vesselVoyageNo}
                    sx={{ mt: 0.5 }}
                  />
                ) : (
                  <Typography variant="body1" sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                    <DirectionsBoatOutlinedIcon sx={{ mr: 1, color: theme.palette.grey[600], fontSize: 'small' }} />
                    {container.vesselVoyageNo || '-'}
                  </Typography>
                )}
              </Box>
              
              <Box>
                <Typography variant="body2" color="text.secondary">Manifest Quantity</Typography>
                {editMode ? (
                  <TextField
                    fullWidth
                    size="small"
                    type="number"
                    value={editedContainer?.manifestQuantity || 0}
                    onChange={(e) => setEditedContainer({...editedContainer!, manifestQuantity: parseInt(e.target.value) || 0})}
                    InputProps={{ inputProps: { min: 0 } }}
                    sx={{ mt: 0.5 }}
                  />
                ) : (
                <Typography variant="body1" sx={{ fontWeight: 500, mt: 0.5 }}>
                  {container.manifestQuantity || '0'}
                </Typography>
                )}
              </Box>

              {/* Date Information (third row) */}
              <Box>
                <Typography variant="body2" color="text.secondary">Created Date</Typography>
                <Typography variant="body1" sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                  <CalendarTodayIcon sx={{ mr: 1, color: theme.palette.grey[600], fontSize: 'small' }} />
                  {formatDateTime(container.createdDate)}
                </Typography>
              </Box>
              
              <Box>
                <Typography variant="body2" color="text.secondary">ETA Requested</Typography>
                {editMode ? (
                  <LocalizationProvider dateAdapter={AdapterDateFns}>
                    <DateTimePicker
                      label="ETA Requested"
                      value={editedContainer?.etaRequestedDate ? new Date(editedContainer.etaRequestedDate) : null}
                      onChange={(newValue) => handleContainerDateChange('etaRequestedDate', newValue)}
                      format="dd MMM yyyy HH:mm"
                      ampm={false}
                      
                      slotProps={{ 
                        textField: { 
                          fullWidth: true, 
                          size: "small", 
                          sx: { mt: 0.5 },
                          InputProps: {
                            endAdornment: editedContainer?.etaRequestedDate ? (
                              <InputAdornment position="end">
                                <IconButton
                                  edge="end"
                                  size="small"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleContainerDateChange('etaRequestedDate', null);
                                  }}
                                  title="Clear date"
                                >
                                  <ClearIcon fontSize="small" />
                                </IconButton>
                              </InputAdornment>
                            ) : null
                          }
                        },
                        popper: { sx: { zIndex: 9999 } }
                      }}
                    />
                  </LocalizationProvider>
                ) : (
                  <Typography variant="body1" sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                    <EventAvailableIcon sx={{ mr: 1, color: theme.palette.grey[600], fontSize: 'small' }} />
                    {formatDateTime(container.etaRequestedDate)}
                  </Typography>
                )}
              </Box>
              
              <Box>
                <Typography variant="body2" color="text.secondary">Portnet ETA</Typography>
                {editMode ? (
                  <LocalizationProvider dateAdapter={AdapterDateFns}>
                    <DateTimePicker
                      value={editedContainer?.portnetEta ? new Date(editedContainer.portnetEta) : null}
                      onChange={(newValue) => handleContainerDateChange('portnetEta', newValue)}
                      format="dd MMM yyyy HH:mm"
                      ampm={false}
                      slotProps={{ 
                        textField: { 
                          fullWidth: true, 
                          size: "small", 
                          sx: { mt: 0.5 },
                          InputProps: {
                            endAdornment: editedContainer?.portnetEta ? (
                              <InputAdornment position="end">
                                <IconButton
                                  edge="end"
                                  size="small"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleContainerDateChange('portnetEta', null);
                                  }}
                                  title="Clear date"
                                >
                                  <ClearIcon fontSize="small" />
                                </IconButton>
                              </InputAdornment>
                            ) : null
                          }
                        },
                        popper: { sx: { zIndex: 9999 } }
                      }}
                    />
                  </LocalizationProvider>
                ) : (
                  <Typography variant="body1" sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                    <DirectionsBoatIcon sx={{ mr: 1, color: theme.palette.grey[600], fontSize: 'small' }} />
                    {formatDateTime(container.portnetEta)}
                  </Typography>
                )}
              </Box>
              
              {/* More Date Information (fourth row) */}
              <Box>
                <Typography variant="body2" color="text.secondary">ETA Allocated</Typography>
                {editMode ? (
                  <LocalizationProvider dateAdapter={AdapterDateFns}>
                    <DateTimePicker
                      value={editedContainer?.etaAllocated ? new Date(editedContainer.etaAllocated) : null}
                      onChange={(newValue) => handleContainerDateChange('etaAllocated', newValue)}
                      format="dd MMM yyyy HH:mm"
                      ampm={false}
                      slotProps={{ 
                        textField: { 
                          fullWidth: true, 
                          size: "small", 
                          sx: { mt: 0.5 },
                          InputProps: {
                            endAdornment: editedContainer?.etaAllocated ? (
                              <InputAdornment position="end">
                                <IconButton
                                  edge="end"
                                  size="small"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleContainerDateChange('etaAllocated', null);
                                  }}
                                  title="Clear date"
                                >
                                  <ClearIcon fontSize="small" />
                                </IconButton>
                              </InputAdornment>
                            ) : null
                          }
                        },
                        popper: { sx: { zIndex: 9999 } }
                      }}
                    />
                  </LocalizationProvider>
                ) : (
                  <Typography variant="body1" sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                    <TimelineIcon sx={{ mr: 1, color: theme.palette.grey[600], fontSize: 'small' }} />
                    {formatDateTime(container.etaAllocated)}
                  </Typography>
                )}
              </Box>
              
              <Box>
                <Typography variant="body2" color="text.secondary">Arrival Date</Typography>
                {editMode ? (
                  <LocalizationProvider dateAdapter={AdapterDateFns}>
                    <DateTimePicker
                      value={editedContainer?.arrivalDate ? new Date(editedContainer.arrivalDate) : null}
                      onChange={(newValue) => handleContainerDateChange('arrivalDate', newValue)}
                      format="dd MMM yyyy HH:mm"
                      ampm={false}
                      slotProps={{ 
                        textField: { 
                          fullWidth: true, 
                          size: "small", 
                          sx: { mt: 0.5 },
                          InputProps: {
                            endAdornment: editedContainer?.arrivalDate ? (
                              <InputAdornment position="end">
                                <IconButton
                                  edge="end"
                                  size="small"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleContainerDateChange('arrivalDate', null);
                                  }}
                                  title="Clear date"
                                >
                                  <ClearIcon fontSize="small" />
                                </IconButton>
                              </InputAdornment>
                            ) : null
                          }
                        },
                        popper: { sx: { zIndex: 9999 } }
                      }}
                    />
                  </LocalizationProvider>
                ) : (
                  <Typography variant="body1" sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                    <LocalShippingIcon sx={{ mr: 1, color: theme.palette.grey[600], fontSize: 'small' }} />
                    {formatDateTime(container.arrivalDate)}
                  </Typography>
                )}
              </Box>
              
              <Box>
                <Typography variant="body2" color="text.secondary">Loading Bay</Typography>
                {editMode ? (
                  <TextField
                    fullWidth
                    size="small"
                    value={editedContainer?.loadingBay || ''}
                    onChange={(e) => setEditedContainer({...editedContainer!, loadingBay: e.target.value})}
                    sx={{ mt: 0.5 }}
                  />
                ) : (
                  <Typography variant="body1" sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                    <WarehouseIcon sx={{ mr: 1, color: theme.palette.grey[600], fontSize: 'small' }} />
                    {container.loadingBay || '-'}
                  </Typography>
                )}
              </Box>
              
              {/* Unstuffing Information (fifth row) */}
              <Box>
                <Typography variant="body2" color="text.secondary">Unstuff Date</Typography>
                {editMode ? (
                  <LocalizationProvider dateAdapter={AdapterDateFns}>
                    <DateTimePicker
                      value={editedContainer?.unstuffDate ? new Date(editedContainer.unstuffDate) : null}
                      onChange={(newValue) => handleContainerDateChange('unstuffDate', newValue)}
                      format="dd MMM yyyy HH:mm"
                      ampm={false}
                      slotProps={{ 
                        textField: { 
                          fullWidth: true, 
                          size: "small", 
                          sx: { mt: 0.5 },
                          InputProps: {
                            endAdornment: editedContainer?.unstuffDate ? (
                              <InputAdornment position="end">
                                <IconButton
                                  edge="end"
                                  size="small"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleContainerDateChange('unstuffDate', null);
                                  }}
                                  title="Clear date"
                                >
                                  <ClearIcon fontSize="small" />
                                </IconButton>
                              </InputAdornment>
                            ) : null
                          }
                        },
                        popper: { sx: { zIndex: 9999 } }
                      }}
                    />
                  </LocalizationProvider>
                ) : (
                  <Typography variant="body1" sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                    <ConstructionIcon sx={{ mr: 1, color: theme.palette.grey[600], fontSize: 'small' }} />
                    {formatDateTime(container.unstuffDate)}
                  </Typography>
                )}
              </Box>
              
              <Box>
                <Typography variant="body2" color="text.secondary">Unstuff Completed Date</Typography>
                {editMode ? (
                  <LocalizationProvider dateAdapter={AdapterDateFns}>
                    <DateTimePicker
                      value={editedContainer?.unstuffCompletedDate ? new Date(editedContainer.unstuffCompletedDate) : null}
                      onChange={(newValue) => handleContainerDateChange('unstuffCompletedDate', newValue)}
                      format="dd MMM yyyy HH:mm"
                      ampm={false}
                      slotProps={{ 
                        textField: { 
                          fullWidth: true, 
                          size: "small", 
                          sx: { mt: 0.5 },
                          InputProps: {
                            endAdornment: editedContainer?.unstuffCompletedDate ? (
                              <InputAdornment position="end">
                                <IconButton
                                  edge="end"
                                  size="small"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleContainerDateChange('unstuffCompletedDate', null);
                                  }}
                                  title="Clear date"
                                >
                                  <ClearIcon fontSize="small" />
                                </IconButton>
                              </InputAdornment>
                            ) : null
                          }
                        },
                        popper: { sx: { zIndex: 9999 } }
                      }}
                    />
                  </LocalizationProvider>
                ) : (
                  <Typography variant="body1" sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                    <CheckCircleIcon sx={{ mr: 1, color: theme.palette.grey[600], fontSize: 'small' }} />
                    {formatDateTime(container.unstuffCompletedDate)}
                  </Typography>
                )}
              </Box>
              
              <Box>
                <Typography variant="body2" color="text.secondary">Pull Out Date</Typography>
                {editMode ? (
                  <LocalizationProvider dateAdapter={AdapterDateFns}>
                    <DateTimePicker
                      value={editedContainer?.pullOutDate ? new Date(editedContainer.pullOutDate) : null}
                      onChange={(newValue) => handleContainerDateChange('pullOutDate', newValue)}
                      format="dd MMM yyyy HH:mm"
                      ampm={false}
                      slotProps={{ 
                        textField: { 
                        fullWidth: true, 
                          size: "small", 
                          sx: { mt: 0.5 },
                          InputProps: {
                            endAdornment: editedContainer?.pullOutDate ? (
                              <InputAdornment position="end">
                                <IconButton
                                  edge="end"
                                  size="small"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleContainerDateChange('pullOutDate', null);
                                  }}
                                  title="Clear date"
                                >
                                  <ClearIcon fontSize="small" />
                                </IconButton>
                              </InputAdornment>
                            ) : null
                          }
                        },
                        popper: { sx: { zIndex: 9999 } }
                      }}
                    />
                  </LocalizationProvider>
                ) : (
                  <Typography variant="body1" sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                    <LocalShippingOutlinedIcon sx={{ mr: 1, color: theme.palette.grey[600], fontSize: 'small' }} />
                    {formatDateTime(container.pullOutDate)}
                  </Typography>
                )}
              </Box>
              
              {/* Unstuff Team (sixth row) */}
              <Box>
                <Typography variant="body2" color="text.secondary">Unstuff Team</Typography>
                {editMode ? (
                  <TextField
                    fullWidth
                    size="small"
                    value={editedContainer?.unstuffTeam || ''}
                    onChange={(e) => setEditedContainer({...editedContainer!, unstuffTeam: e.target.value})}
                    sx={{ mt: 0.5 }}
                  />
                ) : (
                  <Typography variant="body1" sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                    <PersonIcon sx={{ mr: 1, color: theme.palette.grey[600], fontSize: 'small' }} />
                    {container.unstuffTeam || '-'}
                  </Typography>
                )}
              </Box>
            </Box>
            
            {editMode && (
              <Box sx={{ mt: 3 }}>
                <Typography variant="body2" color="text.secondary">Remarks</Typography>
                <TextField
                  fullWidth
                  multiline
                  rows={2}
                  value={editedContainer?.remark || ''}
                  onChange={(e) => setEditedContainer({...editedContainer!, remark: e.target.value})}
                  sx={{ mt: 0.5 }}
                />
              </Box>
            )}
          </Paper>
          
          {/* Manifest List Section */}
          <Paper
            elevation={1}
            sx={{ 
              mb: 4, 
              borderRadius: 2, 
              border: '1px solid rgba(0,0,0,0.08)',
              width: '100%',
              display: 'flex',
              flexDirection: 'column',
              overflow: 'visible', // Changed from 'hidden' to 'visible' to match manifest list
              height: 'auto',
              minHeight: '200px',
              position: 'relative', // Add positioning context
              maxWidth: '100%', // Ensure paper respects parent width
              boxShadow: 2
            }}
          >
            {/* Toolbar with actions and stats */}
            <Toolbar
                  sx={{ 
                pl: { sm: 2 },
                pr: { xs: 1, sm: 1 },
                py: 1,
                borderBottom: '1px solid rgba(224, 224, 224, 1)'
              }}
            >
              <Typography
                sx={{ flex: '1 1 100%' }}
                variant="h6"
                component="div"
              >
                Manifest List
                {!manifestLoading && (
                  <Typography variant="caption" sx={{ ml: 2 }}>
                    {getFilteredManifests && getFilteredManifests.length > 0 
                      ? `${getFilteredManifests.length} manifests for container ${containerNo}` 
                      : "No manifests found for this container"}
                  </Typography>
                )}
                {selectedManifests.length > 0 && (
                  <Typography variant="caption" sx={{ ml: 2 }}>
                    ({selectedManifests.length} selected)
                    </Typography>
                )}
                    </Typography>
              
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                {/* Generate labels button - enabled only when manifests are selected */}
                <Tooltip title={selectedManifests.length === 0 ? "Select manifests to generate labels" : `Generate labels for ${selectedManifests.length} selected manifest(s)`}>
                  <span>
                    <Button
                      variant={selectedManifests.length > 0 ? "contained" : "outlined"}
                      color="primary"
                      startIcon={<PrintIcon />}
                      size="small"
                          onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        console.log("Generate Labels clicked, selected manifests:", selectedManifests.length);
                        if (selectedManifests.length === 0) {
                          console.warn("No manifests selected for label generation");
                          clearManifestSelection(); // Ensure selection is cleared
                          return;
                        }
                        handleBulkLabelGeneration();
                      }}
                      disabled={selectedManifests.length === 0}
                      sx={{ 
                        mr: 1,
                        borderRadius: 2,
                        minWidth: '160px',
                        px: 2,
                        whiteSpace: 'nowrap',
                        fontWeight: selectedManifests.length > 0 ? 'bold' : 'normal',
                        boxShadow: selectedManifests.length > 0 ? 2 : 0,
                      }}
                    >
                      Generate Labels {selectedManifests.length > 0 ? `(${selectedManifests.length})` : ''}
                    </Button>
                  </span>
                </Tooltip>
                
                <Tooltip title={selectedManifests.length === 0 ? "Select manifests to delete" : `Delete ${selectedManifests.length} selected manifest(s)`}>
                  <span>
                    <Button
                      variant={selectedManifests.length > 0 ? "contained" : "outlined"}
                      color="error"
                      startIcon={<DeleteIcon />}
                      size="small"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        if (selectedManifests.length === 0) {
                          console.warn("No manifests selected for deletion");
                          clearManifestSelection(); // Ensure selection is cleared
                          return;
                        }
                        showConfirmDialog(
                          'Bulk Delete Manifests',
                          `Are you sure you want to delete ${selectedManifests.length} selected manifest${selectedManifests.length !== 1 ? 's' : ''}? This action cannot be undone.`,
                          handleBulkDeleteManifests
                        );
                      }}
                      disabled={selectedManifests.length === 0}
                      sx={{ 
                        mr: 1,
                        borderRadius: 2,
                        minWidth: '160px',
                        px: 2,
                        whiteSpace: 'nowrap',
                        fontWeight: selectedManifests.length > 0 ? 'bold' : 'normal',
                        boxShadow: selectedManifests.length > 0 ? 2 : 0,
                      }}
                    >
                      Delete Selected {selectedManifests.length > 0 ? `(${selectedManifests.length})` : ''}
                    </Button>
                  </span>
                </Tooltip>
                
                {/* Bulk Delivery Date Update button */}
                <Tooltip title={selectedManifests.length === 0 ? "Select manifests to update delivery date" : `Update delivery date for ${selectedManifests.length} selected manifest(s)`}>
                  <span>
                    <Button
                      variant={selectedManifests.length > 0 ? "contained" : "outlined"}
                      color="secondary"
                      startIcon={<EventAvailableIcon />}
                      size="small"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        if (selectedManifests.length === 0) {
                          console.warn("No manifests selected for delivery date update");
                          clearManifestSelection(); // Ensure selection is cleared
                          return;
                        }
                        setBulkDeliveryDateDialogOpen(true);
                      }}
                      disabled={selectedManifests.length === 0}
                      sx={{ 
                        mr: 1,
                        borderRadius: 2,
                        minWidth: '160px',
                        px: 2,
                        whiteSpace: 'nowrap',
                        fontWeight: selectedManifests.length > 0 ? 'bold' : 'normal',
                        boxShadow: selectedManifests.length > 0 ? 2 : 0,
                      }}
                    >
                      Update Delivery Date {selectedManifests.length > 0 ? `(${selectedManifests.length})` : ''}
                    </Button>
                  </span>
                </Tooltip>
                
                {/* Export to Excel button */}
                <Button
                  variant="outlined"
                  color="info"
                  startIcon={<GetAppIcon />}
                  size="small"
                  onClick={handleExportToExcel}
                  sx={{
                    mr: 1,
                    borderRadius: 2,
                    minWidth: '120px',
                    px: 2,
                    whiteSpace: 'nowrap',
                    backgroundColor: theme.palette.info.main,
                    color: theme.palette.common.white,
                    '&:hover': {
                      backgroundColor: theme.palette.info.dark,
                    },
                  }}
                >
                  Export {selectedManifests.length > 0 ? `(${selectedManifests.length})` : ''}
                </Button>
                
                {/* Add new manifest button */}
              <Button 
                variant="contained" 
                  startIcon={<AddIcon />}
                  size="small"
                  onClick={handleOpenManifestDialog}
                  sx={{ 
                    fontWeight: 'bold',
                    borderRadius: 2,
                    boxShadow: 2,
                    px: 3,
                    py: 1,
                    textTransform: 'none',
                    whiteSpace: 'nowrap',
                    '&:hover': {
                      boxShadow: 4,
                      bgcolor: theme.palette.primary.dark
                    },
                  }}
                >
                  Create Manifest
              </Button>
                
                <Button 
                  variant="contained"
                  startIcon={<CloudUploadIcon />}
                  size="small"
                  onClick={() => setBulkUploadDialogOpen(true)}
            sx={{ 
                    ml: 2,
                    fontWeight: 'bold',
              borderRadius: 2, 
                    boxShadow: 2,
                    px: 3,
                    py: 1,
                    textTransform: 'none',
                    whiteSpace: 'nowrap',
                    bgcolor: theme.palette.success.main,
                    '&:hover': {
                      boxShadow: 4,
                      bgcolor: theme.palette.success.dark
                    },
                  }}
                >
                  Bulk Upload
                </Button>
                
                {/* Add auto-resize columns button */}
                <Tooltip title="Auto-resize columns">
                  <IconButton 
                    size="small" 
                    onClick={handleAutosizeColumns}
                    sx={{
                      color: theme.palette.primary.main,
                      '&:hover': {
                        bgcolor: 'rgba(25, 118, 210, 0.08)',
                      }
                    }}
                  >
                    <AutorenewIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
                
                {/* Refresh button */}
                <Tooltip title="Refresh manifest list">
                  <span>
                    <IconButton 
                      onClick={fetchAllManifests} 
                      disabled={manifestLoading}
                      size="small"
                sx={{ 
                        color: theme.palette.primary.main,
                        '&:hover': {
                          bgcolor: 'rgba(25, 118, 210, 0.08)',
                        }
                      }}
                    >
                      <RefreshIcon />
                    </IconButton>
                  </span>
                </Tooltip>
              </Box>
            </Toolbar>
            
            {/* Filter Section */}
            <Box sx={{ mb: 2 }}>
              <Paper sx={{ p: 2, borderRadius: 2, mb: 2 }}>
                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle2" sx={{ mb: 1 }}>Search</Typography>
                  <TextField
                    fullWidth
                    placeholder="Search manifests by tracking no, internal ID, customer name, address, etc."
                    variant="outlined"
                    size="small"
                    value={filters.search}
                    onChange={handleSearchChange}
                    sx={{ 
                      bgcolor: 'white',
                      borderRadius: 1,
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 1,
                        '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                          borderColor: theme.palette.primary.main,
                          borderWidth: '1px'
                        },
                        '&:hover .MuiOutlinedInput-notchedOutline': {
                          borderColor: 'rgba(0, 0, 0, 0.42)'
                        }
                      },
                      '& .MuiInputBase-root': {
                        '&:focus, &:focus-visible, &:focus-within': {
                          outline: 'none',
                          boxShadow: 'none'
                        }
                      }
                    }}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <SearchIcon color="action" />
                        </InputAdornment>
                      ),
                      endAdornment: filters.search && (
                        <InputAdornment position="end">
                          <IconButton
                            size="small"
                            onClick={() => setFilters(prev => ({ ...prev, search: '' }))}
                            sx={{
                              '&:hover': {
                                bgcolor: 'rgba(0, 0, 0, 0.04)'
                              }
                            }}
                          >
                            <ClearIcon fontSize="small" />
                          </IconButton>
                        </InputAdornment>
                      )
                    }}
                  />
                  </Box>

                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle2" sx={{ mb: 1 }}>Filter by Status</Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                    {Object.values(ManifestStatus).map((status) => {
                      const isSelected = filters.status.includes(status);
                      const statusColors = getManifestStatusChipColor(status);
                      
                      return (
                        <Chip
                          key={status}
                          label={status.replace(/_/g, ' ')}
                          onClick={() => handleStatusFilterChange(status)}
                          sx={{
                            backgroundColor: isSelected ? statusColors.bg : 'transparent',
                            color: isSelected ? statusColors.color : 'text.secondary',
                            borderColor: isSelected ? 'transparent' : 'divider',
                            border: isSelected ? 'none' : '1px solid',
                            fontWeight: isSelected ? 'medium' : 'normal',
                            '&:hover': {
                              backgroundColor: isSelected ? statusColors.bg : alpha(statusColors.bg, 0.3),
                            }
                          }}
                          variant={isSelected ? "filled" : "outlined"}
                        />
                      );
                    })}
                  </Box>
                  </Box>
                  
                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle2" sx={{ mb: 1 }}>Delivery Date Range</Typography>
                      <Box sx={{ display: 'flex', gap: 2 }}>
                        <LocalizationProvider dateAdapter={AdapterDateFns}>
                      <Box sx={{ width: '50%' }}>
                          <DatePicker
                            label="From"
                            value={filters.dateRange.start}
                            onChange={(date) => handleDateRangeChange('start', date)}
                            format="dd MMM yyyy"
                            slotProps={{ 
                              textField: { 
                                fullWidth: true,
                                size: "small",
                                variant: "outlined",
                                InputProps: {
                                  endAdornment: filters.dateRange.start && (
                                    <InputAdornment position="end">
                                      <IconButton
                                        edge="end"
                                        size="small"
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          handleDateRangeChange('start', null);
                                        }}
                                        title="Clear date"
                                      >
                                        <ClearIcon fontSize="small" />
                                      </IconButton>
                                    </InputAdornment>
                                  )
                                }
                              }
                            }}
                          />
                      </Box>
                        
                      <Box sx={{ width: '50%' }}>
                          <DatePicker
                            label="To"
                            value={filters.dateRange.end}
                            onChange={(date) => handleDateRangeChange('end', date)}
                            format="dd MMM yyyy"
                            slotProps={{ 
                              textField: { 
                                fullWidth: true,
                                size: "small",
                                variant: "outlined",
                                InputProps: {
                                  endAdornment: filters.dateRange.end && (
                                    <InputAdornment position="end">
                                      <IconButton
                                        edge="end"
                                        size="small"
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          handleDateRangeChange('end', null);
                                        }}
                                        title="Clear date"
                                      >
                                        <ClearIcon fontSize="small" />
                                      </IconButton>
                                    </InputAdornment>
                                  )
                                }
                              }
                            }}
                          />
                      </Box>
                        </LocalizationProvider>
                      </Box>
                </Box>
                
                <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
                  <Box sx={{ width: '50%' }}>
                    <Autocomplete
                      options={locationZones}
                      getOptionLabel={(option) => option.name || ''}
                      value={locationZones.find(loc => loc.name === filters.location) || null}
                          onChange={handleLocationFilterChange}
                      loading={locationsLoading}
                      loadingText="Loading location zones..."
                      noOptionsText="No location zones available"
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          label="Location Zone"
                          fullWidth
                          size="small"
                          variant="outlined"
                          InputProps={{
                            ...params.InputProps,
                            startAdornment: (
                              <>
                              <InputAdornment position="start">
                                  <LocationOnIcon color="action" fontSize="small" />
                              </InputAdornment>
                                {params.InputProps.startAdornment}
                              </>
                            ),
                            endAdornment: (
                              <>
                                {locationsLoading ? <CircularProgress color="inherit" size={20} /> : null}
                                {params.InputProps.endAdornment}
                              </>
                            )
                          }}
                        />
                      )}
                    />
                  </Box>
                  
                  <Box sx={{ width: '50%' }}>
                        <Autocomplete
                          options={vehicleTypes}
                          getOptionLabel={(option) => option.name || ''}
                          value={vehicleTypes.find(vehicle => vehicle.name === filters.deliveryVehicle) || null}
                          onChange={(event, newValue) => handleDeliveryVehicleFilterChange(event, newValue)}
                          loading={vehicleTypesLoading}
                          loadingText="Loading vehicle types..."
                          noOptionsText="No vehicle types available"
                          componentsProps={{
                            popper: {
                              sx: {
                                zIndex: 10001
                              }
                            }
                          }}
                          renderInput={(params) => (
                            <TextField
                              {...params}
                              label="Delivery Vehicle"
                              fullWidth
                              size="small"
                              variant="outlined"
                              InputProps={{
                                ...params.InputProps,
                                startAdornment: (
                                  <>
                                  <InputAdornment position="start">
                                      <LocalShippingIcon color="action" fontSize="small" />
                                  </InputAdornment>
                                    {params.InputProps.startAdornment}
                                  </>
                                ),
                                endAdornment: (
                                  <>
                                    {vehicleTypesLoading ? <CircularProgress color="inherit" size={20} /> : null}
                                    {params.InputProps.endAdornment}
                                  </>
                                )
                              }}
                            />
                          )}
                        />
                      </Box>
                </Box>

                {/* Compact Totals Display */}
                <Box sx={{ mt: 2 }}>
                  <Typography variant="body2" sx={{
                    color: theme.palette.text.secondary,
                    fontWeight: 'medium'
                  }}>
                    <strong>Total CBM:</strong> {totals.totalCBM.toFixed(2)} &nbsp;&nbsp;
                    <strong>Total Pallets:</strong> {totals.totalPallets.toLocaleString()} &nbsp;&nbsp;
                    <strong>Total Pieces:</strong> {totals.totalPieces.toLocaleString()} &nbsp;&nbsp;
                    <strong>Total Inbound Pieces:</strong> {totals.totalInboundPieces.toLocaleString()}
                  </Typography>
                </Box>

                      <FormControlLabel
                        control={
                          <Checkbox
                            checked={filters.hasDeliveryDate}
                            onChange={handleHasDeliveryDateChange}
                            color="primary"
                          />
                        }
                        label="Show only manifests with delivery date"
                      />
              
              {(filters.search || 
                filters.status.length > 0 || 
                filters.dateRange.start || 
                filters.dateRange.end || 
                filters.location || 
                filters.deliveryVehicle || 
                filters.hasDeliveryDate) && (
                  <Box sx={{ mt: 2, display: 'flex', justifyContent: 'flex-end' }}>
                    <Button
                      variant="outlined"
                      size="small"
                      onClick={resetFilters}
                      startIcon={<ClearAllIcon />}
                      color="error"
                      sx={{ borderRadius: 2, px: 2 }}
                    >
                      Clear All Filters
                    </Button>
                </Box>
              )}
              </Paper>
            </Box>
            
            {/* Column visibility toggle buttons - in sequence */}
            <Box sx={{ 
              p: 1.5,
              borderBottom: '1px solid rgba(224, 224, 224, 1)'
            }}>
              <Box sx={{ 
                display: 'flex', 
                justifyContent: 'space-between', 
                alignItems: 'center', 
                mb: 1.5
              }}>
                <Typography variant="subtitle2" sx={{ fontWeight: 'medium' }}>
                  Toggle Column Visibility
                  <Typography component="span" variant="caption" sx={{ ml: 1, color: 'text.secondary' }}>
                    ({Object.values(visibleColumns).filter(Boolean).length - 1} of {Object.keys(visibleColumns).length - 1} visible)
                  </Typography>
                </Typography>
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Button 
                  size="small"
                    variant="outlined"
                    onClick={() => {
                      const newColumns = { ...visibleColumns };
                      Object.keys(newColumns).forEach(key => {
                        if (key !== 'actions') { // Keep actions column visible
                          newColumns[key] = true;
                        }
                      });
                      setVisibleColumns(newColumns);
                      try {
                        localStorage.setItem('manifestColumnVisibility', JSON.stringify(newColumns));
                      } catch (error) {
                        console.error('Failed to save column visibility to localStorage:', error);
                      }
                      
                      // Auto-size columns after a short delay
                      setTimeout(() => {
                        handleAutosizeColumns();
                      }, 100);
                    }}
              sx={{
                      borderRadius: 10,
                      fontSize: '0.75rem',
                      textTransform: 'none'
                    }}
                  >
                    Select All
                  </Button>
                    <Button
                      size="small"
                    variant="outlined"
                      onClick={() => {
                      const newColumns = { ...visibleColumns };
                      Object.keys(newColumns).forEach(key => {
                        if (key !== 'actions') { // Keep actions column visible
                          newColumns[key] = false;
                        }
                      });
                      setVisibleColumns(newColumns);
                      try {
                        localStorage.setItem('manifestColumnVisibility', JSON.stringify(newColumns));
                      } catch (error) {
                        console.error('Failed to save column visibility to localStorage:', error);
                      }
                      
                      // Auto-size columns after a short delay
                      setTimeout(() => {
                        handleAutosizeColumns();
                      }, 100);
                    }}
                      sx={{ 
                      borderRadius: 10,
                      fontSize: '0.75rem',
                      textTransform: 'none'
                    }}
                  >
                    Select None
                    </Button>
                <Button
                  size="small"
                    variant="outlined"
                  color="primary"
                    onClick={() => {
                      // Default visible columns
                      const defaultColumns = {
                        trackingNo: true,
                        internalId: true,
                        customerName: true,
                        status: true,
                        pieces: true,
                        inboundPieces: true, // Show inbound pieces by default
                        weight: true,
                        location: true,
                        createdDate: true,
                        deliveryDate: true,
                        timeSlot: true, // Show time slot by default
                        actions: true,
                        client: false,
                        container: false,
                        driverRemarks: false,
                        driver: false,
                        phoneNo: false,
                        address: false,
                        postalCode: false,
                        country: false,
                        cbm: false,
                        actualPalletsCount: true,
                        deliveryVehicle: false,
                        deliveredDate: false,
                        remarks: false, // Add remarks column
                      };
                      setVisibleColumns(defaultColumns);
                      try {
                        localStorage.setItem('manifestColumnVisibility', JSON.stringify(defaultColumns));
                      } catch (error) {
                        console.error('Failed to save column visibility to localStorage:', error);
                      }
                      
                      // Auto-size columns after a short delay
                      setTimeout(() => {
                        handleAutosizeColumns();
                      }, 100);
                    }}
                    sx={{ 
                      borderRadius: 10,
                      fontSize: '0.75rem',
                      textTransform: 'none'
                    }}
                  >
                    Reset to Default
                </Button>
              </Box>
              </Box>
            
            <Box sx={{
              display: 'flex',
              flexWrap: 'wrap',
              gap: 1,
              overflowX: 'auto'
            }}>
                {/* Display buttons in the exact order of columns sequence */}
              {[
                'location', 'internalId', 'trackingNo', 'customerName', 'address', 'postalCode',
                'deliveryDate', 'timeSlot', 'phoneNo', 'driverRemarks', 'driver', 'status', 'deliveryVehicle',
                'pieces', 'inboundPieces', 'actualPalletsCount', 'cbm', 'remarks', 'weight',
                'deliveredDate', 'createdDate', 'country', 'container', 'client'
              ].map((column) => {
                // Only show toggle for columns that exist in visibleColumns
                if (!(column in visibleColumns)) return null;

                const isVisible = visibleColumns[column];
                
                  const columnLabel = column
                    .charAt(0).toUpperCase() 
                    + column.slice(1).replace(/([A-Z])/g, ' $1').trim();
                
                return (
                  <Button
                      key={column}
                          size="small"
                    variant={isVisible ? "contained" : "outlined"}
                    color={isVisible ? "primary" : "inherit"}
                    onClick={() => handleColumnToggle(column)}
                    sx={{ 
                      borderRadius: 10,
                      px: 1.5,
                      py: 0.5,
                      minWidth: 'fit-content',
                      fontSize: '0.75rem',
                      textTransform: 'none',
                      boxShadow: isVisible ? 1 : 0
                    }}
                  >
                    {columnLabel}
                  </Button>
                );
              })}
              </Box>
              </Box>
            
            {/* Manifest DataGrid */}
            {manifestLoading ? (
              <Box sx={{ 
                display: 'flex', 
                justifyContent: 'center', 
                alignItems: 'center', 
                height: '100%', 
                minHeight: '200px' 
              }}>
                <CircularProgress />
              </Box>
            ) : manifestError ? (
              <Alert severity="error" sx={{ m: 1, borderRadius: 2 }}>
                {manifestError}
              </Alert>
            ) : !getFilteredManifests || getFilteredManifests.length === 0 ? (
              <Alert severity="info" sx={{ m: 1, borderRadius: 2 }}>
                No manifests found for this container. You can add a new manifest using the 'Add Manifest' button.
              </Alert>
            ) : (
              <Box sx={{ 
                width: '100%',
                overflow: 'hidden',
                border: 0
              }}>
              <DataGrid
                  key={dataGridKey}
                  apiRef={apiRef}
                  rows={getFilteredManifests.map(manifest => ({
                    ...manifest,
                    id: manifest.trackingNo || Math.random().toString(),
                    // Ensure internalId is accessible as a direct property
                    internalId: manifest.internalId || `${manifest.container?.containerNo || 'Unknown'}-${manifest.sequenceNo || '0'}`
                  }))}
                  columns={columns.filter(col => visibleColumns[col.field])}
                  getRowId={(row) => row.trackingNo || Math.random().toString()}
                  columnVisibilityModel={generateColumnVisibilityModel()}
                  onColumnVisibilityModelChange={(newModel) => {
                    const newVisibleColumns = { ...visibleColumns };
                    Object.keys(newModel).forEach((field) => {
                      if (field in newVisibleColumns) {
                            newVisibleColumns[field] = newModel[field];
                      }
                    });
                    setVisibleColumns(newVisibleColumns);
                    try {
                      localStorage.setItem('manifestColumnVisibility', JSON.stringify(newVisibleColumns));
                    } catch (error) {
                      console.error('Failed to save column visibility to localStorage:', error);
                    }
                    
                    // Auto-size columns after a short delay to ensure the DOM has updated
                    setTimeout(() => {
                      handleAutosizeColumns();
                    }, 100);
                  }}
                  initialState={{
                    pagination: {
                        paginationModel: { pageSize: 100 },
                    },
                    sorting: {
                        sortModel: [{ field: 'internalId', sort: 'asc' }],
                    },
                    columns: {
                      columnVisibilityModel: generateColumnVisibilityModel(),
                    },
                  }}
                  pageSizeOptions={[50, 100]}
                  keepNonExistentRowsSelected
                  density="standard"
                  autoHeight={true}
                  disableVirtualization={true}
                  rowHeight={65}
                  headerHeight={56}
                  getEstimatedRowHeight={() => 65}
                  getRowHeight={() => 'auto'}
                  className="no-cell-focus-outline"
                  checkboxSelection={true}
                  slotProps={{
                    pagination: {
                      SelectProps: {
                        MenuProps: {
                          sx: {
                            zIndex: 10001,
                            '& .MuiPaper-root': {
                              zIndex: 10001
                            }
                          }
                        }
                      }
                    }
                  }}
                  onRowSelectionModelChange={(selectionModel) => {
                    try {
                      console.log("Raw selection model:", selectionModel);
                      
                      // Extract IDs from the selection model (handling both array and object formats)
                      let selectedIds = [];
                      
                      if (selectionModel && typeof selectionModel === 'object') {
                        // Handle the new format: {type: 'include', ids: Set}
                        if (selectionModel.ids && selectionModel.ids instanceof Set) {
                          selectedIds = Array.from(selectionModel.ids);
                        } 
                        // Also handle the old array format
                        else if (Array.isArray(selectionModel)) {
                          selectedIds = selectionModel;
                        }
                        // Handle single ID
                        else if (selectionModel.id) {
                          selectedIds = [selectionModel.id];
                        }
                        // Fallback: treat the whole thing as an array
                        else {
                          selectedIds = Array.isArray(selectionModel) ? selectionModel : [selectionModel];
                        }
                      }
                      
                      console.log("Processed selectedIds:", selectedIds);
                      
                      if (!getFilteredManifests || getFilteredManifests.length === 0) {
                        console.log("No manifests available to select");
                        setSelectedManifests([]);
                        return;
                      }
                      
                      // Find manifests with the selected IDs
                      const selectedItems = getFilteredManifests.filter(manifest => {
                        if (!manifest || !manifest.trackingNo) return false;
                        
                        const isSelected = selectedIds.includes(manifest.trackingNo);
                        console.log(`Checking manifest ${manifest.trackingNo}: ${isSelected ? "SELECTED" : "not selected"}`);
                        return isSelected;
                      });
                      
                      console.log(`Selected ${selectedItems.length} manifests:`, selectedItems.map(m => m.trackingNo));

                      // Update the state
                      setSelectedManifests(selectedItems);
                    } catch (error) {
                      console.error('Error handling selection change:', error);
                      setSelectedManifests([]);
                    }
                  }}
                  components={{
                    NoRowsOverlay: () => (
                      <Box sx={{ 
                        display: 'flex', 
                        justifyContent: 'center', 
                        alignItems: 'center', 
                        height: '100%', 
                        minHeight: '200px' 
                      }}>
                        <Typography>No manifests found</Typography>
                      </Box>
                    ),
                    ErrorOverlay: (props) => (
                      <Box sx={{ 
                        display: 'flex', 
                        justifyContent: 'center', 
                        alignItems: 'center', 
                        height: '100%', 
                        p: 2 
                      }}>
                        <Alert severity="error">
                          An error occurred while loading data. Please try refreshing the page.
                        </Alert>
                    </Box>
                  ),
                }}
                sx={{
                  width: '100% !important', // Ensure DataGrid takes full width
                  minWidth: '100%', // Maintain minimum width
                    border: 'none',
                    borderRadius: 0,
                  display: 'flex',
                  flexDirection: 'column',
                  position: 'relative',
                  
                  '& .MuiDataGrid-root': {
                    display: 'flex',
                    flexDirection: 'column',
                    overflow: 'visible !important',
                    height: 'auto !important',
                    maxHeight: 'none !important',
                    minHeight: '0 !important',
                    width: '100% !important', // Full width
                  },
                  '& .MuiDataGrid-virtualScroller': {
                    position: 'static !important',
                    overflow: 'visible !important',
                    height: 'auto !important',
                    minHeight: '0 !important',
                    maxHeight: 'none !important',
                    width: '100% !important', // Full width
                  },
                  '& .MuiDataGrid-main': {
                    display: 'flex',
                    flexDirection: 'column',
                    overflow: 'visible !important',
                    height: 'auto !important',
                    maxHeight: 'none !important',
                    minHeight: '0 !important',
                    width: '100% !important', // Full width
                    flexGrow: 1,
                    position: 'static !important',
                  },
                  '& .MuiDataGrid-viewport': {
                    overflow: 'visible !important',
                    height: 'auto !important',
                    width: '100% !important', // Full width
                  },
                  '& .MuiDataGrid-window': {
                    position: 'static !important',
                    overflow: 'visible !important',
                    height: 'auto !important',
                    width: '100% !important', // Full width
                  },
                  '& .MuiDataGrid-dataContainer': {
                    position: 'static !important',
                    overflow: 'visible !important',
                    height: 'auto !important',
                    width: '100% !important', // Full width
                  },
                  '& .MuiDataGrid-columnHeadersInner': {
                    width: '100% !important', // Full width for headers
                  },
                  '& .MuiDataGrid-columnHeaders': {
                    minHeight: '56px !important',
                    maxHeight: '56px !important',
                    lineHeight: '56px !important',
                    backgroundColor: '#f5f5f5',
                    borderBottom: '1px solid rgba(224, 224, 224, 1)',
                    padding: '0 0',
                    width: '100% !important', // Full width
                  },
                  '& .MuiDataGrid-columnsContainer': {
                    width: '100% !important', // Full width
                  },
                  '& .MuiDataGrid-row': {
                    width: '100% !important', // Full width
                    maxHeight: 'none !important',
                    minHeight: '65px !important',
                    '&:hover': {
                      backgroundColor: 'rgba(25, 118, 210, 0.04)',
                    },
                    '&:nth-of-type(even)': {
                      backgroundColor: 'rgba(0, 0, 0, 0.02)',
                    },
                  },
                  '& .MuiDataGrid-row .MuiDataGrid-cell': {
                      overflow: 'hidden',
                  },
                  '& .MuiDataGrid-cell': {
                    padding: '16px 16px', // Increased padding from 8px to 16px for more space
                    borderBottom: '1px solid #f0f0f0',
                      whiteSpace: 'nowrap',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                    minHeight: '65px !important', // Increased from 48px to 65px
                    maxHeight: 'none !important',
                    lineHeight: '24px !important', // Increased line height
                      textAlign: 'left',
                    display: 'flex',
                      alignItems: 'center',
                    fontSize: '0.95rem', // Slightly larger text
                  },
                  '& .MuiDataGrid-columnHeader': {
                    padding: '0 16px', // Increased padding from 12px to 16px
                    height: '56px', // Increased from 48px to 56px
                    display: 'flex',
                    alignItems: 'center',
                    '& .MuiDataGrid-columnHeaderTitleContainer': {
                      padding: '0 0',
                    },
                    '& .MuiDataGrid-columnHeaderTitle': {
                      fontWeight: 600, // Make column headers bolder
                      fontSize: '0.95rem', // Slightly larger text
                    },
                    },
                    '& .MuiDataGrid-cell--withRenderer': {
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'flex-start',
                    },
                    '& .MuiDataGrid-cellContent': {
                      textAlign: 'left',
                      display: 'flex',
                      alignItems: 'center',
                    },
                    '& .MuiDataGrid-footerContainer': {
                    minHeight: '56px',
                    maxHeight: '56px',
                      borderTop: '1px solid rgba(224, 224, 224, 1)',
                    overflow: 'visible',
                    position: 'relative',
                    padding: '0 12px',
                    display: 'flex',
                    alignItems: 'center',
                    backgroundColor: '#f5f5f5',
                    },
                    '& .MuiTablePagination-root': {
                    width: '100%',
                    display: 'flex',
                    flexDirection: 'row',
                    justifyContent: 'flex-end',
                    position: 'relative',
                    zIndex: 1,
                    },
                    '& .MuiTablePagination-selectLabel, & .MuiTablePagination-displayedRows': {
                    fontSize: '0.875rem',
                    margin: 0,
                    },
                    '& .MuiTablePagination-select': {
                    fontSize: '0.875rem',
                    marginLeft: '4px',
                    marginRight: '4px',
                    position: 'relative',
                    zIndex: 1300,
                    },
                    '& .MuiSelect-select': {
                      zIndex: 1300,
                      position: 'relative',
                    },
                    '& .MuiSelect-icon': {
                      zIndex: 1300,
                      position: 'relative',
                    },
                    '& .MuiMenu-paper': {
                      zIndex: 10001,
                    },
                    '& .MuiTablePagination-actions': {
                    marginLeft: 0,
                    display: 'flex',
                    alignItems: 'center',
                    },
                    '& .MuiDataGrid-selectedRowCount': {
                    fontSize: '0.875rem',
                    fontWeight: 'medium',
                    color: theme.palette.primary.main,
                    },
                    '& .MuiDataGrid-columnHeaderCheckbox': {
                    padding: '0',
                    height: '48px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  },
                  '& .MuiDataGrid-cellCheckbox': {
                    padding: '0',
                    height: '48px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  },
                  '& .MuiCheckbox-root': {
                    padding: '4px',
                    margin: '0 auto',
                  },
                  '& .MuiDataGrid-columnHeaderDraggableContainer, & .MuiDataGrid-columnHeader': {
                    height: '48px',
                  },
                  '& .MuiDataGrid-columnSeparator': {
                    visibility: 'visible',
                  },
                  // Specific fix for checkbox alignment
                  '& .MuiDataGrid-columnHeaders .MuiDataGrid-iconButtonContainer': {
                    visibility: 'visible',
                    width: '48px',
                    alignItems: 'center',
                    justifyContent: 'center',
                    display: 'flex',
                  },
                  '& .MuiDataGrid-iconButtonContainer .MuiCheckbox-root': {
                    position: 'static',
                    transform: 'none',
                    },
                  }}
                />
                          </Box>
                        )}
                  </Paper>
                  
          {/* Status Update Dialog */}
          <Dialog 
            open={statusDialogOpen} 
            onClose={() => setStatusDialogOpen(false)}
            maxWidth="sm" 
            fullWidth
            PaperProps={{
              sx: { p: 1 }
            }}
          >
            <DialogTitle sx={{ 
              borderBottom: 1, 
              borderColor: 'divider',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between'
            }}>
              <Box display="flex" alignItems="center">
                <LocalShippingOutlinedIcon sx={{ mr: 1 }} />
                Update Container Status
              </Box>
              {container && (
                  <Chip 
                  label={container.status}
                    size="small" 
                  icon={getContainerStatusIcon(container.status)}
                sx={{
                    ...getStatusChipColor(container.status),
                    fontWeight: 'medium',
                    fontSize: '0.75rem'
                  }}
                />
              )}
            </DialogTitle>
            <DialogContent sx={{ py: 3 }}>
              {container && (
                <>
                  <Box sx={{ mb: 3 }}>
                    <Typography variant="subtitle2" gutterBottom>
                      Container: {container.containerNo}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Select the new status for this container:
                    </Typography>
                    <Typography variant="caption" color="error">
                      Note: Changing container status may affect all manifests in this container
                    </Typography>
                </Box>
                  
                  <Box sx={{ 
                    display: 'grid', 
                    gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr' },
                    gap: 2 
                  }}>
                    {Object.values(ContainerStatus).map((status) => {
                      const isSelected = newStatus === status;
                      const isCurrentStatus = container.status === status;
                      const statusColors = getStatusChipColor(status);
                      
                      return (
                        <Paper
                          key={status}
                          elevation={isSelected ? 4 : 1}
                          sx={{
                            p: 2,
                            borderRadius: 2,
                            border: isSelected ? `1px solid ${theme.palette.primary.main}` : '1px solid transparent',
                            cursor: isCurrentStatus ? 'default' : 'pointer',
                            backgroundColor: isSelected ? alpha(statusColors.bg, 0.5) : 'background.paper',
                            opacity: isCurrentStatus ? 0.7 : 1,
                            transition: 'all 0.2s',
                            '&:hover': {
                              backgroundColor: isCurrentStatus ? 'inherit' : alpha(statusColors.bg, 0.3),
                              transform: isCurrentStatus ? 'none' : 'translateY(-2px)',
                            },
                          }}
                          onClick={() => {
                            if (!isCurrentStatus) {
                              setNewStatus(status);
                            }
                          }}
                        >
                          <Box sx={{ 
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'space-between'
                          }}>
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              {getContainerStatusIcon(status)}
                              <Typography sx={{ ml: 1, fontWeight: isSelected ? 'bold' : 'normal' }}>
                      {status.replace(/_/g, ' ')}
                              </Typography>
                            </Box>
                            {isCurrentStatus && (
                              <Chip size="small" label="Current" color="primary" sx={{ height: 20 }} />
                            )}
                          </Box>
                          
                          {isSelected && (
                            <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                              {isCurrentStatus 
                                ? "This is the current status"
                                : `Change status from ${container.status.replace(/_/g, ' ')} to ${status.replace(/_/g, ' ')}`
                              }
                            </Typography>
                          )}
                        </Paper>
                      );
                    })}
                  </Box>
                </>
              )}
            </DialogContent>
            <DialogActions sx={{ px: 3, py: 2, borderTop: 1, borderColor: 'divider' }}>
              <Button onClick={() => setStatusDialogOpen(false)}>Cancel</Button>
              <Button 
                onClick={handleSaveStatus} 
                variant="contained" 
                color="primary"
                disabled={!newStatus || (container && newStatus === container.status)}
                startIcon={<CheckCircleIcon />}
              >
                Update Status
              </Button>
            </DialogActions>
          </Dialog>
          
          {/* Label Dialog */}
          <ManifestLabelDialog
            open={labelDialogOpen}
            onClose={handleCloseLabelDialog}
            manifest={selectedLabelManifest}
            manifests={selectedManifests.length > 1 ? selectedManifests : []}
            initialLabelCount={selectedLabelManifest ? (selectedLabelManifest.actualPalletsCount && selectedLabelManifest.actualPalletsCount > 0 ? selectedLabelManifest.actualPalletsCount : 1) : undefined}
            onSaveLabelCount={handleSaveLabelCount}
            batchMode={selectedManifests.length > 1}
            getLabelCount={(trackingNo) => {
              const manifest = manifests.find(m => m.trackingNo === trackingNo);
              if (!manifest) return 1;
              return manifest.actualPalletsCount && manifest.actualPalletsCount > 0 ? manifest.actualPalletsCount : 1;
            }}
          />
          
          {/* Bulk Upload Dialog */}
          <Dialog
            open={bulkUploadDialogOpen}
            onClose={() => {
              if (!isUploading) {
                setBulkUploadDialogOpen(false);
                handleResetFileUpload();
              }
            }}
            maxWidth="md"
            fullWidth
            keepMounted={false}
            disableRestoreFocus={false}
            PaperProps={{
              sx: {
                borderRadius: 2,
                boxShadow: 24,
                maxHeight: '90vh',
                overflowY: 'auto'
              },
            }}
          >
            <DialogTitle sx={{ 
              bgcolor: theme.palette.primary.main, 
              color: 'white',
              px: 3,
              py: 2,
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center'
            }}>
              <Box display="flex" alignItems="center">
                <CloudUploadIcon sx={{ mr: 2 }} />
                Bulk Import Manifests for Container {containerNo}
              </Box>
              <IconButton
                aria-label="close"
                onClick={() => {
                  if (!isUploading) {
                    setBulkUploadDialogOpen(false);
                    handleResetFileUpload();
                  }
                }}
                sx={{
                  color: 'white',
                }}
                disabled={isUploading}
              >
                <CloseIcon />
              </IconButton>
            </DialogTitle>
            <DialogContent sx={{ px: 3, py: 3 }}>
              <Box sx={{ mb: 3 }}>
                <Typography variant="h6" gutterBottom>Upload Excel File</Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  Upload an Excel file containing manifest details for container <strong>{containerNo}</strong>. Download the template below to ensure your data is properly formatted.
                </Typography>
                <Alert severity="info" sx={{ mb: 2 }}>
                  <Typography variant="body2">
                    <strong>Note:</strong> All manifests will be automatically assigned to container <strong>{containerNo}</strong> and client <strong>{container?.client?.companyName || container?.client?.username || 'Current Client'}</strong>. 
                    You only need to provide customer and shipment details in the Excel file.
                  </Typography>
                </Alert>
                <Button
                  variant="outlined"
                  color="primary"
                  startIcon={<FileDownloadIcon />}
                  onClick={handleDownloadTemplate}
                  sx={{ borderRadius: 2, mr: 2 }}
                >
                  Download Template
                </Button>
              </Box>
              
              {!uploadedFile && !isUploading && !bulkUploadResults && (
                <Box 
                  sx={{ 
                    border: '2px dashed', 
                    borderColor: 'divider',
                    borderRadius: 2,
                    p: 3,
                    textAlign: 'center',
                    bgcolor: alpha(theme.palette.primary.main, 0.04),
                    cursor: 'pointer',
                    '&:hover': {
                      bgcolor: alpha(theme.palette.primary.main, 0.08),
                    }
                  }}
                  onClick={() => fileInputRef.current?.click()}
                  onDrop={handleFileDrop}
                  onDragOver={handleDragOver}
                >
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept=".xlsx,.xls"
                    style={{ display: 'none' }}
                    onChange={handleFileChange}
                  />
                  <CloudUploadIcon sx={{ fontSize: 48, color: theme.palette.primary.main, mb: 1 }} />
                  <Typography variant="h6" gutterBottom>
                    Drop Excel File Here or Click to Browse
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Supports .xlsx and .xls files up to 5MB
                  </Typography>
                </Box>
              )}
              
              {isUploading && (
                <Box sx={{ mt: 2 }}>
                  <Typography variant="body2" gutterBottom>
                    Uploading and processing file...
                  </Typography>
                  <LinearProgress 
                    variant="determinate" 
                    value={uploadProgress} 
                    sx={{ 
                      height: 10, 
                      borderRadius: 5,
                      mb: 1
                    }}
                  />
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Typography variant="caption" color="text.secondary">
                      {uploadProgress}% complete
                    </Typography>
                    {uploadProgress === 100 && (
                      <Button 
                        variant="outlined" 
                        size="small" 
                        onClick={() => {
                          setIsUploading(false);
                          handleResetFileUpload();
                        }}
                      >
                        Reset
                      </Button>
                    )}
                  </Box>
                </Box>
              )}
              
              {uploadedFile && !isUploading && !bulkUploadResults && (
                <Box>
                  <Paper
                    elevation={0}
                    sx={{
                      p: 2,
                      border: '1px solid',
                      borderColor: 'divider',
                      borderRadius: 2,
                      bgcolor: alpha(theme.palette.primary.main, 0.04),
                      display: 'flex',
                      alignItems: 'center',
                      mb: 2
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1 }}>
                      <UploadFileIcon sx={{ color: theme.palette.primary.main, mr: 2 }} />
                      <Box>
                        <Typography variant="body1">{uploadedFile.name}</Typography>
                        <Typography variant="caption" color="text.secondary">
                          {(uploadedFile.size / 1024).toFixed(2)} KB
                        </Typography>
                      </Box>
                    </Box>
                    <IconButton 
                      onClick={handleResetFileUpload}
                      color="error"
                    >
                      <CloseIcon />
                    </IconButton>
                  </Paper>
                  
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={handleFileUpload}
                    fullWidth
                    sx={{ 
                      mt: 2,
                      py: 1.5,
                      borderRadius: 2
                    }}
                  >
                    Upload and Process
                  </Button>
                </Box>
              )}
              
              {bulkUploadResults && (
                <Box>
                  <Typography variant="h6" gutterBottom>Upload Results</Typography>
                  
                  <Box 
                    sx={{ 
                      display: 'flex', 
                      justifyContent: 'space-between', 
                      mb: 3,
                      flexWrap: 'wrap',
                      gap: 2
                    }}
                  >
                    <Paper
                      elevation={0}
                      sx={{
                        p: 2,
                        border: '1px solid',
                        borderColor: 'divider',
                        borderRadius: 2,
                        flex: '1 1 30%',
                        minWidth: '120px',
                        textAlign: 'center',
                        bgcolor: alpha(theme.palette.info.main, 0.1)
                      }}
                    >
                      <Typography variant="h6">{bulkUploadResults.total}</Typography>
                      <Typography variant="body2" color="text.secondary">Total</Typography>
                    </Paper>
                    
                    <Paper
                      elevation={0}
                      sx={{
                        p: 2,
                        border: '1px solid',
                        borderColor: 'divider',
                        borderRadius: 2,
                        flex: '1 1 30%',
                        minWidth: '120px',
                        textAlign: 'center',
                        bgcolor: alpha(theme.palette.success.main, 0.1)
                      }}
                    >
                      <Typography variant="h6" color="success.main">{bulkUploadResults.successful}</Typography>
                      <Typography variant="body2" color="text.secondary">Successful</Typography>
                    </Paper>
                    
                    <Paper
                      elevation={0}
                      sx={{
                        p: 2,
                        border: '1px solid',
                        borderColor: 'divider',
                        borderRadius: 2,
                        flex: '1 1 30%',
                        minWidth: '120px',
                        textAlign: 'center',
                        bgcolor: alpha(theme.palette.error.main, 0.1)
                      }}
                    >
                      <Typography variant="h6" color="error">{bulkUploadResults.failed}</Typography>
                      <Typography variant="body2" color="text.secondary">Failed</Typography>
                    </Paper>
                  </Box>
                  
                  {bulkUploadResults.errors.length > 0 && (
                    <Box sx={{ mb: 3 }}>
                      <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 'medium' }}>
                        Errors:
                      </Typography>
                      <Paper 
                        variant="outlined" 
                        sx={{ 
                          maxHeight: '200px', 
                          overflowY: 'auto',
                          border: '1px solid',
                          borderColor: 'divider',
                          borderRadius: 2
                        }}
                      >
                        <Table size="small">
                          <TableHead sx={{ bgcolor: alpha(theme.palette.error.main, 0.1) }}>
                            <TableRow>
                              <TableCell>Row</TableCell>
                              <TableCell>Error</TableCell>
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            {bulkUploadResults.errors.map((error, index) => (
                              <TableRow key={index}>
                                <TableCell>{error.row}</TableCell>
                                <TableCell>{error.error}</TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </Paper>
                    </Box>
                  )}
                  
                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
                    <Button
                      variant="outlined"
                      onClick={handleResetFileUpload}
                    >
                      Upload Another File
                    </Button>
                    <Button
                      variant="contained"
                      color="primary"
                      onClick={() => {
                        setBulkUploadDialogOpen(false);
                        handleResetFileUpload();
                        // Add a call to fetch all manifests when done
                        fetchAllManifests();
                      }}
                    >
                      Done
                    </Button>
                  </Box>
                </Box>
              )}
            </DialogContent>
          </Dialog>
          
          {/* Add Manifest Creation Dialog */}
          <Dialog 
            open={openManifestDialog} 
            onClose={handleCloseManifestDialog}
            maxWidth="md"
            fullWidth
            PaperProps={{
              sx: {
                borderRadius: 2,
                boxShadow: 24,
                maxHeight: '90vh',
                overflowY: 'auto'
              },
            }}
          >
            <DialogTitle sx={{ 
              bgcolor: theme.palette.primary.main, 
              color: 'white',
              px: 3,
              py: 2
            }}>
              <Box display="flex" justifyContent="space-between" alignItems="center">
                <Typography variant="h6">
                  Create Manifest for Container {containerNo}
                </Typography>
                <IconButton
                  aria-label="close"
                  onClick={handleCloseManifestDialog}
                  sx={{
                    color: 'white',
                  }}
                >
                  <CloseIcon />
                </IconButton>
              </Box>
            </DialogTitle>
            
            <DialogContent dividers sx={{ px: 3, py: 3, '&::-webkit-scrollbar': { width: 8 }, '&::-webkit-scrollbar-thumb': { backgroundColor: 'rgba(0,0,0,0.2)', borderRadius: 4 } }}>
              {manifestFormError && (
                <Alert severity="error" sx={{ mb: 3, borderRadius: 2 }}>
                  {manifestFormError}
                </Alert>
              )}
              
              {/* Identification Section */}
              <Typography variant="h6" color="primary" sx={{ mb: 2, fontWeight: 'bold' }}>
                Identification
              </Typography>
              <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', md: '1fr 1fr' }, gap: 2, mb: 3 }}>
                  <TextField
                  required
                  name="trackingNo"
                  label="Tracking No"
                  value={manifestFormData.trackingNo}
                  onChange={(e) => handleManifestFormChange('trackingNo', e.target.value)}
                  fullWidth
                  margin="normal"
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 1,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: theme.palette.primary.main,
                      },
                    },
                  }}
                />
                
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
                  <Typography variant="caption" color="text.secondary">
                    Internal ID
                  </Typography>
                  <Typography variant="body1" sx={{ fontWeight: 'medium', display: 'flex', alignItems: 'center' }}>
                    <Box component="span" sx={{ 
                      color: theme.palette.primary.main, 
                      display: 'inline-flex',
                      alignItems: 'center',
                      '& svg': { mr: 1 } 
                    }}>
                      <TimelineIcon fontSize="small" />
                    </Box>
                    {containerNo && manifestFormData.sequenceNo ? `${containerNo}-${manifestFormData.sequenceNo}` : 'N/A'}
                  </Typography>
                </Box>
              </Box>
              
              {/* Client Information Section */}
              <Typography variant="h6" color="primary" sx={{ mb: 2, fontWeight: 'bold' }}>
                Client Information
              </Typography>
              <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', md: '1fr 1fr' }, gap: 2, mb: 3 }}>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5, mt: 2 }}>
                  <Typography variant="caption" color="text.secondary">
                    Client
                  </Typography>
                  <Typography variant="body1" sx={{ fontWeight: 'medium', display: 'flex', alignItems: 'center' }}>
                    <Box component="span" sx={{ 
                      color: theme.palette.grey[600], 
                      display: 'inline-flex',
                      alignItems: 'center',
                      '& svg': { mr: 1 } 
                    }}>
                      <PersonIcon fontSize="small" />
                    </Box>
                    {container?.client?.companyName ? 
                      container.client.companyName : 
                      container?.client?.username || 'No client selected'}
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5, mt: 2 }}>
                  <Typography variant="caption" color="text.secondary">
                    Container
                  </Typography>
                  <Typography variant="body1" sx={{ fontWeight: 'medium', display: 'flex', alignItems: 'center' }}>
                    <Box component="span" sx={{ 
                      color: theme.palette.grey[600], 
                      display: 'inline-flex',
                      alignItems: 'center',
                      '& svg': { mr: 1 } 
                    }}>
                      <LocalShippingOutlinedIcon fontSize="small" />
                    </Box>
                    {containerNo || 'No container selected'}
                  </Typography>
                </Box>
              </Box>

              {/* Package Details Section */}
              <Typography variant="h6" color="primary" sx={{ mb: 2, fontWeight: 'bold' }}>
                Package Details
              </Typography>
              <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', md: '1fr 1fr' }, gap: 2, mb: 3 }}>
                  <TextField
                  required
                  name="pieces"
                  label="Pieces"
                  type="number"
                  value={manifestFormData.pieces || ''}
                  onChange={(e) => handleManifestFormChange('pieces', Number(e.target.value))}
                    fullWidth
                  margin="normal"
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 1,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: theme.palette.primary.main,
                      },
                    },
                  }}
                  />
                  <TextField
                  required
                  name="cbm"
                  label="CBM"
                  type="number"
                  value={manifestFormData.cbm || ''}
                  onChange={(e) => handleManifestFormChange('cbm', Number(e.target.value))}
                    fullWidth
                  margin="normal"
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 1,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: theme.palette.primary.main,
                      },
                    },
                  }}
                />
                <Box sx={{ mt: 2, mb: 2 }}>
                  <Typography variant="body2" color="text.secondary">Number of Pallets</Typography>
                  <Typography variant="body1" sx={{ fontWeight: 500, mt: 1 }}>
                    {manifestFormData.actualPalletsCount ?? 0}
                    <Typography variant="caption" color="text.secondary" display="block" sx={{ mt: 0.5 }}>
                      The number of pallets is automatically calculated based on created pallets
                    </Typography>
                  </Typography>
                </Box>
                <FormControl fullWidth margin="normal">
                  <InputLabel id="location-label">Location</InputLabel>
                  <Select
                    labelId="location-label"
                    id="location"
                    name="location"
                    value={manifestFormData.location || ''}
                    onChange={(e) => handleManifestFormChange('location', e.target.value)}
                    label="Location"
                    startAdornment={
                      <InputAdornment position="start">
                        <LocationOnIcon color="action" />
                      </InputAdornment>
                    }
                    sx={{
                      borderRadius: 1,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: theme.palette.primary.main,
                      },
                    }}
                  >
                    <MenuItem value="">
                      <em>None</em>
                    </MenuItem>
                    {locationZones.map((zone) => (
                      <MenuItem key={zone.id} value={zone.name}>
                        {zone.name}
                      </MenuItem>
                    ))}
                  </Select>
                  <FormHelperText>Select a location zone</FormHelperText>
                </FormControl>
                <FormControl fullWidth margin="normal">
                  <InputLabel id="driver-label">Driver</InputLabel>
                  <Select
                    labelId="driver-label"
                    name="driver"
                    value={manifestFormData.driver?.username || ''}
                    onChange={(e) => {
                      const selectedDriver = drivers.find(d => d.username === e.target.value) || { username: '' };
                      handleManifestFormChange('driver', selectedDriver);
                    }}
                    label="Driver"
                    MenuProps={{
                      sx: {
                        zIndex: 10001,
                        '& .MuiPaper-root': {
                          zIndex: 10001
                        }
                      }
                    }}
                    sx={{
                      borderRadius: 1,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: theme.palette.primary.main,
                      },
                    }}
                    startAdornment={
                      <InputAdornment position="start">
                        <PersonIcon color="action" />
                      </InputAdornment>
                    }
                  >
                    <MenuItem value="">
                      <em>Not Assigned</em>
                    </MenuItem>
                    {drivers && drivers.length > 0 ? (
                      drivers.map((driver) => (
                        <MenuItem key={driver.username} value={driver.username}>
                          {`${driver.username} - ${driver.firstName || ''} ${driver.lastName || ''}`.trim()}
                        </MenuItem>
                      ))
                    ) : (
                      <MenuItem disabled value="">
                        No drivers available
                      </MenuItem>
                    )}
                  </Select>
                  <FormHelperText>Driver selection is optional</FormHelperText>
                </FormControl>
                <FormControl fullWidth margin="normal">
                  <InputLabel id="delivery-vehicle-label">Delivery Vehicle</InputLabel>
                  <Select
                    labelId="delivery-vehicle-label"
                    id="deliveryVehicle"
                    name="deliveryVehicle"
                    value={vehicleTypes.some(vt => vt.name === manifestFormData.deliveryVehicle) ? manifestFormData.deliveryVehicle : ''}
                    onChange={(e) => handleManifestFormChange('deliveryVehicle', e.target.value)}
                    label="Delivery Vehicle"
                    MenuProps={{
                      sx: {
                        zIndex: 10001,
                        '& .MuiPaper-root': {
                          zIndex: 10001
                        }
                      }
                    }}
                    startAdornment={
                      <InputAdornment position="start">
                        <LocalShippingOutlinedIcon color="action" />
                      </InputAdornment>
                    }
                    sx={{
                      borderRadius: 1,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: theme.palette.primary.main,
                      },
                    }}
                  >
                    <MenuItem value="">
                      <em>Auto-assign</em>
                    </MenuItem>
                    {vehicleTypes.map((vehicleType) => (
                      <MenuItem key={vehicleType.id} value={vehicleType.name}>
                        {vehicleType.name}
                      </MenuItem>
                    ))}
                  </Select>
                  <FormHelperText sx={{ display: 'flex', alignItems: 'center' }}>
                    Will be auto-assigned based on weight, CBM, and pieces if left empty
                    <Tooltip title="The system will automatically determine the appropriate vehicle type based on the manifest's weight, CBM, and number of pieces. You can manually select a different vehicle type if needed." arrow>
                      <IconButton size="small" sx={{ ml: 0.5, p: 0 }}>
                        <InfoIcon fontSize="small" color="action" />
                      </IconButton>
                    </Tooltip>
                  </FormHelperText>
                </FormControl>
                <TextField
                  name="weight"
                  label="Weight (kg)"
                  type="number"
                  value={manifestFormData.weight || ''}
                  onChange={(e) => handleManifestFormChange('weight', Number(e.target.value))}
                  fullWidth
                  margin="normal"
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 1,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: theme.palette.primary.main,
                      },
                    },
                  }}
                />
                
                <Box position="relative" zIndex={1300} width="100%">
                  <SplitDeliveryDatePicker
                    label="Delivery Date & Time Slot"
                    value={manifestFormData.deliveryDate}
                    onChange={(newValue) => handleManifestFormChange('deliveryDate', newValue)}
                    fullWidth
                    slotProps={{
                      datePicker: {
                      popper: {
                          sx: { zIndex: 9999 }
                        }
                      } 
                    }}
                  />
                </Box>
                <Box position="relative" zIndex={1300} width="100%">
                  <DateTimePicker
                    label="Delivered Date"
                    value={manifestFormData.deliveredDate}
                    onChange={(newValue) => handleManifestFormChange('deliveredDate', newValue)}
                    format="dd MMM yyyy HH:mm"
                    ampm={false}
                    slotProps={{
                      textField: { 
                        fullWidth: true, 
                        margin: "normal",
                        InputProps: {
                          startAdornment: (
                            <InputAdornment position="start">
                              <CalendarTodayIcon color="action" />
                            </InputAdornment>
                          ),
                        },
                        sx: {
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 1,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: theme.palette.primary.main,
                      },
                    },
                        } 
                      },
                      popper: {
                        sx: {
                          zIndex: 9999
                        }
                      }
                    }}
                  />
                </Box>
              </Box>
              
              {/* Shipping Information Section */}
              <Typography variant="h6" color="primary" sx={{ mb: 2, fontWeight: 'bold' }}>
                Shipping Information
              </Typography>
              <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', md: '1fr 1fr' }, gap: 2, mb: 3 }}>
                  <TextField
                  required
                  name="customerName"
                  label="Customer Name"
                    value={manifestFormData.customerName}
                    onChange={(e) => handleManifestFormChange('customerName', e.target.value)}
                    fullWidth
                  margin="normal"
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <PersonIcon color="action" />
                      </InputAdornment>
                    ),
                  }}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 1,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: theme.palette.primary.main,
                      },
                    },
                  }}
                />
                  <TextField
                  required
                  name="phoneNo"
                    label="Phone Number"
                    value={manifestFormData.phoneNo}
                    onChange={(e) => handleManifestFormChange('phoneNo', e.target.value)}
                    fullWidth
                  margin="normal"
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <PhoneIcon color="action" />
                      </InputAdornment>
                    ),
                  }}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 1,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: theme.palette.primary.main,
                      },
                    },
                  }}
                />
                  <TextField
                  required
                  name="address"
                  label="Address"
                    value={manifestFormData.address}
                    onChange={(e) => handleManifestFormChange('address', e.target.value)}
                    fullWidth
                  margin="normal"
                  sx={{ 
                    gridColumn: '1 / -1',
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 1,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: theme.palette.primary.main,
                      },
                    },
                  }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <HomeIcon color="action" />
                      </InputAdornment>
                    ),
                  }}
                />
                  <TextField
                  required
                  name="postalCode"
                    label="Postal Code"
                    value={manifestFormData.postalCode || ''}
                    onChange={(e) => handleManifestFormChange('postalCode', e.target.value)}
                    fullWidth
                  margin="normal"
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 1,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: theme.palette.primary.main,
                      },
                    },
                  }}
                />
                  <TextField
                  required
                  name="country"
                    label="Country"
                    value={manifestFormData.country || ''}
                    onChange={(e) => handleManifestFormChange('country', e.target.value)}
                    fullWidth
                  margin="normal"
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <LocationOnIcon color="action" />
                      </InputAdornment>
                    ),
                  }}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 1,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: theme.palette.primary.main,
                      },
                    },
                    }}
                  />
                </Box>
                
              {/* Remarks (CS) Section */}
              <Typography variant="h6" color="primary" sx={{ mb: 2, fontWeight: 'bold' }}>
                Remarks (CS)
              </Typography>
              <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr' }, gap: 2, mb: 3 }}>
                  <TextField
                  name="remarks"
                    label="Remarks (CS)"
                  value={manifestFormData.remarks || ''}
                    onChange={(e) => handleManifestFormChange('remarks', e.target.value)}
                    multiline
                  rows={4}
                  fullWidth
                  margin="normal"
                  sx={{ 
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 1,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: theme.palette.primary.main,
                      },
                    },
                  }}
                  />
                </Box>
            </DialogContent>
            
            <DialogActions sx={{ px: 3, py: 2 }}>
              <Button onClick={handleCloseManifestDialog} variant="outlined">Cancel</Button>
              <Button onClick={handleSaveManifest} variant="contained" color="primary" disabled={manifestFormLoading}>
                {manifestFormLoading ? <CircularProgress size={24} /> : 'Create'}
              </Button>
            </DialogActions>
          </Dialog>
          
          {/* Edit Manifest Dialog */}
          <Dialog
            open={editManifestDialogOpen}
            onClose={handleCloseEditManifest}
            maxWidth="md"
            fullWidth
            PaperProps={{
              sx: {
                borderRadius: 2,
                boxShadow: 24,
                maxHeight: '90vh',
                overflowY: 'auto'
              },
            }}
          >
            <DialogTitle sx={{ 
              bgcolor: theme.palette.primary.main, 
              color: 'white',
              px: 3,
              py: 2
            }}>
              <Box display="flex" justifyContent="space-between" alignItems="center">
                <Typography variant="h6">
                  Edit Manifest for Container {containerNo}
                </Typography>
                <IconButton
                  aria-label="close"
                  onClick={handleCloseEditManifest}
                  sx={{
                    color: 'white',
                  }}
                >
                  <CloseIcon />
                </IconButton>
              </Box>
            </DialogTitle>
            
            <DialogContent dividers sx={{ px: 3, py: 3, '&::-webkit-scrollbar': { width: 8 }, '&::-webkit-scrollbar-thumb': { backgroundColor: 'rgba(0,0,0,0.2)', borderRadius: 4 } }}>
              {manifestFormError && (
                <Alert severity="error" sx={{ mb: 3, borderRadius: 2 }}>
                  {manifestFormError}
                </Alert>
              )}
              
              {/* Identification Section */}
              <Typography variant="h6" color="primary" sx={{ mb: 2, fontWeight: 'bold' }}>
                Identification
              </Typography>
              <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', md: '1fr 1fr' }, gap: 2, mb: 3 }}>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
                  <Typography variant="caption" color="text.secondary">
                    Tracking Number
                  </Typography>
                  <Typography variant="body1" sx={{ fontWeight: 'medium', display: 'flex', alignItems: 'center' }}>
                    <Box component="span" sx={{ 
                      color: theme.palette.primary.main, 
                      display: 'inline-flex',
                      alignItems: 'center',
                      '& svg': { mr: 1 } 
                    }}>
                      <TimelineIcon fontSize="small" />
                    </Box>
                    {manifestFormData.trackingNo}
                  </Typography>
                </Box>
                
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
                  <Typography variant="caption" color="text.secondary">
                    Internal ID
                  </Typography>
                  <Typography variant="body1" sx={{ fontWeight: 'medium', display: 'flex', alignItems: 'center' }}>
                    <Box component="span" sx={{ 
                      color: theme.palette.primary.main, 
                      display: 'inline-flex',
                      alignItems: 'center',
                      '& svg': { mr: 1 } 
                    }}>
                      <TimelineIcon fontSize="small" />
                    </Box>
                    {containerNo && manifestFormData.sequenceNo ? `${containerNo}-${manifestFormData.sequenceNo}` : 'N/A'}
                  </Typography>
                </Box>
                
                <FormControl fullWidth margin="normal">
                  <InputLabel id="status-label">Status</InputLabel>
                  <Select
                    labelId="status-label"
                    name="status"
                    value={manifestFormData.status}
                    onChange={(e) => handleManifestFormChange('status', e.target.value)}
                    label="Status"
                    MenuProps={{
                      sx: {
                        zIndex: 10001,
                        '& .MuiPaper-root': {
                          zIndex: 10001
                        }
                      }
                    }}
                    sx={{
                      borderRadius: 1,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: theme.palette.primary.main,
                      },
                    }}
                  >
                    {Object.values(ManifestStatus).map((status) => (
                      <MenuItem key={status} value={status}>
                        {status.replace(/_/g, ' ')}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Box>
              
              {/* Client Information Section */}
              <Typography variant="h6" color="primary" sx={{ mb: 2, fontWeight: 'bold' }}>
                Client Information
              </Typography>
              <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', md: '1fr 1fr' }, gap: 2, mb: 3 }}>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5, mt: 2 }}>
                  <Typography variant="caption" color="text.secondary">
                    Client
                  </Typography>
                  <Typography variant="body1" sx={{ fontWeight: 'medium', display: 'flex', alignItems: 'center' }}>
                    <Box component="span" sx={{ 
                      color: theme.palette.grey[600], 
                      display: 'inline-flex',
                      alignItems: 'center',
                      '& svg': { mr: 1 } 
                    }}>
                      <PersonIcon fontSize="small" />
                    </Box>
                    {container?.client?.companyName ? 
                      container.client.companyName : 
                      container?.client?.username || 'No client selected'}
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5, mt: 2 }}>
                  <Typography variant="caption" color="text.secondary">
                    Container
                  </Typography>
                  <Typography variant="body1" sx={{ fontWeight: 'medium', display: 'flex', alignItems: 'center' }}>
                    <Box component="span" sx={{ 
                      color: theme.palette.grey[600], 
                      display: 'inline-flex',
                      alignItems: 'center',
                      '& svg': { mr: 1 } 
                    }}>
                      <LocalShippingOutlinedIcon fontSize="small" />
                    </Box>
                    {containerNo || 'No container selected'}
                  </Typography>
                </Box>
              </Box>

              {/* Package Details Section */}
              <Typography variant="h6" color="primary" sx={{ mb: 2, fontWeight: 'bold' }}>
                Package Details
              </Typography>
              <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', md: '1fr 1fr' }, gap: 2, mb: 3 }}>
                <TextField
                  required
                  name="pieces"
                  label="Pieces"
                  type="number"
                  value={manifestFormData.pieces || ''}
                  onChange={(e) => handleManifestFormChange('pieces', Number(e.target.value))}
                  fullWidth
                  margin="normal"
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 1,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: theme.palette.primary.main,
                      },
                    },
                  }}
                />
                <TextField
                  required
                  name="cbm"
                  label="CBM"
                  type="number"
                  value={manifestFormData.cbm || ''}
                  onChange={(e) => handleManifestFormChange('cbm', Number(e.target.value))}
                  fullWidth
                  margin="normal"
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 1,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: theme.palette.primary.main,
                      },
                    },
                  }}
                />
                <Box sx={{ mt: 2, mb: 2 }}>
                  <Typography variant="body2" color="text.secondary">Number of Pallets</Typography>
                  <Typography variant="body1" sx={{ fontWeight: 500, mt: 1 }}>
                    {manifestFormData.actualPalletsCount ?? 0}
                    <Typography variant="caption" color="text.secondary" display="block" sx={{ mt: 0.5 }}>
                      The number of pallets is automatically calculated based on created pallets
                    </Typography>
                  </Typography>
                </Box>
                <FormControl fullWidth margin="normal">
                  <InputLabel id="location-label">Location</InputLabel>
                  <Select
                    labelId="location-label"
                    id="location"
                    name="location"
                    value={manifestFormData.location || ''}
                    onChange={(e) => handleManifestFormChange('location', e.target.value)}
                    label="Location"
                    startAdornment={
                      <InputAdornment position="start">
                        <LocationOnIcon color="action" />
                      </InputAdornment>
                    }
                    sx={{
                      borderRadius: 1,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: theme.palette.primary.main,
                      },
                    }}
                  >
                    <MenuItem value="">
                      <em>None</em>
                    </MenuItem>
                    {locationZones.map((zone) => (
                      <MenuItem key={zone.id} value={zone.name}>
                        {zone.name}
                      </MenuItem>
                    ))}
                  </Select>
                  <FormHelperText>Select a location zone</FormHelperText>
                </FormControl>
                <FormControl fullWidth margin="normal">
                  <InputLabel id="driver-label">Driver</InputLabel>
                  <Select
                    labelId="driver-label"
                    name="driver"
                    value={manifestFormData.driver?.username || ''}
                    onChange={(e) => {
                      const selectedDriver = drivers.find(d => d.username === e.target.value) || { username: '' };
                      handleManifestFormChange('driver', selectedDriver);
                    }}
                    label="Driver"
                    MenuProps={{
                      sx: {
                        zIndex: 10001,
                        '& .MuiPaper-root': {
                          zIndex: 10001
                        }
                      }
                    }}
                    sx={{
                      borderRadius: 1,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: theme.palette.primary.main,
                      },
                    }}
                    startAdornment={
                      <InputAdornment position="start">
                        <PersonIcon color="action" />
                      </InputAdornment>
                    }
                  >
                    <MenuItem value="">
                      <em>Not Assigned</em>
                    </MenuItem>
                    {drivers && drivers.length > 0 ? (
                      drivers.map((driver) => (
                        <MenuItem key={driver.username} value={driver.username}>
                          {`${driver.username} - ${driver.firstName || ''} ${driver.lastName || ''}`.trim()}
                        </MenuItem>
                      ))
                    ) : (
                      <MenuItem disabled value="">
                        No drivers available
                      </MenuItem>
                    )}
                  </Select>
                  <FormHelperText>Driver selection is optional</FormHelperText>
                </FormControl>
                <FormControl fullWidth margin="normal">
                  <InputLabel id="delivery-vehicle-label">Delivery Vehicle</InputLabel>
                  <Select
                    labelId="delivery-vehicle-label"
                    id="deliveryVehicle"
                    name="deliveryVehicle"
                    value={vehicleTypes.some(vt => vt.name === manifestFormData.deliveryVehicle) ? manifestFormData.deliveryVehicle : ''}
                    onChange={(e) => handleManifestFormChange('deliveryVehicle', e.target.value)}
                    label="Delivery Vehicle"
                    MenuProps={{
                      sx: {
                        zIndex: 10001,
                        '& .MuiPaper-root': {
                          zIndex: 10001
                        }
                      }
                    }}
                    startAdornment={
                      <InputAdornment position="start">
                        <LocalShippingOutlinedIcon color="action" />
                      </InputAdornment>
                    }
                    sx={{
                      borderRadius: 1,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: theme.palette.primary.main,
                      },
                    }}
                  >
                    <MenuItem value="">
                      <em>Auto-assign</em>
                    </MenuItem>
                    {vehicleTypes.map((vehicleType) => (
                      <MenuItem key={vehicleType.id} value={vehicleType.name}>
                        {vehicleType.name}
                      </MenuItem>
                    ))}
                  </Select>
                  <FormHelperText sx={{ display: 'flex', alignItems: 'center' }}>
                    Will be auto-assigned based on weight, CBM, and pieces if left empty
                    <Tooltip title="The system will automatically determine the appropriate vehicle type based on the manifest's weight, CBM, and number of pieces. You can manually select a different vehicle type if needed." arrow>
                      <IconButton size="small" sx={{ ml: 0.5, p: 0 }}>
                        <InfoIcon fontSize="small" color="action" />
                      </IconButton>
                    </Tooltip>
                  </FormHelperText>
                </FormControl>
                <TextField
                  name="weight"
                  label="Weight (kg)"
                  type="number"
                  value={manifestFormData.weight || ''}
                  onChange={(e) => handleManifestFormChange('weight', Number(e.target.value))}
                  fullWidth
                  margin="normal"
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 1,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: theme.palette.primary.main,
                      },
                    },
                  }}
                />
                {manifestFormData.createdDate && (
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5, mt: 2 }}>
                    <Typography variant="caption" color="text.secondary">
                      Created Date
                    </Typography>
                    <Typography variant="body1" sx={{ fontWeight: 'medium', display: 'flex', alignItems: 'center' }}>
                      <Box component="span" sx={{ 
                        color: theme.palette.grey[600], 
                        display: 'inline-flex',
                        alignItems: 'center',
                        '& svg': { mr: 1 } 
                      }}>
                        <CalendarTodayIcon fontSize="small" />
                      </Box>
                      {formatDateTime(manifestFormData.createdDate)}
                    </Typography>
                  </Box>
                )}
                <Box position="relative" zIndex={1300} width="100%">
                  <SplitDeliveryDatePicker
                    label="Delivery Date & Time Slot"
                    value={manifestFormData.deliveryDate}
                    onChange={(newValue) => handleManifestFormChange('deliveryDate', newValue)}
                    fullWidth
                    slotProps={{
                      datePicker: {
                      popper: {
                          sx: { zIndex: 9999 }
                        }
                      } 
                    }}
                  />
                </Box>
                <Box position="relative" zIndex={1300} width="100%">
                  <DateTimePicker
                    label="Delivered Date"
                    value={manifestFormData.deliveredDate}
                    onChange={(newValue) => handleManifestFormChange('deliveredDate', newValue)}
                    format="dd MMM yyyy HH:mm"
                    ampm={false}
                    slotProps={{
                      textField: { 
                        fullWidth: true, 
                        margin: "normal",
                        InputProps: {
                          startAdornment: (
                            <InputAdornment position="start">
                              <CalendarTodayIcon color="action" />
                            </InputAdornment>
                          ),
                        },
                        sx: {
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 1,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: theme.palette.primary.main,
                      },
                    },
                        } 
                      },
                      popper: {
                        sx: {
                          zIndex: 9999
                        }
                      }
                  }}
                />
                </Box>
              </Box>

              {/* Shipping Information Section */}
              <Typography variant="h6" color="primary" sx={{ mb: 2, fontWeight: 'bold' }}>
                Shipping Information
              </Typography>
              <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', md: '1fr 1fr' }, gap: 2, mb: 3 }}>
                <TextField
                  required
                  name="customerName"
                  label="Customer Name"
                  value={manifestFormData.customerName || ''}
                  onChange={(e) => handleManifestFormChange('customerName', e.target.value)}
                  fullWidth
                  margin="normal"
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <PersonIcon color="action" />
                      </InputAdornment>
                    ),
                  }}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 1,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: theme.palette.primary.main,
                      },
                    },
                  }}
                />
                <TextField
                  required
                  name="phoneNo"
                  label="Phone Number"
                  value={manifestFormData.phoneNo || ''}
                  onChange={(e) => handleManifestFormChange('phoneNo', e.target.value)}
                  fullWidth
                  margin="normal"
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <PhoneIcon color="action" />
                      </InputAdornment>
                    ),
                  }}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 1,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: theme.palette.primary.main,
                      },
                    },
                  }}
                />
                <TextField
                  required
                  name="address"
                  label="Address"
                  value={manifestFormData.address || ''}
                  onChange={(e) => handleManifestFormChange('address', e.target.value)}
                  fullWidth
                  margin="normal"
                  sx={{ 
                    gridColumn: '1 / -1',
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 1,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: theme.palette.primary.main,
                      },
                    },
                  }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <HomeIcon color="action" />
                      </InputAdornment>
                    ),
                  }}
                />
                <TextField
                  required
                  name="postalCode"
                  label="Postal Code"
                  value={manifestFormData.postalCode || ''}
                  onChange={(e) => handleManifestFormChange('postalCode', e.target.value)}
                  fullWidth
                  margin="normal"
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 1,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: theme.palette.primary.main,
                      },
                    },
                  }}
                />
                <TextField
                  required
                  name="country"
                  label="Country"
                  value={manifestFormData.country || ''}
                  onChange={(e) => handleManifestFormChange('country', e.target.value)}
                  fullWidth
                  margin="normal"
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <LocationOnIcon color="action" />
                      </InputAdornment>
                    ),
                  }}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 1,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: theme.palette.primary.main,
                      },
                    },
                  }}
                />
              </Box>

              {/* Remarks (CS) Section */}
              <Typography variant="h6" color="primary" sx={{ mb: 2, fontWeight: 'bold' }}>
                Remarks (CS)
              </Typography>
              <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr' }, gap: 2 }}>
                <TextField
                  name="remarks"
                  label="Remarks (CS)"
                  value={manifestFormData.remarks || ''}
                  onChange={(e) => handleManifestFormChange('remarks', e.target.value)}
                  fullWidth
                  multiline
                  rows={3}
                  margin="normal"
                  sx={{ 
                    gridColumn: '1 / -1',
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 1,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: theme.palette.primary.main,
                      },
                    },
                  }}
                />
              </Box>
            </DialogContent>
            
            <DialogActions sx={{ px: 3, py: 2 }}>
              <Button onClick={handleCloseEditManifest} variant="outlined">Cancel</Button>
              <Button onClick={handleSaveEditManifest} variant="contained" color="primary" disabled={manifestFormLoading}>
                {manifestFormLoading ? <CircularProgress size={24} /> : 'Update'}
              </Button>
            </DialogActions>
          </Dialog>
        </>
      ) : (
        <Alert severity="info">Container not found.</Alert>
      )}

      {/* Delivery Date Edit Dialog */}
      <DeliveryDateDialog
        open={deliveryDateDialogOpen}
        onClose={() => setDeliveryDateDialogOpen(false)}
        onSave={(newDate) => {
          if (editingDeliveryDate && newDate) {
            handleQuickEditDeliveryDate(editingDeliveryDate, newDate);
            setDeliveryDateDialogOpen(false);
          }
        }}
        trackingNo={editingDeliveryDate || ''}
        deliveryDate={tempDeliveryDate}
        onDateChange={(newValue) => setTempDeliveryDate(newValue)}
        isEdit={true}
      />

      {/* Confirmation Dialog */}
      <Dialog 
        open={confirmDialogOpen} 
        onClose={() => setConfirmDialogOpen(false)}
        PaperProps={{
          sx: {
            borderRadius: 2,
            boxShadow: 24,
          },
        }}
      >
        <DialogTitle sx={{ 
          bgcolor: theme.palette.primary.main, 
          color: 'white',
          px: 3,
          py: 2
        }}>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Typography variant="h6">
              {confirmTitle}
            </Typography>
            <IconButton onClick={() => setConfirmDialogOpen(false)} size="small" sx={{ color: 'white' }}>
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent sx={{ px: 3, py: 3 }}>
          <DialogContentText>{confirmMessage}</DialogContentText>
        </DialogContent>
        <DialogActions sx={{ px: 3, py: 2 }}>
          <Button onClick={() => setConfirmDialogOpen(false)} variant="outlined">
            Cancel
          </Button>
          <Button 
            onClick={() => {
              confirmAction();
              setConfirmDialogOpen(false);
            }} 
            variant="contained"
            color="primary" 
            autoFocus
          >
            Confirm
                  </Button>
        </DialogActions>
          </Dialog>

      {/* Quick Edit Value Dialog */}
      <Dialog
        open={editValueDialogOpen}
        onClose={() => setEditValueDialogOpen(false)}
        maxWidth="xs"
        fullWidth
        PaperProps={{
          elevation: 3,
          sx: { borderRadius: 2, p: 1 }
        }}
      >
        <DialogTitle sx={{ 
          borderBottom: '1px solid', 
          borderColor: 'divider',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        }}>
          <Box display="flex" alignItems="center">
            <EditIcon sx={{ mr: 1, color: theme.palette.primary.main }} />
            {editValueField === 'cbm' ? 'Edit CBM' : 'Edit Number of Pallets'}
          </Box>
          <IconButton
            size="small"
            onClick={() => setEditValueDialogOpen(false)}
            sx={{ color: 'text.secondary' }}
          >
            <CloseIcon fontSize="small" />
          </IconButton>
        </DialogTitle>
        <DialogContent sx={{ pt: 3, pb: 2 }}>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            {editValueField === 'cbm' ? 'Edit the CBM value for this manifest:' : 'Edit the number of pallets for this manifest:'}
          </Typography>
          <TextField
            label={editValueField === 'cbm' ? 'CBM' : 'Number of Pallets'}
            type="number"
            value={editValueTemp === null ? '' : editValueTemp}
            onChange={(e) => {
              const value = e.target.value;
              if (value === '') {
                // Handle empty value as null for actualPalletsCount
                setEditValueTemp(editValueField === 'actualPalletsCount' ? null : 0);
              } else {
                // Convert to number
                setEditValueTemp(Number(value));
              }
            }}
            fullWidth
            variant="outlined"
            size="medium"
            sx={{ my: 1 }}
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    size="small"
                    onClick={() => setEditValueTemp(editValueField === 'actualPalletsCount' ? null : 0)}
                    disabled={editValueTemp === null && editValueField === 'actualPalletsCount'}
                  >
                    <ClearIcon fontSize="small" />
                  </IconButton>
                </InputAdornment>
              )
            }}
          />
          <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 1 }}>
            {editValueField === 'cbm' ? 'The CBM value will be updated for this manifest.' : 'The number of pallets will be updated for this manifest.'}
          </Typography>
        </DialogContent>
        <DialogActions sx={{ px: 3, py: 2, borderTop: '1px solid', borderColor: 'divider' }}>
          <Button onClick={() => setEditValueDialogOpen(false)} variant="outlined">
            Cancel
          </Button>
          <Button
            onClick={() => {
              if (editValueTrackingNo && editValueTemp !== null) {
                handleSaveEditValueDialog();
                setEditValueDialogOpen(false);
              }
            }}
            variant="contained"
            color="primary"
            disabled={editValueTemp === null && editValueField === 'cbm'}
            startIcon={<SaveIcon />}
          >
            Update
          </Button>
        </DialogActions>
      </Dialog>

      <PalletManagementDialog
        open={palletManagementDialogOpen}
        onClose={handleClosePalletManagementDialog}
        manifest={selectedPalletManifest}
        onPalletOperationComplete={handlePalletOperationComplete}
      />

      {/* Quick Delivery Vehicle Dialog */}
      <QuickDeliveryVehicleDialog
        open={quickVehicleDialogOpen}
        onClose={handleCloseQuickVehicleEdit}
        onSave={handleSaveQuickVehicleEdit}
        trackingNo={selectedManifestForVehicleEdit?.trackingNo || ''}
        currentDeliveryVehicle={selectedManifestForVehicleEdit?.deliveryVehicle || null}
        manifestWeight={selectedManifestForVehicleEdit?.weight}
        manifestCbm={selectedManifestForVehicleEdit?.cbm}
      />
      
      {/* Bulk Delivery Date Dialog */}
      <DeliveryDateDialog
        open={bulkDeliveryDateDialogOpen}
        onClose={() => {
          setBulkDeliveryDateDialogOpen(false);
          setBulkDeliveryDate(null);
        }}
        onSave={handleBulkDeliveryDateUpdate}
        deliveryDate={bulkDeliveryDate}
        onDateChange={(date) => setBulkDeliveryDate(date)}
        isEdit={false}
      />
    </Box>
  );
};

export default ContainerDetail; 