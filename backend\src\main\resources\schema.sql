-- If the server supports it, try to use the database
USE wms_db;

-- Create roles table with all required roles
CREATE TABLE IF NOT EXISTS roles (
    id INTEGER NOT NULL AUTO_INCREMENT,
    name VARCHAR(20) NOT NULL,
    PRIMARY KEY (id)
);

-- Create base users table
CREATE TABLE IF NOT EXISTS users (
    user_number BIGINT,
    username VARCHAR(20) NOT NULL,
    user_email VARCHAR(50) NOT NULL,
    user_password VARCHAR(120) NOT NULL,
    full_name VARCHAR(50) NOT NULL,
    contact VARCHAR(20) NOT NULL,
    status VARCHAR(20) NOT NULL,
    user_type VARCHAR(20) NOT NULL,
    PRIMARY KEY (username)
);

-- Create clients table
CREATE TABLE IF NOT EXISTS clients (
    username VA<PERSON>HA<PERSON>(20) NOT NULL,
    company_name VARCHAR(100),
    designation VA<PERSON>HAR(50),
    alt_contact <PERSON>RCHA<PERSON>(20),
    alt_designation VA<PERSON>HA<PERSON>(50),
    <PERSON><PERSON><PERSON><PERSON> (username),
    <PERSON>OR<PERSON><PERSON><PERSON>Y (username) REFERENCES users(username)
);

-- Create containers table
CREATE TABLE IF NOT EXISTS containers (
    container_number VARCHAR(50) NOT NULL,
    client_username VARCHAR(20) NOT NULL,
    truck_no VARCHAR(50) NOT NULL,
    vessel_voyage_no VARCHAR(50) NOT NULL,
    eta_requested_date DATETIME NOT NULL,
    manifest_quantity INT NOT NULL,
    portnet_eta DATETIME,
    eta_allocated DATETIME,
    created_date DATETIME,
    arrival_date DATETIME,
    loading_bay VARCHAR(50),
    unstuff_date DATETIME,
    unstuff_completed_date DATETIME,
    pull_out_date DATETIME,
    unstuff_team VARCHAR(50),
    remark TEXT,
    status VARCHAR(20) NOT NULL DEFAULT 'CREATED',
    PRIMARY KEY (container_number),
    FOREIGN KEY (client_username) REFERENCES clients(username)
) ENGINE=InnoDB;

-- Create index for container_number if it doesn't exist
CREATE INDEX IF NOT EXISTS idx_container_number ON containers(container_number);

-- Create managers table
CREATE TABLE IF NOT EXISTS managers (
    username VARCHAR(20) NOT NULL,
    department VARCHAR(50),
    PRIMARY KEY (username),
    FOREIGN KEY (username) REFERENCES users(username)
);

-- Create drivers table
CREATE TABLE IF NOT EXISTS drivers (
    username VARCHAR(20) NOT NULL,
    license_no VARCHAR(20),
    PRIMARY KEY (username),
    FOREIGN KEY (username) REFERENCES users(username)
);

-- Create admins table
CREATE TABLE IF NOT EXISTS admins (
    username VARCHAR(20) NOT NULL,
    PRIMARY KEY (username),
    FOREIGN KEY (username) REFERENCES users(username)
);

-- Create user_roles table
CREATE TABLE IF NOT EXISTS user_roles (
    username VARCHAR(20) NOT NULL,
    role_id INTEGER NOT NULL,
    PRIMARY KEY (username, role_id),
    FOREIGN KEY (username) REFERENCES users(username),
    FOREIGN KEY (role_id) REFERENCES roles(id)
);

-- Create vehicle_types table
CREATE TABLE IF NOT EXISTS vehicle_types (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL UNIQUE,
    abbreviation VARCHAR(10),
    description VARCHAR(255),
    min_weight DECIMAL(10,2),
    max_weight DECIMAL(10,2),
    min_cbm DECIMAL(10,2),
    max_cbm DECIMAL(10,2),
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    min_cbm_per_piece DOUBLE PRECISION,
    max_cbm_per_piece DOUBLE PRECISION
);

-- Create vehicles table
CREATE TABLE IF NOT EXISTS vehicles (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    license_plate VARCHAR(20) NOT NULL UNIQUE,
    vehicle_type_id BIGINT NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'AVAILABLE',
    FOREIGN KEY (vehicle_type_id) REFERENCES vehicle_types(id)
);

-- Create vehicle_drivers junction table for many-to-many relationship
CREATE TABLE IF NOT EXISTS vehicle_drivers (
    vehicle_id BIGINT NOT NULL,
    username VARCHAR(20) NOT NULL,
    PRIMARY KEY (vehicle_id, username),
    FOREIGN KEY (vehicle_id) REFERENCES vehicles(id),
    FOREIGN KEY (username) REFERENCES drivers(username)
);

-- Create manifests table
CREATE TABLE IF NOT EXISTS manifests (
    tracking_no VARCHAR(50) NOT NULL,
    client_username VARCHAR(20) NOT NULL,
    container_no VARCHAR(50) NOT NULL,
    driver_username VARCHAR(20),
    sequence_no INT,
    internal_id VARCHAR(50),
    status VARCHAR(50) NOT NULL,
    customer_name VARCHAR(100) NOT NULL,
    phone_no VARCHAR(225) NOT NULL,
    address VARCHAR(255) NOT NULL,
    postal_code VARCHAR(20) NOT NULL,
    country VARCHAR(50) NOT NULL,
    pieces INT NOT NULL,
    cbm DOUBLE NOT NULL,
    weight DOUBLE NOT NULL DEFAULT 0.0,
    created_date DATETIME,
    location VARCHAR(100),
    delivery_date DATE,
    time_slot VARCHAR(50),
    delivered_date DATETIME,
    delivery_vehicle VARCHAR(50),
    driver_remarks VARCHAR(255),
    remarks VARCHAR(255),
    PRIMARY KEY (tracking_no),
    FOREIGN KEY (client_username) REFERENCES clients(username),
    FOREIGN KEY (container_no) REFERENCES containers(container_number),
    FOREIGN KEY (driver_username) REFERENCES drivers(username)
);

-- Create pallets table
CREATE TABLE IF NOT EXISTS pallets (
    pallet_no VARCHAR(100) NOT NULL,
    manifest_tracking_no VARCHAR(50) NOT NULL,
    no_of_pieces INT NOT NULL,
    total_pieces_reference INT NOT NULL,
    pallet_sequence INT,
    PRIMARY KEY (pallet_no),
    FOREIGN KEY (manifest_tracking_no) REFERENCES manifests(tracking_no) ON DELETE CASCADE
);

-- Create location_zones table
CREATE TABLE IF NOT EXISTS location_zones (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    abbreviation VARCHAR(10),
    start_postal_code INT,
    end_postal_code INT,
    special_cases VARCHAR(255),
    description VARCHAR(255),
    is_active BOOLEAN NOT NULL DEFAULT TRUE
);

-- Create manifest_tracking_logs table
CREATE TABLE IF NOT EXISTS manifest_tracking_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    tracking_no VARCHAR(50) NOT NULL,
    previous_status VARCHAR(50),
    new_status VARCHAR(50),
    field_name VARCHAR(100),
    old_value TEXT,
    new_value TEXT,
    action_type VARCHAR(50) NOT NULL,
    updated_by VARCHAR(50) NOT NULL,
    updated_at DATETIME NOT NULL,
    remarks TEXT,
    ip_address VARCHAR(50),
    user_agent VARCHAR(255),
    FOREIGN KEY (tracking_no) REFERENCES manifests(tracking_no) ON DELETE CASCADE,
    FOREIGN KEY (updated_by) REFERENCES users(username)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_tracking_logs_tracking_no ON manifest_tracking_logs(tracking_no);
CREATE INDEX IF NOT EXISTS idx_tracking_logs_action_type ON manifest_tracking_logs(action_type);
CREATE INDEX IF NOT EXISTS idx_tracking_logs_updated_by ON manifest_tracking_logs(updated_by);
CREATE INDEX IF NOT EXISTS idx_tracking_logs_updated_at ON manifest_tracking_logs(updated_at);