package com.wms.service.impl.VehicleServiceImpl;

import com.wms.dto.VehicleTypeDTO;
import com.wms.entity.vehicle.VehicleType;
import com.wms.exception.ResourceNotFoundException;
import com.wms.repository.VehicleRepo.VehicleTypeRepository;
import com.wms.service.VehicleServices.VehicleTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.Comparator;

@Service
public class VehicleTypeServiceImpl implements VehicleTypeService {

    private static final Logger logger = LoggerFactory.getLogger(VehicleTypeServiceImpl.class);

    @Autowired
    private VehicleTypeRepository vehicleTypeRepository;

    @Override
    public VehicleTypeDTO createVehicleType(VehicleTypeDTO vehicleTypeDTO) {
        VehicleType vehicleType = mapToEntity(vehicleTypeDTO);
        VehicleType savedVehicleType = vehicleTypeRepository.save(vehicleType);
        return mapToDTO(savedVehicleType);
    }

    @Override
    public VehicleTypeDTO updateVehicleType(Long id, VehicleTypeDTO vehicleTypeDTO) {
        VehicleType vehicleType = vehicleTypeRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Vehicle type not found with id: " + id));
        
        vehicleType.setName(vehicleTypeDTO.getName());
        vehicleType.setAbbreviation(vehicleTypeDTO.getAbbreviation());
        vehicleType.setDescription(vehicleTypeDTO.getDescription());
        vehicleType.setMinWeight(vehicleTypeDTO.getMinWeight());
        vehicleType.setMaxWeight(vehicleTypeDTO.getMaxWeight());
        vehicleType.setMinCbm(vehicleTypeDTO.getMinCbm());
        vehicleType.setMaxCbm(vehicleTypeDTO.getMaxCbm());
        vehicleType.setMinCbmPerPiece(vehicleTypeDTO.getMinCbmPerPiece());
        vehicleType.setMaxCbmPerPiece(vehicleTypeDTO.getMaxCbmPerPiece());
        vehicleType.setIsActive(vehicleTypeDTO.getIsActive());
        
        VehicleType updatedVehicleType = vehicleTypeRepository.save(vehicleType);
        return mapToDTO(updatedVehicleType);
    }

    @Override
    public void deleteVehicleType(Long id) {
        VehicleType vehicleType = vehicleTypeRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Vehicle type not found with id: " + id));
        vehicleTypeRepository.delete(vehicleType);
    }

    @Override
    public VehicleTypeDTO getVehicleTypeById(Long id) {
        VehicleType vehicleType = vehicleTypeRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Vehicle type not found with id: " + id));
        return mapToDTO(vehicleType);
    }

    @Override
    public List<VehicleTypeDTO> getAllVehicleTypes() {
        List<VehicleType> vehicleTypes = vehicleTypeRepository.findAll();
        return vehicleTypes.stream()
                .map(this::mapToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public VehicleType getVehicleTypeEntityById(Long id) {
        return vehicleTypeRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Vehicle type not found with id: " + id));
    }

    @Override
    public Optional<VehicleType> determineVehicleTypeByWeightAndCbm(Double weight, Double cbm) {
        if (weight == null || cbm == null) {
            logger.warn("Weight or CBM is null - cannot determine vehicle type");
            return Optional.empty();
        }

        // Get all active vehicle types
        List<VehicleType> activeVehicleTypes = vehicleTypeRepository.findByIsActiveTrue();

        logger.debug("Determining vehicle type for weight: {} kg, CBM: {} - checking {} active vehicle types",
                    weight, cbm, activeVehicleTypes.size());

        // Find all matching vehicle types
        List<VehicleType> matchingVehicleTypes = activeVehicleTypes.stream()
                .filter(vehicleType -> isVehicleTypeMatch(vehicleType, weight, cbm, null))
                .collect(Collectors.toList());

        if (matchingVehicleTypes.isEmpty()) {
            logger.warn("No matching vehicle type found for weight: {} kg, CBM: {}", weight, cbm);
            return Optional.empty();
        }

        // Select the most optimal vehicle type (smallest that can handle the load)
        VehicleType optimalVehicleType = selectOptimalVehicleType(matchingVehicleTypes, weight, cbm);

        logger.debug("Selected optimal vehicle type: {} for weight: {} kg, CBM: {}",
                    optimalVehicleType.getName(), weight, cbm);

        return Optional.of(optimalVehicleType);
    }

    @Override
    public Optional<VehicleType> determineVehicleTypeByWeightCbmAndPieces(Double weight, Double cbm, Integer pieces) {
        if (weight == null || cbm == null || pieces == null || pieces <= 0) {
            logger.warn("Weight, CBM, or pieces is invalid - cannot determine vehicle type");
            return Optional.empty();
        }

        // Get all active vehicle types
        List<VehicleType> activeVehicleTypes = vehicleTypeRepository.findByIsActiveTrue();

        logger.debug("Determining vehicle type for weight: {} kg, CBM: {}, pieces: {} - checking {} active vehicle types",
                    weight, cbm, pieces, activeVehicleTypes.size());

        // Find all matching vehicle types
        List<VehicleType> matchingVehicleTypes = activeVehicleTypes.stream()
                .filter(vehicleType -> isVehicleTypeMatch(vehicleType, weight, cbm, pieces))
                .collect(Collectors.toList());

        if (matchingVehicleTypes.isEmpty()) {
            logger.warn("No matching vehicle type found for weight: {} kg, CBM: {}, pieces: {}", weight, cbm, pieces);
            return Optional.empty();
        }

        // Select the most optimal vehicle type (smallest that can handle the load)
        VehicleType optimalVehicleType = selectOptimalVehicleType(matchingVehicleTypes, weight, cbm);

        logger.debug("Selected optimal vehicle type: {} for weight: {} kg, CBM: {}, pieces: {}",
                    optimalVehicleType.getName(), weight, cbm, pieces);

        return Optional.of(optimalVehicleType);
    }

    @Override
    public String getVehicleTypeAbbreviation(String vehicleTypeName) {
        if (vehicleTypeName == null || vehicleTypeName.trim().isEmpty()) {
            return "V"; // Default fallback
        }

        // Try to find the vehicle type by name first
        Optional<VehicleType> vehicleType = vehicleTypeRepository.findByName(vehicleTypeName.trim());
        
        if (vehicleType.isPresent() && vehicleType.get().getAbbreviation() != null 
            && !vehicleType.get().getAbbreviation().trim().isEmpty()) {
            return vehicleType.get().getAbbreviation().toUpperCase();
        }

        // Fallback: generate abbreviation from vehicle type name
        return generateAbbreviationFromVehicleName(vehicleTypeName);
    }

    /**
     * Generates a fallback abbreviation from the vehicle type name
     */
    private String generateAbbreviationFromVehicleName(String vehicleTypeName) {
        if (vehicleTypeName == null || vehicleTypeName.trim().isEmpty()) {
            return "V";
        }

        String veh = vehicleTypeName.toUpperCase().trim();
        
        // Check for specific vehicle types
        if (veh.contains("VAN")) {
            return "V";
        }
        if (veh.contains("LORRY WITH TAILGATE")) {
            return "LT";
        }
        if (veh.contains("LORRY")) {
            return "L";
        }
        if (veh.contains("TRUCK")) {
            return "T";
        }
        if (veh.contains("MOTORCYCLE")) {
            return "M";
        }
        
        // Default to first letter of vehicle type
        return veh.substring(0, 1);
    }

    /**
     * Checks if a vehicle type matches the given weight and CBM requirements
     */
    private boolean isVehicleTypeMatch(VehicleType vehicleType, Double weight, Double cbm, Integer pieces) {
        // Check weight constraints
        if (vehicleType.getMinWeight() != null && weight < vehicleType.getMinWeight()) {
            return false;
        }
        if (vehicleType.getMaxWeight() != null && weight > vehicleType.getMaxWeight()) {
            return false;
        }

        // Check CBM constraints
        if (vehicleType.getMinCbm() != null && cbm < vehicleType.getMinCbm()) {
            return false;
        }
        if (vehicleType.getMaxCbm() != null && cbm > vehicleType.getMaxCbm()) {
            return false;
        }
        
        // Check CBM per piece constraints if pieces is provided
        if (pieces != null && pieces > 0) {
            Double cbmPerPiece = cbm / pieces;
            
            if (vehicleType.getMinCbmPerPiece() != null && cbmPerPiece < vehicleType.getMinCbmPerPiece()) {
                return false;
            }
            if (vehicleType.getMaxCbmPerPiece() != null && cbmPerPiece > vehicleType.getMaxCbmPerPiece()) {
                return false;
            }
        }

        return true;
    }

    /**
     * Selects the most optimal vehicle type from a list of matching vehicle types.
     * The optimal vehicle is the one with the smallest capacity that can still handle the load,
     * prioritizing efficiency and cost-effectiveness.
     */
    private VehicleType selectOptimalVehicleType(List<VehicleType> matchingVehicleTypes, Double weight, Double cbm) {
        return matchingVehicleTypes.stream()
                .min(Comparator
                        // Primary sort: by max weight (ascending) - prefer smaller vehicles
                        .comparing((VehicleType vt) -> vt.getMaxWeight() != null ? vt.getMaxWeight() : Double.MAX_VALUE)
                        // Secondary sort: by max CBM (ascending) - prefer smaller volume
                        .thenComparing(vt -> vt.getMaxCbm() != null ? vt.getMaxCbm() : Double.MAX_VALUE)
                        // Tertiary sort: by name for consistency
                        .thenComparing(VehicleType::getName))
                .orElse(matchingVehicleTypes.get(0)); // Fallback to first match if comparison fails
    }

    private VehicleTypeDTO mapToDTO(VehicleType vehicleType) {
        VehicleTypeDTO vehicleTypeDTO = new VehicleTypeDTO();
        vehicleTypeDTO.setId(vehicleType.getId());
        vehicleTypeDTO.setName(vehicleType.getName());
        vehicleTypeDTO.setAbbreviation(vehicleType.getAbbreviation());
        vehicleTypeDTO.setDescription(vehicleType.getDescription());
        vehicleTypeDTO.setMinWeight(vehicleType.getMinWeight());
        vehicleTypeDTO.setMaxWeight(vehicleType.getMaxWeight());
        vehicleTypeDTO.setMinCbm(vehicleType.getMinCbm());
        vehicleTypeDTO.setMaxCbm(vehicleType.getMaxCbm());
        vehicleTypeDTO.setMinCbmPerPiece(vehicleType.getMinCbmPerPiece());
        vehicleTypeDTO.setMaxCbmPerPiece(vehicleType.getMaxCbmPerPiece());
        vehicleTypeDTO.setIsActive(vehicleType.getIsActive());
        return vehicleTypeDTO;
    }

    private VehicleType mapToEntity(VehicleTypeDTO vehicleTypeDTO) {
        VehicleType vehicleType = new VehicleType();
        vehicleType.setName(vehicleTypeDTO.getName());
        vehicleType.setAbbreviation(vehicleTypeDTO.getAbbreviation());
        vehicleType.setDescription(vehicleTypeDTO.getDescription());
        vehicleType.setMinWeight(vehicleTypeDTO.getMinWeight());
        vehicleType.setMaxWeight(vehicleTypeDTO.getMaxWeight());
        vehicleType.setMinCbm(vehicleTypeDTO.getMinCbm());
        vehicleType.setMaxCbm(vehicleTypeDTO.getMaxCbm());
        vehicleType.setMinCbmPerPiece(vehicleTypeDTO.getMinCbmPerPiece());
        vehicleType.setMaxCbmPerPiece(vehicleTypeDTO.getMaxCbmPerPiece());
        vehicleType.setIsActive(vehicleTypeDTO.getIsActive());
        return vehicleType;
    }
} 