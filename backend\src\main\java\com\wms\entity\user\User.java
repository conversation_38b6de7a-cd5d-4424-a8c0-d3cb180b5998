package com.wms.entity.user;

import jakarta.persistence.*;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;

import com.wms.entity.Role;

@Data
@Entity
@Table(name = "users")
@Inheritance(strategy = InheritanceType.JOINED)
@DiscriminatorColumn(name = "user_type", discriminatorType = DiscriminatorType.STRING)
public abstract class User {

    @Column(name = "user_number")
    private Long userNumber;
    
    @Id
    @NotBlank(message = "Username is required")
    @Size(max = 20)
    @Column(name = "username", unique = true)
    private String username;

    @NotBlank(message = "Email is required")
    @Size(max = 50)
    @Column(name = "user_email", unique = true) 
    @Email
    private String email;

    @NotBlank(message = "Password is required")
    @Size(max = 120)
    @Column(name = "user_password") 
    @JsonIgnore
    private String password;

    @NotBlank(message = "Full Name is required")
    @Size(max = 50)
    @Column(name = "full_name")
    private String fullName;

    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(name = "user_roles",
        joinColumns = @JoinColumn(name = "username"),
        inverseJoinColumns = @JoinColumn(name = "role_id"))
    private Set<Role> roles = new HashSet<>();

    @NotBlank(message = "Contact is required")
    @Size(max = 20)
    @Column(name = "contact")
    protected String contact;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private UserStatus status = UserStatus.ACTIVE;

    @JsonProperty("userType")
    public String getUserType() {
        return this.getClass().getSimpleName().toUpperCase();
    }

    @JsonProperty("roles")
    public Set<String> getRoleNames() {
        return roles.stream()
                .map(Role::getName)
                .collect(Collectors.toSet());
    }
} 
