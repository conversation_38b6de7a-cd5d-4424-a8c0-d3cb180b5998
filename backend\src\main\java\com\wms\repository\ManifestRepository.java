package com.wms.repository;

import com.wms.entity.Manifest;
import com.wms.entity.ManifestStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import java.util.List;
import java.util.Optional;

@Repository
public interface ManifestRepository extends JpaRepository<Manifest, String> {
    List<Manifest> findByClientUsername(String username);
    List<Manifest> findByContainerContainerNo(String containerNo);
    List<Manifest> findByStatus(ManifestStatus status);
    
    @Query("SELECT COALESCE(MAX(m.sequenceNo), 0) FROM Manifest m WHERE m.container.containerNo = :containerNo")
    Integer findMaxSequenceNoByContainerContainerNo(@Param("containerNo") String containerNo);
    
    List<Manifest> findByDriverUsername(String username);
    
    @Query("SELECT m FROM Manifest m LEFT JOIN FETCH m.pallets")
    List<Manifest> findAllWithPallets();
    
    @Query("SELECT m FROM Manifest m LEFT JOIN FETCH m.pallets WHERE m.trackingNo = :trackingNo")
    Optional<Manifest> findByIdWithPallets(@Param("trackingNo") String trackingNo);
    
    @Query("SELECT m FROM Manifest m LEFT JOIN FETCH m.pallets WHERE m.client.username = :username")
    List<Manifest> findByClientUsernameWithPallets(@Param("username") String username);
    
    @Query("SELECT m FROM Manifest m LEFT JOIN FETCH m.pallets WHERE m.container.containerNo = :containerNo")
    List<Manifest> findByContainerContainerNoWithPallets(@Param("containerNo") String containerNo);
    
    @Query("SELECT m FROM Manifest m LEFT JOIN FETCH m.pallets WHERE m.driver.username = :username")
    List<Manifest> findByDriverUsernameWithPallets(@Param("username") String username);
    
    @Query("SELECT m FROM Manifest m WHERE m.deliveryDate IS NOT NULL AND m.status IN :statuses")
    List<Manifest> findManifestsWithDeliveryDates(@Param("statuses") List<ManifestStatus> statuses);
} 