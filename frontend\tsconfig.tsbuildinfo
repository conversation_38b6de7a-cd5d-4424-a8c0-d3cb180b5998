{"root": ["./src/app.tsx", "./src/main.tsx", "./src/theme.ts", "./src/vite-env.d.ts", "./src/components/dashboardlayout.tsx", "./src/components/layout.tsx", "./src/components/manifestlabel.tsx", "./src/components/manifestlabeldialog.tsx", "./src/components/privateroute.tsx", "./src/components/sessionexpirationhandler.tsx", "./src/config/api.config.ts", "./src/contexts/authcontext.tsx", "./src/contexts/toastcontext.tsx", "./src/pages/clientcontainermanagement.tsx", "./src/pages/containerdetail.tsx", "./src/pages/containermanagement.tsx", "./src/pages/dashboard.tsx", "./src/pages/locationzonemanagement.tsx", "./src/pages/login.tsx", "./src/pages/manifestdetail.tsx", "./src/pages/manifestmanagement.tsx", "./src/pages/testapi.tsx", "./src/pages/usermanagement.tsx", "./src/services/auth.service.ts", "./src/services/container.service.ts", "./src/services/location.service.ts", "./src/services/manifest.service.ts", "./src/services/user.service.ts", "./src/types/container.ts", "./src/types/user.ts", "./src/types/api.ts", "./src/types/location.ts", "./src/types/manifest.ts"], "version": "5.7.3"}