import React, { useState, useEffect, useMemo } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Typography,
  Box,
  CircularProgress,
  InputAdornment,
  Alert
} from '@mui/material';
import { LocalShippingOutlined as LocalShippingOutlinedIcon } from '@mui/icons-material';
import { VehicleType } from '../types/Vehicle';
import * as vehicleService from '../services/vehicle.service';

interface QuickDeliveryVehicleDialogProps {
  open: boolean;
  onClose: () => void;
  onSave: (deliveryVehicle: string | null) => Promise<void>;
  trackingNo: string;
  currentDeliveryVehicle: string | null;
  manifestWeight?: number;
  manifestCbm?: number;
}

const QuickDeliveryVehicleDialog: React.FC<QuickDeliveryVehicleDialogProps> = ({
  open,
  onClose,
  onSave,
  trackingNo,
  currentDeliveryVehicle,
  manifestWeight,
  manifestCbm
}) => {
  const [selectedVehicle, setSelectedVehicle] = useState<string | null>(currentDeliveryVehicle);
  const [vehicleTypes, setVehicleTypes] = useState<VehicleType[]>([]);
  const [loading, setLoading] = useState(false);
  const [vehicleTypesLoading, setVehicleTypesLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [suggestedVehicle, setSuggestedVehicle] = useState<VehicleType | null>(null);

  // Load vehicle types when dialog opens
  useEffect(() => {
    if (open) {
      fetchVehicleTypes();
      if (manifestWeight && manifestCbm) {
        fetchSuggestedVehicle();
      }
    }
  }, [open, manifestWeight, manifestCbm]);

  // Update selected vehicle when currentDeliveryVehicle changes
  useEffect(() => {
    setSelectedVehicle(currentDeliveryVehicle);
  }, [currentDeliveryVehicle]);

  // Check if current delivery vehicle exists in vehicle types list
  const isCurrentVehicleValid = useMemo(() => {
    if (!currentDeliveryVehicle || vehicleTypes.length === 0) return true;
    return vehicleTypes.some(vt => vt.name === currentDeliveryVehicle);
  }, [currentDeliveryVehicle, vehicleTypes]);

  // Reset selected vehicle if it's not valid
  useEffect(() => {
    if (!isCurrentVehicleValid && vehicleTypes.length > 0) {
      console.warn(`Current delivery vehicle "${currentDeliveryVehicle}" not found in vehicle types. Resetting to auto-assign.`);
      setSelectedVehicle(null);
    }
  }, [isCurrentVehicleValid, currentDeliveryVehicle, vehicleTypes.length]);

  const fetchVehicleTypes = async () => {
    try {
      setVehicleTypesLoading(true);
      setError(null);
      console.log('Fetching vehicle types...');
      
      // Try to get vehicle types without auth first, fallback to empty array
      try {
        const types = await vehicleService.getVehicleTypes();
        console.log('Vehicle types fetched:', types);
        if (Array.isArray(types) && types.length > 0) {
          setVehicleTypes(types);
        } else {
          // Use fallback vehicle types if none loaded
          console.warn('No vehicle types returned from API, using fallback types');
          setVehicleTypes(getFallbackVehicleTypes());
        }
      } catch (authError) {
        console.warn('Failed to fetch vehicle types, using fallback types:', authError);
        setVehicleTypes(getFallbackVehicleTypes());
      }
    } catch (error) {
      console.error('Error fetching vehicle types:', error);
      setError('Failed to load vehicle types');
      setVehicleTypes(getFallbackVehicleTypes()); // Use fallback even on error
    } finally {
      setVehicleTypesLoading(false);
    }
  };

  // Fallback vehicle types to prevent Select component errors
  const getFallbackVehicleTypes = (): VehicleType[] => {
    return [
      { 
        id: 1, 
        name: 'VAN', 
        description: 'Small delivery van', 
        maxWeight: 1000, 
        maxCbm: 10, 
        minWeight: 0, 
        minCbm: 0,
        minCbmPerPiece: null,
        maxCbmPerPiece: null,
        isActive: true
      },
      { 
        id: 2, 
        name: 'TRUCK', 
        description: 'Medium truck', 
        maxWeight: 5000, 
        maxCbm: 50, 
        minWeight: 0, 
        minCbm: 0,
        minCbmPerPiece: null,
        maxCbmPerPiece: null,
        isActive: true
      },
      { 
        id: 3, 
        name: 'LORRY', 
        description: 'Large lorry', 
        maxWeight: 10000, 
        maxCbm: 100, 
        minWeight: 0, 
        minCbm: 0,
        minCbmPerPiece: null,
        maxCbmPerPiece: null,
        isActive: true
      },
      { 
        id: 4, 
        name: 'MOTORCYCLE', 
        description: 'Motorcycle for small packages', 
        maxWeight: 50, 
        maxCbm: 2, 
        minWeight: 0, 
        minCbm: 0,
        minCbmPerPiece: null,
        maxCbmPerPiece: null,
        isActive: true
      }
    ];
  };

  const fetchSuggestedVehicle = async () => {
    if (!manifestWeight || !manifestCbm) return;
    
    try {
      const suggested = await vehicleService.suggestVehicleType(manifestWeight, manifestCbm);
      setSuggestedVehicle(suggested);
    } catch (error) {
      console.error('Error fetching suggested vehicle:', error);
    }
  };

  const handleSave = async () => {
    try {
      setLoading(true);
      setError(null);
      await onSave(selectedVehicle);
      onClose();
    } catch (error: any) {
      setError(error.message || 'Failed to update delivery vehicle');
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setSelectedVehicle(currentDeliveryVehicle);
    setError(null);
    setSuggestedVehicle(null);
    onClose();
  };

  const useSuggestedVehicle = () => {
    if (suggestedVehicle) {
      setSelectedVehicle(suggestedVehicle.name);
    }
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          minHeight: '300px'
        }
      }}
    >
      <DialogTitle sx={{ pb: 1 }}>
        <Box display="flex" alignItems="center" gap={1}>
          <LocalShippingOutlinedIcon color="primary" />
          <Typography variant="h6">
            Update Delivery Vehicle
          </Typography>
        </Box>
        <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
          Manifest: {trackingNo}
        </Typography>
      </DialogTitle>

      <DialogContent>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {!isCurrentVehicleValid && vehicleTypes.length > 0 && currentDeliveryVehicle && (
          <Alert severity="warning" sx={{ mb: 2 }}>
            <Typography variant="body2">
              <strong>Notice:</strong> The current delivery vehicle "{currentDeliveryVehicle}" is no longer available in the system. 
              Please select a new vehicle or choose auto-assign.
            </Typography>
          </Alert>
        )}

        {suggestedVehicle && (
          <Alert 
            severity="info" 
            sx={{ mb: 2 }}
            action={
              <Button 
                color="inherit" 
                size="small" 
                onClick={useSuggestedVehicle}
                disabled={selectedVehicle === suggestedVehicle.name}
              >
                Use Suggested
              </Button>
            }
          >
            <Typography variant="body2">
              <strong>Suggested:</strong> {suggestedVehicle.name} (based on weight: {manifestWeight}kg, CBM: {manifestCbm})
            </Typography>
          </Alert>
        )}

        <FormControl fullWidth sx={{ mt: 1 }}>
          <InputLabel id="delivery-vehicle-label">Delivery Vehicle</InputLabel>
          <Select
            labelId="delivery-vehicle-label"
            value={
              // Only use selectedVehicle if it exists in vehicleTypes or is empty/null
              selectedVehicle && vehicleTypes.some(vt => vt.name === selectedVehicle) 
                ? selectedVehicle 
                : ''
            }
            onChange={(e) => setSelectedVehicle(e.target.value || null)}
            label="Delivery Vehicle"
            disabled={vehicleTypesLoading}
          >
            <MenuItem value="">
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <LocalShippingOutlinedIcon sx={{ mr: 1, color: 'action.active' }} fontSize="small" />
                <em>Auto-assign (System will choose based on weight/CBM)</em>
              </Box>
            </MenuItem>
            {vehicleTypes.map((vehicleType) => (
              <MenuItem key={vehicleType.id} value={vehicleType.name}>
                <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                  <LocalShippingOutlinedIcon sx={{ mr: 1, color: 'action.active' }} fontSize="small" />
                  <Box sx={{ flex: 1 }}>
                    <Typography variant="body2" fontWeight="medium">
                      {vehicleType.name}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Max: {vehicleType.maxWeight}kg, {vehicleType.maxCbm} CBM
                    </Typography>
                  </Box>
                </Box>
              </MenuItem>
            ))}
          </Select>
          {vehicleTypesLoading && (
            <Box display="flex" justifyContent="center" mt={1}>
              <CircularProgress size={20} />
            </Box>
          )}
        </FormControl>

        <Box mt={2}>
          <Typography variant="body2" color="text.secondary">
            Current: {currentDeliveryVehicle || 'Auto-assigned'}
          </Typography>
          {manifestWeight && manifestCbm && (
            <Typography variant="body2" color="text.secondary">
              Manifest: {manifestWeight}kg, {manifestCbm} CBM
            </Typography>
          )}
        </Box>
      </DialogContent>

      <DialogActions sx={{ px: 3, py: 2, gap: 1 }}>
        <Button onClick={handleClose} variant="outlined">
          Cancel
        </Button>
        <Button 
          onClick={handleSave} 
          variant="contained" 
          disabled={loading || vehicleTypesLoading}
          startIcon={loading ? <CircularProgress size={16} /> : undefined}
        >
          {loading ? 'Updating...' : 'Update Vehicle'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default QuickDeliveryVehicleDialog; 