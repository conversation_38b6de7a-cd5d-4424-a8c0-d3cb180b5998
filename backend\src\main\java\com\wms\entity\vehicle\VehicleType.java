package com.wms.entity.vehicle;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
@Entity
@Table(name = "vehicle_types")
public class VehicleType {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotBlank(message = "Type name is required")
    @Size(max = 50)
    @Column(name = "name", unique = true)
    private String name;

    @Size(max = 10, message = "Abbreviation must not exceed 10 characters")
    @Column(name = "abbreviation")
    private String abbreviation;

    @Size(max = 255)
    @Column(name = "description")
    private String description;

    @Column(name = "min_weight")
    private Double minWeight;

    @Column(name = "max_weight")
    private Double maxWeight;

    @Column(name = "min_cbm")
    private Double minCbm;

    @Column(name = "max_cbm")
    private Double maxCbm;

    @Column(name = "min_cbm_per_piece")
    private Double minCbmPerPiece;

    @Column(name = "max_cbm_per_piece")
    private Double maxCbmPerPiece;

    @Column(name = "is_active", nullable = false)
    private Boolean isActive = true;

    @OneToMany(mappedBy = "vehicleType", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<Vehicle> vehicles = new ArrayList<>();
} 