package com.wms.controller;

import com.wms.entity.Manifest;
import com.wms.service.ManifestService;
import com.wms.dto.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping("/api/manifests")
@CrossOrigin(origins = "*")
public class ManifestRemarksController {

    @Autowired
    private ManifestService manifestService;

    /**
     * Update only remarks fields for a manifest
     * This endpoint is optimized for quick edits and only updates the specific remarks field
     */
    @PutMapping("/{trackingNo}/remarks")
    public ResponseEntity<ApiResponse<Manifest>> updateManifestRemarks(
            @PathVariable String trackingNo,
            @RequestBody Map<String, String> requestBody) {
        
        try {
            String field = requestBody.get("field");
            String value = requestBody.get("value");
            
            if (field == null || (!field.equals("remarks") && !field.equals("driverRemarks"))) {
                return ResponseEntity.badRequest()
                    .body(new ApiResponse<>(false, "Invalid field. Must be 'remarks' or 'driverRemarks'", null));
            }
            
            if (value == null) {
                value = ""; // Allow empty string
            }
            
            // Validate value length (VARCHAR(255) constraint)
            if (value.length() > 255) {
                return ResponseEntity.badRequest()
                    .body(new ApiResponse<>(false, "Value too long. Maximum 255 characters allowed.", null));
            }
            
            Manifest updatedManifest = manifestService.updateRemarksField(trackingNo, field, value);
            
            if (updatedManifest != null) {
                return ResponseEntity.ok(new ApiResponse<>(true, "Remarks updated successfully", updatedManifest));
            } else {
                return ResponseEntity.status(404)
                    .body(new ApiResponse<>(false, "Manifest not found", null));
            }
            
        } catch (Exception e) {
            return ResponseEntity.internalServerError()
                .body(new ApiResponse<>(false, "Failed to update remarks: " + e.getMessage(), null));
        }
    }

    /**
     * Update a specific field using PATCH with direct SQL
     * This is a fallback endpoint for targeted field updates
     */
    @PatchMapping("/{trackingNo}/update-field")
    public ResponseEntity<ApiResponse<Manifest>> updateManifestField(
            @PathVariable String trackingNo,
            @RequestBody Map<String, String> requestBody) {
        
        try {
            String fieldName = requestBody.get("fieldName");
            String fieldValue = requestBody.get("fieldValue");
            
            if (fieldName == null || fieldValue == null) {
                return ResponseEntity.badRequest()
                    .body(new ApiResponse<>(false, "fieldName and fieldValue are required", null));
            }
            
            // Only allow specific fields for security
            if (!fieldName.equals("remarks") && !fieldName.equals("driver_remarks")) {
                return ResponseEntity.badRequest()
                    .body(new ApiResponse<>(false, "Field not allowed for update: " + fieldName, null));
            }
            
            // Validate value length
            if (fieldValue.length() > 255) {
                return ResponseEntity.badRequest()
                    .body(new ApiResponse<>(false, "Value too long. Maximum 255 characters allowed.", null));
            }
            
            Manifest updatedManifest = manifestService.updateSpecificField(trackingNo, fieldName, fieldValue);
            
            if (updatedManifest != null) {
                return ResponseEntity.ok(new ApiResponse<>(true, "Field updated successfully", updatedManifest));
            } else {
                return ResponseEntity.status(404)
                    .body(new ApiResponse<>(false, "Manifest not found", null));
            }
            
        } catch (Exception e) {
            return ResponseEntity.internalServerError()
                .body(new ApiResponse<>(false, "Failed to update field: " + e.getMessage(), null));
        }
    }

    /**
     * Update remarks using query parameters (alternative approach)
     * Using a different path to avoid conflicts with existing ManifestController
     */
    @PutMapping("/{trackingNo}/quick-update")
    public ResponseEntity<ApiResponse<Manifest>> updateManifestWithQueryParams(
            @PathVariable String trackingNo,
            @RequestParam(required = false) String updateField,
            @RequestParam(required = false) String value) {

        // Only handle this if it's a remarks update via query params
        if (updateField != null && value != null) {
            try {
                if (!updateField.equals("remarks") && !updateField.equals("driverRemarks")) {
                    return ResponseEntity.badRequest()
                        .body(new ApiResponse<>(false, "Invalid updateField. Must be 'remarks' or 'driverRemarks'", null));
                }

                // Validate value length
                if (value.length() > 255) {
                    return ResponseEntity.badRequest()
                        .body(new ApiResponse<>(false, "Value too long. Maximum 255 characters allowed.", null));
                }

                Manifest updatedManifest = manifestService.updateRemarksField(trackingNo, updateField, value);

                if (updatedManifest != null) {
                    return ResponseEntity.ok(new ApiResponse<>(true, "Remarks updated successfully", updatedManifest));
                } else {
                    return ResponseEntity.status(404)
                        .body(new ApiResponse<>(false, "Manifest not found", null));
                }

            } catch (Exception e) {
                return ResponseEntity.internalServerError()
                    .body(new ApiResponse<>(false, "Failed to update remarks: " + e.getMessage(), null));
            }
        }

        // If not a remarks update, return method not allowed
        return ResponseEntity.status(405)
            .body(new ApiResponse<>(false, "Method not supported for this endpoint", null));
    }
}
