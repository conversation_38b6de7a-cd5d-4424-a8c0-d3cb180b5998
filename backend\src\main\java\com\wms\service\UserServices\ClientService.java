package com.wms.service.UserServices;

import com.wms.entity.user.Client;
import java.util.List;
import java.util.Optional;

public interface ClientService {
    List<Client> getAllClients();
    Optional<Client> getClientByUsername(String username);
    Client createClient(Client client);
    Client updateClient(String username, Client client);
    Client deactivateClient(String username);
    boolean existsByEmail(String email);
    boolean existsByUsername(String username);
} 