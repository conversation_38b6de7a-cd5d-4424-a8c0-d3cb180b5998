package com.wms.controller.VehicleControllers;

import com.wms.dto.ApiResponse;
import com.wms.dto.VehicleTypeDTO;
import com.wms.entity.vehicle.VehicleType;
import com.wms.service.VehicleServices.VehicleTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import jakarta.validation.Valid;
import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/api/vehicle-types")
@CrossOrigin(origins = "*", maxAge = 3600)
public class VehicleTypeController {

    @Autowired
    private VehicleTypeService vehicleTypeService;

    @PostMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<VehicleTypeDTO> createVehicleType(@Valid @RequestBody VehicleTypeDTO vehicleTypeDTO) {
        VehicleTypeDTO createdVehicleType = vehicleTypeService.createVehicleType(vehicleTypeDTO);
        return ResponseEntity.status(HttpStatus.CREATED).body(createdVehicleType);
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<VehicleTypeDTO> updateVehicleType(@PathVariable Long id, 
                                                            @Valid @RequestBody VehicleTypeDTO vehicleTypeDTO) {
        VehicleTypeDTO updatedVehicleType = vehicleTypeService.updateVehicleType(id, vehicleTypeDTO);
        return ResponseEntity.ok(updatedVehicleType);
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Void> deleteVehicleType(@PathVariable Long id) {
        vehicleTypeService.deleteVehicleType(id);
        return ResponseEntity.noContent().build();
    }

    @GetMapping("/{id}")
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER')")
    public ResponseEntity<VehicleTypeDTO> getVehicleType(@PathVariable Long id) {
        VehicleTypeDTO vehicleType = vehicleTypeService.getVehicleTypeById(id);
        return ResponseEntity.ok(vehicleType);
    }

    @GetMapping
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER')")
    public ResponseEntity<List<VehicleTypeDTO>> getAllVehicleTypes() {
        List<VehicleTypeDTO> vehicleTypes = vehicleTypeService.getAllVehicleTypes();
        return ResponseEntity.ok(vehicleTypes);
    }

    @GetMapping("/suggest")
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER', 'CLIENT')")
    public ResponseEntity<ApiResponse<VehicleTypeDTO>> suggestVehicleType(
            @RequestParam Double weight, 
            @RequestParam Double cbm) {
        
        try {
            Optional<VehicleType> suggestedVehicleType = vehicleTypeService.determineVehicleTypeByWeightAndCbm(weight, cbm);
            
            if (suggestedVehicleType.isPresent()) {
                VehicleTypeDTO vehicleTypeDTO = vehicleTypeService.getVehicleTypeById(suggestedVehicleType.get().getId());
                return ResponseEntity.ok(ApiResponse.success("Vehicle type suggestion found", vehicleTypeDTO));
            } else {
                return ResponseEntity.ok(ApiResponse.success("No suitable vehicle type found for the given parameters", null));
            }
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Error suggesting vehicle type: " + e.getMessage()));
        }
    }

    @GetMapping("/abbreviation/{vehicleTypeName}")
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER', 'CLIENT')")
    public ResponseEntity<ApiResponse<String>> getVehicleTypeAbbreviation(@PathVariable String vehicleTypeName) {
        try {
            String abbreviation = vehicleTypeService.getVehicleTypeAbbreviation(vehicleTypeName);
            return ResponseEntity.ok(ApiResponse.success(abbreviation));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Error getting vehicle type abbreviation: " + e.getMessage()));
        }
    }
} 