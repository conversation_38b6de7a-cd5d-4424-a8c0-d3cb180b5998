import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Typography,
  Alert,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  Paper,
  CircularProgress,
  Divider,
  Tabs,
  Tab,
  Snackbar,
  Stack,
  Chip,
  IconButton,
  Tooltip,
  useTheme,
  DialogContentText,
  SelectChangeEvent,
  DialogProps,
  InputAdornment,
  Toolbar,
} from '@mui/material';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { DataGrid, useGridApiRef } from '@mui/x-data-grid';
import type { GridColDef, GridRenderCellParams } from '@mui/x-data-grid';
import AddIcon from '@mui/icons-material/Add';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import LocalShippingIcon from '@mui/icons-material/LocalShipping';
import TimelineIcon from '@mui/icons-material/Timeline';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import DirectionsBoatIcon from '@mui/icons-material/DirectionsBoat';
import ConstructionIcon from '@mui/icons-material/Construction';
import WarehouseIcon from '@mui/icons-material/Warehouse';
import CloseIcon from '@mui/icons-material/Close';
import ClearIcon from '@mui/icons-material/Clear';
import ReportProblemIcon from '@mui/icons-material/ReportProblem';
// import ViewColumnIcon from '@mui/icons-material/ViewColumn';
import { useAuth } from '../contexts/AuthContext';
import usePageTitle from '../hooks/usePageTitle';
import containerService from '../services/container.service';
import userService from '../services/user.service';
import { Container, ContainerStatus } from '../types/Container';
import { Client } from '../types/User';
import { format } from 'date-fns';
import { formatInTimeZone } from 'date-fns-tz';
import { css } from '@emotion/react';
import { Global } from '@emotion/react';
import SearchIcon from '@mui/icons-material/Search';
import PersonIcon from '@mui/icons-material/Person';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import EventAvailableIcon from '@mui/icons-material/EventAvailable';
import LocalShippingOutlinedIcon from '@mui/icons-material/LocalShippingOutlined';
import DirectionsBoatOutlinedIcon from '@mui/icons-material/DirectionsBoatOutlined';
import { alpha } from '@mui/material/styles';
import UpdateIcon from '@mui/icons-material/Update';
import { formatDateString, formatDate } from '../utils/dateUtils';
import CustomDatePickerWithCounts from '../components/CustomDatePickerWithCounts';

// Add a global style to ensure popups appear in front
const globalStyles = css`
  .MuiPickersPopper-root, 
  .MuiDialog-root,
  .MuiPopover-root,
  .MuiModal-root {
    z-index: 9999 !important;
  }
`;

// Add a custom Dialog component
const StaticPositionDialog = (props: DialogProps) => {
  return (
    <Dialog
      {...props}
      sx={{
        position: 'static',
        '& .MuiDialog-container': {
          position: 'static'
        },
        ...props.sx
      }}
    />
  );
};

// Add the ETA rejection dialog component
interface ETARejectionDialogProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (reason: string, suggestedEtaRequestedDate: string) => void;
  containerNo: string;
  initialEtaRequestedDate?: string;
  containers: Container[]; // Add containers prop
}

const ETARejectionDialog: React.FC<ETARejectionDialogProps> = ({ 
  open, 
  onClose, 
  onSubmit, 
  containerNo, 
  initialEtaRequestedDate,
  containers // Add containers prop here
}) => {
  const [reason, setReason] = useState('');
  const [suggestedEtaRequestedDate, setSuggestedEtaRequestedDate] = useState<Date | null>(() => {
    try {
      return initialEtaRequestedDate ? new Date(initialEtaRequestedDate) : new Date();
    } catch (e) {
      console.error('Invalid initial ETA requested date provided:', initialEtaRequestedDate);
      return new Date();
    }
  });
  const [error, setError] = useState('');
  const theme = useTheme();

  const handleSubmit = () => {
    if (!reason.trim()) {
      setError('Please provide a reason for rejection');
      return;
    }
    
    if (!suggestedEtaRequestedDate) {
      setError('Please provide a suggested ETA requested date');
      return;
    }
    
    onSubmit(reason, formatDate(suggestedEtaRequestedDate));
    setReason('');
    setSuggestedEtaRequestedDate(new Date());
    setError('');
  };

  useEffect(() => {
    if (open) {
      try {
        setSuggestedEtaRequestedDate(initialEtaRequestedDate ? new Date(initialEtaRequestedDate) : new Date());
      } catch (e) {
        console.error('Invalid initial ETA requested date on open:', initialEtaRequestedDate);
        setSuggestedEtaRequestedDate(new Date());
      }
    }
  }, [open, initialEtaRequestedDate]);

  return (
    <Dialog 
      open={open} 
      onClose={onClose}
      fullWidth
      maxWidth="sm"
      slotProps={{
        paper: {
          sx: {
            borderRadius: 2,
            boxShadow: 24,
          }
        }
      }}
    >
      <DialogTitle sx={{ 
        bgcolor: theme.palette.error.main, 
        color: 'white',
        px: 3,
        py: 2
      }}>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography variant="h6">
            Reject ETA Request for Container {containerNo}
          </Typography>
          <IconButton onClick={onClose} size="small" sx={{ color: 'white' }}>
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>
      <DialogContent sx={{ px: 3, py: 3 }}>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}
        
        <Typography variant="body1" sx={{ mb: 2 }}>
          Please provide a reason for rejection and suggest an alternative ETA date:
        </Typography>
        
        <TextField
          label="Rejection Reason"
          value={reason}
          onChange={(e) => setReason(e.target.value)}
          fullWidth
          required
          multiline
          rows={3}
          margin="normal"
          sx={{
            '& .MuiOutlinedInput-root': {
              borderRadius: 1,
            },
          }}
        />
        
        <Box position="relative" zIndex={1300} width="100%">
          <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 'bold' }}>
            Suggested Alternative ETA Requested Date
          </Typography>
          
          <CustomDatePickerWithCounts
            label="Suggested ETA Requested Date"
            value={suggestedEtaRequestedDate}
            onChange={(date) => setSuggestedEtaRequestedDate(date)}
            containers={containers}
            format="dd MMM yyyy HH:mm"
            ampm={false}
            slotProps={{
              textField: {
                fullWidth: true,
                required: true,
                margin: "normal"
              },
              popper: {
                sx: {
                  zIndex: 9999
                }
              },
            }}
          />
        </Box>
      </DialogContent>
      <DialogActions sx={{ px: 3, py: 2, bgcolor: '#f9fafc' }}>
        <Button 
          onClick={onClose} 
          color="inherit"
          sx={{ borderRadius: 2 }}
        >
          Cancel
        </Button>
        <Button
          onClick={handleSubmit}
          variant="contained"
          color="error"
          sx={{ borderRadius: 2 }}
        >
          Reject & Suggest New ETA Requested Date
        </Button>
      </DialogActions>
    </Dialog>
  );
};

// Define a type for the column visibility state
type ColumnVisibility = {
  containerNo: boolean;
  client: boolean;
  status: boolean;
  etaRequestedDate: boolean;
  truckNo: boolean;
  vesselVoyageNo: boolean;
  manifestQuantity: boolean;
  portnetEta: boolean;
  etaAllocated: boolean;
  arrivalDate: boolean;
  loadingBay: boolean;
  unstuffDate: boolean;
  unstuffCompletedDate: boolean;
  pullOutDate: boolean;
  unstuffTeam: boolean;
  remark: boolean;
  createdDate: boolean;
  actions: boolean;
};

// Add after the imports but before component definition
// Utility function to preserve the exact local date and time
const preserveLocalDateTime = (date: Date): string => {
  // Create a date string that preserves the exact local date and time
  // This is important for midnight values (00:00) to avoid timezone conversions
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  
  // Use ISO format without the Z suffix to preserve local time
  return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}.000`;
};

// Create a utility function to parse our custom date format
const parseLocalDateTime = (dateStr: string): Date | null => {
  if (!dateStr) return null;
  
  try {
    // Parse the date parts directly
    const [datePart, timePart] = dateStr.split('T');
    if (!datePart || !timePart) {
      return new Date(dateStr);
    }
    
    const [year, month, day] = datePart.split('-').map(Number);
    const [time] = timePart.split('.');
    const [hours, minutes, seconds] = time.split(':').map(Number);
    
    // Create a date using local components
    const date = new Date();
    date.setFullYear(year);
    date.setMonth(month - 1); // Month is 0-indexed in JS
    date.setDate(day);
    date.setHours(hours);
    date.setMinutes(minutes);
    date.setSeconds(seconds || 0);
    date.setMilliseconds(0);
    
    return date;
  } catch (e) {
    console.error('Error parsing date:', e, dateStr);
    return null;
  }
};

const ContainerManagement: React.FC = () => {
  const { currentUser, hasRole } = useAuth();
  const navigate = useNavigate(); // Add navigate hook
  const [containers, setContainers] = useState<Container[]>([]);
  const [loading, setLoading] = useState(true);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedContainer, setSelectedContainer] = useState<Container | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [filterState, setFilterState] = useState({
    tab: 0,
    search: '',
  });
  const theme = useTheme();
  const [clients, setClients] = useState<Client[]>([]);
  // Remove column menu anchor state since we're using the inline UI now
  const [visibleColumns, setVisibleColumns] = useState<ColumnVisibility>({
    containerNo: true,
    client: true,
    status: true,
    etaRequestedDate: true,
    truckNo: true,
    vesselVoyageNo: true,
    manifestQuantity: true,
    portnetEta: true,
    etaAllocated: true,
    arrivalDate: true,
    loadingBay: false,
    unstuffDate: false,
    unstuffCompletedDate: false,
    pullOutDate: false,
    unstuffTeam: false,
    remark: false,
    createdDate: false,
    actions: true, // Always visible
  });

  // Default column visibility to use when resetting
  const defaultColumnVisibility: ColumnVisibility = {
    containerNo: true,
    client: true,
    status: true,
    etaRequestedDate: true,
    truckNo: true,
    vesselVoyageNo: true,
    manifestQuantity: true,
    portnetEta: true,
    etaAllocated: true,
    arrivalDate: true,
    loadingBay: false,
    unstuffDate: false,
    unstuffCompletedDate: false,
    pullOutDate: false,
    unstuffTeam: false,
    remark: false,
    createdDate: false,
    actions: true, // Always visible
  };

  const [formData, setFormData] = useState({
    containerNo: '',
    client: { username: '' },
    truckNo: '',
    vesselVoyageNo: '',
    etaRequestedDate: new Date().toISOString(),
    manifestQuantity: 0,
    status: ContainerStatus.CREATED,
    portnetEta: '',
    etaAllocated: '',
    arrivalDate: '',
    loadingBay: '',
    unstuffDate: '',
    unstuffCompletedDate: '',
    pullOutDate: '',
    unstuffTeam: '',
    remark: '',
  });

  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [confirmAction, setConfirmAction] = useState<() => void>(() => {});
  const [confirmTitle, setConfirmTitle] = useState('');
  const [confirmMessage, setConfirmMessage] = useState('');

  // Add new states for ETA rejection
  const [etaRejectionDialogOpen, setEtaRejectionDialogOpen] = useState(false);
  const [selectedContainerForRejection, setSelectedContainerForRejection] = useState<{ containerNo: string; initialEtaRequestedDate?: string } | null>(null);
  
  // Add state for status update dialog
  const [statusDialogOpen, setStatusDialogOpen] = useState(false);
  const [containerToUpdateStatus, setContainerToUpdateStatus] = useState<Container | null>(null);
  const [newStatus, setNewStatus] = useState<ContainerStatus | null>(null);
  
  // Helper function to determine if user can update status
  const canUpdateStatus = () => {
    return hasRole('ROLE_ADMIN') || hasRole('ROLE_MANAGER');
  };
  
  // Add function to handle ETA rejection
  const handleEtaRejection = (containerNo: string) => {
    const container = containers.find(c => c.containerNo === containerNo);
    if (container) {
      setSelectedContainerForRejection({ 
        containerNo: container.containerNo, 
        initialEtaRequestedDate: container.etaRequestedDate 
      });
    } else {
      // Handle case where container might not be found (shouldn't happen ideally)
      setSelectedContainerForRejection({ containerNo }); 
    }
    setEtaRejectionDialogOpen(true);
  };
  
  const handleEtaRejectionSubmit = async (rejectionReason: string, suggestedEtaRequestedDate: string) => {
    if (!currentUser?.token) {
      setError('Authentication required');
      return;
    }
    
    try {
      // Container number is now accessed via selectedContainerForRejection.containerNo
      const containerToUpdate = containers.find(c => c.containerNo === selectedContainerForRejection?.containerNo);
      
      if (!containerToUpdate || !selectedContainerForRejection) {
        setError('Container not found or selection error');
        return;
      }
      
      // Format the rejection reason to include the suggested date in a consistent format
      const formattedReason = `${rejectionReason}\n\nSuggested date: ${suggestedEtaRequestedDate}`;
      
      // Create updated container with rejection info and new suggested date
      // Ensure we use the date format without Z suffix
      const updatedContainer: Container = {
        ...containerToUpdate,
        etaRejected: true,
        etaRejectionReason: formattedReason,
        // Use suggestedEtaRequestedDate as is since it's already a string
        etaRequestedDate: suggestedEtaRequestedDate,
        // Keep the status as CREATED
        status: ContainerStatus.CREATED
      };
      
      // Update container in backend
      await containerService.updateContainer(
        selectedContainerForRejection.containerNo,
        updatedContainer,
        currentUser.token
      );
      
      setSuccessMessage('ETA rejected and new suggested ETA Requested Date submitted. Waiting for client confirmation.');
      fetchContainers();
      setEtaRejectionDialogOpen(false);
    } catch (err: any) {
      if (err.response) {
        setError(`Failed: ${err.response.data?.message || err.message}`);
      } else {
        setError('Failed: Unknown error');
      }
      console.error('Error rejecting ETA:', err);
    }
  };
  
  // Update the handleUpdateStatus function with more logging
  const handleUpdateStatus = async (containerNo: string, status: ContainerStatus) => {
    if (!currentUser?.token) {
      setError('Authentication required');
      return;
    }
    
    // Only allow admin and manager to update status
    if (!canUpdateStatus()) {
      setError('Only administrators and managers can update container status');
      return;
    }
    
    console.log(`Processing status update for container ${containerNo}: -> ${status}`);
    
    try {
      // Get the container to update
      const containerToUpdate = containers.find(c => c.containerNo === containerNo);
      
      if (!containerToUpdate) {
        setError('Container not found');
        return;
      }
      
      console.log(`Current container status: ${containerToUpdate.status}, requested: ${status}`);
      
      // Show confirmation dialog
      showConfirmDialog(
        'Update Container Status',
        `Are you sure you want to update the container status to ${status}? This will also update the status of all manifests associated with this container.`,
        async () => {
          try {
            // Update container status
            const response = await containerService.updateContainerStatus(containerNo, status, currentUser.token);
            
            if (response.success) {
              setSuccessMessage(`Container status updated to ${status}. Associated manifests have been updated accordingly.`);
              
              // Refresh containers list
              await fetchContainers();
              
              // Explicitly refresh the container's detailed data if we're updating from a details page
              if (window.location.pathname.includes(`/containers/${containerNo}`)) {
                console.log('Refreshing container detail data after status update');
                // Force page refresh to ensure manifest data is updated
                window.location.reload();
              }
            } else {
              setError(response.message || 'Failed to update container status');
            }
          } catch (err: any) {
            console.error('Error updating container status:', err);
            setError(err.response?.data?.message || 'Failed to update container status');
          }
        }
      );
    } catch (err: any) {
      console.error('Error in handleUpdateStatus:', err);
      setError(err.response?.data?.message || 'Failed to update container status');
    }
  };

  useEffect(() => {
    fetchContainers();
    fetchClients();

    // Add custom CSS for DataGrid header styling and z-index fixes
    const style = document.createElement('style');
    style.innerHTML = `
      .super-app-theme--header {
        background-color: ${theme.palette.primary.main};
        font-weight: bold;
      }
      
      .MuiDataGrid-columnHeaders .MuiDataGrid-columnHeader:focus,
      .MuiDataGrid-columnHeaders .MuiDataGrid-columnHeader:focus-within {
        outline: none !important;
      }
      
      .no-cell-focus-outline *:focus,
      .no-cell-focus-outline *:focus-visible,
      .no-cell-focus-outline *:focus-within,
      .no-cell-focus-outline .MuiDataGrid-root *,
      .no-cell-focus-outline .MuiDataGrid-cell,
      .no-cell-focus-outline .MuiDataGrid-row {
        outline: none !important;
        box-shadow: none !important;
      }
      
      .MuiPickersPopper-root, 
      .MuiDialog-root,
      .MuiPopover-root,
      .MuiModal-root,
      .MuiPopover-paper,
      .MuiDialog-paper,
      .MuiPickersLayout-root {
        z-index: 9999 !important;
      }
      .MuiBackdrop-root {
        z-index: 9998 !important;
      }
    `;
    document.head.appendChild(style);
    
    return () => {
      document.head.removeChild(style);
    };
  }, [theme.palette.primary.main]);

  // Debug function to check the container service implementation
  useEffect(() => {
    if (currentUser?.token) {
      // Add a debug log to check the container service
      console.log('Container service methods:', {
        updateContainer: containerService.updateContainer,
        updateContainerStatus: containerService.updateContainerStatus
      });
    }
  }, [currentUser?.token]);

  useEffect(() => {
    if (containers.length > 0) {
      console.log('Containers loaded:', containers.length);
      
      // Log the data for the first few containers to examine their fields
      const sampleSize = Math.min(3, containers.length);
      console.log(`Detailed data for ${sampleSize} sample containers:`);
      
      for (let i = 0; i < sampleSize; i++) {
        const container = containers[i];
        console.log(`Container ${i+1}:`, {
          containerNo: container.containerNo,
          client: container.client,
          createdDate: container.createdDate,
          etaRequestedDate: container.etaRequestedDate,
          status: container.status,
        });
        
        // Check for missing or invalid data
        if (!container.createdDate) {
          console.warn(`Container ${container.containerNo} is missing createdDate`);
        }
        
        if (!container.etaRequestedDate) {
          console.warn(`Container ${container.containerNo} is missing etaRequestedDate`);
        }
      }
    }
  }, [containers]);

  const fetchContainers = async () => {
    if (!currentUser?.token) {
      setError('Authentication required');
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      const response = await containerService.getAllContainers(currentUser.token);
      
      // Debug output to check the raw response data
      console.log('Raw container data:', response);
      
      // Check if any container has client or createdDate issues
      if (response.data && response.data.length > 0) {
        const issueCounts = {
          missingClient: 0,
          missingCreatedDate: 0,
          missingETARequestedDate: 0
        };
        
        response.data.forEach(container => {
          if (!container.client) issueCounts.missingClient++;
          if (!container.createdDate) issueCounts.missingCreatedDate++;
          if (!container.etaRequestedDate) issueCounts.missingETARequestedDate++;
        });
        
        if (issueCounts.missingClient > 0 || issueCounts.missingCreatedDate > 0 || issueCounts.missingETARequestedDate > 0) {
          console.warn('Container data issues found:', issueCounts);
        }
      }
      
      setContainers(response.data);
    } catch (err: any) {
      if (err.response) {
        setError(`Failed to fetch containers: ${err.response.data?.message || err.message}`);
      } else {
        setError('Failed to fetch containers: Unknown error');
      }
      console.error('Error fetching containers:', err);
    } finally {
      setLoading(false);
    }
  };

  const fetchClients = async () => {
    if (!currentUser?.token) {
      setError('Authentication required');
      return;
    }

    try {
      const response = await userService.getAllClients(currentUser.token);
      if (response.data.success) {
        setClients(response.data.data);
      } else {
        console.error('Failed to fetch clients:', response.data.message);
        // Set empty array to prevent UI issues
        setClients([]);
      }
    } catch (err: any) {
      console.error('Error fetching clients:', err);
      // Set empty array to prevent UI issues
      setClients([]);
      
      // Don't surface these errors to the user UI since they may
      // just be permission-related based on role
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  const handleNumberChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: parseInt(value) || 0,
    });
  };

  const handleClientChange = (e: SelectChangeEvent) => {
    const clientUsername = e.target.value;
    const selectedClient = clients.find(client => client.username === clientUsername) || { username: clientUsername };
    
    setFormData({
      ...formData,
      client: selectedClient,
    });
  };

  // Update handleDateChange function
  const handleDateChange = (name: string, value: Date | null) => {
    if (value) {
      // Format the date using our utility function
      const formattedDate = formatDate(value);
      setFormData({ ...formData, [name]: formattedDate });
    } else {
      setFormData({ ...formData, [name]: '' });
    }
  };

  const handleStatusChange = (e: SelectChangeEvent) => {
    setFormData({
      ...formData,
      status: e.target.value as ContainerStatus,
    });
  };

  const handleOpenDialog = async (_?: React.MouseEvent, container?: Container) => {
    if (container) {
      setSelectedContainer(container);
      setFormData({
        containerNo: container.containerNo,
        client: container.client,
        truckNo: container.truckNo,
        vesselVoyageNo: container.vesselVoyageNo,
        etaRequestedDate: container.etaRequestedDate,
        manifestQuantity: container.manifestQuantity,
        status: container.status,
        portnetEta: container.portnetEta || '',
        etaAllocated: container.etaAllocated || '',
        arrivalDate: container.arrivalDate || '',
        loadingBay: container.loadingBay || '',
        unstuffDate: container.unstuffDate || '',
        unstuffCompletedDate: container.unstuffCompletedDate || '',
        pullOutDate: container.pullOutDate || '',
        unstuffTeam: container.unstuffTeam || '',
        remark: container.remark || '',
      });
    } else {
      setSelectedContainer(null);
      // When creating a new container, use preserveLocalDateTime to create the ISO string without Z suffix
      const now = new Date();
      setFormData({
        containerNo: '',
        client: { username: '' },
        truckNo: '',
        vesselVoyageNo: '',
        etaRequestedDate: preserveLocalDateTime(now),
        manifestQuantity: 0,
        status: ContainerStatus.CREATED,
        portnetEta: '',
        etaAllocated: '',
        arrivalDate: '',
        loadingBay: '',
        unstuffDate: '',
        unstuffCompletedDate: '',
        pullOutDate: '',
        unstuffTeam: '',
        remark: '',
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setError(null);
  };

  const handleSubmit = async () => {
    if (!currentUser?.token) {
      setError('Authentication required');
      return;
    }

    try {
      // Create a proper container object with required client fields
      const containerData: Container = {
        ...formData,
        // No longer manually setting createdDate, will be handled by backend
        status: selectedContainer ? formData.status : ContainerStatus.CREATED,
        // Always send only the username for the client field, but cast as Client to satisfy TS
        client: { username: formData.client.username } as Client
      };

      if (selectedContainer) {
        // Update existing container
        await containerService.updateContainer(
          selectedContainer.containerNo,
          containerData,
          currentUser.token
        );
        setSuccessMessage('Container updated successfully');
      } else {
        // Create new container
        await containerService.createContainer(containerData, currentUser.token);
        setSuccessMessage('Container created successfully');
      }
      handleCloseDialog();
      fetchContainers();
    } catch (err: any) {
      if (err.response) {
        setError(`Failed: ${err.response.data?.message || err.message}`);
      } else {
        setError('Failed: Unknown error');
      }
      console.error('Error:', err);
    }
  };

  const handleSuccessClose = () => {
    setSuccessMessage(null);
  };

  const showConfirmDialog = (title: string, message: string, action: () => void) => {
    setConfirmTitle(title);
    setConfirmMessage(message);
    setConfirmAction(() => action);
    setConfirmDialogOpen(true);
  };

  const handleDelete = async (containerNo: string) => {
    if (!currentUser?.token) {
      setError('Authentication required');
      return;
    }

    try {
      await containerService.deleteContainer(containerNo, currentUser.token);
      setSuccessMessage('Container deleted successfully');
      fetchContainers();
    } catch (err: any) {
      if (err.response) {
        setError(`Failed to delete container: ${err.response.data?.message || err.message}`);
      } else {
        setError('Failed to delete container: Unknown error');
      }
      console.error('Error deleting container:', err);
    }
  };

  const formatDateTime = (dateStr?: string) => {
    return formatDateString(dateStr);
  };

  const getStatusChipColor = (status: ContainerStatus) => {
    switch (status) {
      case ContainerStatus.CREATED:
        return { bg: 'rgba(255, 167, 38, 0.1)', color: 'warning.main' };
      case ContainerStatus.CONFIRMED:
        return { bg: 'rgba(33, 150, 243, 0.1)', color: 'info.main' };
      case ContainerStatus.ARRIVED:
        return { bg: 'rgba(0, 200, 83, 0.1)', color: 'success.main' };
      case ContainerStatus.UNSTUFFING:
        return { bg: 'rgba(255, 193, 7, 0.1)', color: 'warning.main' };
      case ContainerStatus.UNSTUFF_COMPLETED:
        return { bg: 'rgba(0, 200, 83, 0.1)', color: 'success.main' };
      case ContainerStatus.READY_TO_PULL_OUT:
        return { bg: 'rgba(0, 200, 83, 0.1)', color: 'success.main' };
      case ContainerStatus.PULLED_OUT:
        return { bg: 'rgba(76, 175, 80, 0.1)', color: 'success.dark' };
      case ContainerStatus.RED_SEAL:
        return { bg: 'rgba(244, 67, 54, 0.1)', color: 'error.main' };
      default:
        return { bg: 'rgba(158, 158, 158, 0.1)', color: 'text.secondary' };
    }
  };

  // Add function to get container status icon
  const getContainerStatusIcon = (status: ContainerStatus) => {
    switch (status) {
      case ContainerStatus.CREATED:
        return <CalendarTodayIcon sx={{ color: theme.palette.warning.main }} />;
      case ContainerStatus.CONFIRMED:
        return <CheckCircleIcon sx={{ color: theme.palette.info.main }} />;
      case ContainerStatus.ARRIVED:
        return <DirectionsBoatIcon sx={{ color: theme.palette.success.main }} />;
      case ContainerStatus.UNSTUFFING:
        return <ConstructionIcon sx={{ color: theme.palette.warning.main }} />;
      case ContainerStatus.UNSTUFF_COMPLETED:
        return <CheckCircleIcon sx={{ color: theme.palette.success.main }} />;
      case ContainerStatus.READY_TO_PULL_OUT:
        return <LocalShippingOutlinedIcon sx={{ color: theme.palette.success.main }} />;
      case ContainerStatus.PULLED_OUT:
        return <LocalShippingIcon sx={{ color: theme.palette.success.dark }} />;
      case ContainerStatus.RED_SEAL:
        return <ReportProblemIcon sx={{ color: theme.palette.error.main }} />;
      default:
        return <TimelineIcon sx={{ color: theme.palette.grey[500] }} />;
    }
  };

  const columns: GridColDef<Container>[] = [
    {
      field: 'containerNo',
      headerName: 'Container No',
      flex: 1,
      minWidth: 150,
      headerClassName: 'super-app-theme--header',
      renderCell: (params: GridRenderCellParams) => (
        <Box 
          sx={{ 
            display: 'flex', 
            alignItems: 'center',
            cursor: 'pointer',
            '&:hover': {
              color: theme.palette.primary.main,
              textDecoration: 'underline'
            }
          }}
          onClick={() => handleContainerClick(params.value as string)}
        >
          <LocalShippingOutlinedIcon sx={{ mr: 1, color: theme.palette.primary.main }} />
          <Typography sx={{ fontWeight: 'bold' }}>
            {params.value}
          </Typography>
        </Box>
      ),
    },
    {
      field: 'client',
      headerName: 'Client',
      flex: 1,
      minWidth: 150,
      headerClassName: 'super-app-theme--header',
      renderCell: (params: GridRenderCellParams) => {
        try {
          if (params.row && params.row.client) {
            const companyName = params.row.client.companyName;
            const username = params.row.client.username;
            return (
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <PersonIcon sx={{ mr: 1, color: theme.palette.grey[600] }} fontSize="small" />
                <span>{companyName || username || '-'}</span>
              </Box>
            );
          }
          return <span>-</span>;
        } catch (err) {
          console.error('Error rendering client:', err);
          return <span>-</span>;
        }
      },
    },
    {
      field: 'createdDate',
      headerName: 'Created On',
      flex: 1,
      minWidth: 180,
      headerClassName: 'super-app-theme--header',
      renderCell: (params: GridRenderCellParams) => {
        try {
          if (params.row && params.row.createdDate) {
            return (
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <CalendarTodayIcon sx={{ mr: 1, color: theme.palette.grey[600] }} fontSize="small" />
                <span>{formatDateTime(params.row.createdDate)}</span>
              </Box>
            );
          }
          return <span>-</span>;
        } catch (err) {
          console.error('Error rendering createdDate:', err);
          return <span>-</span>;
        }
      },
    },
    {
      field: 'etaRequestedDate',
      headerName: 'ETA Requested',
      flex: 1,
      minWidth: 180,
      headerClassName: 'super-app-theme--header',
      renderCell: (params: GridRenderCellParams) => {
        try {
          if (params.row && params.row.etaRequestedDate) {
            return (
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <EventAvailableIcon sx={{ mr: 1, color: theme.palette.grey[600] }} fontSize="small" />
                <span>{formatDateTime(params.row.etaRequestedDate)}</span>
              </Box>
            );
          }
          return <span>-</span>;
        } catch (err) {
          console.error('Error rendering etaRequestedDate:', err);
          return <span>-</span>;
        }
      },
    },
    {
      field: 'truckNo',
      headerName: 'Truck No',
      flex: 1,
      minWidth: 120,
      headerClassName: 'super-app-theme--header',
      renderCell: (params: GridRenderCellParams) => (
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <LocalShippingIcon sx={{ mr: 1, color: theme.palette.grey[600] }} fontSize="small" />
          <span>{params.value || '-'}</span>
        </Box>
      ),
    },
    {
      field: 'vesselVoyageNo',
      headerName: 'Vessel Voyage',
      flex: 1,
      minWidth: 150,
      headerClassName: 'super-app-theme--header',
      renderCell: (params: GridRenderCellParams) => (
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <DirectionsBoatOutlinedIcon sx={{ mr: 1, color: theme.palette.grey[600] }} fontSize="small" />
          <span>{params.value || '-'}</span>
        </Box>
      ),
    },
    {
      field: 'manifestQuantity',
      headerName: 'Quantity',
      flex: 0.7,
      minWidth: 100,
      headerClassName: 'super-app-theme--header',
      renderCell: (params: GridRenderCellParams) => (
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Typography variant="body2" fontWeight="medium">
            {params.value || '0'}
          </Typography>
        </Box>
      ),
    },
    {
      field: 'portnetEta',
      headerName: 'Portnet ETA',
      flex: 1,
      minWidth: 180,
      headerClassName: 'super-app-theme--header',
      renderCell: (params: GridRenderCellParams) => {
        try {
          if (params.row && params.row.portnetEta) {
            return (
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <EventAvailableIcon sx={{ mr: 1, color: theme.palette.grey[600] }} fontSize="small" />
                <span>{formatDateTime(params.row.portnetEta)}</span>
              </Box>
            );
          }
          return <span>-</span>;
        } catch (err) {
          console.error('Error rendering portnetEta:', err);
          return <span>-</span>;
        }
      },
    },
    {
      field: 'etaAllocated',
      headerName: 'ETA Allocated',
      flex: 1,
      minWidth: 180,
      headerClassName: 'super-app-theme--header',
      renderCell: (params: GridRenderCellParams) => {
        try {
          if (params.row && params.row.etaAllocated) {
            return (
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <EventAvailableIcon sx={{ mr: 1, color: theme.palette.grey[600] }} fontSize="small" />
                <span>{formatDateTime(params.row.etaAllocated)}</span>
              </Box>
            );
          }
          return <span>-</span>;
        } catch (err) {
          console.error('Error rendering etaAllocated:', err);
          return <span>-</span>;
        }
      },
    },
    {
      field: 'arrivalDate',
      headerName: 'Arrival Date',
      flex: 1,
      minWidth: 180,
      headerClassName: 'super-app-theme--header',
      renderCell: (params: GridRenderCellParams) => {
        try {
          if (params.row && params.row.arrivalDate) {
            return (
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <DirectionsBoatIcon sx={{ mr: 1, color: theme.palette.grey[600] }} fontSize="small" />
                <span>{formatDateTime(params.row.arrivalDate)}</span>
              </Box>
            );
          }
          return <span>-</span>;
        } catch (err) {
          console.error('Error rendering arrivalDate:', err);
          return <span>-</span>;
        }
      },
    },
    {
      field: 'loadingBay',
      headerName: 'Loading Bay',
      flex: 1,
      minWidth: 120,
      headerClassName: 'super-app-theme--header',
      renderCell: (params: GridRenderCellParams) => (
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <WarehouseIcon sx={{ mr: 1, color: theme.palette.grey[600] }} fontSize="small" />
          <span>{params.value || '-'}</span>
        </Box>
      ),
    },
    {
      field: 'unstuffDate',
      headerName: 'Unstuff Date',
      flex: 1,
      minWidth: 180,
      headerClassName: 'super-app-theme--header',
      renderCell: (params: GridRenderCellParams) => {
        try {
          if (params.row && params.row.unstuffDate) {
            return (
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <ConstructionIcon sx={{ mr: 1, color: theme.palette.grey[600] }} fontSize="small" />
                <span>{formatDateTime(params.row.unstuffDate)}</span>
              </Box>
            );
          }
          return <span>-</span>;
        } catch (err) {
          console.error('Error rendering unstuffDate:', err);
          return <span>-</span>;
        }
      },
    },
    {
      field: 'unstuffCompletedDate',
      headerName: 'Unstuff Completed',
      flex: 1,
      minWidth: 180,
      headerClassName: 'super-app-theme--header',
      renderCell: (params: GridRenderCellParams) => {
        try {
          if (params.row && params.row.unstuffCompletedDate) {
            return (
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <CheckCircleIcon sx={{ mr: 1, color: theme.palette.success.main }} fontSize="small" />
                <span>{formatDateTime(params.row.unstuffCompletedDate)}</span>
              </Box>
            );
          }
          return <span>-</span>;
        } catch (err) {
          console.error('Error rendering unstuffCompletedDate:', err);
          return <span>-</span>;
        }
      },
    },
    {
      field: 'pullOutDate',
      headerName: 'Pull Out Date',
      flex: 1,
      minWidth: 180,
      headerClassName: 'super-app-theme--header',
      renderCell: (params: GridRenderCellParams) => {
        try {
          if (params.row && params.row.pullOutDate) {
            return (
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <LocalShippingIcon sx={{ mr: 1, color: theme.palette.success.main }} fontSize="small" />
                <span>{formatDateTime(params.row.pullOutDate)}</span>
              </Box>
            );
          }
          return <span>-</span>;
        } catch (err) {
          console.error('Error rendering pullOutDate:', err);
          return <span>-</span>;
        }
      },
    },
    {
      field: 'unstuffTeam',
      headerName: 'Unstuff Team',
      flex: 1,
      minWidth: 150,
      headerClassName: 'super-app-theme--header',
      renderCell: (params: GridRenderCellParams) => (
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <PersonIcon sx={{ mr: 1, color: theme.palette.grey[600] }} fontSize="small" />
          <span>{params.value || '-'}</span>
        </Box>
      ),
    },
    {
      field: 'remark',
      headerName: 'Remarks',
      flex: 1.5,
      minWidth: 200,
      headerClassName: 'super-app-theme--header',
      renderCell: (params: GridRenderCellParams) => (
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <span>{params.value || '-'}</span>
        </Box>
      ),
    },
    {
      field: 'status',
      headerName: 'Status',
      flex: 1,
      minWidth: 150,
      headerClassName: 'super-app-theme--header',
      renderCell: (params: GridRenderCellParams<Container>) => {
        const status = params.row.status;
        const { bg, color } = getStatusChipColor(status);
        
        return (
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Tooltip title={canUpdateStatus() ? "Click to update status" : "Container status"}>
              <Chip
                icon={getContainerStatusIcon(status)}
                label={status.replace(/_/g, ' ')}
                onClick={(event) => {
                  event.stopPropagation();
                  if (canUpdateStatus()) {
                    handleOpenStatusDialog(params.row);
                  }
                }}
                sx={{
                  backgroundColor: bg,
                  color: color,
                  fontWeight: 'medium',
                  fontSize: '0.75rem',
                  cursor: canUpdateStatus() ? 'pointer' : 'default',
                  '&:hover': canUpdateStatus() ? {
                    opacity: 0.8,
                    boxShadow: '0px 2px 4px rgba(0,0,0,0.1)',
                  } : {},
                  transition: 'all 0.2s ease',
                }}
              />
            </Tooltip>
            
            {/* Show ETA rejected indicator if applicable */}
            {params.row.etaRejected && (
              <Tooltip title={`ETA was rejected: ${params.row.etaRejectionReason}`}>
                <Box 
                  sx={{
                    ml: 1,
                    bgcolor: 'error.main',
                    color: 'white',
                    borderRadius: '50%',
                    width: 20,
                    height: 20,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontSize: '0.75rem',
                    fontWeight: 'bold'
                  }}
                >
                  !
                </Box>
              </Tooltip>
            )}
          </Box>
        );
      },
    },
    {
      field: 'actions',
      headerName: 'Actions',
      flex: 1,
      minWidth: 150,
      sortable: false,
      headerClassName: 'super-app-theme--header',
      renderCell: (params: GridRenderCellParams<Container>) => {
        const handleDeleteAction = () => {
          const container = params.row;
          showConfirmDialog(
            'Delete Container',
            `Are you sure you want to delete container ${container.containerNo}?`,
            () => handleDelete(container.containerNo)
          );
        };

        const getNextStatusAction = (currentStatus: ContainerStatus) => {
          switch (currentStatus) {
            case ContainerStatus.CREATED:
              return {
                status: ContainerStatus.CONFIRMED,
                label: 'Confirm',
                icon: <CheckCircleIcon fontSize="small" />
              };
            case ContainerStatus.CONFIRMED:
              return {
                status: ContainerStatus.ARRIVED,
                label: 'Mark Arrived',
                icon: <DirectionsBoatIcon fontSize="small" />
              };
            case ContainerStatus.ARRIVED:
              return {
                status: ContainerStatus.UNSTUFFING,
                label: 'Start Unstuffing',
                icon: <ConstructionIcon fontSize="small" />
              };
            case ContainerStatus.UNSTUFFING:
              return {
                status: ContainerStatus.UNSTUFF_COMPLETED,
                label: 'Complete Unstuffing',
                icon: <CheckCircleIcon fontSize="small" />
              };
            case ContainerStatus.UNSTUFF_COMPLETED:
              return {
                status: ContainerStatus.READY_TO_PULL_OUT,
                label: 'Ready to Pull Out',
                icon: <WarehouseIcon fontSize="small" />
              };
            case ContainerStatus.READY_TO_PULL_OUT:
              return {
                status: ContainerStatus.PULLED_OUT,
                label: 'Mark Pulled Out',
                icon: <LocalShippingIcon fontSize="small" />
              };
            default:
              return null;
          }
        };

        const nextStatusAction = getNextStatusAction(params.row.status);

        return (
          <Stack direction="row" spacing={1}>
            <Tooltip title="Edit">
              <IconButton
                size="small"
                onClick={(e) => handleOpenDialog(e, params.row)}
                sx={{ color: 'primary.main' }}
              >
                <EditIcon fontSize="small" />
              </IconButton>
            </Tooltip>
            
            <Tooltip title="Delete">
              <IconButton
                size="small"
                onClick={handleDeleteAction}
                sx={{ color: 'error.main' }}
              >
                <DeleteIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          </Stack>
        );
      },
    },
  ];

  const getFilteredContainers = () => {
    // Map tab index to status
    const statusMap = [
      ContainerStatus.CREATED,
      ContainerStatus.CONFIRMED,
      ContainerStatus.ARRIVED,
      ContainerStatus.UNSTUFFING,
      ContainerStatus.UNSTUFF_COMPLETED,
      ContainerStatus.READY_TO_PULL_OUT,
      ContainerStatus.PULLED_OUT,
      ContainerStatus.RED_SEAL,
    ];

    // First filter out containers with invalid data
    let filtered = [...containers].filter(container => {
      return container && container.containerNo; // Ensure container has a valid containerNo
    });

    // Ensure each container has valid dates
    filtered = filtered.map(container => {
      // Make a copy to avoid modifying the original
      const containerCopy = { ...container };
      
      // No longer manually setting createdDate, will be handled by backend
      
      // If etaRequestedDate is missing, set it to current date
      if (!containerCopy.etaRequestedDate) {
        console.warn(`Adding missing etaRequestedDate for container ${containerCopy.containerNo}`);
        containerCopy.etaRequestedDate = new Date().toISOString();
      }
      
      return containerCopy;
    });

    // Then apply other filters
    // Filter by tab (status)
    if (filterState.tab > 0 && filterState.tab <= statusMap.length) {
      filtered = filtered.filter(container => container.status === statusMap[filterState.tab - 1]);
    }

    // Filter by search term (container number or client)
    if (filterState.search.trim() !== '') {
      const searchTerm = filterState.search.toLowerCase();
      filtered = filtered.filter(
        container =>
          container.containerNo.toLowerCase().includes(searchTerm) ||
          (container.client?.username || '').toLowerCase().includes(searchTerm) ||
          (container.truckNo || '').toLowerCase().includes(searchTerm) ||
          (container.vesselVoyageNo || '').toLowerCase().includes(searchTerm)
      );
    }

    return filtered;
  };

  const handleTabChange = (_: React.SyntheticEvent, newValue: number) => {
    setFilterState({...filterState, tab: newValue});
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setFilterState({...filterState, search: event.target.value});
  };

  // Function to handle column visibility toggling
  const handleColumnToggle = (column: keyof ColumnVisibility) => {
    // Don't allow hiding the actions column
    if (column === 'actions') return;
    
    setVisibleColumns(prev => {
      const newState = {
        ...prev,
        [column]: !prev[column]
      };
      
      // Save to localStorage
      try {
        localStorage.setItem('containerColumnVisibility', JSON.stringify(newState));
      } catch (error) {
        console.error('Failed to save column visibility to localStorage:', error);
      }
      
      return newState;
    });
  };

  // Reset column visibility to defaults
  const resetColumnVisibility = () => {
    setVisibleColumns({...defaultColumnVisibility});
    
    // Save default settings to localStorage
    try {
      localStorage.setItem('containerColumnVisibility', JSON.stringify(defaultColumnVisibility));
    } catch (error) {
      console.error('Failed to save default column visibility to localStorage:', error);
    }
  };

  // Function to select all columns
  const selectAllColumns = () => {
    const allSelected = { ...visibleColumns };
    (Object.keys(allSelected) as Array<keyof ColumnVisibility>).forEach(key => {
      if (key !== 'actions') { // Keep actions as is
        allSelected[key] = true;
      }
    });
    setVisibleColumns(allSelected);
    
    // Save to localStorage
    try {
      localStorage.setItem('containerColumnVisibility', JSON.stringify(allSelected));
    } catch (error) {
      console.error('Failed to save column visibility to localStorage:', error);
    }
  };

  // Function to deselect all columns
  const selectNoColumns = () => {
    const noneSelected = { ...visibleColumns };
    (Object.keys(noneSelected) as Array<keyof ColumnVisibility>).forEach(key => {
      if (key !== 'actions') { // Keep actions as is
        noneSelected[key] = false;
      }
    });
    // Keep at least containerNo visible
    noneSelected.containerNo = true;
    
    setVisibleColumns(noneSelected);
    
    // Save to localStorage
    try {
      localStorage.setItem('containerColumnVisibility', JSON.stringify(noneSelected));
    } catch (error) {
      console.error('Failed to save column visibility to localStorage:', error);
    }
  };

  // Generate column visibility model for DataGrid
  const generateColumnVisibilityModel = () => {
    const model: Record<string, boolean> = {};
    
    (Object.keys(visibleColumns) as Array<keyof ColumnVisibility>).forEach((field) => {
      model[field] = visibleColumns[field];
    });
    
    return model;
  };

  // Function to navigate to container detail
  const handleContainerClick = (containerNo: string) => {
    navigate(`/containers/${containerNo}`);
  };

  // Add function to open status dialog
  const handleOpenStatusDialog = (container: Container) => {
    if (!canUpdateStatus()) {
      setError('Only administrators and managers can update container status');
      return;
    }
    
    setContainerToUpdateStatus(container);
    setNewStatus(container.status);
    setStatusDialogOpen(true);
  };

  // Add function to close status dialog
  const handleCloseStatusDialog = () => {
    setStatusDialogOpen(false);
    setContainerToUpdateStatus(null);
    setNewStatus(null);
  };

  // Add function to handle status update from dialog
  const handleStatusUpdateFromDialog = () => {
    if (!containerToUpdateStatus || !newStatus) return;
    
    handleUpdateStatus(containerToUpdateStatus.containerNo, newStatus);
    handleCloseStatusDialog();
  };

  // Add useEffect to load saved column visibility preferences from localStorage
  useEffect(() => {
    try {
      const savedColumnVisibility = localStorage.getItem('containerColumnVisibility');
      if (savedColumnVisibility) {
        const parsedVisibility = JSON.parse(savedColumnVisibility) as ColumnVisibility;
        // Ensure the actions column is always visible
        setVisibleColumns({
          ...parsedVisibility,
          actions: true
        });
      }
    } catch (error) {
      console.error('Failed to load column visibility from localStorage:', error);
    }
  }, []);

  const apiRef = useGridApiRef();

  // Function to handle auto-sizing columns using the DataGrid API
  const handleAutosizeColumns = () => {
    if (apiRef.current) {
      // Get only the visible columns
      const visibleColumnFields = columns
        .filter(col => visibleColumns[col.field as keyof typeof visibleColumns])
        .map(col => col.field);
      // Auto-size only the visible columns with a minimum width of 100px
      apiRef.current.autosizeColumns({
        columns: visibleColumnFields,
      });
    }
  };

  useEffect(() => {
    // Autosize columns after visibleColumns changes (e.g., columns selected/deselected)
    const timeout = setTimeout(() => {
      if (typeof handleAutosizeColumns === 'function') {
        handleAutosizeColumns();
      }
    }, 100); // Delay to allow DOM update
    return () => clearTimeout(timeout);
  }, [visibleColumns]);

  // Set page title
  usePageTitle('Container Management');

  return (
    <Box 
      className="no-cell-focus-outline"
      sx={{ 
        width: '100%', 
        maxWidth: '100%',
        p: 3,
        overflowX: 'auto' // Page-level horizontal scrolling
      }}
    >
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" sx={{ fontWeight: 'bold', color: theme.palette.primary.main }}>
          Container Management
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleOpenDialog}
          sx={{
            fontWeight: 'bold',
            borderRadius: 2,
            boxShadow: 2,
            px: 3,
            py: 1,
            textTransform: 'none',
            '&:hover': {
              boxShadow: 4
            },
          }}
        >
          Add Container
        </Button>
      </Box>

      {/* Welcome message */}
      <Paper sx={{ p: 3, mb: 3, borderLeft: '4px solid #1976d2', bgcolor: 'rgba(25, 118, 210, 0.04)' }}>
        <Typography variant="h6" gutterBottom>
          Container Management Dashboard
        </Typography>
        <Typography variant="body1" color="textSecondary">
          View, add, update, and manage all containers in the system. Use the tabs below to filter by container status.
        </Typography>
      </Paper>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' }, gap: 2, mb: 3 }}>
        <Paper 
          sx={{ 
            flexGrow: 1, 
            borderRadius: 2, 
            boxShadow: 'none', 
            border: '1px solid rgba(224, 224, 224, 0.5)',
            overflow: 'hidden',
          }}
        >
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', p: 1 }}>
            <Tabs
              value={filterState.tab}
              onChange={handleTabChange}
              aria-label="container status tabs"
              variant="scrollable"
              scrollButtons="auto"
              sx={{
                '& .MuiTab-root': {
                  fontWeight: 500,
                  py: 1.5,
                  px: 3,
                  minWidth: 'auto',
                  textTransform: 'none',
                  fontSize: '0.9rem',
                  color: 'text.secondary',
                  '&:focus, &:focus-visible': {
                    outline: 'none'
                  }
                },
                '& .Mui-selected': {
                  color: theme.palette.primary.main,
                  fontWeight: 'bold'
                },
                '& .MuiTabs-indicator': {
                  height: 3,
                  borderRadius: '3px 3px 0 0',
                },
              }}
            >
              <Tab label="All Containers" />
              <Tab label="Created" />
              <Tab label="Confirmed" />
              <Tab label="ETA Set" />
              <Tab label="Arrived" />
              <Tab label="Unstuffing" />
              <Tab label="Unstuffed" />
              <Tab label="Ready to Pull Out" />
              <Tab label="Pulled Out" />
              <Tab label="Red Seal" />
            </Tabs>
          </Box>
        </Paper>

          <TextField
            placeholder="Search by container no, client, truck no, or vessel voyage..."
            variant="outlined"
            size="small"
            value={filterState.search}
            onChange={handleSearchChange}
          sx={{ 
            width: { xs: '100%', md: '300px' },
            bgcolor: 'white',
            borderRadius: 1,
            '& .MuiOutlinedInput-root': {
              borderRadius: 1,
              '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                borderColor: theme.palette.primary.main,
                borderWidth: '1px'
              },
              '&:hover .MuiOutlinedInput-notchedOutline': {
                borderColor: 'rgba(0, 0, 0, 0.42)'
              }
            },
            '& .MuiInputBase-root': {
              '&:focus, &:focus-visible, &:focus-within': {
                outline: 'none',
                boxShadow: 'none'
              }
            }
          }}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon color="action" />
              </InputAdornment>
            ),
            endAdornment: filterState.search && (
              <InputAdornment position="end">
                <IconButton size="small" onClick={() => setFilterState({...filterState, search: ''})}>
                  <ClearIcon fontSize="small" />
                </IconButton>
              </InputAdornment>
            ),
          }}
        />
      </Box>

      <Paper sx={{ 
        width: '100%', 
        borderRadius: 2, 
        boxShadow: 2,
        flex: 'none',
        display: 'flex',
        flexDirection: 'column',
        overflow: 'visible', // Keep visible to allow content to expand
        height: 'auto',
        minHeight: '200px',
        position: 'relative', // Add positioning context
        maxWidth: '100%', // Ensure paper respects parent width 
      }}>
            {loading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%', minHeight: '200px' }}>
                <CircularProgress />
              </Box>
        ) : error ? (
          <Alert severity="error">{error}</Alert>
        ) : getFilteredContainers().length === 0 ? (
          <Alert severity="info">No containers found. Try changing the filter or add a new container.</Alert>
            ) : (
          <Box sx={{ 
            width: '100%',
            display: 'flex',
            flexDirection: 'column',
            overflow: 'visible' // Changed from 'hidden' to 'visible' to match manifest list
          }}>
              <Toolbar
                sx={{
                  pl: { sm: 2 },
                  pr: { xs: 1, sm: 1 },
                  borderBottom: '1px solid rgba(224, 224, 224, 1)',
                  // Removed background color comment
                }}
              >
                <Typography
                  sx={{ flex: '1 1 100%' }}
                  variant="h6"
                  component="div"
                >
                  Container List
                </Typography>
              </Toolbar>
              
              {/* Column visibility toggle buttons - in sequence */}
              <Box sx={{ 
                p: 1.5,
                borderBottom: '1px solid rgba(224, 224, 224, 1)'
              }}>
                <Box sx={{ 
                  display: 'flex', 
                  justifyContent: 'space-between', 
                  alignItems: 'center',
                  mb: 1.5
                }}>
                  <Typography variant="subtitle2" sx={{ fontWeight: 'medium' }}>
                    Toggle Column Visibility
                    <Typography component="span" variant="caption" sx={{ ml: 1, color: 'text.secondary' }}>
                      ({Object.values(visibleColumns).filter(Boolean).length - 1} of {Object.keys(visibleColumns).length - 1} visible)
                    </Typography>
                  </Typography>
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <Button 
                      size="small" 
                      variant="outlined"
                      onClick={selectAllColumns}
                      sx={{ 
                        borderRadius: 10,
                        fontSize: '0.75rem',
                        textTransform: 'none'
                      }}
                    >
                      Select All
                    </Button>
                    <Button 
                      size="small" 
                      variant="outlined"
                      onClick={selectNoColumns}
                      sx={{ 
                        borderRadius: 10,
                        fontSize: '0.75rem',
                        textTransform: 'none'
                      }}
                    >
                      Select None
                    </Button>
                    <Button 
                      size="small"
                      variant="outlined"
                      color="primary"
                      onClick={resetColumnVisibility}
                      sx={{ 
                        borderRadius: 10,
                        fontSize: '0.75rem',
                        textTransform: 'none'
                      }}
                    >
                      Reset to Default
                    </Button>
                  </Box>
                </Box>
                
                <Box sx={{ 
                  display: 'flex', 
                  flexWrap: 'wrap', 
                  gap: 1, 
                  overflowX: 'auto'
                }}>
                  {/* Display buttons in the exact order of columns in the visibleColumns state */}
                  {Object.entries(visibleColumns).map(([column, isVisible]) => {
                    // Skip the actions column as it should always be visible
                    if (column === 'actions') return null;
                    
                    const columnLabel = column
                      .charAt(0).toUpperCase() 
                      + column.slice(1).replace(/([A-Z])/g, ' $1').trim();
                    
                    return (
                      <Button
                        key={column}
                        size="small"
                        variant={isVisible ? "contained" : "outlined"}
                        color={isVisible ? "primary" : "inherit"}
                        onClick={() => handleColumnToggle(column as keyof ColumnVisibility)}
                        sx={{ 
                          borderRadius: 10,
                          px: 1.5,
                          py: 0.5,
                          minWidth: 'fit-content',
                          fontSize: '0.75rem',
                          textTransform: 'none',
                          boxShadow: isVisible ? 1 : 0
                        }}
                      >
                        {columnLabel}
                      </Button>
                    );
                  })}
                </Box>
              </Box>
              
              <Box sx={{ 
                width: '100%',
                // No overflow property here
              }}>
                <DataGrid
                  apiRef={apiRef}
                  rows={getFilteredContainers()}
                  columns={columns.filter(col => visibleColumns[col.field as keyof typeof visibleColumns])}
                  getRowId={(row) => row.containerNo || Math.random().toString(36).substr(2, 9)}
                  columnVisibilityModel={generateColumnVisibilityModel()}
                  onColumnVisibilityModelChange={(newModel) => {
                    const newVisibleColumns = { ...visibleColumns };
                    Object.keys(newModel).forEach((field) => {
                      if (field in newVisibleColumns) {
                        newVisibleColumns[field as keyof typeof newVisibleColumns] = newModel[field];
                      }
                    });
                    setVisibleColumns(newVisibleColumns);
                    // Save column visibility preferences to localStorage for persistence
                    try {
                      localStorage.setItem('containerColumnVisibility', JSON.stringify(newVisibleColumns));
                    } catch (error) {
                      console.error('Failed to save column visibility to localStorage:', error);
                    }
                  }}
                  initialState={{
                    pagination: {
                      paginationModel: { pageSize: 10 }
                    },
                    sorting: {
                      sortModel: [{ field: 'createdDate', sort: 'desc' }],
                    },
                    columns: {
                      columnVisibilityModel: generateColumnVisibilityModel(),
                    },
                  }}
                  pageSizeOptions={[10, 25, 50]}
                  disableColumnMenu={false}
                  disableRowSelectionOnClick
                  density="standard"
                  getEstimatedRowHeight={() => 60}
                  getRowHeight={() => 'auto'}
                  className="no-borders-datagrid"
                  sx={{
                    width: '100% !important',
                    minWidth: '100%',
                    border: 'none',
                    borderRadius: 0,
                    display: 'flex',
                    flexDirection: 'column',
                    position: 'relative',
                    '& .MuiDataGrid-root': {
                      display: 'flex',
                      flexDirection: 'column',
                      overflow: 'visible !important',
                      height: 'auto !important',
                      maxHeight: 'none !important',
                      minHeight: '0 !important',
                      width: '100% !important',
                    },
                    '& .MuiDataGrid-virtualScroller': {
                      position: 'static !important',
                      overflow: 'visible !important',
                      height: 'auto !important',
                      minHeight: '0 !important',
                      maxHeight: 'none !important',
                      width: '100% !important',
                    },
                    '& .MuiDataGrid-main': {
                      display: 'flex',
                      flexDirection: 'column',
                      overflow: 'visible !important',
                      height: 'auto !important',
                      maxHeight: 'none !important',
                      minHeight: '0 !important',
                      width: '100% !important',
                      flexGrow: 1,
                      position: 'static !important',
                    },
                    '& .MuiDataGrid-viewport': {
                      overflow: 'visible !important',
                      height: 'auto !important',
                      width: '100% !important',
                    },
                    '& .MuiDataGrid-window': {
                      position: 'static !important',
                      overflow: 'visible !important',
                      height: 'auto !important',
                      width: '100% !important',
                    },
                    '& .MuiDataGrid-dataContainer': {
                      position: 'static !important',
                      overflow: 'visible !important',
                      height: 'auto !important',
                      width: '100% !important',
                    },
                    '& .MuiDataGrid-columnHeadersInner': {
                      width: '100% !important',
                    },
                    '& .MuiDataGrid-columnHeaders': {
                      minHeight: '56px !important',
                      maxHeight: '56px !important',
                      lineHeight: '56px !important',
                      backgroundColor: '#f5f5f5',
                      borderBottom: '1px solid rgba(224, 224, 224, 1)',
                      padding: '0 0',
                      width: '100% !important',
                    },
                    '& .MuiDataGrid-columnsContainer': {
                      width: '100% !important',
                    },
                    '& .MuiDataGrid-row': {
                      width: '100% !important',
                      maxHeight: 'none !important',
                      minHeight: '65px !important',
                      '&:hover': {
                        backgroundColor: 'rgba(25, 118, 210, 0.04)',
                      },
                      '&:nth-of-type(even)': {
                        backgroundColor: 'rgba(0, 0, 0, 0.02)',
                      },
                    },
                    '& .MuiDataGrid-row .MuiDataGrid-cell': {
                      overflow: 'hidden',
                    },
                    '& .MuiDataGrid-cell': {
                      padding: '16px 16px',
                      borderBottom: '1px solid #f0f0f0',
                      whiteSpace: 'nowrap',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      minHeight: '65px !important',
                      maxHeight: 'none !important',
                      lineHeight: '24px !important',
                      textAlign: 'left',
                      display: 'flex',
                      alignItems: 'center',
                      fontSize: '0.95rem',
                    },
                    '& .MuiDataGrid-columnHeader': {
                      padding: '0 16px',
                      height: '56px',
                      display: 'flex',
                      alignItems: 'center',
                      '& .MuiDataGrid-columnHeaderTitleContainer': {
                        padding: '0 0',
                      },
                      '& .MuiDataGrid-columnHeaderTitle': {
                        fontWeight: 600,
                        fontSize: '0.95rem',
                      },
                    },
                    '& .MuiDataGrid-cell--withRenderer': {
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'flex-start',
                    },
                    '& .MuiDataGrid-cellContent': {
                      textAlign: 'left',
                      display: 'flex',
                      alignItems: 'center',
                    },
                    '& .MuiDataGrid-footerContainer': {
                      minHeight: '56px',
                      maxHeight: '56px',
                      borderTop: '1px solid rgba(224, 224, 224, 1)',
                      overflow: 'hidden',
                      padding: '0 12px',
                      display: 'flex',
                      alignItems: 'center',
                    },
                    '& .MuiTablePagination-root': {
                      width: '100%',
                      display: 'flex',
                      flexDirection: 'row',
                      justifyContent: 'flex-end',
                    },
                    '& .MuiTablePagination-selectLabel, & .MuiTablePagination-displayedRows': {
                      fontSize: '0.75rem',
                      margin: 0,
                      overflow: 'visible',
                    },
                    '& .MuiTablePagination-select': {
                      fontSize: '0.75rem',
                      marginLeft: '4px',
                      marginRight: '4px',
                      overflow: 'visible',
                    },
                    '& .MuiTablePagination-actions': {
                      marginLeft: 0,
                      display: 'flex',
                      alignItems: 'center',
                      overflow: 'visible',
                    },
                    '& .MuiDataGrid-columnHeaderCheckbox': {
                      padding: '0',
                      height: '48px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                    },
                    '& .MuiDataGrid-cellCheckbox': {
                      padding: '0',
                    },
                  }}
                />
              </Box>
            </Box>
            )}
      </Paper>

      <StaticPositionDialog
        open={openDialog}
        onClose={handleCloseDialog}
        fullWidth
        maxWidth="md"
        slotProps={{
          paper: {
          sx: {
            borderRadius: 2,
              boxShadow: 24,
              maxHeight: '90vh',
            },
          }
        }}
      >
        <DialogTitle sx={{ 
          bgcolor: theme.palette.primary.main, 
          color: 'white',
          px: 3,
          py: 2
        }}>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Box display="flex" alignItems="center">
              {selectedContainer ? <EditIcon sx={{ mr: 1 }} /> : <AddIcon sx={{ mr: 1 }} />}
            <Typography variant="h6">
              {selectedContainer ? 'Edit Container' : 'Add New Container'}
            </Typography>
            </Box>
            <IconButton onClick={handleCloseDialog} size="small" sx={{ color: 'white' }}>
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>

        <DialogContent sx={{ px: 3, py: 2 }}>
          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          <Global styles={globalStyles} />

          <LocalizationProvider 
            dateAdapter={AdapterDateFns}
            localeText={{
              cancelButtonLabel: 'Cancel',
              okButtonLabel: 'OK',
            }}
          >
            <Box sx={{ mb: 3, mt: 1 }}>
              <Typography variant="subtitle1" sx={{ fontWeight: 'bold', mb: 2, color: theme.palette.text.primary }}>
                Container Information
              </Typography>
              <Divider sx={{ mb: 3 }} />
            </Box>
            
            <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', md: '1fr 1fr' }, gap: 2 }}>
              <TextField
                label="Container No"
                name="containerNo"
                value={formData.containerNo}
                onChange={handleInputChange}
                fullWidth
                required
                disabled={!!selectedContainer}
                margin="normal"
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 1,
                    '&:hover .MuiOutlinedInput-notchedOutline': {
                      borderColor: theme.palette.primary.main,
                    },
                  },
                }}
              />
              
              <FormControl fullWidth margin="normal">
                <InputLabel>Client</InputLabel>
                <Select
                value={formData.client.username}
                onChange={handleClientChange}
                  label="Client"
                  name="client"
                  sx={{
                    borderRadius: 1,
                    '&:hover .MuiOutlinedInput-notchedOutline': {
                      borderColor: theme.palette.primary.main,
                    },
                  }}
                >
                  {clients.length > 0 ? (
                    clients.map((client) => (
                      <MenuItem key={client.username} value={client.username}>
                        {client.username} ({client.companyName})
                      </MenuItem>
                    ))
                  ) : (
                    <MenuItem disabled value="">
                      {hasRole('ROLE_MANAGER') ? 
                        'Manager access does not have client list permissions' : 
                        'No clients available'}
                    </MenuItem>
                  )}
                </Select>
              </FormControl>
              
              <Box sx={{ gridColumn: { md: '1 / 3' }, mt: 2, mb: 1 }}>
                <Typography variant="subtitle1" sx={{ fontWeight: 'bold', mb: 2, color: theme.palette.text.primary }}>
                  Shipping Details
                </Typography>
                <Divider />
              </Box>
              
              <TextField
                label="Truck No"
                name="truckNo"
                value={formData.truckNo}
                onChange={handleInputChange}
                fullWidth
                required
                margin="normal"
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 1,
                    '&:hover .MuiOutlinedInput-notchedOutline': {
                      borderColor: theme.palette.primary.main,
                    },
                  },
                }}
              />
              
              <TextField
                label="Vessel Voyage No"
                name="vesselVoyageNo"
                value={formData.vesselVoyageNo}
                onChange={handleInputChange}
                fullWidth
                required
                margin="normal"
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 1,
                    '&:hover .MuiOutlinedInput-notchedOutline': {
                      borderColor: theme.palette.primary.main,
                    },
                  },
                }}
              />
              
              <TextField
                label="Manifest Quantity"
                name="manifestQuantity"
                value={formData.manifestQuantity}
                onChange={handleNumberChange}
                fullWidth
                type="number"
                required
                margin="normal"
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 1,
                    '&:hover .MuiOutlinedInput-notchedOutline': {
                      borderColor: theme.palette.primary.main,
                    },
                  },
                }}
              />
              
              <FormControl fullWidth margin="normal">
                <InputLabel>Status</InputLabel>
                <Select
                  value={formData.status}
                  onChange={handleStatusChange}
                  label="Status"
                  name="status"
                  fullWidth
                  disabled={!selectedContainer}  // Disable for new containers
                  sx={{
                    borderRadius: 1,
                    '&:hover .MuiOutlinedInput-notchedOutline': {
                      borderColor: theme.palette.primary.main,
                    },
                  }}
                >
                  {Object.values(ContainerStatus).map((status) => (
                    <MenuItem key={status} value={status}>
                      {status.replace(/_/g, ' ')}
                    </MenuItem>
                  ))}
                </Select>
                {!selectedContainer && (
                  <Typography variant="caption" color="text.secondary" sx={{ mt: 1 }}>
                    New containers will be created with "CREATED" status
                  </Typography>
                )}
              </FormControl>
              
              <Box sx={{ gridColumn: { md: '1 / 3' }, mt: 2, mb: 1 }}>
                <Typography variant="subtitle1" sx={{ fontWeight: 'bold', mb: 2, color: theme.palette.text.primary }}>
                  Schedule Information
                </Typography>
                <Divider />
              </Box>
              
              <Box position="relative" zIndex={1300} width="100%">
                <CustomDatePickerWithCounts
                  label="ETA Requested Date"
                  value={formData.etaRequestedDate ? parseLocalDateTime(formData.etaRequestedDate) : null}
                  onChange={(date) => handleDateChange('etaRequestedDate', date)}
                  containers={containers}
                  format="dd MMM yyyy HH:mm"
                  ampm={false}
                  slotProps={{
                    textField: {
                      fullWidth: true,
                      required: true,
                      margin: "normal",
                      InputProps: {
                        endAdornment: formData.etaRequestedDate ? (
                          <InputAdornment position="end">
                            <IconButton
                              edge="end"
                              size="small"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDateChange('etaRequestedDate', null);
                              }}
                              title="Clear date"
                            >
                              <ClearIcon fontSize="small" />
                            </IconButton>
                          </InputAdornment>
                        ) : null
                      }
                    },
                    popper: {
                      sx: {
                        zIndex: 9999
                      }
                    },
                  }}
                />
              </Box>
              
              <Box position="relative" zIndex={1300} width="100%">
              <DateTimePicker
                label="Portnet ETA"
                value={formData.portnetEta ? parseLocalDateTime(formData.portnetEta) : null}
                onChange={(date) => handleDateChange('portnetEta', date)}
                format="dd MMM yyyy HH:mm"
                ampm={false}
                timeSteps={{ minutes: 1 }}
                referenceDate={new Date()}
                slotProps={{
                  textField: {
                    fullWidth: true,
                    margin: "normal",
                    InputProps: {
                      endAdornment: formData.portnetEta ? (
                        <InputAdornment position="end">
                          <IconButton
                            edge="end"
                            size="small"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDateChange('portnetEta', null);
                            }}
                            title="Clear date"
                          >
                            <ClearIcon fontSize="small" />
                          </IconButton>
                        </InputAdornment>
                      ) : null
                    }
                  },
                  popper: {
                    sx: {
                      zIndex: 9999
                    }
                  },
                }}
              />
              </Box>
              
              <Box position="relative" zIndex={1300} width="100%">
              <DateTimePicker
                label="ETA Allocated"
                value={formData.etaAllocated ? parseLocalDateTime(formData.etaAllocated) : null}
                onChange={(date) => handleDateChange('etaAllocated', date)}
                format="dd MMM yyyy HH:mm"
                ampm={false}
                timeSteps={{ minutes: 1 }}
                referenceDate={new Date()}
                slotProps={{
                  textField: {
                    fullWidth: true,
                    margin: "normal",
                    InputProps: {
                      endAdornment: formData.etaAllocated ? (
                        <InputAdornment position="end">
                          <IconButton
                            edge="end"
                            size="small"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDateChange('etaAllocated', null);
                            }}
                            title="Clear date"
                          >
                            <ClearIcon fontSize="small" />
                          </IconButton>
                        </InputAdornment>
                      ) : null
                    }
                  },
                  popper: {
                    sx: {
                      zIndex: 9999
                    }
                  },
                }}
              />
              </Box>
              
              <Box position="relative" zIndex={1300} width="100%">
                <DateTimePicker
                  label="Arrival Date"
                  value={formData.arrivalDate ? parseLocalDateTime(formData.arrivalDate) : null}
                  onChange={(date) => handleDateChange('arrivalDate', date)}
                  format="dd MMM yyyy HH:mm"
                  ampm={false}
                  timeSteps={{ minutes: 1 }}
                  referenceDate={new Date()}
                  slotProps={{
                    textField: {
                      fullWidth: true,
                      margin: "normal",
                      InputProps: {
                        endAdornment: formData.arrivalDate ? (
                          <InputAdornment position="end">
                            <IconButton
                              edge="end"
                              size="small"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDateChange('arrivalDate', null);
                              }}
                              title="Clear date"
                            >
                              <ClearIcon fontSize="small" />
                            </IconButton>
                          </InputAdornment>
                        ) : null
                      }
                    },
                    popper: {
                      sx: {
                        zIndex: 9999
                      }
                    },
                  }}
                />
              </Box>
              
              <Box sx={{ gridColumn: { md: '1 / 3' }, mt: 2, mb: 1 }}>
                <Typography variant="subtitle1" sx={{ fontWeight: 'bold', mb: 2, color: theme.palette.text.primary }}>
                  Additional Information
                </Typography>
                <Divider />
              </Box>
              
              <TextField
                label="Loading Bay"
                name="loadingBay"
                value={formData.loadingBay}
                onChange={handleInputChange}
                fullWidth
                margin="normal"
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 1,
                    '&:hover .MuiOutlinedInput-notchedOutline': {
                      borderColor: theme.palette.primary.main,
                    },
                  },
                }}
              />
              
              <TextField
                label="Unstuff Team"
                name="unstuffTeam"
                value={formData.unstuffTeam}
                onChange={handleInputChange}
                fullWidth
                margin="normal"
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 1,
                    '&:hover .MuiOutlinedInput-notchedOutline': {
                      borderColor: theme.palette.primary.main,
                    },
                  },
                }}
              />
              
              <Box position="relative" zIndex={1300} width="100%">
                <DateTimePicker
                  label="Unstuff Date"
                  value={formData.unstuffDate ? parseLocalDateTime(formData.unstuffDate) : null}
                  onChange={(date) => handleDateChange('unstuffDate', date)}
                  format="dd MMM yyyy HH:mm"
                  ampm={false}
                  timeSteps={{ minutes: 1 }}
                  referenceDate={new Date()}
                  slotProps={{
                    textField: {
                      fullWidth: true,
                      margin: "normal",
                      sx: {
                        '& .MuiOutlinedInput-root': {
                          borderRadius: 1,
                          '&:hover .MuiOutlinedInput-notchedOutline': {
                            borderColor: theme.palette.primary.main,
                          },
                        },
                      },
                      InputProps: {
                        endAdornment: formData.unstuffDate ? (
                          <InputAdornment position="end">
                            <IconButton
                              edge="end"
                              size="small"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDateChange('unstuffDate', null);
                              }}
                              title="Clear date"
                            >
                              <ClearIcon fontSize="small" />
                            </IconButton>
                          </InputAdornment>
                        ) : null
                      }
                    },
                    popper: {
                      sx: {
                        zIndex: 9999
                      }
                    },
                  }}
                />
              </Box>
              
              <Box position="relative" zIndex={1300} width="100%">
                <DateTimePicker
                  label="Unstuff Completed Date"
                  value={formData.unstuffCompletedDate ? parseLocalDateTime(formData.unstuffCompletedDate) : null}
                  onChange={(date) => handleDateChange('unstuffCompletedDate', date)}
                  format="dd MMM yyyy HH:mm"
                  ampm={false}
                  timeSteps={{ minutes: 1 }}
                  referenceDate={new Date()}
                  slotProps={{
                    textField: {
                      fullWidth: true,
                      margin: "normal",
                      sx: {
                        '& .MuiOutlinedInput-root': {
                          borderRadius: 1,
                          '&:hover .MuiOutlinedInput-notchedOutline': {
                            borderColor: theme.palette.primary.main,
                          },
                        },
                      },
                      InputProps: {
                        endAdornment: formData.unstuffCompletedDate ? (
                          <InputAdornment position="end">
                            <IconButton
                              edge="end"
                              size="small"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDateChange('unstuffCompletedDate', null);
                              }}
                              title="Clear date"
                            >
                              <ClearIcon fontSize="small" />
                            </IconButton>
                          </InputAdornment>
                        ) : null
                      }
                    },
                    popper: {
                      sx: {
                        zIndex: 9999
                      }
                    },
                  }}
                />
              </Box>
              
              <Box position="relative" zIndex={1300} width="100%">
                <DateTimePicker
                  label="Pull Out Date"
                  value={formData.pullOutDate ? parseLocalDateTime(formData.pullOutDate) : null}
                  onChange={(date) => handleDateChange('pullOutDate', date)}
                  format="dd MMM yyyy HH:mm"
                  ampm={false}
                  timeSteps={{ minutes: 1 }}
                  referenceDate={new Date()}
                  slotProps={{
                    textField: {
                      fullWidth: true,
                      margin: "normal",
                      sx: {
                        '& .MuiOutlinedInput-root': {
                          borderRadius: 1,
                          '&:hover .MuiOutlinedInput-notchedOutline': {
                            borderColor: theme.palette.primary.main,
                          },
                        },
                      },
                      InputProps: {
                        endAdornment: formData.pullOutDate ? (
                          <InputAdornment position="end">
                            <IconButton
                              edge="end"
                              size="small"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDateChange('pullOutDate', null);
                              }}
                              title="Clear date"
                            >
                              <ClearIcon fontSize="small" />
                            </IconButton>
                          </InputAdornment>
                        ) : null
                      }
                    },
                    popper: {
                      sx: {
                        zIndex: 9999
                      }
                    },
                  }}
                />
              </Box>
              
              <TextField
                label="Remark"
                name="remark"
                value={formData.remark}
                onChange={handleInputChange}
                fullWidth
                multiline
                rows={2}
                margin="normal"
                sx={{ 
                  gridColumn: { md: '1 / 3' },
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 1,
                    '&:hover .MuiOutlinedInput-notchedOutline': {
                      borderColor: theme.palette.primary.main,
                    },
                  },
                }}
              />
            </Box>
          </LocalizationProvider>
        </DialogContent>

        <DialogActions sx={{ px: 3, py: 2, bgcolor: '#f9fafc' }}>
          <Button onClick={handleCloseDialog} color="inherit" sx={{ borderRadius: 2 }}>
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            disableElevation
            sx={{ borderRadius: 2 }}
          >
            {selectedContainer ? 'Update Container' : 'Add Container'}
          </Button>
        </DialogActions>
      </StaticPositionDialog>

      <Dialog
        open={confirmDialogOpen}
        onClose={() => setConfirmDialogOpen(false)}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        slotProps={{
          paper: {
            sx: {
              borderRadius: 2,
              boxShadow: 8,
              maxWidth: '450px',
            },
          }
        }}
      >
        <DialogTitle 
          id="alert-dialog-title"
          sx={{ 
            borderBottom: '1px solid rgba(0, 0, 0, 0.12)',
            pb: 2,
          }}
        >
          <Typography variant="h6" component="div" sx={{ fontWeight: 'bold' }}>
            {confirmTitle}
          </Typography>
        </DialogTitle>
        <DialogContent sx={{ mt: 2 }}>
          <DialogContentText id="alert-dialog-description">
            {confirmMessage}
          </DialogContentText>
        </DialogContent>
        <DialogActions sx={{ px: 3, py: 2, borderTop: '1px solid rgba(0, 0, 0, 0.12)' }}>
          <Button 
            onClick={() => setConfirmDialogOpen(false)} 
            color="inherit"
            sx={{ borderRadius: 2 }}
          >
            Cancel
          </Button>
          <Button
            onClick={() => {
              confirmAction();
              setConfirmDialogOpen(false);
            }}
            variant="contained"
            color="primary"
            autoFocus
            sx={{ borderRadius: 2 }}
          >
            Confirm
          </Button>
        </DialogActions>
      </Dialog>

      <Snackbar
        open={!!successMessage}
        autoHideDuration={6000}
        onClose={handleSuccessClose}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
        sx={{
          '& .MuiSnackbarContent-root': {
            bgcolor: 'success.main',
            color: '#fff',
            borderRadius: 2,
            boxShadow: 3,
          }
        }}
      >
        <Alert 
          onClose={handleSuccessClose} 
          severity="success" 
          variant="filled"
          sx={{ width: '100%' }}
        >
          {successMessage}
        </Alert>
      </Snackbar>

      {/* Add ETA Rejection Dialog */}
      <ETARejectionDialog
        open={etaRejectionDialogOpen}
        onClose={() => setEtaRejectionDialogOpen(false)}
        onSubmit={handleEtaRejectionSubmit}
        containerNo={selectedContainerForRejection?.containerNo || ''}
        initialEtaRequestedDate={selectedContainerForRejection?.initialEtaRequestedDate}
        containers={containers}
      />

      {/* Container Status Update Dialog */}
      <Dialog 
        open={statusDialogOpen} 
        onClose={handleCloseStatusDialog} 
        maxWidth="sm" 
        fullWidth
        sx={{ zIndex: 9000 }}
        PaperProps={{
          sx: { zIndex: 9001, p: 1 }
        }}
      >
        <DialogTitle sx={{ 
          borderBottom: 1, 
          borderColor: 'divider',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        }}>
          <Box display="flex" alignItems="center">
            <LocalShippingOutlinedIcon sx={{ mr: 1 }} />
            Update Container Status
          </Box>
          {containerToUpdateStatus && (
            <Chip
              icon={getContainerStatusIcon(containerToUpdateStatus.status)}
              label={containerToUpdateStatus.status.replace(/_/g, ' ')}
              size="small"
              sx={{
                ...getStatusChipColor(containerToUpdateStatus.status),
                fontSize: '0.75rem',
                height: '22px',
                textTransform: 'uppercase'
              }}
            />
          )}
        </DialogTitle>
        <DialogContent sx={{ py: 3 }}>
          {containerToUpdateStatus && (
            <>
              <Box sx={{ mb: 3 }}>
                <Typography variant="subtitle2" gutterBottom>
                  Container: {containerToUpdateStatus.containerNo}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Select the new status for this container:
                </Typography>
                <Typography variant="caption" color="error">
                  Note: Changing container status may affect all manifests in this container
                </Typography>
              </Box>
              
              {/* For CREATED status, show special options */}
              {containerToUpdateStatus.status === ContainerStatus.CREATED && (
                <Box sx={{ mb: 2, p: 2, bgcolor: 'background.default', borderRadius: 1 }}>
                  <Typography variant="subtitle2" color="primary" gutterBottom>
                    Special Options for Created Status
                  </Typography>
                  <Button
                    variant="outlined"
                    color="error"
                    onClick={() => {
                      handleCloseStatusDialog();
                      handleEtaRejection(containerToUpdateStatus.containerNo);
                    }}
                    startIcon={<CloseIcon />}
                    sx={{ mr: 1, mb: 1 }}
                  >
                    Reject ETA & Suggest New Date
                  </Button>
                  <Button
                    variant="contained"
                    color="success"
                    onClick={() => {
                      setNewStatus(ContainerStatus.CONFIRMED);
                    }}
                    startIcon={<CheckCircleIcon />}
                    sx={{ mb: 1 }}
                  >
                    Confirm Container
                  </Button>
                </Box>
              )}
              
              <Box sx={{ 
                display: 'grid', 
                gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr' },
                gap: 2 
              }}>
                {Object.values(ContainerStatus).map((status) => {
                  const isSelected = newStatus === status;
                  const isCurrentStatus = containerToUpdateStatus.status === status;
                  const statusColors = getStatusChipColor(status);
                  
                  return (
                    <Paper
                      key={status}
                      elevation={isSelected ? 4 : 1}
                      sx={{
                        p: 2,
                        borderRadius: 2,
                        border: isSelected ? `1px solid ${theme.palette.primary.main}` : '1px solid transparent',
                        cursor: isCurrentStatus ? 'default' : 'pointer',
                        backgroundColor: isSelected ? alpha(statusColors.bg, 0.5) : 'background.paper',
                        opacity: isCurrentStatus ? 0.7 : 1,
                        transition: 'all 0.2s',
                        '&:hover': {
                          backgroundColor: isCurrentStatus ? 'inherit' : alpha(statusColors.bg, 0.3),
                          transform: isCurrentStatus ? 'none' : 'translateY(-2px)',
                        },
                      }}
                      onClick={() => {
                        if (!isCurrentStatus) {
                          setNewStatus(status);
                        }
                      }}
                    >
                      <Box sx={{ 
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'space-between'
                      }}>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          {getContainerStatusIcon(status)}
                          <Typography sx={{ ml: 1, fontWeight: isSelected ? 'bold' : 'normal' }}>
                            {status.replace(/_/g, ' ')}
                          </Typography>
                        </Box>
                        {isCurrentStatus && (
                          <Chip size="small" label="Current" color="primary" sx={{ height: 20 }} />
                        )}
                      </Box>
                      
                      {isSelected && (
                        <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                          {isCurrentStatus 
                            ? "This is the current status"
                            : `Change status from ${containerToUpdateStatus?.status.replace(/_/g, ' ')} to ${status.replace(/_/g, ' ')}`
                          }
                        </Typography>
                      )}
                    </Paper>
                  );
                })}
              </Box>
            </>
          )}
        </DialogContent>
        <DialogActions sx={{ px: 3, py: 2, borderTop: 1, borderColor: 'divider' }}>
          <Button onClick={handleCloseStatusDialog}>Cancel</Button>
          <Button 
            onClick={handleStatusUpdateFromDialog} 
            variant="contained" 
            color="primary"
            disabled={Boolean(newStatus === undefined || (containerToUpdateStatus && newStatus === containerToUpdateStatus.status))}
            startIcon={<CheckCircleIcon />}
          >
            Update Status
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ContainerManagement; 