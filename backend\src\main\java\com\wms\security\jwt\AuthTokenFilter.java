package com.wms.security.jwt;

import com.wms.security.services.UserDetailsImpl;
import com.wms.security.services.UserDetailsServiceImpl;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

public class AuthTokenFilter extends OncePerRequestFilter {
    @Autowired
    private JwtUtils jwtUtils;

    @Autowired
    private UserDetailsServiceImpl userDetailsService;
    
    @Autowired
    private JwtTokenStore tokenStore;

    private static final Logger logger = LoggerFactory.getLogger(AuthTokenFilter.class);
    
    // Authentication cache - maps username to UserDetails with expiration
    private final ConcurrentMap<String, CachedAuthentication> authenticationCache = new ConcurrentHashMap<>();
    
    // Cache expiration time (5 minutes)
    private static final long CACHE_EXPIRATION_MS = TimeUnit.MINUTES.toMillis(5);
    
    // Metrics for tracking authentication requests
    private final ConcurrentMap<String, AtomicInteger> authAttempts = new ConcurrentHashMap<>();
    private final ConcurrentMap<String, AtomicInteger> authFailures = new ConcurrentHashMap<>();
    private final ConcurrentMap<String, Long> lastSuccessful = new ConcurrentHashMap<>();
    
    // List of path patterns that should not be filtered
    private final List<String> excludedPaths = Arrays.asList(
        "/api/auth/signin", 
        "/api/test-auth/**",
        "/test-auth/**",  
        "/api/test/**",
        "/test/**",      
        "/error",
        "/actuator/**",
        "/favicon.ico",
        "/",
        "/api/admin-init/**"
    );
    
    private final AntPathMatcher pathMatcher = new AntPathMatcher();

    @Override
    protected boolean shouldNotFilter(HttpServletRequest request) {
        String path = request.getServletPath();
        logger.info("Checking if path should be filtered: {}", path);
        
        for (String pattern : excludedPaths) {
            if (pathMatcher.match(pattern, path)) {
                logger.info("Path {} matches excluded pattern {}, skipping filter", path, pattern);
                return true;
            }
        }
        
        logger.info("Path {} requires authentication, applying filter", path);
        return false;
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {
        long startTime = System.currentTimeMillis();
        String path = request.getServletPath();
        logger.info("Processing request: {} {}", request.getMethod(), path);
        
        try {
            String jwt = parseJwt(request);
            if (jwt != null) {
                logger.info("JWT token found, attempting to validate");
                
                if (jwtUtils.validateJwtToken(jwt)) {
                    String username = jwtUtils.getUserNameFromJwtToken(jwt);
                    
                    if (username != null) {
                        logger.info("Valid JWT token for user: {}", username);
                        
                        // Track authentication metrics
                        authAttempts.computeIfAbsent(username, k -> new AtomicInteger(0)).incrementAndGet();
                        lastSuccessful.put(username, System.currentTimeMillis());
                        
                        try {
                            // Check if we have a cached authentication
                            UserDetails userDetails = getCachedUserDetails(username);
                            
                            if (userDetails == null) {
                                // Not in cache, load from database
                                logger.debug("User {} not in cache, loading from database", username);
                                userDetails = userDetailsService.loadUserByUsername(username);
                                
                                // Cache the successful authentication
                                if (userDetails != null) {
                                    logger.debug("Caching authentication for user {}", username);
                                    cacheUserDetails(username, userDetails);
                                }
                            } else {
                                logger.debug("Using cached authentication for user {}", username);
                            }
                            
                            UsernamePasswordAuthenticationToken authentication =
                                    new UsernamePasswordAuthenticationToken(
                                            userDetails,
                                            null,
                                            userDetails.getAuthorities());
                                            
                            authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                            SecurityContextHolder.getContext().setAuthentication(authentication);
                            
                            logger.info("User {} is authenticated with authorities: {}", 
                                    username, userDetails.getAuthorities());
                        } catch (UsernameNotFoundException e) {
                            // User in token but not found in database
                            logger.error("User from valid token not found in database: {}", username);
                            authFailures.computeIfAbsent(username, k -> new AtomicInteger(0)).incrementAndGet();
                            
                            // If this happens repeatedly for the same token, invalidate it
                            if (authFailures.get(username).get() > 3) {
                                logger.warn("Invalidating token after repeated failures for user: {}", username);
                                tokenStore.invalidateToken(jwt);
                                
                                // Clear the cache for this user
                                authenticationCache.remove(username);
                            }
                        }
                    } else {
                        logger.warn("Could not extract username from valid token");
                    }
                } else {
                    logger.warn("JWT token validation failed");
                }
            } else {
                logger.info("No JWT token found in request");
            }
        } catch (Exception e) {
            logger.error("Cannot set user authentication: {}", e.getMessage());
        } finally {
            long duration = System.currentTimeMillis() - startTime;
            logger.debug("Request processing time for {}: {}ms", path, duration);
            
            // Log a warning for slow authentication processing
            if (duration > 100) {
                logger.warn("Slow authentication processing for {}: {}ms", path, duration);
            }
        }

        filterChain.doFilter(request, response);
    }

    private String parseJwt(HttpServletRequest request) {
        String headerAuth = request.getHeader("Authorization");
        logger.debug("Authorization header: {}", headerAuth != null ? 
                     headerAuth.substring(0, Math.min(20, headerAuth.length())) + "..." : "null");

        if (StringUtils.hasText(headerAuth) && headerAuth.startsWith("Bearer ")) {
            return headerAuth.substring(7);
        }

        return null;
    }
    
    /**
     * Get cached user details if available and not expired
     * 
     * @param username Username to look up
     * @return UserDetails if found and valid, null otherwise
     */
    private UserDetails getCachedUserDetails(String username) {
        CachedAuthentication cached = authenticationCache.get(username);
        
        if (cached != null) {
            // Check if the cache has expired
            if (System.currentTimeMillis() - cached.timestamp <= CACHE_EXPIRATION_MS) {
                return cached.userDetails;
            } else {
                // Cache expired, remove it
                logger.debug("Cache expired for user {}", username);
                authenticationCache.remove(username);
            }
        }
        
        return null;
    }
    
    /**
     * Cache user details for future requests
     * 
     * @param username Username to cache
     * @param userDetails UserDetails to cache
     */
    private void cacheUserDetails(String username, UserDetails userDetails) {
        authenticationCache.put(username, new CachedAuthentication(userDetails, System.currentTimeMillis()));
    }
    
    /**
     * Clear the authentication cache
     * Called periodically to free memory
     */
    @Scheduled(fixedRate = 600000) // Every 10 minutes
    public void clearExpiredCacheEntries() {
        logger.info("Clearing expired authentication cache entries");
        long now = System.currentTimeMillis();
        int removed = 0;
        
        for (Map.Entry<String, CachedAuthentication> entry : authenticationCache.entrySet()) {
            if (now - entry.getValue().timestamp > CACHE_EXPIRATION_MS) {
                authenticationCache.remove(entry.getKey());
                removed++;
            }
        }
        
        logger.info("Removed {} expired cache entries, {} remaining", 
                removed, authenticationCache.size());
    }
    
    /**
     * Inner class to hold cached authentication with timestamp
     */
    private static class CachedAuthentication {
        final UserDetails userDetails;
        final long timestamp;
        
        CachedAuthentication(UserDetails userDetails, long timestamp) {
            this.userDetails = userDetails;
            this.timestamp = timestamp;
        }
    }
} 