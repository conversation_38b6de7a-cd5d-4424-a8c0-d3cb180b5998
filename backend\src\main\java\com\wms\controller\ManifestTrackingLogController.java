package com.wms.controller;

import com.wms.dto.ApiResponse;
import com.wms.dto.ManifestTrackingLogDTO;
import com.wms.entity.ManifestTrackingLog;
import com.wms.service.ManifestTrackingLogService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@RestController
@CrossOrigin(origins = "*")
@PreAuthorize("hasAnyRole('ADMIN', 'MANAGER')")
public class ManifestTrackingLogController {

    private final ManifestTrackingLogService trackingLogService;
    private static final Logger logger = LoggerFactory.getLogger(ManifestTrackingLogController.class);

    @Autowired
    public ManifestTrackingLogController(ManifestTrackingLogService trackingLogService) {
        this.trackingLogService = trackingLogService;
    }

    @GetMapping({"/api/manifests/{trackingNo}/tracking-logs", "/manifests/{trackingNo}/tracking-logs"})
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER', 'CLIENT')")
    public ResponseEntity<ApiResponse<List<ManifestTrackingLogDTO>>> getTrackingLogsByManifest(
            @PathVariable String trackingNo) {
        try {
            List<ManifestTrackingLog> logs = trackingLogService.getTrackingLogsByManifest(trackingNo);
            List<ManifestTrackingLogDTO> logDTOs = logs.stream()
                    .map(ManifestTrackingLogDTO::fromEntity)
                    .collect(Collectors.toList());
            return ResponseEntity.ok(ApiResponse.success("Tracking logs retrieved successfully", logDTOs));
        } catch (Exception e) {
            logger.error("Error fetching tracking logs for manifest {}: {}", trackingNo, e.getMessage());
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("Error fetching tracking logs: " + e.getMessage()));
        }
    }

    @GetMapping({"/api/manifests/{trackingNo}/tracking-logs/status-changes", "/manifests/{trackingNo}/tracking-logs/status-changes"})
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER', 'CLIENT')")
    public ResponseEntity<ApiResponse<List<ManifestTrackingLogDTO>>> getStatusChangeHistory(
            @PathVariable String trackingNo) {
        try {
            List<ManifestTrackingLog> logs = trackingLogService.getStatusChangeHistory(trackingNo);
            List<ManifestTrackingLogDTO> logDTOs = logs.stream()
                    .map(ManifestTrackingLogDTO::fromEntity)
                    .collect(Collectors.toList());
            return ResponseEntity.ok(ApiResponse.success("Status change history retrieved successfully", logDTOs));
        } catch (Exception e) {
            logger.error("Error fetching status change history for manifest {}: {}", trackingNo, e.getMessage());
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("Error fetching status change history: " + e.getMessage()));
        }
    }

    @GetMapping({"/api/manifests/{trackingNo}/tracking-logs/latest", "/manifests/{trackingNo}/tracking-logs/latest"})
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER', 'CLIENT')")
    public ResponseEntity<ApiResponse<ManifestTrackingLogDTO>> getLatestTrackingLog(
            @PathVariable String trackingNo) {
        try {
            Optional<ManifestTrackingLog> latestLog = trackingLogService.getLatestTrackingLog(trackingNo);
            if (latestLog.isPresent()) {
                ManifestTrackingLogDTO logDTO = ManifestTrackingLogDTO.fromEntity(latestLog.get());
                return ResponseEntity.ok(ApiResponse.success("Latest tracking log retrieved successfully", logDTO));
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            logger.error("Error fetching latest tracking log for manifest {}: {}", trackingNo, e.getMessage());
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("Error fetching latest tracking log: " + e.getMessage()));
        }
    }

    @GetMapping({"/api/manifests/{trackingNo}/tracking-logs/count", "/manifests/{trackingNo}/tracking-logs/count"})
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER')")
    public ResponseEntity<ApiResponse<Long>> getTrackingLogCount(@PathVariable String trackingNo) {
        try {
            Long count = trackingLogService.countTrackingLogs(trackingNo);
            return ResponseEntity.ok(ApiResponse.success("Tracking log count retrieved successfully", count));
        } catch (Exception e) {
            logger.error("Error counting tracking logs for manifest {}: {}", trackingNo, e.getMessage());
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("Error counting tracking logs: " + e.getMessage()));
        }
    }

    @GetMapping({"/api/tracking-logs/user/{username}", "/tracking-logs/user/{username}"})
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER')")
    public ResponseEntity<ApiResponse<List<ManifestTrackingLog>>> getTrackingLogsByUser(
            @PathVariable String username) {
        try {
            List<ManifestTrackingLog> logs = trackingLogService.getTrackingLogsByUser(username);
            return ResponseEntity.ok(ApiResponse.success("User tracking logs retrieved successfully", logs));
        } catch (Exception e) {
            logger.error("Error fetching tracking logs for user {}: {}", username, e.getMessage());
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("Error fetching user tracking logs: " + e.getMessage()));
        }
    }

    @GetMapping({"/api/tracking-logs/action/{actionType}", "/tracking-logs/action/{actionType}"})
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER')")
    public ResponseEntity<ApiResponse<List<ManifestTrackingLog>>> getTrackingLogsByAction(
            @PathVariable ManifestTrackingLog.ActionType actionType) {
        try {
            List<ManifestTrackingLog> logs = trackingLogService.getTrackingLogsByActionType(actionType);
            return ResponseEntity.ok(ApiResponse.success("Action tracking logs retrieved successfully", logs));
        } catch (Exception e) {
            logger.error("Error fetching tracking logs for action {}: {}", actionType, e.getMessage());
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("Error fetching action tracking logs: " + e.getMessage()));
        }
    }

    @GetMapping({"/api/tracking-logs/date-range", "/tracking-logs/date-range"})
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER')")
    public ResponseEntity<ApiResponse<List<ManifestTrackingLog>>> getTrackingLogsInDateRange(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endDate) {
        try {
            List<ManifestTrackingLog> logs = trackingLogService.getTrackingLogsInDateRange(startDate, endDate);
            return ResponseEntity.ok(ApiResponse.success("Date range tracking logs retrieved successfully", logs));
        } catch (Exception e) {
            logger.error("Error fetching tracking logs for date range {} to {}: {}", startDate, endDate, e.getMessage());
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("Error fetching date range tracking logs: " + e.getMessage()));
        }
    }

    @GetMapping({"/api/manifests/{trackingNo}/tracking-logs/date-range", "/manifests/{trackingNo}/tracking-logs/date-range"})
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER', 'CLIENT')")
    public ResponseEntity<ApiResponse<List<ManifestTrackingLogDTO>>> getTrackingLogsByManifestAndDateRange(
            @PathVariable String trackingNo,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endDate) {
        try {
            List<ManifestTrackingLog> logs = trackingLogService.getTrackingLogsByManifestAndDateRange(trackingNo, startDate, endDate);
            List<ManifestTrackingLogDTO> logDTOs = logs.stream()
                    .map(ManifestTrackingLogDTO::fromEntity)
                    .collect(Collectors.toList());
            return ResponseEntity.ok(ApiResponse.success("Manifest date range tracking logs retrieved successfully", logDTOs));
        } catch (Exception e) {
            logger.error("Error fetching tracking logs for manifest {} in date range {} to {}: {}", 
                        trackingNo, startDate, endDate, e.getMessage());
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("Error fetching manifest date range tracking logs: " + e.getMessage()));
        }
    }

    @DeleteMapping({"/api/tracking-logs/{logId}", "/tracking-logs/{logId}"})
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Void>> deleteTrackingLog(@PathVariable Long logId) {
        try {
            trackingLogService.deleteTrackingLog(logId);
            return ResponseEntity.ok(ApiResponse.success("Tracking log deleted successfully", null));
        } catch (Exception e) {
            logger.error("Error deleting tracking log {}: {}", logId, e.getMessage());
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("Error deleting tracking log: " + e.getMessage()));
        }
    }

    @DeleteMapping({"/api/manifests/{trackingNo}/tracking-logs", "/manifests/{trackingNo}/tracking-logs"})
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Void>> deleteAllTrackingLogsForManifest(@PathVariable String trackingNo) {
        try {
            trackingLogService.deleteAllTrackingLogsForManifest(trackingNo);
            return ResponseEntity.ok(ApiResponse.success("All tracking logs deleted successfully for manifest", null));
        } catch (Exception e) {
            logger.error("Error deleting all tracking logs for manifest {}: {}", trackingNo, e.getMessage());
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("Error deleting tracking logs: " + e.getMessage()));
        }
    }
} 