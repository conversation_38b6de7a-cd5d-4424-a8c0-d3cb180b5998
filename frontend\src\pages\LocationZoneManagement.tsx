import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Typography,
  Alert,
  Paper,
  CircularProgress,
  IconButton,
  Tooltip,
  useTheme,
  FormControlLabel,
  Switch,
  Snackbar,
  // Divider
} from '@mui/material';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import { useAuth } from '../contexts/AuthContext';
import usePageTitle from '../hooks/usePageTitle';
import AddIcon from '@mui/icons-material/Add';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import CloseIcon from '@mui/icons-material/Close';
import MailOutlineIcon from '@mui/icons-material/MailOutline';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import { LocationZone } from '../types/location';
import locationService from '../services/location.service';

const LocationZoneManagement: React.FC = () => {
  const { currentUser } = useAuth();
  
  // Set page title
  usePageTitle('Location Zones');
  
  const [locationZones, setLocationZones] = useState<LocationZone[]>([]);
  const [loading, setLoading] = useState(true);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedLocationZone, setSelectedLocationZone] = useState<LocationZone | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const theme = useTheme();

  const [formData, setFormData] = useState<LocationZone>({
    id: 0,
    name: '',
    abbreviation: '',
    startPostalCode: null,
    endPostalCode: null,
    specialCases: null,
    description: null,
    isActive: true
  });

  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [confirmAction, setConfirmAction] = useState<() => void>(() => {});
  const [confirmTitle, setConfirmTitle] = useState('');
  const [confirmMessage, setConfirmMessage] = useState('');

  useEffect(() => {
    fetchLocationZones();
  }, [currentUser?.token]);

  const fetchLocationZones = async () => {
    try {
      setLoading(true);
      setError(null);
      
      if (!currentUser?.token) {
        setError('Authentication token is missing');
        setLoading(false);
        return;
      }
      
      const response = await locationService.getAllLocationZones(currentUser.token);
      
      console.log("API Response:", response);
      
      if (response.success && response.data) {
        console.log("Location Zones Data:", response.data);
        
        // Ensure all objects have proper values for all fields
        const sanitizedData = response.data.map(zone => {
          console.log("Processing zone:", zone);
          return {
            ...zone,
            startPostalCode: zone.startPostalCode ?? null,
            endPostalCode: zone.endPostalCode ?? null,
            specialCases: zone.specialCases ?? null,
            description: zone.description ?? null
          };
        });
        
        console.log("Sanitized Data:", sanitizedData);
        setLocationZones(sanitizedData);
      } else {
        setError(response.message || 'Failed to fetch location zones');
      }
    } catch (err: any) {
      console.error('Error fetching location zones:', err);
      setError('An error occurred while fetching location zones: ' + (err.message || 'Unknown error'));
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleNumberChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    const numValue = value === '' ? null : parseInt(value);
    setFormData({ ...formData, [name]: numValue });
  };

  const handleSwitchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setFormData({ ...formData, [name]: checked });
  };

  const handleOpenDialog = (locationZone?: LocationZone) => {
    if (locationZone) {
      setSelectedLocationZone(locationZone);
      setFormData({
        id: locationZone.id,
        name: locationZone.name,
        abbreviation: locationZone.abbreviation ?? '',
        startPostalCode: locationZone.startPostalCode ?? null,
        endPostalCode: locationZone.endPostalCode ?? null,
        specialCases: locationZone.specialCases ?? null,
        description: locationZone.description ?? null,
        isActive: locationZone.isActive
      });
    } else {
      setSelectedLocationZone(null);
      setFormData({
        id: 0,
        name: '',
        abbreviation: '',
        startPostalCode: null,
        endPostalCode: null,
        specialCases: null,
        description: null,
        isActive: true
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setError(null);
  };

  const handleSaveLocationZone = async () => {
    try {
      setLoading(true);
      const token = currentUser?.token;
      if (!token) {
        setError("Authentication token not found");
        setTimeout(() => window.location.href = '/login', 2000);
        return;
      }

      // Ensure data is properly formatted
      const dataToSend = {
        ...formData,
        // Convert empty strings to null
        specialCases: formData.specialCases?.trim() === '' ? null : formData.specialCases,
        description: formData.description?.trim() === '' ? null : formData.description
      };

      let response;
      
      if (selectedLocationZone) {
        response = await locationService.updateLocationZone(
          selectedLocationZone.id,
          dataToSend,
          token
        );
        setSuccessMessage('Location zone updated successfully');
      } else {
        response = await locationService.createLocationZone(
          dataToSend,
          token
        );
        setSuccessMessage('Location zone created successfully');
      }

      if (response.success) {
        handleCloseDialog();
        fetchLocationZones();
      } else {
        setError(response.message);
      }
    } catch (err: any) {
      console.error('Error saving location zone:', err);
      setError(err.response?.data?.message || 'Failed to save location zone');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    try {
      const response = await locationService.deleteLocationZone(
        id,
        currentUser?.token || ''
      );

      if (response.success) {
        setSuccessMessage('Location zone deleted successfully');
        fetchLocationZones();
      } else {
        setError(response.message);
      }
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to delete location zone');
    }
  };

  const handleSuccessClose = () => {
    setSuccessMessage(null);
  };

  const showConfirmDialog = (title: string, message: string, action: () => void) => {
    setConfirmTitle(title);
    setConfirmMessage(message);
    setConfirmAction(() => action);
    setConfirmDialogOpen(true);
  };

  const columns: GridColDef[] = [
    {
      field: 'id',
      headerName: 'ID',
      width: 70,
    },
    {
      field: 'name',
      headerName: 'Name',
      flex: 1,
      minWidth: 150,
    },
    {
      field: 'abbreviation',
      headerName: 'Abbreviation',
      flex: 0.8,
      minWidth: 120,
      renderCell: (params: any) => {
        const value = params.row.abbreviation;
        return (
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center',
            justifyContent: 'center',
            fontWeight: 'bold',
            color: value ? theme.palette.primary.main : theme.palette.text.secondary,
            bgcolor: value ? 'rgba(25, 118, 210, 0.08)' : 'transparent',
            borderRadius: 1,
            px: 1,
            py: 0.5,
            minWidth: '40px'
          }}>
            {value || 'N/A'}
          </Box>
        );
      },
    },
    {
      field: 'startPostalCode',
      headerName: 'Start Postal Code',
      flex: 1,
      minWidth: 120,
      renderCell: (params: any) => {
        const value = params.row.startPostalCode;
        return <span>{value !== null && value !== undefined ? value : 'N/A'}</span>;
      },
    },
    {
      field: 'endPostalCode',
      headerName: 'End Postal Code',
      flex: 1,
      minWidth: 120,
      renderCell: (params: any) => {
        const value = params.row.endPostalCode;
        return <span>{value !== null && value !== undefined ? value : 'N/A'}</span>;
      },
    },
    {
      field: 'specialCases',
      headerName: 'Special Cases',
      flex: 1,
      minWidth: 150,
      renderCell: (params: any) => {
        const value = params.row.specialCases;
        return <span>{value !== null && value !== undefined && value !== '' ? value : 'None'}</span>;
      },
    },
    {
      field: 'description',
      headerName: 'Description',
      flex: 2,
      minWidth: 200,
      renderCell: (params: any) => {
        const value = params.row.description;
        return <span>{value !== null && value !== undefined && value !== '' ? value : 'N/A'}</span>;
      },
    },
    {
      field: 'isActive',
      headerName: 'Active',
      flex: 0.5,
      minWidth: 100,
      renderCell: (params) => (
        <Box sx={{ 
          display: 'flex', 
          alignItems: 'center',
          color: params.value ? theme.palette.success.main : theme.palette.error.main,
          fontWeight: 'bold'
        }}>
          {params.value ? 'Yes' : 'No'}
        </Box>
      ),
    },
    {
      field: 'actions',
      headerName: 'Actions',
      sortable: false,
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Tooltip title="Edit">
            <IconButton 
              size="small" 
              onClick={() => handleOpenDialog(params.row as LocationZone)}
              sx={{ 
                color: theme.palette.primary.main,
                bgcolor: 'rgba(25, 118, 210, 0.08)',
                '&:hover': {
                  bgcolor: 'rgba(25, 118, 210, 0.15)',
                }
              }}
            >
              <EditIcon fontSize="small" />
            </IconButton>
          </Tooltip>
          <Tooltip title="Delete">
            <IconButton 
              size="small" 
              onClick={() => {
                showConfirmDialog(
                  'Delete Location Zone',
                  `Are you sure you want to delete location zone "${params.row.name}"?`,
                  () => handleDelete(params.row.id)
                );
              }}
              sx={{ 
                color: theme.palette.error.main,
                bgcolor: 'rgba(211, 47, 47, 0.08)',
                '&:hover': {
                  bgcolor: 'rgba(211, 47, 47, 0.15)',
                }
              }}
            >
              <DeleteIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        </Box>
      ),
    },
  ];

  const ConfirmDialog: React.FC<{
    open: boolean;
    title: string;
    message: string;
    onConfirm: () => void;
    onCancel: () => void;
  }> = ({ open, title, message, onConfirm, onCancel }) => {
    return (
      <Dialog 
        open={open} 
        onClose={onCancel}
        PaperProps={{
          sx: {
            borderRadius: 2,
            boxShadow: 24,
          },
        }}
      >
        <DialogTitle sx={{ 
          bgcolor: theme.palette.primary.main, 
          color: 'white',
          px: 3,
          py: 2
        }}>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Typography variant="h6">
              {title}
            </Typography>
            <IconButton onClick={onCancel} size="small" sx={{ color: 'white' }}>
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent sx={{ px: 3, py: 3 }}>
          <Typography>{message}</Typography>
        </DialogContent>
        <DialogActions sx={{ px: 3, py: 2 }}>
          <Button onClick={onCancel} variant="outlined">
            Cancel
          </Button>
          <Button 
            onClick={() => {
              onConfirm();
              onCancel();
            }} 
            variant="contained"
            color="primary" 
            autoFocus
          >
            Confirm
          </Button>
        </DialogActions>
      </Dialog>
    );
  };

  return (
    <Box sx={{ 
      p: { xs: 2, sm: 3 },
      height: 'auto',
      display: 'flex',
      flexDirection: 'column',
      maxWidth: '100%',
      mx: 'auto',
      overflow: 'visible'
    }}>
      <Box sx={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center', 
        mb: 2,
        flexWrap: { xs: 'wrap', sm: 'nowrap' },
        gap: 2
      }}>
        <Typography 
          variant="h4" 
          sx={{ 
            fontWeight: 'bold', 
            color: theme.palette.primary.main,
            fontSize: { xs: '1.5rem', sm: '2rem' }
          }}
        >
          Location Zone Management
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => handleOpenDialog()}
          sx={{
            fontWeight: 'bold',
            borderRadius: 2,
            boxShadow: 2,
            px: 3,
            py: 1,
            textTransform: 'none',
            '&:hover': {
              boxShadow: 4,
              bgcolor: theme.palette.primary.dark
            },
          }}
        >
          Add Location Zone
        </Button>
      </Box>

      <Paper sx={{ 
        p: { xs: 2, sm: 3 }, 
        mb: 2, 
        borderLeft: '4px solid #1976d2', 
        bgcolor: 'rgba(25, 118, 210, 0.04)',
        display: { xs: 'none', sm: 'block' },
        borderRadius: 2
      }}>
        <Typography variant="h6" gutterBottom>
          Location Zone Configuration
        </Typography>
        <Typography variant="body1" color="textSecondary">
          Manage location zones used for automatic manifest allocation based on postal codes. 
          Each zone defines a range of postal codes that will be assigned to that location.
        </Typography>
      </Paper>

      {error && (
        <Alert 
          severity="error" 
          sx={{ 
            mb: 2,
            borderRadius: 2,
            '& .MuiAlert-icon': {
              alignItems: 'center'
            }
          }} 
          onClose={() => setError(null)}
        >
          {error}
        </Alert>
      )}

      <Paper sx={{ 
        width: '100%', 
        borderRadius: 2, 
        boxShadow: 2,
        flex: 'none',
        display: 'flex',
        flexDirection: 'column',
        overflow: 'visible',
        height: 'auto',
        minHeight: '200px',
        position: 'relative',
        maxWidth: '100%',
      }}>
        {loading ? (
          <Box sx={{ 
            display: 'flex', 
            justifyContent: 'center', 
            alignItems: 'center', 
            height: '100%', 
            minHeight: '200px' 
          }}>
            <CircularProgress />
          </Box>
        ) : (
          <DataGrid
            rows={locationZones}
            columns={columns}
            getRowId={(row) => row.id}
            initialState={{
              pagination: {
                paginationModel: { pageSize: 10 },
              },
              sorting: {
                sortModel: [{ field: 'name', sort: 'asc' }],
              },
            }}
            pageSizeOptions={[5, 10, 25, 50]}
            autoHeight
            disableRowSelectionOnClick
            sx={{
              '& .MuiDataGrid-columnHeaders': {
                backgroundColor: '#f5f5f5',
                borderBottom: '1px solid rgba(224, 224, 224, 1)',
              },
              '& .MuiDataGrid-row:nth-of-type(even)': {
                backgroundColor: 'rgba(0, 0, 0, 0.02)',
              },
              '& .MuiDataGrid-cell:focus, & .MuiDataGrid-cell:focus-within': {
                outline: 'none',
              },
              '& .MuiDataGrid-columnHeader:focus, & .MuiDataGrid-columnHeader:focus-within': {
                outline: 'none',
              },
            }}
          />
        )}
      </Paper>

      <Dialog 
        open={openDialog} 
        onClose={handleCloseDialog}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 2,
            boxShadow: 24,
          },
        }}
      >
        <DialogTitle sx={{ 
          bgcolor: theme.palette.primary.main, 
          color: 'white',
          px: 3,
          py: 2
        }}>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Typography variant="h6">
              {selectedLocationZone ? 'Edit Location Zone' : 'Create Location Zone'}
            </Typography>
            <IconButton
              aria-label="close"
              onClick={handleCloseDialog}
              sx={{
                color: 'white',
              }}
            >
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent dividers sx={{ px: 3, py: 3 }}>
          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}
          <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', md: '1fr 1fr' }, gap: 2 }}>
            <TextField
              required
              name="name"
              label="Zone Name"
              value={formData.name}
              onChange={handleInputChange}
              fullWidth
              margin="normal"
              helperText="Unique name for the location zone"
              sx={{ gridColumn: '1 / -1' }}
            />
            
            <TextField
              name="abbreviation"
              label="Abbreviation"
              value={formData.abbreviation || ''}
              onChange={handleInputChange}
              fullWidth
              margin="normal"
              inputProps={{ maxLength: 10 }}
              helperText="Short abbreviation for pallet labels (e.g., 'NE', 'W', 'C')"
              sx={{ gridColumn: '1 / -1' }}
            />
            
            <TextField
              name="startPostalCode"
              label="Start Postal Code"
              type="number"
              value={formData.startPostalCode === null ? '' : formData.startPostalCode}
              onChange={handleNumberChange}
              fullWidth
              margin="normal"
              InputProps={{
                startAdornment: (
                  <LocationOnIcon sx={{ color: 'action.active', mr: 1 }} />
                ),
              }}
              helperText="First postal code in the main range"
            />
            
            <TextField
              name="endPostalCode"
              label="End Postal Code"
              type="number"
              value={formData.endPostalCode === null ? '' : formData.endPostalCode}
              onChange={handleNumberChange}
              fullWidth
              margin="normal"
              InputProps={{
                startAdornment: (
                  <LocationOnIcon sx={{ color: 'action.active', mr: 1 }} />
                ),
              }}
              helperText="Last postal code in the main range"
            />
            
            <TextField
              name="specialCases"
              label="Special Cases"
              value={formData.specialCases || ''}
              onChange={handleInputChange}
              fullWidth
              margin="normal"
              sx={{ gridColumn: '1 / -1' }}
              helperText="Additional postal codes or ranges (e.g., '12,53,55' or '13-32,54')"
            />
            
            <TextField
              name="description"
              label="Description"
              value={formData.description || ''}
              onChange={handleInputChange}
              fullWidth
              margin="normal"
              multiline
              rows={2}
              sx={{ gridColumn: '1 / -1' }}
            />
            
            <FormControlLabel
              control={
                <Switch
                  checked={formData.isActive}
                  onChange={handleSwitchChange}
                  name="isActive"
                  color="primary"
                />
              }
              label="Active"
              sx={{ gridColumn: '1 / -1', mt: 1 }}
            />
          </Box>
        </DialogContent>
        <DialogActions sx={{ px: 3, py: 2 }}>
          <Button onClick={handleCloseDialog} variant="outlined">Cancel</Button>
          <Button 
            onClick={handleSaveLocationZone} 
            variant="contained" 
            color="primary"
            disabled={!formData.name.trim()}
          >
            {selectedLocationZone ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>

      <ConfirmDialog
        open={confirmDialogOpen}
        title={confirmTitle}
        message={confirmMessage}
        onConfirm={confirmAction}
        onCancel={() => setConfirmDialogOpen(false)}
      />

      <Snackbar
        open={!!successMessage}
        autoHideDuration={6000}
        onClose={handleSuccessClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert 
          onClose={handleSuccessClose} 
          severity="success" 
          elevation={6}
          variant="filled"
        >
          {successMessage}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default LocationZoneManagement; 