import { formatInTimeZone, toZonedTime } from 'date-fns-tz';

/**
 * Singapore timezone constant
 */
export const SINGAPORE_TIMEZONE = 'Asia/Singapore';

/**
 * Define the time slots used throughout the application
 */
export interface TimeSlot {
  id: string;
  label: string;
  startTime: string;
  endTime: string;
}

export const TIME_SLOTS: TimeSlot[] = [
  {
    id: 'no_time',
    label: 'No specific time',
    startTime: '00:00',
    endTime: '23:59'
  },
  {
    id: 'morning_12',
    label: '09:00 - 12:00',
    startTime: '09:00',
    endTime: '12:00'
  },
  {
    id: 'morning_13',
    label: '09:00 - 13:00',
    startTime: '09:30', // Using 9:30 to differentiate from morning_12
    endTime: '13:00'
  },
  {
    id: 'afternoon',
    label: '14:00 - 17:00',
    startTime: '14:00',
    endTime: '17:00'
  }
];

// --------------------------------------------------
// BASIC DATE UTILITIES
// --------------------------------------------------

/**
 * Normalizes a date by extracting only the date part (YYYY-MM-DD)
 * and creating a new Date object from it to avoid timezone issues
 * @param date The date to normalize
 * @returns A new Date object normalized to midnight UTC
 */
export const normalizeDate = (date: Date): Date => {
  const dateStr = date.toISOString().split('T')[0];
  return new Date(dateStr);
};

/**
 * Calculates the difference in days between two dates
 * @param date1 First date
 * @param date2 Second date
 * @returns Number of days between the dates (positive if date2 is after date1)
 */
export const getDaysDifference = (date1: Date, date2: Date): number => {
  const normalizedDate1 = normalizeDate(date1);
  const normalizedDate2 = normalizeDate(date2);
  
  const diffTime = normalizedDate2.getTime() - normalizedDate1.getTime();
  return Math.round(diffTime / (1000 * 60 * 60 * 24));
};

/**
 * Gets the current date and time in Singapore timezone
 * @returns Date object representing current time in Singapore timezone
 */
export const getSingaporeNow = (): Date => {
  return toZonedTime(new Date(), SINGAPORE_TIMEZONE);
};

/**
 * Converts a local date to Singapore timezone for backend submission
 * @param date The date to convert
 * @returns Date object adjusted to Singapore timezone
 */
export const toSingaporeTime = (date: Date): Date => {
  return toZonedTime(date, SINGAPORE_TIMEZONE);
};

// --------------------------------------------------
// DATE FORMATTING UTILITIES
// --------------------------------------------------

/**
 * Formats a date string to Singapore timezone format (dd MMM yyyy HH:mm)
 * @param dateStr The date string to format
 * @param defaultValue Value to return if dateStr is null/undefined or invalid
 * @returns Formatted date string in Singapore timezone or defaultValue if input is invalid
 */
export const formatDateString = (dateStr?: string | null, defaultValue: string = '-'): string => {
  if (!dateStr) return defaultValue;
  try {
    const date = new Date(dateStr);
    
    // Validate the date
    if (isNaN(date.getTime())) {
      console.warn('Invalid date string provided to formatDateString:', dateStr);
      return defaultValue;
    }
    
    return formatInTimeZone(date, SINGAPORE_TIMEZONE, 'dd MMM yyyy HH:mm');
  } catch (error) {
    console.error('Error formatting date:', error);
    return defaultValue;
  }
};

/**
 * Formats a Date object to Singapore timezone format (dd MMM yyyy HH:mm)
 * @param date The Date object to format
 * @param defaultValue Value to return if date is null/undefined or invalid
 * @returns Formatted date string in Singapore timezone
 */
export const formatDate = (date?: Date | null | string, defaultValue: string = '-'): string => {
  if (!date) return defaultValue;
  try {
    // Convert string dates to Date objects
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    
    // Validate the date
    if (!(dateObj instanceof Date) || isNaN(dateObj.getTime())) {
      console.warn('Invalid date provided to formatDate:', date);
      return defaultValue;
    }
    
    return formatInTimeZone(dateObj, SINGAPORE_TIMEZONE, 'dd MMM yyyy HH:mm');
  } catch (error) {
    console.error('Error formatting date:', error);
    return defaultValue;
  }
};

/**
 * Legacy alias for formatDate to maintain backward compatibility
 */
export const formatDateTime = formatDateString;

/**
 * Formats a Date object to Singapore timezone format WITHOUT time component (dd MMM yyyy)
 * @param date The Date object to format
 * @param defaultValue Value to return if date is null/undefined or invalid
 * @returns Formatted date string in Singapore timezone without time
 */
export const formatDateOnly = (date?: Date | null | string, defaultValue: string = '-'): string => {
  if (!date) return defaultValue;

  try {
    // Convert string dates to Date objects
    const dateObj = typeof date === 'string' ? new Date(date) : date;

    // Validate the date
    if (!(dateObj instanceof Date) || isNaN(dateObj.getTime())) {
      console.warn('Invalid date provided to formatDateOnly:', date);
      return defaultValue;
    }

    return formatInTimeZone(dateObj, SINGAPORE_TIMEZONE, 'dd MMM yyyy');
  } catch (error) {
    console.error('Error formatting date:', error);
    return defaultValue;
  }
};

/**
 * Formats a Date object WITHOUT timezone conversion (dd MMM yyyy)
 * Use this for dates from date pickers that are already in the correct timezone
 * @param date The Date object to format
 * @param defaultValue Value to return if date is null/undefined or invalid
 * @returns Formatted date string without timezone conversion
 */
export const formatDateOnlyLocal = (date?: Date | null, defaultValue: string = '-'): string => {
  if (!date) return defaultValue;

  try {
    // Validate the date
    if (!(date instanceof Date) || isNaN(date.getTime())) {
      console.warn('Invalid date provided to formatDateOnlyLocal:', date);
      return defaultValue;
    }

    // Format without timezone conversion
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                   'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

    const day = date.getDate();
    const month = months[date.getMonth()];
    const year = date.getFullYear();

    return `${day} ${month} ${year}`;
  } catch (error) {
    console.error('Error formatting date locally:', error);
    return defaultValue;
  }
};

/**
 * Format date for export without time component
 * @param dateStr - The date string to format
 * @returns Formatted date string without time (dd MMM yyyy)
 */
export const formatDateForExport = (dateStr?: string | null, defaultValue: string = ''): string => {
  if (!dateStr) return defaultValue;
  
  try {
    const date = new Date(dateStr);
    if (isNaN(date.getTime())) return defaultValue;
    
    return formatInTimeZone(date, SINGAPORE_TIMEZONE, 'dd MMM yyyy');
  } catch (error) {
    console.error('Error formatting date for export:', error);
    return defaultValue;
  }
};

/**
 * Formats a date with timezone indicator
 * @param date The date to format
 * @param defaultValue Value to return if date is null/undefined
 * @returns Formatted date string with timezone indicator
 */
export const formatDateWithTimezone = (date?: Date | null, defaultValue: string = '-'): string => {
  if (!date) return defaultValue;
  try {
    return formatInTimeZone(date, SINGAPORE_TIMEZONE, 'dd MMM yyyy HH:mm (zzz)');
  } catch (error) {
    console.error('Error formatting date with timezone:', error);
    return defaultValue;
  }
};

/**
 * Formats a date as a timestamp for filenames in Singapore timezone (yyyyMMdd_HHmmss)
 * @param date The date to format
 * @returns Formatted timestamp string in Singapore timezone
 */
export const formatTimestamp = (date: Date = new Date()): string => {
  return formatInTimeZone(date, SINGAPORE_TIMEZONE, 'yyyyMMdd_HHmmss');
};

// --------------------------------------------------
// ISO & API DATE FORMATS
// --------------------------------------------------

/**
 * Converts a formatted date string (e.g. "19 Jul 2025") to ISO format WITHOUT time (YYYY-MM-DD)
 * @param dateStr The formatted date string to convert
 * @returns ISO formatted date string without time component
 */
export const formatDateStringToIso = (dateStr: string): string => {
  try {
    // Check if the date is in "dd MMM yyyy" format (e.g., "19 Jul 2025")
    if (dateStr.match(/^\d{1,2}\s+[A-Za-z]{3}\s+\d{4}$/)) {
      // Parse using date-fns-tz to handle Singapore timezone properly
      const parts = dateStr.split(' ');
      const day = parseInt(parts[0], 10);
      const monthStr = parts[1];
      const year = parseInt(parts[2], 10);
      
      // Map month abbreviation to month number (0-indexed)
      const months: {[key: string]: number} = {
        'Jan': 0, 'Feb': 1, 'Mar': 2, 'Apr': 3, 'May': 4, 'Jun': 5,
        'Jul': 6, 'Aug': 7, 'Sep': 8, 'Oct': 9, 'Nov': 10, 'Dec': 11
      };
      
      const month = months[monthStr];
      if (month === undefined) {
        throw new Error(`Invalid month: ${monthStr}`);
      }
      
      // Create date in local timezone - format as YYYY-MM-DD without time
      const isoDate = `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
      return isoDate;
    }
    
    // Fallback to standard Date parsing for other formats
    const date = new Date(dateStr);
    return date.toISOString().split('T')[0]; // Just the date part
  } catch (error) {
    console.error('Error converting date string to ISO:', error, dateStr);
    // Return the original string if parsing fails
    return dateStr;
  }
};

/**
 * Converts a formatted date string (e.g. "25 Jul 2025") to a Date object
 * @param dateStr The formatted date string to convert
 * @returns Date object or null if parsing fails
 */
export const parseDateString = (dateStr: string): Date | null => {
  try {
    // Check if the date is in "dd MMM yyyy" format (e.g., "25 Jul 2025")
    if (dateStr.match(/^\d{1,2}\s+[A-Za-z]{3}\s+\d{4}$/)) {
      const parts = dateStr.split(' ');
      const day = parseInt(parts[0], 10);
      const monthStr = parts[1];
      const year = parseInt(parts[2], 10);

      // Map month abbreviation to month number (0-indexed)
      const months: {[key: string]: number} = {
        'Jan': 0, 'Feb': 1, 'Mar': 2, 'Apr': 3, 'May': 4, 'Jun': 5,
        'Jul': 6, 'Aug': 7, 'Sep': 8, 'Oct': 9, 'Nov': 10, 'Dec': 11
      };

      const month = months[monthStr];
      if (month === undefined) {
        throw new Error(`Invalid month: ${monthStr}`);
      }

      // Create date in Singapore timezone
      return new Date(year, month, day);
    }

    // Fallback to standard Date parsing for other formats
    return new Date(dateStr);
  } catch (error) {
    console.error('Error parsing date string:', error, dateStr);
    return null;
  }
};

/**
 * Converts a Date object to ISO format WITHOUT time (YYYY-MM-DD)
 * @param date The Date object to convert
 * @returns ISO formatted date string without time component
 */
export const formatDateToIso = (date: Date): string => {
  try {
    // Format to ISO and ensure we only use the date part
    return date.toISOString().split('T')[0];
  } catch (error) {
    console.error('Error converting Date to ISO:', error);
    // Return empty string if conversion fails
    return '';
  }
};

/**
 * Formats a date as YYYY-MM-DD for API requests (in Singapore timezone)
 * @param date The date to format
 * @returns Formatted date string in YYYY-MM-DD format
 */
export const formatDateForApi = (date: Date): string => {
  return formatInTimeZone(date, SINGAPORE_TIMEZONE, 'yyyy-MM-dd');
};

/**
 * Converts a date string from backend (Singapore timezone) to a Date object
 * @param dateStr The date string from backend
 * @returns Date object or null if invalid
 */
export const parseBackendDate = (dateStr?: string | null): Date | null => {
  if (!dateStr) return null;
  try {
    // Backend sends dates in "dd MMM yyyy HH:mm" format in Singapore timezone
    const date = new Date(dateStr);
    return date;
  } catch (error) {
    console.error('Error parsing backend date:', error);
    return null;
  }
};

// --------------------------------------------------
// TIME SLOT UTILITIES
// --------------------------------------------------

/**
 * Get time slot information from a date
 * @param dateValue - The date value to analyze
 * @returns Time slot object or null
 */
export const getTimeSlotFromDate = (dateValue: string | Date | null): TimeSlot | null => {
  if (!dateValue) return null;
  
  try {
    // Convert string dates to Date objects
    const date = typeof dateValue === 'string' ? new Date(dateValue) : dateValue;
    
    // Validate the date
    if (!(date instanceof Date) || isNaN(date.getTime())) {
      console.warn('Invalid date provided to getTimeSlotFromDate:', dateValue);
      return null;
    }
    
    const hour = date.getHours();
    const minutes = date.getMinutes();
    
    // Exact time matches for specific time slots
    if (hour === 9) {
      if (minutes === 0) {
        return TIME_SLOTS.find(slot => slot.id === 'morning_12') || null;
      } else if (minutes === 30) {
        return TIME_SLOTS.find(slot => slot.id === 'morning_13') || null;
      }
    } else if (hour === 14 && minutes === 0) {
      return TIME_SLOTS.find(slot => slot.id === 'afternoon') || null;
    }
    
    // Default fallbacks based on hour range
    if (hour === 9) {
      // Default to morning_12 for 9am
      return TIME_SLOTS.find(slot => slot.id === 'morning_12') || null;
    } else if (hour >= 10 && hour < 12) {
      return TIME_SLOTS.find(slot => slot.id === 'morning_12') || null;
    } else if (hour >= 12 && hour < 14) {
      return TIME_SLOTS.find(slot => slot.id === 'morning_13') || null;
    } else if (hour >= 14 && hour < 17) {
      return TIME_SLOTS.find(slot => slot.id === 'afternoon') || null;
    }
    
    return null;
  } catch (error) {
    console.error('Error getting time slot from date:', error);
    return null;
  }
};

/**
 * Get time slot ID from a date
 * @param dateValue - The date value to analyze
 * @returns Time slot ID or null
 */
export const getTimeSlotIdFromDate = (dateValue: string | Date | null): string | null => {
  if (!dateValue) return null;
  
  try {
    // Convert string dates to Date objects
    const date = typeof dateValue === 'string' ? new Date(dateValue) : dateValue;
    
    // Validate the date
    if (!(date instanceof Date) || isNaN(date.getTime())) {
      console.warn('Invalid date provided to getTimeSlotIdFromDate:', dateValue);
      return null;
    }
    
    const hour = date.getHours();
    const minutes = date.getMinutes();

    // If time is 00:00 (start of day), return 'no_time'
    if (hour === 0 && minutes === 0) {
      return 'no_time';
    }

    // Exact time matches for specific time slots
    if (hour === 9) {
      if (minutes === 0) {
        return 'morning_12';
      } else if (minutes === 30) {
        return 'morning_13';
      }
    } else if (hour === 14 && minutes === 0) {
      return 'afternoon';
    }

    // Default fallbacks based on hour range
    if (hour === 9) {
      // Default to morning_12 for 9am
      return 'morning_12';
    } else if (hour >= 10 && hour < 12) {
      return 'morning_12';
    } else if (hour >= 12 && hour < 14) {
      return 'morning_13';
    } else if (hour >= 14 && hour < 17) {
      return 'afternoon';
    }

    // For any other time, default to 'no_time'
    return 'no_time';
  } catch (error) {
    console.error('Error getting time slot ID from date:', error);
    return null;
  }
};

/**
 * Format time slot for display
 * @param timeSlotId - The time slot ID
 * @returns Formatted time slot string
 */
export const formatTimeSlot = (timeSlotId: string | null): string => {
  if (!timeSlotId) return '-';

  // Handle the special case of 'no_time' slot
  if (timeSlotId === 'no_time') return 'No specific time';

  const timeSlot = TIME_SLOTS.find(slot => slot.id === timeSlotId);
  return timeSlot ? timeSlot.label : '-';
};

// --------------------------------------------------
// DELIVERY DATE SPECIFIC UTILITIES
// --------------------------------------------------

/**
 * Format delivery date to show date with time slot
 * @param dateValue - The date value to format
 * @returns Formatted string showing date and time slot
 */
export const formatDeliveryDateTime = (dateValue: string | Date | null): string => {
  if (!dateValue) return 'Not set';
  
  try {
    // Convert string dates to Date objects
    const date = typeof dateValue === 'string' ? new Date(dateValue) : dateValue;
    
    // Validate the date
    if (!(date instanceof Date) || isNaN(date.getTime())) {
      console.warn('Invalid date provided to formatDeliveryDateTime:', dateValue);
      return 'Invalid date';
    }
    
    // Format the date part using timezone-aware function
    const dateStr = formatDateOnly(date);
    
    // Get the time slot and format it
    const timeSlotId = getTimeSlotIdFromDate(date);
    const timeSlotLabel = formatTimeSlot(timeSlotId);
    
    return `${dateStr} (${timeSlotLabel})`;
  } catch (error) {
    console.error('Error formatting delivery date time:', error);
    return 'Invalid date';
  }
};

/**
 * Format delivery date to show only the date part (without time slot)
 * Alias for formatDateOnly but with different default value
 * @param dateValue - The date value to format
 * @returns Formatted date string
 */
export const formatDeliveryDate = (dateValue: Date | null | undefined | string, defaultValue: string = 'Not set'): string => {
  return formatDateOnly(dateValue, defaultValue);
};

/**
 * Creates a Date object with the proper time set based on a time slot ID
 * @param date - The base date to use
 * @param timeSlotId - The time slot ID
 * @returns A new Date with time set according to the time slot
 */
export const createDateWithTimeSlot = (date: Date, timeSlotId: string): Date => {
  const timeSlot = TIME_SLOTS.find(slot => slot.id === timeSlotId);
  if (!timeSlot) return date;

  // For 'no_time' slot, return the date without setting specific time
  if (timeSlotId === 'no_time') {
    const newDate = new Date(date);
    newDate.setHours(0, 0, 0, 0); // Set to start of day
    return newDate;
  }

  const newDate = new Date(date);
  const [hours, minutes] = timeSlot.startTime.split(':').map(Number);
  newDate.setHours(hours, minutes, 0, 0);

  return newDate;
};