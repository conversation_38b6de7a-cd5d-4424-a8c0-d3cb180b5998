package com.wms.controller;

import com.wms.payload.response.MessageResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import com.wms.security.jwt.JwtUtils;

import java.util.HashMap;
import java.util.Map;

@RestController
@CrossOrigin(origins = "*", maxAge = 3600)
public class TestAuthController {

    private static final Logger logger = LoggerFactory.getLogger(TestAuthController.class);
    
    @Autowired
    private JwtUtils jwtUtils;

    @GetMapping({"/api/test-auth/public", "/test-auth/public"})
    public ResponseEntity<?> publicEndpoint() {
        logger.info("Public endpoint accessed");
        return ResponseEntity.ok(new MessageResponse("Public endpoint works!"));
    }

    @GetMapping({"/api/test-auth/login", "/test-auth/login"})
    public ResponseEntity<?> testLoginGet() {
        logger.info("GET request received for login endpoint - redirecting to proper usage info");
        Map<String, Object> response = new HashMap<>();
        response.put("error", "Method not allowed");
        response.put("message", "This endpoint requires a POST request with username and password in the request body");
        response.put("example", Map.of(
            "username", "admin",
            "password", "admin123"
        ));
        return ResponseEntity.status(405).body(response);
    }

    @PostMapping({"/api/test-auth/login", "/test-auth/login"})
    public ResponseEntity<?> testLogin(@RequestBody Map<String, String> loginRequest) {
        logger.info("Test login endpoint accessed with username: {}", loginRequest.get("username"));
        
        // Hard-coded check for demo purposes
        if ("admin".equals(loginRequest.get("username")) && "admin123".equals(loginRequest.get("password"))) {
            // Generate a real JWT token for testing
            String token = jwtUtils.generateTokenFromUsername("admin");
            logger.info("Generated valid JWT token for test login");
            
            Map<String, Object> response = new HashMap<>();
            response.put("token", token);
            response.put("id", 1);
            response.put("username", "admin");
            response.put("email", "<EMAIL>");
            response.put("roles", new String[]{"ROLE_ADMIN"});
            
            logger.info("Test login successful");
            return ResponseEntity.ok(response);
        } else {
            logger.info("Test login failed - invalid credentials");
            return ResponseEntity.status(401).body(new MessageResponse("Invalid credentials"));
        }
    }
    
    @GetMapping({"/api/test-auth/status", "/test-auth/status"})
    public ResponseEntity<?> serviceStatus() {
        logger.info("Service status endpoint accessed");
        
        Map<String, Object> status = new HashMap<>();
        status.put("status", "UP");
        status.put("timestamp", System.currentTimeMillis());
        status.put("message", "Backend service is running");
        
        return ResponseEntity.ok(status);
    }
} 