:root {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

body {
  margin: 0;
  display: flex;
  place-items: center;
  min-width: 320px;
  min-height: 100vh;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  cursor: pointer;
  transition: border-color 0.25s;
}
button:hover {
  border-color: #646cff;
}
button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
  a:hover {
    color: #747bff;
  }
  button {
    background-color: #f9f9f9;
  }
}

/* MUI DatePicker fix for calendar display */
.MuiPickersPopper-root {
  z-index: 9999 !important;
}

.MuiPaper-root.MuiPickersLayout-root {
  z-index: 9999 !important;
  position: relative !important;
  background-color: white !important;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2) !important;
  border-radius: 8px !important;
  overflow: visible !important;
}

/* Fix for date picker modal visibility */
.MuiModal-root {
  z-index: 9999 !important;
}

.MuiCalendarOrClockPicker-root {
  background-color: white !important;
}

/* Date picker popup fix */
.MuiPickersPopper-root {
  z-index: 9999 !important;
  position: fixed !important;
}

.MuiPickersLayout-root {
  z-index: 9999 !important;
  position: fixed !important;
}

.MuiPopover-root {
  z-index: 9999 !important;
}

.MuiDialog-root {
  z-index: 9999 !important;
}

.MuiModal-root {
  z-index: 9999 !important;
}

.MuiPaper-root.MuiPickersLayout-root {
  position: fixed !important;
  z-index: 9999 !important;
}

.MuiYearCalendar-root, 
.MuiMonthCalendar-root, 
.MuiDayCalendar-root {
  z-index: 9999 !important;
  position: relative !important;
}

/* Ensure the date picker has higher z-index than dialog */
.MuiDialog-root .MuiDialog-container .MuiPaper-root {
  z-index: 9999 !important;
}

/* Force the date picker to be visible and on top */
.MuiDateCalendar-root, 
.MuiDateTimePickerToolbar-root,
.MuiClock-root {
  z-index: 10000 !important;
  position: relative !important;
}

/* Make sure backdrop is below the date picker but above other content */
.MuiBackdrop-root {
  z-index: 9998 !important;
}
