import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  IconButton,
  Typography,
  Box,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import PalletManagement from './PalletManagement';
import { Manifest } from '../types/manifest';

interface PalletManagementDialogProps {
  open: boolean;
  onClose: () => void;
  manifest: Manifest | null;
  onPalletOperationComplete?: () => void;
}

const PalletManagementDialog: React.FC<PalletManagementDialogProps> = ({
  open,
  onClose,
  manifest,
  onPalletOperationComplete
}) => {
  if (!manifest) return null;

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          boxShadow: 24,
          maxHeight: '90vh',
        },
      }}
    >
      <DialogTitle
        sx={{
          bgcolor: 'primary.main',
          color: 'white',
          px: 3,
          py: 2,
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}
      >
        <Box>
          <Typography variant="h6">
            Pallet Management
          </Typography>
          <Typography variant="subtitle2" sx={{ opacity: 0.9 }}>
            Manifest: {manifest.trackingNo}
          </Typography>
        </Box>
        <IconButton
          aria-label="close"
          onClick={onClose}
          sx={{
            color: 'white',
          }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <DialogContent sx={{ p: 0 }}>
        <Box sx={{ p: 3 }}>
          <PalletManagement
            manifestTrackingNo={manifest.trackingNo}
            manifestPieces={manifest.pieces}
            manifest={manifest}
            readonly={false}
            onPalletOperationComplete={onPalletOperationComplete}
          />
        </Box>
      </DialogContent>
    </Dialog>
  );
};

export default PalletManagementDialog; 