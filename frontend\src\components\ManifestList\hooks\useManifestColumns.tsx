import { useState, useMemo, useEffect } from 'react';
import { GridColDef, GridRenderCellParams } from '@mui/x-data-grid';
import { Manifest } from '../../../types/manifest';
import IconButton from '@mui/material/IconButton';
import Box from '@mui/material/Box';
import Chip from '@mui/material/Chip';
import Typography from '@mui/material/Typography';
import Tooltip from '@mui/material/Tooltip';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import EventAvailableIcon from '@mui/icons-material/EventAvailable';
import LocalShippingIcon from '@mui/icons-material/LocalShipping';
import ViewInArIcon from '@mui/icons-material/ViewInAr';
import { formatDateTime, formatDeliveryDate, formatTimeSlot, getTimeSlotIdFromDate } from '../../../utils/dateUtils';
import { useNavigate } from 'react-router-dom';
import QuickEditCell from '../../QuickEditCell';

// Helper function for safe value getting
const safeValueGetter = (params: any, accessor: (row: any) => any, defaultValue: any = '') => {
  if (!params || !params.row) return defaultValue;
  try {
    return accessor(params.row) || defaultValue;
  } catch (e) {
    console.error('Error in valueGetter:', e);
    return defaultValue;
  }
};

// Utility function to format column names for display
export const formatColumnName = (column: string): string => {
  return column
    .replace(/([A-Z])/g, ' $1')
    .replace(/^./, (str) => str.toUpperCase())
    .trim();
};

// Helper function to parse dates for sorting
const parseDateForSorting = (dateValue: any): Date | null => {
  if (!dateValue) return null;

  try {
    // Handle different date formats that might come from the backend
    if (typeof dateValue === 'string') {
      // Check if it's in "dd MMM yyyy" format (e.g., "25 Jul 2025")
      if (dateValue.match(/^\d{1,2}\s+[A-Za-z]{3}\s+\d{4}$/)) {
        const parts = dateValue.split(' ');
        const day = parseInt(parts[0], 10);
        const monthStr = parts[1];
        const year = parseInt(parts[2], 10);

        const months: {[key: string]: number} = {
          'Jan': 0, 'Feb': 1, 'Mar': 2, 'Apr': 3, 'May': 4, 'Jun': 5,
          'Jul': 6, 'Aug': 7, 'Sep': 8, 'Oct': 9, 'Nov': 10, 'Dec': 11
        };

        const month = months[monthStr];
        if (month !== undefined) {
          return new Date(year, month, day);
        }
      }
      // Try parsing as ISO date or other standard formats
      return new Date(dateValue);
    }
    return new Date(dateValue);
  } catch (error) {
    console.warn('Error parsing date for sorting:', dateValue, error);
    return null;
  }
};

// Interface for column action handlers
interface ColumnActionHandlers {
  onEdit: (manifest: Manifest) => void;
  onDelete: (trackingNo: string) => void;
  onEditDeliveryDate: (trackingNo: string) => void;
  onEditDeliveryVehicle: (manifest: Manifest) => void;
  onQuickEditRemarks: (manifest: Manifest, field: 'remarks' | 'driverRemarks', newValue: string) => Promise<void>;
  onManagePallets?: (manifest: Manifest) => void; // Optional handler for managing pallets
}

export function useManifestColumns(
  isContainerView: boolean = false,
  actionHandlers: ColumnActionHandlers,
  viewportOptions?: {
    fixedWidth?: number;
    containerWidth?: number;
  }
) {
  // Define default column visibility settings in the new order
  const defaultColumnVisibility: Record<string, boolean> = {
    actions: true,
    location: true,
    internalId: true,
    trackingNo: true,
    customerName: true,
    address: false,
    postalCode: false,
    deliveryDate: true,
    timeSlot: true, // Show time slot by default
    phoneNo: false,
    driverRemarks: true,
    driver: false,
    status: true,
    deliveryVehicle: true, // Show delivery vehicle by default
    pieces: true,
    inboundPieces: true, // Show inbound pieces by default
    actualPalletsCount: true,
    cbm: false,
    remarks: false,
    weight: true,
    deliveredDate: false,
    createdDate: true,
    country: false,
    container: !isContainerView, // Only show container if not in container view
    client: false,
  };

  // Get navigation function from React Router
  const navigate = useNavigate();

  // Define default visible columns based on view type
  const getDefaultVisibleColumns = (): Record<string, boolean> => {
    // Try to load saved preferences from localStorage
    try {
      const saved = localStorage.getItem('manifestColumnVisibility');
      if (saved) {
        const savedColumns = JSON.parse(saved);
        // Make sure the actions column is always visible
        if (savedColumns && typeof savedColumns === 'object') {
          savedColumns.actions = true;
          // Ensure trackingNo is visible for usability
          savedColumns.trackingNo = true;
          return savedColumns;
        }
      }
    } catch (error) {
      console.error('Failed to load column visibility settings', error);
      // If there's an error, remove the corrupted data
      try {
        localStorage.removeItem('manifestColumnVisibility');
      } catch (e) {
        // Ignore error
      }
    }
    
    return { ...defaultColumnVisibility };
  };
  
  const [visibleColumns, setVisibleColumns] = useState<Record<string, boolean>>(getDefaultVisibleColumns());
  

  
  // Get manifest status chip color - improved with more professional colors
  const getStatusChipColor = (status: string): { color: string; backgroundColor: string; border?: string } => {
    switch (status) {
      case 'CREATED':
        return { color: '#1565c0', backgroundColor: '#e3f2fd', border: '1px solid #90caf9' };
      case 'ETA_TO_WAREHOUSE':
        return { color: '#ef6c00', backgroundColor: '#fff3e0', border: '1px solid #ffb74d' };
      case 'ARRIVED':
        return { color: '#2e7d32', backgroundColor: '#e8f5e9', border: '1px solid #81c784' };
      case 'INBOUNDING':
        return { color: '#d84315', backgroundColor: '#fbe9e7', border: '1px solid #ff8a65' };
      case 'INBOUNDED_TO_WAREHOUSE':
        return { color: '#303f9f', backgroundColor: '#e8eaf6', border: '1px solid #7986cb' };
      case 'READY_TO_DELIVER':
        return { color: '#00695c', backgroundColor: '#e0f2f1', border: '1px solid #4db6ac' };
      case 'PENDING_DELIVER':
        return { color: '#512da8', backgroundColor: '#ede7f6', border: '1px solid #9575cd' };
      case 'DELIVERING':
        return { color: '#1b5e20', backgroundColor: '#e1f5fe', border: '1px solid #29b6f6' };
      case 'DELIVERED':
        return { color: '#004d40', backgroundColor: '#e0f7fa', border: '1px solid #26a69a' };
      case 'ON_HOLD':
        return { color: '#d32f2f', backgroundColor: '#ffebee', border: '1px solid #e57373' };
      case 'DISCREPANCY':
        return { color: '#c62828', backgroundColor: '#ffcdd2', border: '1px solid #ef5350' };
      default:
        return { color: '#616161', backgroundColor: '#f5f5f5', border: '1px solid #bdbdbd' };
    }
  };
  
  // Define all possible columns in the new order: Location Zone, Internal ID, Tracking No, Customer Name, Address, Postal code, Delivery Date, Time Slot, Phone Number, Driver, Status, Vehicle Type, Pieces, Inbound pieces, Pallets, CBM, Weight, Delivered date, Created date, Country, Container, Client, Actions
  const columns = useMemo<GridColDef[]>(() => [
    {
      field: 'location',
      headerName: 'Region',
      minWidth: 120,
      resizable: true,
      headerAlign: 'left',
      align: 'left',
    },
    {
      field: 'internalId',
      headerName: 'ID',
      minWidth: 120,
      resizable: true,
      headerAlign: 'left',
      align: 'left',
      // Add custom sorting function for alphanumeric sorting (e.g., A-123, B-456)
      sortComparator: (v1, v2) => {
        const str1 = (v1 || '').toString();
        const str2 = (v2 || '').toString();

        // Extract letter prefix and numbers
        const prefix1 = str1.match(/^[A-Za-z]+/)?.[0] || '';
        const prefix2 = str2.match(/^[A-Za-z]+/)?.[0] || '';

        // If prefixes are different, sort by prefix
        if (prefix1 !== prefix2) {
          return prefix1.localeCompare(prefix2);
        }

        // If prefixes are the same, extract and compare the numbers
        // For format like "MS1227-24", we want to extract the last number after the dash
        const num1 = parseInt(str1.split('-').pop() || '0') || 0;
        const num2 = parseInt(str2.split('-').pop() || '0') || 0;

        return num1 - num2;
      },
    },
    {
      field: 'trackingNo',
      headerName: 'Tracking #',
      minWidth: 150,
      resizable: true,
      headerAlign: 'left',
      align: 'left',
      renderCell: (params: GridRenderCellParams) => {
        if (!params.row || !params.row.trackingNo) {
          return null;
        }

        return (
          <Typography
            variant="body2"
            sx={{
              cursor: 'pointer',
              color: 'primary.main',
              whiteSpace: 'normal',
              wordBreak: 'break-word',
              textAlign: 'left',
              lineHeight: '1.4',
              width: '100%',
              '&:hover': {
                textDecoration: 'underline',
                color: 'primary.dark',
                fontWeight: 'bold'
              }
            }}
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              if (!params.row?.trackingNo) return;
              // Navigate to manifest details page
              navigate(`/manifests/${params.row.trackingNo}`);
            }}
          >
            {params.row.trackingNo}
          </Typography>
        );
      },
    },
    {
      field: 'customerName',
      headerName: 'Cust.',
      minWidth: 150,
      resizable: true,
      headerAlign: 'left',
      align: 'left',
    },
    {
      field: 'address',
      headerName: 'Address',
      minWidth: 250,
      resizable: true,
      headerAlign: 'left',
      align: 'left',
    },
    {
      field: 'postalCode',
      headerName: 'Postal Code',
      minWidth: 120,
      resizable: true,
      headerAlign: 'left',
      align: 'left',
    },
    {
      field: 'deliveryDate',
      headerName: 'Deliver On',
      type: 'date',
      minWidth: 170,
      resizable: true,
      headerAlign: 'left',
      align: 'left',
      sortable: true,
      sortComparator: (v1: any, v2: any) => {
        const date1 = parseDateForSorting(v1);
        const date2 = parseDateForSorting(v2);

        // Convert dates to timestamps, using a very large number for null dates
        // This ensures null dates always sort to the end regardless of sort direction
        const timestamp1 = date1 ? date1.getTime() : Number.MAX_SAFE_INTEGER;
        const timestamp2 = date2 ? date2.getTime() : Number.MAX_SAFE_INTEGER;

        return timestamp1 - timestamp2;
      },
      valueFormatter: (params: any) => {
        if (!params || params.value === undefined || params.value === null) return '';
        return formatDeliveryDate(params.row?.deliveryDate);
      },
      renderCell: (params: any) => {
        const manifest = params.row as Manifest;
        const trackingNo = manifest.trackingNo || '';

        if (!params || !params.row) return null;

        try {
          if (params.row.deliveryDate) {
            const dateStr = formatDeliveryDate(params.row.deliveryDate);
            return (
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  width: '100%'
                }}
                onClick={(e) => e.stopPropagation()} // Prevent row selection
              >
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <EventAvailableIcon sx={{ mr: 1, color: 'grey.600' }} fontSize="small" />
                  <Typography variant="body2">{dateStr}</Typography>
                </Box>
                <Tooltip title="Edit delivery date">
                  <IconButton
                    size="small"
                    onClick={(e) => {
                      e.stopPropagation();
                      if (actionHandlers.onEditDeliveryDate) {
                        actionHandlers.onEditDeliveryDate(trackingNo);
                      }
                    }}
                    sx={{
                      opacity: 0.5,
                      '&:hover': { opacity: 1 },
                      padding: '2px',
                    }}
                  >
                    <EditIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              </Box>
            );
          } else {
            // No delivery date set - show button to set one
            return (
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  width: '100%'
                }}
                onClick={(e) => e.stopPropagation()} // Prevent row selection
              >
                <Typography variant="body2" color="text.secondary">-</Typography>
                <Tooltip title="Set delivery date">
                  <IconButton
                    size="small"
                    onClick={(e) => {
                      e.stopPropagation();
                      if (actionHandlers.onEditDeliveryDate) {
                        actionHandlers.onEditDeliveryDate(trackingNo);
                      }
                    }}
                    sx={{
                      opacity: 0.5,
                      '&:hover': { opacity: 1 },
                      padding: '2px',
                    }}
                  >
                    <EventAvailableIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              </Box>
            );
          }
        } catch (error) {
          console.error('Error rendering delivery date:', error);
          return <Typography variant="body2" color="text.secondary">-</Typography>;
        }
      },
    },
    {
      field: 'timeSlot',
      headerName: 'Time Slot (Cust. door step)',
      type: 'string',
      minWidth: 200,
      resizable: true,
      headerAlign: 'left',
      align: 'left',
      sortable: false,
      valueGetter: (params: any) => {
        if (!params || !params.row) return '';
        // Get time slot from the timeSlot field or extract from deliveryDate
        return params.row.timeSlot || getTimeSlotIdFromDate(params.row.deliveryDate);
      },
      valueFormatter: (params: any) => {
        if (!params || params.value === undefined || params.value === null) return '';
        return formatTimeSlot(params.value);
      },
      renderCell: (params: any) => {
        const manifest = params.row as Manifest;
        const trackingNo = manifest.trackingNo || '';

        if (!params || !params.row) return null;

        try {
          const timeSlotId = params.row.timeSlot || getTimeSlotIdFromDate(params.row.deliveryDate);
          const timeSlotLabel = formatTimeSlot(timeSlotId);

          return (
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                width: '100%'
              }}
              onClick={(e) => e.stopPropagation()} // Prevent row selection
            >
              <Typography variant="body2" sx={{
                color: timeSlotId ? 'text.primary' : 'text.secondary'
              }}>
                {timeSlotLabel}
              </Typography>
              <Tooltip title="Edit delivery date & time slot">
                <IconButton
                  size="small"
                  onClick={(e) => {
                    e.stopPropagation();
                    if (actionHandlers.onEditDeliveryDate) {
                      actionHandlers.onEditDeliveryDate(trackingNo);
                    }
                  }}
                  sx={{
                    opacity: 0.5,
                    '&:hover': { opacity: 1 },
                    padding: '2px',
                  }}
                >
                  <EditIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Box>
          );
        } catch (error) {
          console.error('Error rendering time slot:', error);
          return <Typography variant="body2" color="text.secondary">-</Typography>;
        }
      },
    },
    {
      field: 'phoneNo',
      headerName: 'Phone #',
      minWidth: 120,
      resizable: true,
      headerAlign: 'left',
      align: 'left',
    },
    {
      field: 'driverRemarks',
      headerName: 'For Driver Only',
      minWidth: 300, // Fixed width to prevent loop
      headerAlign: 'left',
      align: 'left',
      cellClassName: 'remarks-cell', // Add custom class for styling
      renderCell: (params: GridRenderCellParams) => {
        const manifest = params.row as Manifest;

        return (
          <div style={{
            width: '100%',
            height: '100%',
            display: 'block' // Changed from flex to block
          }}>
            <QuickEditCell
              value={params.value}
              onSave={(newValue) => actionHandlers.onQuickEditRemarks(manifest, 'driverRemarks', newValue)}
              placeholder="Click to add driver remarks..."
              fieldName="Driver Remarks"
              maxLength={1000}
              maxLines={4}
            />
          </div>
        );
      },
    },
    {
      field: 'driver',
      headerName: 'Driver',
      minWidth: 120,
      resizable: true,
      headerAlign: 'left',
      align: 'left',
      valueGetter: (params: any) => safeValueGetter(params, (row) => row.driver?.username, 'Not Assigned'),
    },
    {
      field: 'status',
      headerName: 'Status',
      minWidth: 150,
      resizable: true,
      headerAlign: 'left',
      align: 'left',
      renderCell: (params: GridRenderCellParams) => {
        if (!params || params.value === undefined || params.value === null) {
          return <Chip label="Unknown" color="default" size="small" variant="outlined" />;
        }
        return (
          <Chip
            label={params.value}
            sx={{
              ...getStatusChipColor(params.value as string),
              fontWeight: 'medium',
              fontSize: '0.75rem',
              height: '24px',
              borderRadius: '12px',
              '& .MuiChip-label': {
                px: 1.5
              }
            }}
            size="small"
            variant="outlined"
          />
        );
      },
    },
    {
      field: 'deliveryVehicle',
      headerName: 'Vehicle Type',
      minWidth: 200,
      resizable: true,
      headerAlign: 'left',
      align: 'left',
      sortable: false,
      renderCell: (params: GridRenderCellParams) => {
        const manifest = params.row as Manifest;

        return (
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              width: '100%',
              justifyContent: 'space-between'
            }}
            onClick={(e) => e.stopPropagation()} // Prevent row selection
          >
            <Box sx={{ display: 'flex', alignItems: 'center', flex: 1 }}>
              <LocalShippingIcon sx={{ mr: 1, color: 'grey.600' }} fontSize="small" />
              <Typography variant="body2" sx={{ flexGrow: 1 }}>
                {params.value || 'Auto-assign'}
              </Typography>
            </Box>
            <Tooltip title="Quick Edit Vehicle">
              <IconButton
                size="small"
                onClick={(event) => {
                  event.stopPropagation();
                  if (actionHandlers.onEditDeliveryVehicle) {
                    actionHandlers.onEditDeliveryVehicle(manifest);
                  }
                }}
                sx={{
                  opacity: 0.5,
                  '&:hover': { opacity: 1 },
                  padding: '2px',
                }}
              >
                <EditIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          </Box>
        );
      },
    },
    {
      field: 'pieces',
      headerName: 'Pcs',
      type: 'number',
      minWidth: 50,
      resizable: true,
      headerAlign: 'left',
      align: 'left',
      renderCell: (params: GridRenderCellParams) => {
        if (params.value === undefined || params.value === null) {
          return null;
        }
        return (
          <Typography variant="body2" sx={{ textAlign: 'left', whiteSpace: 'normal', wordBreak: 'break-word', lineHeight: '1.4', width: '100%' }}>
            {params.value.toLocaleString()}
          </Typography>
        );
      },
    },
    {
      field: 'inboundPieces',
      headerName: 'IB Pcs',
      type: 'number',
      minWidth: 50,
      resizable: true,
      headerAlign: 'left',
      align: 'left',
      renderCell: (params: GridRenderCellParams) => {
        const manifest = params.row as Manifest;
        const inboundPieces = params.value || 0;
        const totalPieces = manifest.pieces || 0;

        // Determine the color based on inbound vs total pieces
        let color = 'text.primary';

        if (inboundPieces === 0) {
          color = 'text.secondary';
        } else if (inboundPieces < totalPieces) {
          color = 'warning.main';
        } else if (inboundPieces === totalPieces) {
          color = 'success.main';
        } else if (inboundPieces > totalPieces) {
          color = 'error.main';
        }

        return (
          <Typography variant="body2" sx={{
            color,
            fontWeight: 'medium',
            textAlign: 'left',
            whiteSpace: 'normal',
            wordBreak: 'break-word',
            lineHeight: '1.4',
            width: '100%'
          }}>
            {inboundPieces.toLocaleString()}
          </Typography>
        );
      },
    },
    {
      field: 'actualPalletsCount',
      headerName: 'Pallets',
      type: 'number',
      minWidth: 100,
      resizable: true,
      headerAlign: 'left',
      align: 'left',
      sortable: false,
      renderCell: (params: GridRenderCellParams) => {
        const manifest = params.row as Manifest;
        const palletCount = params.value || 0;

        return (
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 1,
              width: '100%',
              justifyContent: 'space-between'
            }}
            onClick={(e) => e.stopPropagation()} // Prevent row selection
          >
            <Typography
              variant="body2"
              sx={{
                textAlign: 'left',
                whiteSpace: 'normal',
                wordBreak: 'break-word',
                lineHeight: '1.4',
                minWidth: 'fit-content'
              }}
            >
              {palletCount.toLocaleString()}
            </Typography>

            {/* Manage Pallet Button */}
            <Tooltip title="Manage Pallets">
              <IconButton
                size="small"
                onClick={(event) => {
                  event.stopPropagation(); // Prevent row selection
                  if (actionHandlers.onManagePallets) {
                    actionHandlers.onManagePallets(manifest);
                  }
                }}
                sx={{
                  color: 'success.main',
                  bgcolor: 'rgba(76, 175, 80, 0.08)',
                  '&:hover': {
                    bgcolor: 'rgba(76, 175, 80, 0.15)',
                  },
                  minWidth: '28px',
                  height: '28px'
                }}
              >
                <ViewInArIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          </Box>
        );
      },
    },
    {
      field: 'cbm',
      headerName: 'CBM',
      type: 'number',
      minWidth: 70,
      resizable: true,
      headerAlign: 'left',
      align: 'left',
      renderCell: (params: GridRenderCellParams) => {
        if (params.value === undefined || params.value === null) {
          return null;
        }
        return (
          <Typography variant="body2" sx={{ textAlign: 'left', whiteSpace: 'normal', wordBreak: 'break-word', lineHeight: '1.4', width: '100%' }}>
            {params.value.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
          </Typography>
        );
      },
    },
    {
      field: 'remarks',
      headerName: 'For CS Only',
      minWidth: 300, // Force minimum width
      headerAlign: 'left',
      align: 'left',
      cellClassName: 'remarks-cell', // Add custom class for styling
      renderCell: (params: GridRenderCellParams) => {
        const manifest = params.row as Manifest;

        return (
          <div style={{
            width: '100%',
            height: '100%',
            display: 'flex',
            minWidth: '300px', // Force minimum content width
            maxWidth: '100%'   // Allow full expansion
          }}>
            <QuickEditCell
              value={params.value}
              onSave={(newValue) => actionHandlers.onQuickEditRemarks(manifest, 'remarks', newValue)}
              placeholder="Click to add CS remarks..."
              fieldName="CS Remarks"
              maxLength={1000}
              maxLines={4}
            />
          </div>
        );
      },
    },
    {
      field: 'weight',
      headerName: 'Weight (kg)',
      type: 'number',
      minWidth: 120,
      resizable: true,
      headerAlign: 'left',
      align: 'left',
      renderCell: (params: GridRenderCellParams) => {
        if (params.value === undefined || params.value === null) {
          return null;
        }
        return (
          <Typography variant="body2" sx={{ textAlign: 'left', whiteSpace: 'normal', wordBreak: 'break-word', lineHeight: '1.4', width: '100%' }}>
            {params.value.toLocaleString()}
          </Typography>
        );
      },
    },
    {
      field: 'deliveredDate',
      headerName: 'POD',
      minWidth: 140,
      resizable: true,
      headerAlign: 'left',
      align: 'left',
      sortable: true,
      valueGetter: (params: any) => {
        if (!params || !params.row) return null;
        return parseDateForSorting(params.row.deliveredDate);
      },
      valueFormatter: (params: any) => {
        if (!params || params.value === undefined || params.value === null) return '';
        return formatDateTime(params.value);
      },
      renderCell: (params: any) => {
        if (!params || !params.row || !params.row.deliveredDate) return null;

        try {
          const dateStr = formatDateTime(params.row.deliveredDate);
          return (
            <Typography variant="body2">
              {dateStr}
            </Typography>
          );
        } catch (error) {
          console.error('Error rendering delivered date:', error);
          return null;
        }
      },
    },
    {
      field: 'createdDate',
      headerName: 'Created On',
      minWidth: 140,
      resizable: true,
      headerAlign: 'left',
      align: 'left',
      sortable: true,
      valueGetter: (params: any) => {
        if (!params || !params.row) return null;
        return parseDateForSorting(params.row.createdDate);
      },
      valueFormatter: (params: any) => {
        if (!params || params.value === undefined || params.value === null) return '';
        return formatDateTime(params.value);
      },
      renderCell: (params: any) => {
        if (!params || !params.row || !params.row.createdDate) return null;

        try {
          const dateStr = formatDateTime(params.row.createdDate);
          return (
            <Typography variant="body2">
              {dateStr}
            </Typography>
          );
        } catch (error) {
          console.error('Error rendering created date:', error);
          return null;
        }
      },
    },
    {
      field: 'country',
      headerName: 'Country',
      minWidth: 120,
      resizable: true,
      headerAlign: 'left',
      align: 'left',
    },
    {
      field: 'container',
      headerName: 'Container',
      minWidth: 130,
      resizable: true,
      headerAlign: 'left',
      align: 'left',
      renderCell: (params: GridRenderCellParams) => {
        if (!params.row || !params.row.container) {
          return null;
        }

        try {
          return (
            <Typography
              variant="body2"
              sx={{
                whiteSpace: 'normal',
                wordBreak: 'break-word',
                textAlign: 'left',
                lineHeight: '1.4',
                width: '100%',
              }}
            >
              {params.row.container.containerNo || ''}
            </Typography>
          );
        } catch (err) {
          console.error('Error rendering container:', err);
          return null;
        }
      },
      valueGetter: (params: any) => safeValueGetter(params, (row) => row.container?.containerNo, ''),
    },
    {
      field: 'client',
      headerName: 'Client',
      minWidth: 120,
      resizable: true,
      headerAlign: 'left',
      align: 'left',
      valueGetter: (params: any) => safeValueGetter(params, (row) => row.client?.username, ''),
    },
    {
      field: 'actions',
      headerName: 'Actions Buttons',
      minWidth: 140,
      resizable: true,
      headerAlign: 'left',
      align: 'left',
      sortable: false,
      filterable: false,
      renderCell: (params: GridRenderCellParams) => {
        if (!params || !params.row) return null;

        // Safely get the row data
        const manifest = params.row as Manifest;
        const trackingNo = manifest.trackingNo || '';

        // Skip rendering actions if no tracking number
        if (!trackingNo) return null;

        return (
          <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap', justifyContent: 'flex-start' }}>
            <IconButton
              size="small"
              color="primary"
              onClick={() => actionHandlers.onEdit(manifest)}
              title="Edit manifest"
            >
              <EditIcon fontSize="small" />
            </IconButton>


            <IconButton
              size="small"
              color="error"
              onClick={() => actionHandlers.onDelete(trackingNo)}
              title="Delete manifest"
            >
              <DeleteIcon fontSize="small" />
            </IconButton>
          </Box>
        );
      },
    },
  ], [navigate, visibleColumns]);
  
  const handleColumnToggle = (column: string) => {
    setVisibleColumns(prev => {
      const newState = {
        ...prev,
        [column]: !prev[column]
      };

      // Save to localStorage
      try {
        localStorage.setItem('manifestColumnVisibility', JSON.stringify(newState));
      } catch (error) {
        console.error('Failed to save column visibility to localStorage:', error);
      }

      return newState;
    });
  };
  
  const resetColumnVisibility = () => {
    // Create fresh default columns object in the new order
    const freshDefaultColumns = {
      actions: true,
      location: true,
      internalId: true,
      trackingNo: true,
      customerName: true,
      address: false,
      postalCode: false,
      deliveryDate: true,
      timeSlot: true, // Show time slot by default
      phoneNo: false,
      driverRemarks: false,
      driver: false,
      status: true,
      deliveryVehicle: true, // Show delivery vehicle by default
      pieces: true,
      inboundPieces: true, // Show inbound pieces by default
      actualPalletsCount: true,
      cbm: false,
      remarks: false,
      weight: true,
      deliveredDate: false,
      createdDate: true,
      country: false,
      container: !isContainerView, // Only show container if not in container view
      client: false,
    };
    
    // Set to default columns
    setVisibleColumns({ ...freshDefaultColumns });
    
    // Remove from localStorage instead of saving
    try {
      localStorage.removeItem('manifestColumnVisibility');
    } catch (error) {
      console.error('Failed to remove column visibility from localStorage:', error);
    }
    
    // Return true to indicate success (following ManifestManagement pattern)
    return true;
  };
  
  const selectAllColumns = () => {
    const allSelected = { ...visibleColumns };
    Object.keys(allSelected).forEach(key => {
      allSelected[key] = true;
    });
    
    setVisibleColumns(allSelected);
    
    // Save to localStorage
    try {
      localStorage.setItem('manifestColumnVisibility', JSON.stringify(allSelected));
    } catch (error) {
      console.error('Failed to save column visibility settings', error);
    }
  };
  
  const selectNoColumns = () => {
    const noneSelected = { ...visibleColumns };
    Object.keys(noneSelected).forEach(key => {
      // Always keep actions and trackingNo columns visible for usability
      if (key !== 'actions' && key !== 'trackingNo') {
        noneSelected[key] = false;
      }
    });
    
    setVisibleColumns(noneSelected);
    
    // Save to localStorage
    try {
      localStorage.setItem('manifestColumnVisibility', JSON.stringify(noneSelected));
    } catch (error) {
      console.error('Failed to save column visibility settings', error);
    }
  };
  
  return {
    visibleColumns,
    columns,
    handleColumnToggle,
    resetColumnVisibility,
    selectAllColumns,
    selectNoColumns
  };
} 