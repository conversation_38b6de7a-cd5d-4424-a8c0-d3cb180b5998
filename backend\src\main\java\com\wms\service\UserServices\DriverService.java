package com.wms.service.UserServices;

import com.wms.dto.VehicleDTO;
import com.wms.entity.user.Driver;
import com.wms.entity.user.UserStatus;

import java.util.List;
import java.util.Optional;

public interface DriverService {
    List<Driver> getAllDrivers();
    Optional<Driver> getDriverByUsername(String username);
    Driver createDriver(Driver driver);
    Driver updateDriver(String username, Driver driver);
    Driver deactivateDriver(String username);
    boolean existsByEmail(String email);
    boolean existsByUsername(String username);
    List<VehicleDTO> getVehiclesByDriver(String username);
} 