import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import { LocalizationProvider } from '@mui/x-date-pickers'
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns'
import './index.css'
import './noBorders.css'
import App from './App.tsx'
import { StagewiseToolbar } from '@stagewise/toolbar-react'
import { ReactPlugin } from '@stagewise-plugins/react'

// Add global style for better focus management
const style = document.createElement('style');
style.textContent = `
  /* Ensure focus is visible only when using keyboard navigation */
  *:focus:not(:focus-visible) {
    outline: none;
  }
  
  /* Ensure clicked elements maintain focus */
  button:focus, 
  a:focus,
  [role="button"]:focus,
  input:focus,
  select:focus,
  textarea:focus {
    outline: none;
  }
  
  /* Show focus styles only when using keyboard */
  *:focus-visible {
    outline: 2px solid #1976d2;
    outline-offset: 2px;
  }
`;
document.head.appendChild(style);

// Disable @emotion/react warnings in development
const originalConsoleWarn = console.warn
console.warn = function filterWarnings(msg, ...args) {
  if (typeof msg === 'string' && msg.includes('@emotion/react')) {
    return
  }
  originalConsoleWarn(msg, ...args)
}

// Create stagewise config
const stagewiseConfig = {
  plugins: [ReactPlugin]
};

// Main app rendering with Singapore timezone configuration
createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <LocalizationProvider 
      dateAdapter={AdapterDateFns}
      adapterLocale={undefined} // Use default locale but with custom timezone handling
    >
      <App />
      {/* Render StagewiseToolbar directly in the main app tree - it will only show in dev mode */}
      <StagewiseToolbar config={stagewiseConfig} />
    </LocalizationProvider>
  </StrictMode>,
)
