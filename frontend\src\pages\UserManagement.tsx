import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Typography,
  Alert,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  Paper,
  CircularProgress,
  Divider,
  Tabs,
  Tab,
  Snackbar,
  Stack,
  Chip,
  IconButton,
  InputAdornment,
  Tooltip,
  useTheme,
  DialogContentText,
  Popover,
  FormGroup,
  FormControlLabel,
  Checkbox,
  Toolbar,
  Card,
  CardContent,
} from '@mui/material';
import { DataGrid, GridColDef, GridRenderCellParams } from '@mui/x-data-grid';
import PersonAddIcon from '@mui/icons-material/PersonAdd';
import EditIcon from '@mui/icons-material/Edit';
import BlockIcon from '@mui/icons-material/Block';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import EmailIcon from '@mui/icons-material/Email';
import PhoneIcon from '@mui/icons-material/Phone';
import BusinessIcon from '@mui/icons-material/Business';
import BadgeIcon from '@mui/icons-material/Badge';
import DriveEtaIcon from '@mui/icons-material/DriveEta';
import WorkIcon from '@mui/icons-material/Work';
import PersonIcon from '@mui/icons-material/Person';
import CloseIcon from '@mui/icons-material/Close';
import { Visibility, VisibilityOff } from '@mui/icons-material';
import ViewColumnIcon from '@mui/icons-material/ViewColumn';
import axios from 'axios';
import { useAuth } from '../contexts/AuthContext';
import usePageTitle from '../hooks/usePageTitle';
import API_CONFIG from '../config/api.config';
import UserService from '../services/user.service';
import { Vehicle } from '../types/Vehicle';

// Base API URL
const API_BASE_URL = API_CONFIG.baseUrl;

interface User {
  id: number;
  userNumber: number;
  username: string;
  email: string;
  fullName: string;
  contact: string;
  status: string;
  userType: string;
  roles: string[];
  // Client-specific fields
  companyName?: string;
  designation?: string;
  altDesignation?: string;
  altContact?: string;
  // Driver-specific fields
  licenseNumber?: string;
  // Manager-specific fields
  department?: string;
}

interface ApiResponse<T> {
  success: boolean;
  message: string;
  data: T;
}

const roles = [
  { value: 'ROLE_ADMIN', label: 'Admin' },
  { value: 'ROLE_MANAGER', label: 'Manager' },
  { value: 'ROLE_CLIENT', label: 'Client' },
  { value: 'ROLE_DRIVER', label: 'Driver' },
];

const UserManagement: React.FC = () => {
  const { currentUser } = useAuth();
  
  // Set page title
  usePageTitle('User Management');
  
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [openDialog, setOpenDialog] = useState(false);
  const [viewDetailsDialog, setViewDetailsDialog] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [showPassword, setShowPassword] = useState(false);
  const [filterState, setFilterState] = useState({
    tab: 0,
    search: ''
  });
  const theme = useTheme();
  const [editMode, setEditMode] = useState(false);
  const [editedUserData, setEditedUserData] = useState<User | null>(null);
  
  // Change password state
  const [changePasswordDialog, setChangePasswordDialog] = useState(false);
  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const [formData, setFormData] = useState({
    username: '',
    email: '',
    contact: '',
    password: '',
    fullName: '',
    roles: ['ROLE_CLIENT'],
    companyName: '',
    designation: '',
    altDesignation: '',
    altContact: '',
    licenseNumber: '',
    department: '',
  });

  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [confirmTitle, setConfirmTitle] = useState('');
  const [confirmMessage, setConfirmMessage] = useState('');
  const [confirmAction, setConfirmAction] = useState<() => void>(() => {});

  const [columnMenuAnchor, setColumnMenuAnchor] = useState<null | HTMLElement>(null);
  const [visibleColumns, setVisibleColumns] = useState({
    userNumber: true,
    username: true,
    email: true,
    contact: true,
    status: true,
    userType: true,
    roles: true,
    companyName: true,
    designation: true,
    licenseNumber: true,
    fullName: true,
    department: true,
  });

  // Add global style for removing focus outlines
  useEffect(() => {
    // Create a style element
    const style = document.createElement('style');
    // Add a rule to remove all focus outlines within the DataGrid
    style.innerHTML = `
      .no-cell-focus-outline *:focus,
      .no-cell-focus-outline *:focus-visible,
      .no-cell-focus-outline *:focus-within,
      .no-cell-focus-outline .MuiDataGrid-root *,
      .no-cell-focus-outline .MuiDataGrid-cell,
      .no-cell-focus-outline .MuiDataGrid-row {
        outline: none !important;
        box-shadow: none !important;
      }
    `;
    // Append to document head
    document.head.appendChild(style);
    
    // Clean up on unmount
    return () => {
      document.head.removeChild(style);
    };
  }, []);

  // Update the global styling to include text fields and inputs
  useEffect(() => {
    // Create a style element
    const style = document.createElement('style');
    // Add rules to remove all focus outlines within buttons and inputs
    style.innerHTML = `
      .MuiButton-root:focus,
      .MuiButton-root:focus-visible,
      .MuiButton-root:focus-within,
      .MuiIconButton-root:focus,
      .MuiIconButton-root:focus-visible,
      .MuiIconButton-root:focus-within,
      .MuiInputBase-root:focus,
      .MuiInputBase-root:focus-visible,
      .MuiInputBase-root:focus-within,
      .MuiOutlinedInput-root:focus,
      .MuiOutlinedInput-root:focus-visible,
      .MuiOutlinedInput-root:focus-within,
      .MuiTextField-root:focus,
      .MuiTextField-root:focus-visible,
      .MuiTextField-root:focus-within,
      .MuiSelect-select:focus,
      .MuiSelect-select:focus-visible,
      .MuiTab-root:focus,
      .MuiTab-root:focus-visible,
      .MuiTab-root:focus-within,
      .MuiMenuItem-root:focus,
      .MuiMenuItem-root:focus-visible,
      .MuiMenuItem-root:focus-within,
      .MuiSelect-root:focus,
      .MuiSelect-root:focus-visible,
      .MuiSelect-root:focus-within {
        outline: none !important;
        box-shadow: none !important;
      }
      
      .MuiOutlinedInput-notchedOutline {
        border-color: rgba(0, 0, 0, 0.23) !important;
      }
      
      .MuiInputBase-root.Mui-focused .MuiOutlinedInput-notchedOutline {
        border-color: #1976d2 !important;
        border-width: 1px !important;
      }
    `;
    // Append to document head
    document.head.appendChild(style);
    
    // Clean up on unmount
    return () => {
      document.head.removeChild(style);
    };
  }, []);

  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    if (!currentUser?.token) {
      setError('Authentication required');
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      console.log('Fetching users...');
      const response = await axios.get<ApiResponse<User[]>>(`${API_BASE_URL}/api/admin/users`, {
        headers: {
          'Authorization': `Bearer ${currentUser.token}`
        }
      });
      console.log('API Response:', response);
      console.log('Response data:', response.data);
      setUsers(response.data.data);
    } catch (err: any) {
      console.error('Error details:', err);
      if (err.response) {
        console.error('Error response:', err.response);
        setError(`Failed to fetch users: ${err.response.data?.message || err.message}`);
      } else {
        setError('Failed to fetch users: Unknown error');
      }
      console.error('Error fetching users:', err);
    } finally {
      setLoading(false);
    }
  };

  const fetchUserDetails = async (user: User) => {
    // Add a check to prevent redundant calls
    if (!currentUser?.token || !openDialog) {
      console.log('Skipping API call - dialog not open or no token');
      return;
    }
    
    try {
      // Don't set any loading state that might affect the entire component state
      // Only update state related to the dialog
      
      let detailedUserEndpoint = '';
      
      // Determine the correct API endpoint based on user type
      switch (user.userType) {
        case 'CLIENT':
          detailedUserEndpoint = `${API_BASE_URL}/api/admin/clients/${user.username}`;
          break;
        case 'DRIVER':
          detailedUserEndpoint = `${API_BASE_URL}/api/admin/drivers/${user.username}`;
          break;
        case 'MANAGER':
          detailedUserEndpoint = `${API_BASE_URL}/api/admin/managers/${user.username}`;
          break;
        case 'ADMIN':
          detailedUserEndpoint = `${API_BASE_URL}/api/admin/admins/${user.username}`;
          break;
        default:
          detailedUserEndpoint = `${API_BASE_URL}/api/admin/users/${user.username}`;
      }
      
      console.log(`Fetching detailed user info from: ${detailedUserEndpoint}`);
      
      // Use a local loading state just for this operation
      
      // Make the API call without affecting the global component state
      const response = await axios.get<ApiResponse<any>>(detailedUserEndpoint, {
        headers: {
          'Authorization': `Bearer ${currentUser.token}`
        }
      });
      
      const detailedUser = response.data.data;
      console.log('Detailed user info received successfully');
      
      // Only update form data - nothing else
      // Create a new object to avoid reference issues
      const updatedFormData = {
        // Preserve the current form state
        ...formData,
        // Update with the new data
        username: detailedUser.username || user.username,
        email: detailedUser.email || user.email,
        contact: detailedUser.contact || user.contact,
        roles: detailedUser.roles || user.roles,
      };
      
      // Add role-specific fields based on user type
      switch (user.userType) {
        case 'CLIENT':
          updatedFormData.companyName = detailedUser.companyName || '';
          updatedFormData.designation = detailedUser.designation || '';
          updatedFormData.altDesignation = detailedUser.altDesignation || '';
          updatedFormData.altContact = detailedUser.altContact || '';
          break;
        case 'DRIVER':
          updatedFormData.licenseNumber = detailedUser.licenseNumber || '';
          break;
        case 'MANAGER':
          updatedFormData.fullName = detailedUser.fullName || '';
          updatedFormData.department = detailedUser.department || '';
          break;
    }
      
      // Use a specific update function to prevent cascading updates
      const updateFormDataOnly = () => {
        setFormData(updatedFormData);
  };

      // Update the form data in the next animation frame
      // This decouples it from the current render cycle
      requestAnimationFrame(updateFormDataOnly);
      
    } catch (err: any) {
      console.error('Error fetching detailed user info:', err);
      // Only show the error message, don't modify any other state
      const errorMsg = `Failed to fetch user details: ${err.response?.data?.message || err.message}`;
      console.error(errorMsg);
      
      // Update error state in next animation frame to prevent cascading updates
      requestAnimationFrame(() => {
        setError(errorMsg);
      });
    }
  };

  const handleSubmit = async () => {
    if (!currentUser?.token) {
      setError('Authentication required');
      return;
    }

    // Validate required fields based on role
    const role = formData.roles[0];
    let missingFields: string[] = [];

    // Common required fields
    if (!formData.username) missingFields.push('Username');
    if (!formData.email) missingFields.push('Email');
    if (!formData.contact) missingFields.push('Contact');
    if (!formData.fullName) missingFields.push('Full Name');
    if (!selectedUser && !formData.password) missingFields.push('Password');

    // Role-specific required fields
    switch (role) {
      case 'ROLE_CLIENT':
        if (!formData.companyName) missingFields.push('Company Name');
        if (!formData.designation) missingFields.push('Designation');
        break;
      case 'ROLE_DRIVER':
        if (!formData.licenseNumber) missingFields.push('License Number');
        break;
      case 'ROLE_MANAGER':
        if (!formData.department) missingFields.push('Department');
        break;
    }

    if (missingFields.length > 0) {
      setError(`Please fill in all required fields: ${missingFields.join(', ')}`);
      return;
    }

    try {
      let response;
      
      if (selectedUser) {
        // UPDATING EXISTING USER
        
        // Determine the correct API endpoint based on user type
        let apiEndpoint = '';
        switch (selectedUser.userType) {
          case 'CLIENT':
            apiEndpoint = `${API_BASE_URL}/api/admin/clients/${selectedUser.username}`;
            break;
          case 'DRIVER':
            apiEndpoint = `${API_BASE_URL}/api/admin/drivers/${selectedUser.username}`;
            break;
          case 'MANAGER':
            apiEndpoint = `${API_BASE_URL}/api/admin/managers/${selectedUser.username}`;
            break;
          case 'ADMIN':
            apiEndpoint = `${API_BASE_URL}/api/admin/admins/${selectedUser.username}`;
            break;
          default:
            apiEndpoint = `${API_BASE_URL}/api/admin/users/${selectedUser.username}`;
        }
        
        // Create request data based on user type
        let finalRequestData: any = {
          username: selectedUser.username, // Keep username the same
          email: formData.email,
          contact: formData.contact,
          fullName: formData.fullName,
        };
        
        // Only include password if a new one is provided
        if (formData.password && formData.password.trim() !== '') {
          finalRequestData.password = formData.password;
        }
        
        // Add role-specific fields
        switch (selectedUser.userType) {
          case 'CLIENT':
            finalRequestData.companyName = formData.companyName;
            finalRequestData.designation = formData.designation;
            finalRequestData.altDesignation = formData.altDesignation || '';
            finalRequestData.altContact = formData.altContact || '';
            break;
          case 'DRIVER':
            finalRequestData.licenseNumber = formData.licenseNumber;
            break;
          case 'MANAGER':
            finalRequestData.fullName = formData.fullName;
            finalRequestData.department = formData.department;
            break;
        }
        
        console.log(`Updating user at endpoint: ${apiEndpoint}`, finalRequestData);
        
        response = await axios.put(
          apiEndpoint,
          finalRequestData,
          {
            headers: {
              Authorization: `Bearer ${currentUser.token}`,
              'Content-Type': 'application/json'
            }
          }
        );
      } else {
        // CREATING NEW USER
        
        // Create request data for new user
        const newUserData = {
        username: formData.username,
        email: formData.email,
        password: formData.password,
        contact: formData.contact,
        fullName: formData.fullName,
        roles: [formData.roles[0]]
      };

        // Add role-specific fields
      switch (role) {
        case 'ROLE_CLIENT':
            Object.assign(newUserData, {
            companyName: formData.companyName,
            designation: formData.designation,
            altDesignation: formData.altDesignation || '',
            altContact: formData.altContact || ''
          });
          break;
        case 'ROLE_DRIVER':
            Object.assign(newUserData, {
            licenseNumber: formData.licenseNumber,
          });
          break;
        case 'ROLE_MANAGER':
            Object.assign(newUserData, {
            department: formData.department
          });
          break;
      }

        console.log('Creating new user:', newUserData);
        
        response = await axios.post(
          `${API_BASE_URL}/api/auth/signup`,
          newUserData,
          {
            headers: {
              Authorization: `Bearer ${currentUser.token}`,
              'Content-Type': 'application/json'
            }
          }
        );
      }

      if (response.status === 200 || response.status === 201) {
        // Set success message based on operation type
        setSuccessMessage(selectedUser 
          ? `User ${selectedUser.username} has been successfully updated` 
          : `New user ${formData.username} has been successfully created`);
        
        // Close the dialog but don't reset form state yet (will be done by the Dialog's onExited)
        setOpenDialog(false);
        
        // Use the new function that preserves filters
        updateUsersList(false);
      }
    } catch (error: any) {
      console.error('Full error details:', error);
      if (error.response) {
        console.error('Error response:', error.response);
        console.error('Error response data:', error.response.data);
        setError(error.response.data?.message || 'Failed to save user. Server returned an error.');
      } else if (error.request) {
        console.error('Error request:', error.request);
        setError('Request was made but no response was received');
      } else {
        console.error('Error message:', error.message);
        setError(`Failed to save user: ${error.message}`);
      }
    }
  };

  // Function to handle closing the success message
  const handleSuccessClose = () => {
    setSuccessMessage(null);
  };

  const showConfirmDialog = (title: string, message: string, action: () => void) => {
    // Reset all dialog state
    setConfirmTitle(title);
    setConfirmMessage(message);
    setConfirmAction(() => action); // Use a function to avoid stale closures
    setConfirmDialogOpen(true);
  };

  const handleActivate = async (username: string, userType: string) => {
    if (!currentUser?.token) {
      setError('Authentication required');
      return;
    }

    try {
      console.log(`Attempting to activate user ${username} of type ${userType}`);
      
      // Determine the correct endpoint based on user type
      let apiEndpoint = '';
      switch (userType) {
        case 'CLIENT':
          apiEndpoint = `${API_BASE_URL}/api/admin/clients/${username}/activate`;
          break;
        case 'DRIVER':
          apiEndpoint = `${API_BASE_URL}/api/admin/drivers/${username}/activate`;
          break;
        case 'MANAGER':
          apiEndpoint = `${API_BASE_URL}/api/admin/managers/${username}/activate`;
          break;
        case 'ADMIN':
          apiEndpoint = `${API_BASE_URL}/api/admin/admins/${username}/activate`;
          break;
        default:
          apiEndpoint = `${API_BASE_URL}/api/admin/users/${username}/activate`;
      }

      console.log('Using API endpoint:', apiEndpoint);
      
      // Send activate request
      await axios.put(
        apiEndpoint,
        {},
        {
          headers: {
            'Authorization': `Bearer ${currentUser.token}`,
            'Content-Type': 'application/json'
          }
        }
      );
      
      setSuccessMessage(`User ${username} has been successfully activated`);
      updateUsersList(false);
    } catch (err: any) {
      console.error('Failed to activate user:', err);
      if (err.response) {
        console.error('Response status:', err.response.status);
        console.error('Response data:', err.response.data);
      }
      
      setError(`Failed to activate user: ${err.response?.data?.message || err.message}`);
    }
  };

  const handleDeactivate = async (username: string, userType: string) => {
    if (!currentUser?.token) {
      setError('Authentication required');
      return;
    }

    try {
      console.log(`Attempting to deactivate user ${username} of type ${userType}`);
      
      // Determine the correct endpoint based on user type
      let apiEndpoint = '';
      switch (userType) {
        case 'CLIENT':
          apiEndpoint = `${API_BASE_URL}/api/admin/clients/${username}/deactivate`;
          break;
        case 'DRIVER':
          apiEndpoint = `${API_BASE_URL}/api/admin/drivers/${username}/deactivate`;
          break;
        case 'MANAGER':
          apiEndpoint = `${API_BASE_URL}/api/admin/managers/${username}/deactivate`;
          break;
        case 'ADMIN':
          apiEndpoint = `${API_BASE_URL}/api/admin/admins/${username}/deactivate`;
          break;
        default:
          apiEndpoint = `${API_BASE_URL}/api/admin/users/${username}/deactivate`;
      }

      console.log('Using API endpoint:', apiEndpoint);
      
      // Send deactivate request
      await axios.put(
        apiEndpoint,
        {},
        {
          headers: {
            'Authorization': `Bearer ${currentUser.token}`,
            'Content-Type': 'application/json'
          }
        }
      );
      
      setSuccessMessage(`User ${username} has been successfully deactivated`);
      updateUsersList(false);
    } catch (err: any) {
      console.error('Failed to deactivate user:', err);
      if (err.response) {
        console.error('Response status:', err.response.status);
        console.error('Response data:', err.response.data);
      }
      
      setError(`Failed to deactivate user: ${err.response?.data?.message || err.message}`);
    }
  };

  const getColumnsForRole = (role: string): GridColDef<User>[] => {
    // Define common column properties
    const commonProps = {
      sortable: true,
      filterable: false,
      disableColumnMenu: true,
      hideable: false,
      headerAlign: 'left' as const,
      align: 'left' as const,
      // Remove flex to allow columns to size to their content
      minWidth: 100,
    };

    const baseColumns: GridColDef<User>[] = [
      { 
        ...commonProps,
        field: 'userNumber', 
        headerName: 'ID', 
        width: 100,
        renderCell: (params: GridRenderCellParams) => (
          <Typography variant="body2" fontWeight="bold">
            #{params.value}
          </Typography>
        )
      },
      { 
        ...commonProps,
        field: 'username', 
        headerName: 'Username', 
        width: 150,
        renderCell: (params: GridRenderCellParams<User>) => (
          <Box 
            sx={{ 
              display: 'flex', 
              alignItems: 'center',
              cursor: 'pointer',
              '&:hover': {
                color: theme.palette.primary.main,
                textDecoration: 'underline',
              }
            }}
            onClick={(e) => {
              e.stopPropagation();
              handleViewUserDetails(params.row);
            }}
          >
            <PersonIcon sx={{ mr: 1, color: theme.palette.primary.main }} />
            <Typography variant="body2">{params.value}</Typography>
          </Box>
        )
      },
      { 
        ...commonProps,
        field: 'email', 
        headerName: 'Email', 
        width: 220,
        renderCell: (params: GridRenderCellParams) => (
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <EmailIcon sx={{ mr: 1, color: theme.palette.grey[600] }} fontSize="small" />
            <Typography variant="body2">{params.value}</Typography>
          </Box>
        )
      },
      { 
        ...commonProps,
        field: 'contact', 
        headerName: 'Contact', 
        width: 150,
        renderCell: (params: GridRenderCellParams) => (
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <PhoneIcon sx={{ mr: 1, color: theme.palette.grey[600] }} fontSize="small" />
            <Typography variant="body2">{params.value}</Typography>
          </Box>
        )
      },
      {
        ...commonProps,
        field: 'userType',
        headerName: 'Role',
        width: 130,
        renderCell: (params: GridRenderCellParams) => {
          // Define color based on role
          let color;
          switch (params.value) {
            case 'ADMIN':
              color = theme.palette.primary.main;
              break;
            case 'MANAGER':
              color = theme.palette.success.main;
              break;
            case 'CLIENT':
              color = theme.palette.info.main;
              break;
            case 'DRIVER':
              color = theme.palette.warning.main;
              break;
            default:
              color = theme.palette.grey[600];
          }
          
          return (
            <Typography 
              variant="body2" 
              sx={{ 
                fontWeight: 500,
                color: color
              }}
            >
              {params.value}
            </Typography>
          );
        }
      },
      { 
        ...commonProps,
        field: 'status', 
        headerName: 'Status', 
        width: 120,
        renderCell: (params: GridRenderCellParams) => (
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Chip
              label={params.value}
              size="small"
              color={params.value === 'ACTIVE' ? 'success' : 'error'}
              icon={params.value === 'ACTIVE' ? <CheckCircleIcon /> : <BlockIcon />}
              sx={{ fontWeight: 'bold' }}
            />
          </Box>
        )
      },
    ];

    const roleSpecificColumns: GridColDef<User>[] = [];

    switch (role) {
      case 'CLIENT':
        roleSpecificColumns.push(
          { 
            ...commonProps,
            field: 'companyName', 
            headerName: 'Company', 
            width: 180,
            renderCell: (params: GridRenderCellParams) => (
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <BusinessIcon sx={{ mr: 1, color: theme.palette.grey[600] }} fontSize="small" />
                <Typography variant="body2">{params.value || '-'}</Typography>
              </Box>
            )
          },
          { 
            ...commonProps,
            field: 'designation', 
            headerName: 'Designation', 
            width: 150,
            renderCell: (params: GridRenderCellParams) => (
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Typography variant="body2">{params.value || '-'}</Typography>
              </Box>
            )
          },
        );
        break;
      case 'DRIVER':
        roleSpecificColumns.push(
          { 
            ...commonProps,
            field: 'licenseNumber', 
            headerName: 'License', 
            width: 150,
            renderCell: (params: GridRenderCellParams) => (
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <BadgeIcon sx={{ mr: 1, color: theme.palette.grey[600] }} fontSize="small" />
                <Typography variant="body2">{params.value || '-'}</Typography>
              </Box>
            )
          }
        );
        break;
      case 'MANAGER':
        roleSpecificColumns.push(
          { 
            ...commonProps,
            field: 'fullName', 
            headerName: 'Full Name', 
            width: 150,
            renderCell: (params: GridRenderCellParams) => (
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Typography variant="body2">{params.value || '-'}</Typography>
              </Box>
            )
          },
          { 
            ...commonProps,
            field: 'department', 
            headerName: 'Department', 
            width: 150,
            renderCell: (params: GridRenderCellParams) => (
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <WorkIcon sx={{ mr: 1, color: theme.palette.grey[600] }} fontSize="small" />
                <Typography variant="body2">{params.value || '-'}</Typography>
              </Box>
            )
          }
        );
        break;
    }

    const actionColumn: GridColDef<User> = {
      field: 'actions',
      headerName: 'Actions',
      width: 320,
      minWidth: 320,
      hideable: false,
      sortable: false,
      filterable: false,
      disableColumnMenu: true,
      headerAlign: 'left',
      align: 'left',
      renderCell: (params: GridRenderCellParams<User>) => {
        return (
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Stack direction="row" spacing={1}>
          <Button
                size="small"
            variant="contained"
            color="primary"
                onClick={(e) => {
                  // Prevent any events that might trigger refresh
                  if (e) {
                    e.preventDefault();
                    e.stopPropagation();
                    if (e.nativeEvent) {
                      e.nativeEvent.preventDefault();
                      e.nativeEvent.stopImmediatePropagation();
                    }
                  }
                  
                  // Store row data locally to avoid referencing grid state
                  const userData = { ...params.row };
                  
                  // Use a single animation frame to batch all state updates
                  requestAnimationFrame(() => {
                    // Set user data
                    setSelectedUser(userData);
                    
                    // Set form data
                    setFormData({
                      username: userData.username,
                      email: userData.email,
                      contact: userData.contact,
                      password: '',
                      fullName: userData.fullName || '',
                      roles: userData.roles,
                      companyName: userData.companyName || '',
                      designation: userData.designation || '',
                      altDesignation: userData.altDesignation || '',
                      altContact: userData.altContact || '',
                      licenseNumber: userData.licenseNumber || '',
                      department: userData.department || '',
                    });
                    
                    // Open dialog
                    setOpenDialog(true);
                    
                    // Delay the API call to ensure it doesn't interfere with dialog opening
                    setTimeout(() => {
                      console.log('Making API call after dialog is open');
                      fetchUserDetails(userData);
                    }, 100);
                  });
                  
                  // Return false to prevent default
                  return false;
                }}
                sx={{ 
                  minWidth: '60px',
                  '&:focus, &:focus-visible': {
                    outline: 'none',
                    boxShadow: 'none'
                  }
                }}
                type="button"
                disableRipple
          >
            Edit
          </Button>
          
          <Button
            size="small"
            variant="outlined"
            color="secondary"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              handleOpenChangePassword(params.row);
            }}
            sx={{ 
              minWidth: '100px',
              '&:focus, &:focus-visible': {
                outline: 'none',
                boxShadow: 'none'
              }
            }}
            type="button"
            disableRipple
          >
            Change Password
          </Button>
              
          {params.row.status === 'ACTIVE' ? (
            <Button
              size="small"
                  variant="contained"
                  color="error"
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    
                    // Show the confirmation dialog with the proper action
                    showConfirmDialog(
                      'Deactivate User',
                      `Are you sure you want to deactivate user ${params.row.username}? They will no longer be able to log in.`,
                      () => handleDeactivate(params.row.username, params.row.userType)
                    );
                  }}
                  sx={{ 
                    minWidth: '90px',
                    '&:focus, &:focus-visible': {
                      outline: 'none',
                      boxShadow: 'none'
                    }
                  }}
                  type="button"
                  disableRipple
            >
              Deactivate
            </Button>
          ) : (
            <Button
                  size="small"
              variant="contained"
              color="success"
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    
                    // Show the confirmation dialog with the proper action
                    showConfirmDialog(
                      'Activate User',
                      `Are you sure you want to activate user ${params.row.username}? They will be able to log in again.`,
                      () => handleActivate(params.row.username, params.row.userType)
                    );
                  }}
                  sx={{ 
                    minWidth: '90px',
                    '&:focus, &:focus-visible': {
                      outline: 'none',
                      boxShadow: 'none'
                    }
                  }}
                  type="button"
                  disableRipple
            >
              Activate
            </Button>
          )}
            </Stack>
        </Box>
        );
      },
    };

    return [...baseColumns, ...roleSpecificColumns, actionColumn];
  };

  const getFilteredUsers = () => {
    let filtered = users;
    
    // Apply search filtering
    if (filterState.search) {
      const lowercasedSearch = filterState.search.toLowerCase();
      filtered = filtered.filter(user => 
        user.username.toLowerCase().includes(lowercasedSearch) ||
        user.email.toLowerCase().includes(lowercasedSearch) ||
        user.contact.toLowerCase().includes(lowercasedSearch)
      );
    }
    
    // Apply tab filtering
    switch (filterState.tab) {
      case 0: // All Users
        return filtered;
      case 1: // Admin
        return filtered.filter(user => user.userType === 'ADMIN');
      case 2: // Manager
        return filtered.filter(user => user.userType === 'MANAGER');
      case 3: // Client
        return filtered.filter(user => user.userType === 'CLIENT');
      case 4: // Driver
        return filtered.filter(user => user.userType === 'DRIVER');
      default:
        return filtered;
    }
  };

  const getCurrentColumns = () => {
    let columns: GridColDef<User>[] = [];
    
    switch (filterState.tab) {
      case 0: // All Users
        columns = [...getColumnsForRole('ALL')];
        break;
      case 1: // Admin
        columns = [...getColumnsForRole('ADMIN')];
        break;
      case 2: // Manager
        columns = [...getColumnsForRole('MANAGER')];
        break;
      case 3: // Client
        columns = [...getColumnsForRole('CLIENT')];
        break;
      case 4: // Driver
        columns = [...getColumnsForRole('DRIVER')];
        break;
      default:
        columns = [...getColumnsForRole('ALL')];
    }
    
    // Make sure each column has these properties
    return columns.map(column => ({
      ...column,
      // Ensure every column is visible and properly configured
      disableColumnMenu: true,
      hideable: false,
    }));
  };

  const renderRoleSpecificFields = () => {
    const role = formData.roles[0];
    
    switch (role) {
      case 'ROLE_CLIENT':
        return (
          <>
            <Divider sx={{ my: 2 }}>Client Details</Divider>
            <TextField
              margin="dense"
              label="Company Name"
              fullWidth
              value={formData.companyName}
              onChange={(e) => setFormData({ ...formData, companyName: e.target.value })}
              required
            />
            <TextField
              margin="dense"
              label="Designation"
              fullWidth
              value={formData.designation}
              onChange={(e) => setFormData({ ...formData, designation: e.target.value })}
              required
            />
            <TextField
              margin="dense"
              label="Alternative Designation"
              fullWidth
              value={formData.altDesignation}
              onChange={(e) => setFormData({ ...formData, altDesignation: e.target.value })}
            />
            <TextField
              margin="dense"
              label="Alternative Contact"
              fullWidth
              value={formData.altContact}
              onChange={(e) => setFormData({ ...formData, altContact: e.target.value })}
            />
          </>
        );
        
      case 'ROLE_DRIVER':
        return (
          <>
            <Divider sx={{ my: 2 }}>Driver Details</Divider>
            <TextField
              margin="dense"
              label="License Number"
              fullWidth
              value={formData.licenseNumber}
              onChange={(e) => setFormData({ ...formData, licenseNumber: e.target.value })}
              required
            />
          </>
        );
        
      case 'ROLE_MANAGER':
        return (
          <>
            <Divider sx={{ my: 2 }}>Manager Details</Divider>
            
            <TextField
              margin="dense"
              label="Department"
              fullWidth
              value={formData.department}
              onChange={(e) => setFormData({ ...formData, department: e.target.value })}
              required
            />
          </>
        );
        
      case 'ROLE_ADMIN':
        return null;
        
      default:
        return null;
    }
  };

  const handleTogglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  // Create a new function for updating users without resetting filters
  const updateUsersList = async (shouldShowLoading = false) => {
    if (!currentUser?.token) {
      setError('Authentication required');
      return;
    }

    try {
      // Only show loading if explicitly requested
      if (shouldShowLoading) {
        setLoading(true);
      }
      
      setError(null);
      console.log('Updating users list without changing filters...');
      
      const response = await axios.get<ApiResponse<User[]>>(`${API_BASE_URL}/api/admin/users`, {
        headers: {
          'Authorization': `Bearer ${currentUser.token}`
        }
      });
      
      // Use requestAnimationFrame to update the users state without causing a full re-render
      requestAnimationFrame(() => {
        // Update users without affecting the filter state
        setUsers(response.data.data);
        
        // If successful, turn off loading indicator
        if (shouldShowLoading) {
          setLoading(false);
        }
      });
    } catch (err: any) {
      console.error('Error updating users list:', err);
      
      // Only show error if it's a critical failure
      if (err.response && err.response.status >= 500) {
        setError(`Failed to update users: ${err.response.data?.message || err.message}`);
      }
      
      // Always turn off loading if it was on
      if (shouldShowLoading) {
        setLoading(false);
      }
    }
  };

  const handleColumnMenuClick = (event: React.MouseEvent<HTMLElement>) => {
    setColumnMenuAnchor(event.currentTarget);
  };

  const handleColumnMenuClose = () => {
    setColumnMenuAnchor(null);
  };

  const handleColumnToggle = (column: string) => {
    setVisibleColumns(prev => ({
      ...prev,
      [column]: !prev[column as keyof typeof prev]
    }));
  };

  const handleCloseDialog = (event?: React.MouseEvent) => {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }
    setOpenDialog(false);
    // The form data and selected user will be reset by the Dialog's onExited callback
  };

  // Function to handle viewing user details
  const handleViewUserDetails = (user: User) => {
    setSelectedUser(user);
    setEditedUserData(null);
    setEditMode(false);
    
    // Delay the API call to ensure it doesn't interfere with dialog opening
    setTimeout(() => {
      fetchUserDetails(user);
    }, 100);
    
    setViewDetailsDialog(true);
  };
  
  // Function to transition to edit mode within the details dialog
  const handleEditInDetailsDialog = () => {
    if (selectedUser) {
      setEditedUserData({...selectedUser});
      setEditMode(true);
    }
  };
  
  // Function to cancel edit mode
  const handleCancelEdit = () => {
    setEditMode(false);
    setEditedUserData(null);
  };
  
  // Function to save user edits from the details dialog
  const handleSaveUserEdit = async () => {
    if (!selectedUser || !editedUserData || !currentUser?.token) {
      setError('Cannot save user - missing data');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Create request data based on user type
      let finalRequestData: any = {
        username: selectedUser.username, // Keep username the same
        email: editedUserData.email,
        contact: editedUserData.contact,
        fullName: editedUserData.fullName,
      };
      
      // Add role-specific fields
      switch (selectedUser.userType) {
        case 'CLIENT':
          finalRequestData.companyName = editedUserData.companyName;
          finalRequestData.designation = editedUserData.designation;
          finalRequestData.altDesignation = editedUserData.altDesignation || '';
          finalRequestData.altContact = editedUserData.altContact || '';
          break;
        case 'DRIVER':
          finalRequestData.licenseNumber = editedUserData.licenseNumber;
          break;
        case 'MANAGER':
          finalRequestData.fullName = editedUserData.fullName;
          finalRequestData.department = editedUserData.department;
          break;
      }
      
      // Determine the correct API endpoint based on user type
      let apiEndpoint = '';
      switch (selectedUser.userType) {
        case 'CLIENT':
          apiEndpoint = `${API_BASE_URL}/api/admin/clients/${selectedUser.username}`;
          break;
        case 'DRIVER':
          apiEndpoint = `${API_BASE_URL}/api/admin/drivers/${selectedUser.username}`;
          break;
        case 'MANAGER':
          apiEndpoint = `${API_BASE_URL}/api/admin/managers/${selectedUser.username}`;
          break;
        case 'ADMIN':
          apiEndpoint = `${API_BASE_URL}/api/admin/admins/${selectedUser.username}`;
          break;
        default:
          apiEndpoint = `${API_BASE_URL}/api/admin/users/${selectedUser.username}`;
      }

      const response = await axios.put(
        apiEndpoint,
        finalRequestData,
        {
          headers: {
            Authorization: `Bearer ${currentUser.token}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (response.status === 200 || response.status === 201) {
        // Update the user in the local list
        setUsers(users.map(u => 
          u.username === selectedUser.username ? {...u, ...editedUserData} : u
        ));

        // Update the selected user with the new data
        setSelectedUser({...selectedUser, ...editedUserData});
        
        // Exit edit mode
        setEditMode(false);
        setEditedUserData(null);
        
        // Show success message
        setSuccessMessage(`User ${selectedUser.username} has been successfully updated`);
        
        // Refresh the user list to get the latest data
        updateUsersList(false);
      } else {
        setError('Failed to update user');
      }
    } catch (err: any) {
      console.error('Error updating user:', err);
      if (err.response) {
        console.error('Error response:', err.response);
        console.error('Error response data:', err.response.data);
        setError(err.response.data?.message || 'Failed to save user. Server returned an error.');
      } else {
        setError(`Failed to update user: ${err.message}`);
      }
    } finally {
      setLoading(false);
    }
  };
  
  // Function to close the details dialog
  const handleCloseDetailsDialog = () => {
    setViewDetailsDialog(false);
    setEditMode(false);
    setEditedUserData(null);
  };

  // Function to handle deactivate/activate from details dialog
  const handleStatusChangeFromDetails = () => {
    if (!selectedUser) return;
    
    const isActive = selectedUser.status === 'ACTIVE';
    const action = isActive ? handleDeactivate : handleActivate;
    
    showConfirmDialog(
      isActive ? 'Deactivate User' : 'Activate User',
      `Are you sure you want to ${isActive ? 'deactivate' : 'activate'} user ${selectedUser.username}? ${isActive ? 'They will no longer be able to log in.' : 'They will be able to log in again.'}`,
      () => action(selectedUser.username, selectedUser.userType)
    );
  };

  // Change Password Functions
  const handleOpenChangePassword = (user: User) => {
    setSelectedUser(user);
    setPasswordData({
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    });
    setChangePasswordDialog(true);
  };

  const handleCloseChangePassword = () => {
    setChangePasswordDialog(false);
    // Don't clear selectedUser here as it's needed for the user details dialog
    setPasswordData({
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    });
    setShowCurrentPassword(false);
    setShowNewPassword(false);
    setShowConfirmPassword(false);
  };

  const handleChangePassword = async () => {
    if (!selectedUser || !currentUser?.token) {
      setError('Authentication required');
      return;
    }

    // Validation
    if (!passwordData.currentPassword) {
      setError('Current password is required');
      return;
    }

    if (!passwordData.newPassword) {
      setError('New password is required');
      return;
    }

    if (passwordData.newPassword.length < 6) {
      setError('New password must be at least 6 characters long');
      return;
    }

    if (passwordData.newPassword !== passwordData.confirmPassword) {
      setError('New password and confirm password do not match');
      return;
    }

    if (passwordData.currentPassword === passwordData.newPassword) {
      setError('New password must be different from current password');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const response = await axios.put(
        `${API_BASE_URL}/api/admin/users/${selectedUser.username}/change-password`,
        {
          currentPassword: passwordData.currentPassword,
          newPassword: passwordData.newPassword
        },
        {
          headers: {
            Authorization: `Bearer ${currentUser.token}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (response.status === 200) {
        setSuccessMessage(`Password for user ${selectedUser.username} has been successfully changed`);
        handleCloseChangePassword();
      }
    } catch (err: any) {
      console.error('Error changing password:', err);
      if (err.response) {
        setError(err.response.data?.message || 'Failed to change password. Server returned an error.');
      } else {
        setError(`Failed to change password: ${err.message}`);
      }
    } finally {
      setLoading(false);
    }
  };

  const DriverVehiclesSection = ({ username }: { username: string }) => {
    const [vehicles, setVehicles] = useState<Vehicle[]>([]);
    const [loadingVehicles, setLoadingVehicles] = useState(true);
    const [vehicleError, setVehicleError] = useState<string | null>(null);

    useEffect(() => {
      fetchVehicles();
    }, [username, currentUser?.token]);

    const fetchVehicles = async () => {
      if (!currentUser?.token) {
        setVehicleError('Authentication required');
        setLoadingVehicles(false);
        return;
      }

      try {
        setLoadingVehicles(true);
        setVehicleError(null);
        const response = await UserService.getDriverVehicles(username, currentUser.token);
        setVehicles(response.data);
      } catch (err: any) {
        console.error('Error fetching vehicles:', err);
        setVehicleError('Failed to fetch assigned vehicles');
      } finally {
        setLoadingVehicles(false);
      }
    };

    const getVehicleColumns = (): GridColDef<Vehicle>[] => {
      return [
        {
          field: 'id',
          headerName: 'ID',
          width: 80,
          renderCell: (params: GridRenderCellParams) => (
            <Typography variant="body2" fontWeight="bold">
              #{params.value}
            </Typography>
          )
        },
        {
          field: 'licensePlate',
          headerName: 'License Plate',
          width: 150,
          renderCell: (params: GridRenderCellParams) => (
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <DriveEtaIcon sx={{ mr: 1, color: theme.palette.grey[600] }} fontSize="small" />
              <Typography variant="body2">{params.value}</Typography>
            </Box>
          )
        },
        {
          field: 'vehicleTypeName',
          headerName: 'Type',
          width: 150,
          renderCell: (params: GridRenderCellParams) => (
            <Typography variant="body2">{params.value}</Typography>
          )
        },
        {
          field: 'status',
          headerName: 'Status',
          width: 120,
          renderCell: (params: GridRenderCellParams) => (
            <Chip
              label={params.value}
              size="small"
              color={params.value === 'AVAILABLE' ? 'success' : params.value === 'IN_USE' ? 'warning' : 'error'}
              sx={{ fontWeight: 'bold' }}
            />
          )
        }
      ];
    };

    return (
      <Box sx={{ mt: 2 }}>
        <Divider sx={{ mb: 2 }} />
        <Typography variant="subtitle1" fontWeight="bold" color="text.secondary" gutterBottom>
          Assigned Vehicles
        </Typography>
        
        {loadingVehicles ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 2 }}>
            <CircularProgress size={24} />
          </Box>
        ) : vehicleError ? (
          <Alert severity="error" sx={{ mb: 2 }}>{vehicleError}</Alert>
        ) : vehicles.length === 0 ? (
          <Alert severity="info" sx={{ mb: 2 }}>No vehicles assigned to this driver.</Alert>
        ) : (
          <Box sx={{ height: 300, width: '100%' }}>
            <DataGrid
              rows={vehicles}
              columns={getVehicleColumns()}
              getRowId={(row) => row.id!}
              disableRowSelectionOnClick
              hideFooter={vehicles.length <= 5}
              sx={{
                '& .MuiDataGrid-cell': {
                  borderBottom: '1px solid #f0f0f0',
                },
                '& .MuiDataGrid-columnHeaders': {
                  backgroundColor: '#f5f5f5',
                  borderBottom: '1px solid rgba(224, 224, 224, 1)',
                },
              }}
            />
          </Box>
        )}
      </Box>
    );
  };

  return (
    <Box sx={{ p: { xs: 2, sm: 3 } }}>
      <Box sx={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center', 
        mb: 3 
      }}>
        <Typography variant="h4" sx={{ fontWeight: 'bold', color: theme.palette.primary.main }}>
          User Management
        </Typography>
        <Button 
          variant="contained" 
          color="primary" 
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            
            // Reset form for new user
            setSelectedUser(null);
            setFormData({
              username: '',
              email: '',
              contact: '',
              password: '',
              fullName: '',
              roles: ['ROLE_CLIENT'],
              companyName: '',
              designation: '',
              altDesignation: '',
              altContact: '',
              licenseNumber: '',
              department: '',
            });
            
            setOpenDialog(true);
          }}
          startIcon={<PersonAddIcon />}
          sx={{ 
            borderRadius: 2,
            px: 3,
            boxShadow: 2,
            '&:hover': {
              boxShadow: 4
            },
            '&:focus, &:focus-visible': {
              outline: 'none',
              boxShadow: 4
            }
          }}
          type="button"
          disableRipple
        >
          Add User
        </Button>
      </Box>

      {/* Welcome message */}
      <Paper sx={{ p: 3, mb: 3, borderLeft: '4px solid #1976d2', bgcolor: 'rgba(25, 118, 210, 0.04)' }}>
        <Typography variant="h6" gutterBottom>
          User Management Dashboard
        </Typography>
        <Typography variant="body1" color="textSecondary">
          View, add, update, and manage all users in the system. Use the tabs below to filter by user type.
        </Typography>
      </Paper>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Success message snackbar */}
      <Snackbar 
        open={!!successMessage} 
        autoHideDuration={6000} 
        onClose={handleSuccessClose}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert 
          onClose={handleSuccessClose} 
          severity="success" 
          sx={{ width: '100%' }}
        >
          {successMessage}
        </Alert>
      </Snackbar>

      <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' }, gap: 2, mb: 3 }}>
        <Paper sx={{ flexGrow: 1, borderRadius: 2, boxShadow: 'none', border: '1px solid rgba(224, 224, 224, 0.5)' }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', p: 1 }}>
            <Tabs
              value={filterState.tab}
              onChange={(_, newValue) => setFilterState(prev => ({...prev, tab: newValue}))}
              variant="scrollable"
              scrollButtons="auto"
              sx={{
                '& .MuiTab-root': { 
                  fontWeight: 500,
                  py: 1.5,
                  px: 3,
                  minWidth: 'auto',
                  textTransform: 'none',
                  fontSize: '0.9rem',
                  color: 'text.secondary',
                  '&:focus, &:focus-visible': {
                    outline: 'none'
                  }
                },
                '& .Mui-selected': {
                  color: theme.palette.primary.main,
                  fontWeight: 'bold'
                },
                '& .MuiTabs-indicator': {
                  height: 3,
                  borderRadius: '3px 3px 0 0',
                },
              }}
            >
              <Tab label="All Users" />
              <Tab label="Admins" />
              <Tab label="Managers" />
              <Tab label="Clients" />
              <Tab label="Drivers" />
            </Tabs>
          </Box>
        </Paper>
        
        <TextField
          placeholder="Search users..."
          variant="outlined"
          size="small"
          value={filterState.search}
          onChange={(e) => setFilterState(prev => ({...prev, search: e.target.value}))}
          sx={{ 
            width: { xs: '100%', md: '300px' },
            bgcolor: 'white',
            borderRadius: 1,
            '& .MuiOutlinedInput-root': {
              borderRadius: 1,
              '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                borderColor: theme.palette.primary.main,
                borderWidth: '1px'
              },
              '&:hover .MuiOutlinedInput-notchedOutline': {
                borderColor: 'rgba(0, 0, 0, 0.42)'
              }
            },
            '& .MuiInputBase-root': {
              '&:focus, &:focus-visible, &:focus-within': {
                outline: 'none',
                boxShadow: 'none'
              }
            }
          }}
          slots={{
            input: undefined
          }}
          slotProps={{
            input: {
              startAdornment: (
                <InputAdornment position="start">
                  <PersonIcon color="action" />
                </InputAdornment>
              ),
              endAdornment: filterState.search && (
                <InputAdornment position="end">
                  <IconButton size="small" onClick={() => setFilterState(prev => ({...prev, search: ''}))}>
                    <CloseIcon fontSize="small" />
                  </IconButton>
                </InputAdornment>
              ),
            }
          }}
        />
      </Box>

      <Popover
        open={Boolean(columnMenuAnchor)}
        anchorEl={columnMenuAnchor}
        onClose={handleColumnMenuClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
        PaperProps={{
          sx: {
            mt: 1,
            p: 2,
            borderRadius: 2,
            boxShadow: 3,
            minWidth: 200,
          },
        }}
      >
        <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 'bold' }}>
          Visible Columns
        </Typography>
        <FormGroup>
          {Object.entries(visibleColumns).map(([column, isVisible]) => (
            <FormControlLabel
              key={column}
              control={
                <Checkbox
                  checked={isVisible}
                  onChange={() => handleColumnToggle(column)}
                  size="small"
                />
              }
              label={column.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
              sx={{
                '& .MuiFormControlLabel-label': {
                  fontSize: '0.875rem',
                },
              }}
            />
          ))}
        </FormGroup>
      </Popover>

      <Paper sx={{ width: '100%', borderRadius: 2, boxShadow: 2 }}>
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%', minHeight: '200px' }}>
            <CircularProgress />
          </Box>
        ) : error ? (
          <Alert severity="error">{error}</Alert>
        ) : users.length === 0 ? (
          <Alert severity="info">No users found. Try changing the filter or add a new user.</Alert>
        ) : (
          <div style={{ width: '100%' }}>
            <Toolbar
              sx={{
                pl: { sm: 2 },
                pr: { xs: 1, sm: 1 },
                borderBottom: '1px solid rgba(224, 224, 224, 1)',
                bgcolor: 'background.paper',
              }}
            >
              <Typography
                sx={{ flex: '1 1 100%' }}
                variant="h6"
                component="div"
              >
                User List
              </Typography>
              <Tooltip title="Column Settings">
                <IconButton onClick={handleColumnMenuClick}>
                  <ViewColumnIcon />
                </IconButton>
              </Tooltip>
            </Toolbar>
            <DataGrid
              rows={getFilteredUsers()}
              columns={getCurrentColumns().filter(col => visibleColumns[col.field as keyof typeof visibleColumns])}
              getRowId={(row) => row.username}
              initialState={{
                pagination: {
                  paginationModel: { pageSize: 10 }
                },
                sorting: {
                  sortModel: [{ field: 'username', sort: 'asc' }],
                },
              }}
              pageSizeOptions={[10, 25, 50]}
              disableColumnMenu={false}
              disableRowSelectionOnClick
              density="standard"
              autoHeight={true}
              getEstimatedRowHeight={() => 60}
              getRowHeight={() => 'auto'}
              className="no-borders-datagrid"
              sx={{
                width: '100%',
                '&, & *': {
                  outline: 'none !important',
                },
                '& *:focus, & *:focus-visible, & *:focus-within': {
                  outline: 'none !important',
                  border: 'none !important',
                },
                '& .MuiDataGrid-virtualScroller': {
                  outline: 'none !important',
                  overflowX: 'auto !important',
                  '&::-webkit-scrollbar': {
                    height: '8px',
                  },
                  '&::-webkit-scrollbar-track': {
                    background: '#f1f1f1',
                    borderRadius: '4px',
                  },
                  '&::-webkit-scrollbar-thumb': {
                    background: '#888',
                    borderRadius: '4px',
                    '&:hover': {
                      background: '#555',
                    },
                  },
                },
                '& .MuiDataGrid-root .MuiDataGrid-cell:focus-within': {
                  outline: 'none !important',
                },
                '& .MuiDataGrid-row.Mui-selected': {
                  backgroundColor: 'rgba(25, 118, 210, 0.08)',
                  border: 'none !important',
                  outline: 'none !important',
                },
                '& .MuiDataGrid-cell.Mui-selected': {
                  outline: 'none !important',
                  border: 'none !important',
                  borderColor: 'transparent !important',
                },
                '& .MuiDataGrid-cell:focus, & .MuiDataGrid-row:focus': {
                  outline: 'none !important',
                },
                '& .MuiDataGrid-cell': {
                  borderBottom: '1px solid #f0f0f0',
                  padding: '8px 16px',
                  display: 'flex',
                  alignItems: 'center',
                  overflow: 'visible',
                  minHeight: '48px',
                  maxHeight: 'unset !important',
                  whiteSpace: 'normal',
                  lineHeight: '1.43',
                  fontSize: '0.875rem',
                  color: 'rgba(0, 0, 0, 0.87)',
                  '&:focus': {
                    outline: 'none !important',
                  },
                  '&:focus-within': {
                    outline: 'none !important',
                  },
                },
                '& .MuiDataGrid-row:focus-within': {
                  outline: 'none !important',
                },
                '& .MuiDataGrid-row:focus-visible': {
                  outline: 'none !important',
                },
                '& .MuiDataGrid-withBorderColor': {
                  borderColor: '#f0f0f0 !important',
                },
                '& .MuiDataGrid-cell--editing': {
                  boxShadow: 'none !important',
                  border: 'none !important',
                },
                '& [role="cell"]:focus, & [role="cell"]:focus-within': {
                  outline: 'none !important',
                },
                '@supports selector(:has(*))': {
                  '& .MuiDataGrid-row:has(.MuiDataGrid-cellCheckbox.Mui-checked)': {
                    outline: 'none !important',
                    border: 'none !important',
                  }
                },
                '& .MuiDataGrid-cell:focus-visible': {
                  outline: 'none !important',
                },
                '& .MuiDataGrid-row:hover': {
                  backgroundColor: 'rgba(0, 0, 0, 0.04)',
                  cursor: 'default'
                },
                '& .MuiDataGrid-columnHeaders': {
                  backgroundColor: '#f5f5f5',
                  borderBottom: '1px solid rgba(224, 224, 224, 1)',
                  '& .MuiDataGrid-columnHeader': {
                    '&:focus, &:focus-within': {
                      outline: 'none !important',
                    },
                  },
                },
                '& .MuiDataGrid-main': {
                  width: '100%'
                },
                '& .MuiDataGrid-columnHeaderTitleContainer, & .MuiDataGrid-cellContent': {
                  width: 'auto', 
                  whiteSpace: 'nowrap',
                  overflow: 'visible',
                },
                '& .MuiDataGrid-columnHeader': {
                  visibility: 'visible !important',
                  whiteSpace: 'normal',
                  paddingLeft: 1,
                  paddingRight: 1,
                },
                '& .MuiDataGrid-columnHeader, & .MuiDataGrid-cell': {
                  borderRight: '1px solid rgba(224, 224, 224, 0.3)',
                  '&:last-child': {
                    borderRight: 'none',
                  },
                  padding: '0 16px',
                  whiteSpace: 'normal',
                  overflow: 'visible',
                  textOverflow: 'clip',
                },
                border: '1px solid rgba(224, 224, 224, 1)',
                borderRadius: 1,
                '& .MuiDataGrid-columnHeaderTitle': {
                  fontWeight: 600,
                  fontSize: '0.875rem',
                  color: 'rgba(0, 0, 0, 0.87)',
                  textTransform: 'none',
                  letterSpacing: '0.01071em',
                },
                '& .MuiDataGrid-row': {
                  minHeight: '48px !important',
                  maxHeight: 'unset !important',
                },
                '& .MuiDataGrid-columnHeader[data-field="actions"]': {
                  visibility: 'visible !important'
                },
                '& .MuiDataGrid-footerContainer': {
                  borderTop: '1px solid rgba(224, 224, 224, 1)',
                  backgroundColor: '#fafafa',
                  position: 'sticky',
                  bottom: 0,
                  zIndex: 1,
                },
                '& .MuiTablePagination-root': {
                  color: 'rgba(0, 0, 0, 0.87)',
                },
                '& .MuiTablePagination-selectLabel, & .MuiTablePagination-displayedRows': {
                  margin: 0,
                  fontSize: '0.875rem',
                },
                '& .MuiTablePagination-select': {
                  paddingLeft: 1,
                  paddingRight: 1,
                  fontSize: '0.875rem',
                },
                '& .MuiTablePagination-actions': {
                  marginLeft: 1,
                },
              }}
            />
          </div>
        )}
      </Paper>

      <Dialog 
        open={openDialog} 
        onClose={(_, reason) => {
          if (reason !== 'backdropClick' && reason !== 'escapeKeyDown') return;
          handleCloseDialog();
        }}
        maxWidth="sm" 
        fullWidth
        slotProps={{
          transition: {
            onExited: () => {
              // Reset form data only after the exit animation is complete
              setSelectedUser(null);
              setFormData({
                username: '',
                email: '',
                contact: '',
                password: '',
                fullName: '',
                roles: ['ROLE_CLIENT'],
                companyName: '',
                designation: '',
                altDesignation: '',
                altContact: '',
                licenseNumber: '',
                department: '',
              });
            }
          },
          paper: { 
            sx: { 
              borderRadius: 2,
              boxShadow: 24,
              maxHeight: '90vh'
            } 
          } 
        }}
      >
        <DialogTitle sx={{ 
          bgcolor: theme.palette.primary.main, 
          color: 'white',
          px: 3,
          py: 2
        }}>
          <Box display="flex" alignItems="center">
            {selectedUser ? <EditIcon sx={{ mr: 1 }} /> : <PersonAddIcon sx={{ mr: 1 }} />}
            <Typography variant="h6">
              {selectedUser ? 'Edit User' : 'Add New User'}
            </Typography>
          </Box>
        </DialogTitle>
        <IconButton
          sx={{
            position: 'absolute',
            right: 8,
            top: 8,
            color: 'white'
          }}
          onClick={handleCloseDialog}
        >
          <CloseIcon />
        </IconButton>
        <DialogContent sx={{ px: 3, py: 2 }}>
          <Box sx={{ mb: 3, mt: 1 }}>
            <Typography variant="subtitle1" fontWeight="bold" color="text.secondary" gutterBottom>
              Basic Information
            </Typography>
            <Divider />
          </Box>
          
          <TextField
            autoFocus
            margin="dense"
            label="Username"
            fullWidth
            value={formData.username}
            onChange={(e) => setFormData({ ...formData, username: e.target.value })}
            disabled={!!selectedUser}
            required
            slotProps={{
              input: {
                startAdornment: (
                  <InputAdornment position="start">
                    <PersonIcon color="action" />
                  </InputAdornment>
                ),
              }
            }}
            sx={{ mb: 2 }}
          />
          
          <TextField
            margin="dense"
            label="Full Name"
            fullWidth
            value={formData.fullName}
            onChange={(e) => setFormData({ ...formData, fullName: e.target.value })}
            required
            slotProps={{
              input: {
                startAdornment: (
                  <InputAdornment position="start">
                    <PersonIcon color="action" />
                  </InputAdornment>
                ),
              }
            }}
            sx={{ mb: 2 }}
          />
          
          <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
          <TextField
            margin="dense"
            label="Email"
            type="email"
            fullWidth
            value={formData.email}
            onChange={(e) => setFormData({ ...formData, email: e.target.value })}
            required
              slotProps={{
                input: {
                  startAdornment: (
                    <InputAdornment position="start">
                      <EmailIcon color="action" />
                    </InputAdornment>
                  ),
                }
              }}
          />
            
          <TextField
            margin="dense"
            label="Contact"
            fullWidth
            value={formData.contact}
            onChange={(e) => setFormData({ ...formData, contact: e.target.value })}
            required
              slotProps={{
                input: {
                  startAdornment: (
                    <InputAdornment position="start">
                      <PhoneIcon color="action" />
                    </InputAdornment>
                  ),
                }
              }}
            />
          </Box>
          
          <TextField
            margin="dense"
            label="Password"
            type={showPassword ? 'text' : 'password'}
            fullWidth
            value={formData.password}
            onChange={(e) => setFormData({ ...formData, password: e.target.value })}
            required={!selectedUser}
            slotProps={{
              input: {
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      onClick={handleTogglePasswordVisibility}
                      edge="end"
                    >
                      {showPassword ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  </InputAdornment>
                ),
              }
            }}
            sx={{ mb: 2 }}
          />
          
          <FormControl fullWidth margin="dense" sx={{ mb: 3 }}>
            <InputLabel>Role</InputLabel>
            <Select
              value={formData.roles[0]}
              label="Role"
              onChange={(e) => setFormData({ ...formData, roles: [e.target.value as string] })}
              MenuProps={{
                sx: {
                  zIndex: 10001,
                  '& .MuiPaper-root': {
                    zIndex: 10001
                  }
                }
              }}
              sx={{
                '&:focus, &:focus-visible': {
                  outline: 'none'
                },
                '& .MuiOutlinedInput-notchedOutline': {
                  borderColor: 'rgba(0, 0, 0, 0.23)'
                },
                '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                  borderColor: theme.palette.primary.main,
                  borderWidth: '1px'
                },
                '&:hover .MuiOutlinedInput-notchedOutline': {
                  borderColor: theme.palette.primary.main
                }
              }}
            >
              {roles.map((role) => (
                <MenuItem 
                  key={role.value} 
                  value={role.value}
                  sx={{
                    '&:focus, &:focus-visible': {
                      outline: 'none'
                    },
                    '&:hover': {
                      backgroundColor: theme.palette.action.hover
                    }
                  }}  
                >
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Chip 
                      size="small" 
                      label={role.label} 
                      sx={{ mr: 1, fontWeight: 'bold' }} 
                    />
                    <Typography>{role.label}</Typography>
                  </Box>
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          
          {renderRoleSpecificFields()}
        </DialogContent>
        <DialogActions sx={{ px: 3, py: 2, bgcolor: '#f9fafc' }}>
          <Button 
            onClick={handleCloseDialog}
            variant="outlined"
            color="inherit"
            sx={{ 
              borderRadius: 2,
              '&:focus, &:focus-visible': {
                outline: 'none',
                boxShadow: 'none'
              }
            }}
          >
            Cancel
          </Button>
          <Button 
            onClick={handleSubmit} 
            variant="contained" 
            color="primary"
            sx={{ 
              borderRadius: 2, 
              px: 3,
              '&:focus, &:focus-visible': {
                outline: 'none',
                boxShadow: 'none'
              }
            }}
          >
            {selectedUser ? 'Update' : 'Add'}
          </Button>
        </DialogActions>
      </Dialog>

      <Dialog
        open={confirmDialogOpen}
        onClose={() => setConfirmDialogOpen(false)}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogTitle id="alert-dialog-title">{confirmTitle}</DialogTitle>
        <DialogContent>
          <DialogContentText id="alert-dialog-description">
            {confirmMessage}
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button 
            onClick={() => {
              setConfirmDialogOpen(false);
              confirmAction();
            }} 
            color="primary"
            variant="contained"
            sx={{
              '&:focus, &:focus-visible': {
                outline: 'none',
                boxShadow: 'none'
              }
            }}
          >
            Confirm
          </Button>
          <Button 
            onClick={() => {
              setConfirmDialogOpen(false);
            }} 
            color="inherit" 
            variant="outlined"
            autoFocus
            sx={{
              '&:focus, &:focus-visible': {
                outline: 'none',
                boxShadow: 'none'
              }
            }}
          >
            Cancel
          </Button>
        </DialogActions>
      </Dialog>

      {/* User Details Dialog */}
      <Dialog 
        open={viewDetailsDialog} 
        onClose={handleCloseDetailsDialog}
        maxWidth="sm" 
        fullWidth
        slotProps={{
          paper: { 
            sx: { 
              borderRadius: 2,
              boxShadow: 24,
              maxHeight: '90vh'
            } 
          } 
        }}
      >
        <DialogTitle sx={{ 
          bgcolor: theme.palette.primary.main, 
          color: 'white',
          px: 3,
          py: 2
        }}>
          <Box display="flex" alignItems="center" justifyContent="space-between">
            <Box display="flex" alignItems="center">
              <PersonIcon sx={{ mr: 1 }} />
              <Typography variant="h6">
                {editMode ? 'Edit User' : 'User Details'}
              </Typography>
            </Box>
            <Box>
              {!editMode && (
                <Tooltip title="Edit User">
                  <IconButton
                    aria-label="edit"
                    onClick={handleEditInDetailsDialog}
                    sx={{ color: 'white', mr: 1 }}
                  >
                    <EditIcon />
                  </IconButton>
                </Tooltip>
              )}
              <IconButton
                aria-label="close"
                onClick={handleCloseDetailsDialog}
                sx={{ color: 'white' }}
              >
                <CloseIcon />
              </IconButton>
            </Box>
          </Box>
        </DialogTitle>
        <DialogContent sx={{ px: 3, py: 2 }}>
          {selectedUser && (
            <>
              <Box sx={{ mb: 3, mt: 1 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="h6" fontWeight="bold">
                    {selectedUser.username}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    ID: #{selectedUser.userNumber}
                  </Typography>
                </Box>
                <Chip
                  label={selectedUser.status}
                  size="small"
                  color={selectedUser.status === 'ACTIVE' ? 'success' : 'error'}
                  icon={selectedUser.status === 'ACTIVE' ? <CheckCircleIcon /> : <BlockIcon />}
                  sx={{ fontWeight: 'bold', mr: 1 }}
                />
                <Chip
                  label={selectedUser.userType}
                  size="small"
                  color="primary"
                  sx={{ fontWeight: 'bold' }}
                />
              </Box>
              
              <Divider sx={{ mb: 2 }} />
              
              <Typography variant="subtitle1" fontWeight="bold" color="text.secondary" gutterBottom>
                Basic Information
              </Typography>
              
              {!editMode ? (
                <Box component="div" sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr' }, gap: 2, mb: 3 }}>
                  <Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <PersonIcon sx={{ mr: 1, color: theme.palette.grey[600] }} fontSize="small" />
                      <Typography variant="body2" color="text.secondary">Full Name:</Typography>
                    </Box>
                    <Typography variant="body1">{selectedUser.fullName}</Typography>
                  </Box>
                  <Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <EmailIcon sx={{ mr: 1, color: theme.palette.grey[600] }} fontSize="small" />
                      <Typography variant="body2" color="text.secondary">Email:</Typography>
                    </Box>
                    <Typography variant="body1">{selectedUser.email}</Typography>
                  </Box>
                  <Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <PhoneIcon sx={{ mr: 1, color: theme.palette.grey[600] }} fontSize="small" />
                      <Typography variant="body2" color="text.secondary">Contact:</Typography>
                    </Box>
                    <Typography variant="body1">{selectedUser.contact}</Typography>
                  </Box>
                </Box>
              ) : (
                <Box component="div" sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr' }, gap: 2, mb: 3 }}>
                  <TextField
                    fullWidth
                    label="Full Name"
                    value={editedUserData?.fullName || ''}
                    onChange={(e) => setEditedUserData({...editedUserData!, fullName: e.target.value})}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <PersonIcon color="action" />
                        </InputAdornment>
                      ),
                    }}
                  />
                  <TextField
                    fullWidth
                    label="Email"
                    value={editedUserData?.email || ''}
                    onChange={(e) => setEditedUserData({...editedUserData!, email: e.target.value})}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <EmailIcon color="action" />
                        </InputAdornment>
                      ),
                    }}
                  />
                  <TextField
                    fullWidth
                    label="Contact"
                    value={editedUserData?.contact || ''}
                    onChange={(e) => setEditedUserData({...editedUserData!, contact: e.target.value})}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <PhoneIcon color="action" />
                        </InputAdornment>
                      ),
                    }}
                  />
                </Box>
              )}
              
              {/* Role specific details */}
              {selectedUser.userType === 'CLIENT' && (
                <>
                  <Divider sx={{ mb: 2 }} />
                  <Typography variant="subtitle1" fontWeight="bold" color="text.secondary" gutterBottom>
                    Client Details
                  </Typography>
                  
                  {!editMode ? (
                    <Box component="div" sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr' }, gap: 2, mb: 2 }}>
                      <Box>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                          <BusinessIcon sx={{ mr: 1, color: theme.palette.grey[600] }} fontSize="small" />
                          <Typography variant="body2" color="text.secondary">Company:</Typography>
                        </Box>
                        <Typography variant="body1">{selectedUser.companyName || '-'}</Typography>
                      </Box>
                      <Box>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                          <WorkIcon sx={{ mr: 1, color: theme.palette.grey[600] }} fontSize="small" />
                          <Typography variant="body2" color="text.secondary">Designation:</Typography>
                        </Box>
                        <Typography variant="body1">{selectedUser.designation || '-'}</Typography>
                      </Box>
                      {(selectedUser.altDesignation || selectedUser.altContact) && (
                        <>
                          <Box>
                            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                              <WorkIcon sx={{ mr: 1, color: theme.palette.grey[600] }} fontSize="small" />
                              <Typography variant="body2" color="text.secondary">Alt. Designation:</Typography>
                            </Box>
                            <Typography variant="body1">{selectedUser.altDesignation || '-'}</Typography>
                          </Box>
                          <Box>
                            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                              <PhoneIcon sx={{ mr: 1, color: theme.palette.grey[600] }} fontSize="small" />
                              <Typography variant="body2" color="text.secondary">Alt. Contact:</Typography>
                            </Box>
                            <Typography variant="body1">{selectedUser.altContact || '-'}</Typography>
                          </Box>
                        </>
                      )}
                    </Box>
                  ) : (
                    <Box component="div" sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr' }, gap: 2, mb: 2 }}>
                      <TextField
                        fullWidth
                        label="Company Name"
                        value={editedUserData?.companyName || ''}
                        onChange={(e) => setEditedUserData({...editedUserData!, companyName: e.target.value})}
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <BusinessIcon color="action" />
                            </InputAdornment>
                          ),
                        }}
                      />
                      <TextField
                        fullWidth
                        label="Designation"
                        value={editedUserData?.designation || ''}
                        onChange={(e) => setEditedUserData({...editedUserData!, designation: e.target.value})}
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <WorkIcon color="action" />
                            </InputAdornment>
                          ),
                        }}
                      />
                      <TextField
                        fullWidth
                        label="Alt. Designation"
                        value={editedUserData?.altDesignation || ''}
                        onChange={(e) => setEditedUserData({...editedUserData!, altDesignation: e.target.value})}
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <WorkIcon color="action" />
                            </InputAdornment>
                          ),
                        }}
                      />
                      <TextField
                        fullWidth
                        label="Alt. Contact"
                        value={editedUserData?.altContact || ''}
                        onChange={(e) => setEditedUserData({...editedUserData!, altContact: e.target.value})}
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <PhoneIcon color="action" />
                            </InputAdornment>
                          ),
                        }}
                      />
                    </Box>
                  )}
                </>
              )}
              
              {selectedUser.userType === 'DRIVER' && (
                <>
                  <Divider sx={{ mb: 2 }} />
                  <Typography variant="subtitle1" fontWeight="bold" color="text.secondary" gutterBottom>
                    Driver Details
                  </Typography>
                  
                  {!editMode ? (
                    <Box component="div" sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr' }, gap: 2, mb: 2 }}>
                      <Box>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                          <BadgeIcon sx={{ mr: 1, color: theme.palette.grey[600] }} fontSize="small" />
                          <Typography variant="body2" color="text.secondary">License Number:</Typography>
                        </Box>
                        <Typography variant="body1">{selectedUser.licenseNumber || '-'}</Typography>
                      </Box>
                    </Box>
                  ) : (
                    <Box component="div" sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr' }, gap: 2, mb: 2 }}>
                      <TextField
                        fullWidth
                        label="License Number"
                        value={editedUserData?.licenseNumber || ''}
                        onChange={(e) => setEditedUserData({...editedUserData!, licenseNumber: e.target.value})}
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <BadgeIcon color="action" />
                            </InputAdornment>
                          ),
                        }}
                      />
                    </Box>
                  )}
                  
                  <DriverVehiclesSection username={selectedUser.username} />
                </>
              )}
              
              {selectedUser.userType === 'MANAGER' && (
                <>
                  <Divider sx={{ mb: 2 }} />
                  <Typography variant="subtitle1" fontWeight="bold" color="text.secondary" gutterBottom>
                    Manager Details
                  </Typography>
                  
                  {!editMode ? (
                    <Box component="div" sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr' }, gap: 2, mb: 2 }}>

                      <Box>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                          <WorkIcon sx={{ mr: 1, color: theme.palette.grey[600] }} fontSize="small" />
                          <Typography variant="body2" color="text.secondary">Department:</Typography>
                        </Box>
                        <Typography variant="body1">{selectedUser.department || '-'}</Typography>
                      </Box>
                    </Box>
                  ) : (
                    <Box component="div" sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr' }, gap: 2, mb: 2 }}>

                      <TextField
                        fullWidth
                        label="Department"
                        value={editedUserData?.department || ''}
                        onChange={(e) => setEditedUserData({...editedUserData!, department: e.target.value})}
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <WorkIcon color="action" />
                            </InputAdornment>
                          ),
                        }}
                      />
                    </Box>
                  )}
                </>
              )}
            </>
          )}
        </DialogContent>
        <DialogActions sx={{ px: 3, py: 2, bgcolor: '#f9fafc', display: 'flex', justifyContent: 'space-between' }}>
          <Button 
            onClick={handleStatusChangeFromDetails}
            variant="outlined"
            color={selectedUser?.status === 'ACTIVE' ? 'error' : 'success'}
            startIcon={selectedUser?.status === 'ACTIVE' ? <BlockIcon /> : <CheckCircleIcon />}
            sx={{ 
              borderRadius: 2,
              '&:focus, &:focus-visible': {
                outline: 'none',
                boxShadow: 'none'
              }
            }}
          >
            {selectedUser?.status === 'ACTIVE' ? 'Deactivate' : 'Activate'}
          </Button>
          <Box>
            {!editMode ? (
              <>
                <Button 
                  onClick={handleCloseDetailsDialog}
                  variant="outlined"
                  color="inherit"
                  sx={{ 
                    borderRadius: 2,
                    mr: 1,
                    '&:focus, &:focus-visible': {
                      outline: 'none',
                      boxShadow: 'none'
                    }
                  }}
                >
                  Close
                </Button>
                <Button 
                  onClick={() => selectedUser && handleOpenChangePassword(selectedUser)}
                  variant="outlined" 
                  color="secondary"
                  sx={{ 
                    borderRadius: 2, 
                    mr: 1,
                    '&:focus, &:focus-visible': {
                      outline: 'none',
                      boxShadow: 'none'
                    }
                  }}
                >
                  Change Password
                </Button>
                <Button 
                  onClick={handleEditInDetailsDialog} 
                  variant="contained" 
                  color="primary"
                  startIcon={<EditIcon />}
                  sx={{ 
                    borderRadius: 2, 
                    px: 3,
                    '&:focus, &:focus-visible': {
                      outline: 'none',
                      boxShadow: 'none'
                    }
                  }}
                >
                  Edit User
                </Button>
              </>
            ) : (
              <>
                <Button 
                  onClick={handleCancelEdit}
                  variant="outlined"
                  color="inherit"
                  sx={{ 
                    borderRadius: 2,
                    mr: 1,
                    '&:focus, &:focus-visible': {
                      outline: 'none',
                      boxShadow: 'none'
                    }
                  }}
                >
                  Cancel
                </Button>
                <Button 
                  onClick={handleSaveUserEdit} 
                  variant="contained" 
                  color="primary"
                  sx={{ 
                    borderRadius: 2, 
                    px: 3,
                    '&:focus, &:focus-visible': {
                      outline: 'none',
                      boxShadow: 'none'
                    }
                  }}
                >
                  Save Changes
                </Button>
              </>
            )}
          </Box>
        </DialogActions>
      </Dialog>

      {/* Change Password Dialog */}
      <Dialog 
        open={changePasswordDialog} 
        onClose={handleCloseChangePassword}
        maxWidth="sm" 
        fullWidth
        slotProps={{
          paper: { 
            sx: { 
              borderRadius: 2,
              boxShadow: 24,
              maxHeight: '90vh'
            } 
          } 
        }}
      >
        <DialogTitle sx={{ 
          bgcolor: theme.palette.secondary.main, 
          color: 'white',
          px: 3,
          py: 2
        }}>
          <Box display="flex" alignItems="center" justifyContent="space-between">
            <Box display="flex" alignItems="center">
              <Typography variant="h6">
                Change Password for {selectedUser?.username}
              </Typography>
            </Box>
            <IconButton
              aria-label="close"
              onClick={handleCloseChangePassword}
              sx={{ color: 'white' }}
            >
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent sx={{ px: 3, py: 2 }}>
          <Box sx={{ mb: 2, mt: 1 }}>
            <Typography variant="body2" color="text.secondary">
              Enter the current password and a new password for the user.
            </Typography>
          </Box>
          
          <TextField
            fullWidth
            margin="dense"
            label="Current Password"
            type={showCurrentPassword ? 'text' : 'password'}
            value={passwordData.currentPassword}
            onChange={(e) => setPasswordData({ ...passwordData, currentPassword: e.target.value })}
            required
            sx={{ mb: 2 }}
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                    edge="end"
                  >
                    {showCurrentPassword ? <VisibilityOff /> : <Visibility />}
                  </IconButton>
                </InputAdornment>
              ),
            }}
          />
          
          <TextField
            fullWidth
            margin="dense"
            label="New Password"
            type={showNewPassword ? 'text' : 'password'}
            value={passwordData.newPassword}
            onChange={(e) => setPasswordData({ ...passwordData, newPassword: e.target.value })}
            required
            sx={{ mb: 2 }}
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    onClick={() => setShowNewPassword(!showNewPassword)}
                    edge="end"
                  >
                    {showNewPassword ? <VisibilityOff /> : <Visibility />}
                  </IconButton>
                </InputAdornment>
              ),
            }}
            helperText="Password must be at least 6 characters long"
          />
          
          <TextField
            fullWidth
            margin="dense"
            label="Confirm New Password"
            type={showConfirmPassword ? 'text' : 'password'}
            value={passwordData.confirmPassword}
            onChange={(e) => setPasswordData({ ...passwordData, confirmPassword: e.target.value })}
            required
            sx={{ mb: 2 }}
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    edge="end"
                  >
                    {showConfirmPassword ? <VisibilityOff /> : <Visibility />}
                  </IconButton>
                </InputAdornment>
              ),
            }}
          />
        </DialogContent>
        <DialogActions sx={{ px: 3, py: 2, bgcolor: '#f9fafc' }}>
          <Button 
            onClick={handleCloseChangePassword}
            variant="outlined"
            color="inherit"
            sx={{ 
              borderRadius: 2,
              '&:focus, &:focus-visible': {
                outline: 'none',
                boxShadow: 'none'
              }
            }}
          >
            Cancel
          </Button>
          <Button 
            onClick={handleChangePassword} 
            variant="contained" 
            color="secondary"
            disabled={loading}
            sx={{ 
              borderRadius: 2, 
              px: 3,
              '&:focus, &:focus-visible': {
                outline: 'none',
                boxShadow: 'none'
              }
            }}
          >
            {loading ? <CircularProgress size={20} /> : 'Change Password'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default UserManagement; 