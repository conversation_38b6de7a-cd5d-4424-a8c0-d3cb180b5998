package com.wms.service.impl;

import com.wms.entity.Manifest;
import com.wms.entity.ManifestStatus;
import com.wms.entity.Container;
import com.wms.entity.Pallet;
import com.wms.entity.user.Client;
import com.wms.exception.ResourceNotFoundException;
import com.wms.repository.ManifestRepository;
import com.wms.repository.ContainerRepository;
import com.wms.repository.UserRepo.ClientRepository;
import com.wms.service.ManifestService;
import com.wms.service.ManifestStatusService;
import com.wms.service.ManifestTrackingLogService;
import com.wms.service.PalletService;
import com.wms.util.ManifestAllocationUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Objects;

@Service
public class ManifestServiceImpl implements ManifestService {

    private final ManifestRepository manifestRepository;
    private final ContainerRepository containerRepository;
    private final ManifestAllocationUtil manifestAllocationUtil;
    private final ClientRepository clientRepository;
    private final PalletService palletService;
    private final ManifestTrackingLogService trackingLogService;
    private final ManifestStatusService manifestStatusService;
    private static final Logger logger = LoggerFactory.getLogger(ManifestServiceImpl.class);

    @Autowired
    public ManifestServiceImpl(ManifestRepository manifestRepository, ContainerRepository containerRepository, 
                              ManifestAllocationUtil manifestAllocationUtil, ClientRepository clientRepository,
                              PalletService palletService, ManifestTrackingLogService trackingLogService,
                              ManifestStatusService manifestStatusService) {
        this.manifestRepository = manifestRepository;
        this.containerRepository = containerRepository;
        this.manifestAllocationUtil = manifestAllocationUtil;
        this.clientRepository = clientRepository;
        this.palletService = palletService;
        this.trackingLogService = trackingLogService;
        this.manifestStatusService = manifestStatusService;
    }

    @Override
    @Transactional(propagation = org.springframework.transaction.annotation.Propagation.REQUIRES_NEW)
    public Manifest createManifest(Manifest manifest) {
        // Check if container exists
        Container container = containerRepository.findById(manifest.getContainer().getContainerNo())
                .orElseThrow(() -> new ResourceNotFoundException("Container not found with number: " + manifest.getContainer().getContainerNo()));
        
        // Check if container can accept more manifests
        if (!canCreateManifest(container)) {
            throw new IllegalStateException("Container " + container.getContainerNo() + 
                                          " has reached its manifest limit of " + container.getManifestQuantity());
        }
        
        // Auto-assign the sequence number
        Integer maxSequence = manifestRepository.findMaxSequenceNoByContainerContainerNo(manifest.getContainer().getContainerNo());
        // Since we're using COALESCE(MAX(m.sequenceNo), 0), we need to add 1 in all cases
        int nextSequence = maxSequence + 1;
        manifest.setSequenceNo(nextSequence);
        
        logger.debug("Assigned sequence number {} to new manifest for container {}", 
                  nextSequence, manifest.getContainer().getContainerNo());
        
        // Fetch and set the Client entity using the provided username
        if (manifest.getClient() != null && manifest.getClient().getUsername() != null) {
            Client client = clientRepository.findByUsername(manifest.getClient().getUsername())
                .orElseThrow(() -> new ResourceNotFoundException("Client not found with username: " + manifest.getClient().getUsername()));
            manifest.setClient(client);
        }

        // Set created date if not already set
        if (manifest.getCreatedDate() == null) {
        manifest.setCreatedDate(LocalDateTime.now());
        }
        
        // Set initial status based on container status
        if (manifest.getStatus() == null) {
            manifest.setStatus(ManifestStatus.CREATED);
        }
        
        // Auto-set status based on delivery date ONLY if manifest has already been inbounded
        // For new manifests, we don't auto-set delivery-based statuses regardless of delivery date
        // This ensures new manifests follow the proper workflow
        
        manifestAllocationUtil.allocateLocationAndVehicle(manifest);
        Manifest savedManifest = manifestRepository.save(manifest);
        
        // Log manifest creation
        try {
            String currentUser = getCurrentUser();
            trackingLogService.logManifestCreation(savedManifest, currentUser, "Manifest created");
        } catch (Exception e) {
            logger.warn("Failed to log manifest creation for {}: {}", savedManifest.getTrackingNo(), e.getMessage());
        }
        
        return savedManifest;
    }
    
    /**
     * Checks if a container can accept more manifests based on its manifestQuantity property
     * 
     * @param container The container to check
     * @return true if more manifests can be created, false otherwise
     */
    private boolean canCreateManifest(Container container) {
        if (container.getManifestQuantity() == null || container.getManifestQuantity() <= 0) {
            // If manifest quantity is not set or invalid, allow creation
            logger.warn("Container {} has invalid manifest quantity: {}", 
                       container.getContainerNo(), container.getManifestQuantity());
            return true;
        }
        
        // Get current number of manifests for this container
        List<Manifest> existingManifests = manifestRepository.findByContainerContainerNo(container.getContainerNo());
        int currentCount = existingManifests.size();
        
        logger.debug("Container {} has {}/{} manifests", 
                   container.getContainerNo(), currentCount, container.getManifestQuantity());
                   
        // Check if we're at or over the limit
        return currentCount < container.getManifestQuantity();
    }

    @Override
    @Transactional
    public Manifest updateManifest(String trackingNo, Manifest manifestDetails) {
        Manifest manifest = manifestRepository.findById(trackingNo)
                .orElseThrow(() -> new ResourceNotFoundException("Manifest not found with tracking number: " + trackingNo));
        
        // Store the original manifest for tracking changes
        Manifest originalManifest = new Manifest();
        originalManifest.setTrackingNo(manifest.getTrackingNo());
        originalManifest.setStatus(manifest.getStatus());
        originalManifest.setCustomerName(manifest.getCustomerName());
        originalManifest.setAddress(manifest.getAddress());
        originalManifest.setPhoneNo(manifest.getPhoneNo());
        originalManifest.setLocation(manifest.getLocation());
        originalManifest.setDeliveryVehicle(manifest.getDeliveryVehicle());
        originalManifest.setDeliveryDate(manifest.getDeliveryDate());
        originalManifest.setTimeSlot(manifest.getTimeSlot());  // Store original time slot
        originalManifest.setDeliveredDate(manifest.getDeliveredDate());
        originalManifest.setDriver(manifest.getDriver());
        originalManifest.setRemarks(manifest.getRemarks());
        originalManifest.setPieces(manifest.getPieces());
        originalManifest.setCbm(manifest.getCbm());
        originalManifest.setWeight(manifest.getWeight());
        
        // Store the original status for reference
        ManifestStatus originalStatus = manifest.getStatus();
        
        // Store the original delivery vehicle for reference
        String originalDeliveryVehicle = manifest.getDeliveryVehicle();
        
        // Fetch and set the Client entity using the provided username
        if (manifestDetails.getClient() != null && manifestDetails.getClient().getUsername() != null) {
            Client client = clientRepository.findByUsername(manifestDetails.getClient().getUsername())
                .orElseThrow(() -> new ResourceNotFoundException("Client not found with username: " + manifestDetails.getClient().getUsername()));
            manifest.setClient(client);
        }
        
        // Don't set status yet - we'll handle it after checking delivery date
        // manifest.setStatus(manifestDetails.getStatus());
        
        manifest.setPostalCode(manifestDetails.getPostalCode());
        manifest.setCbm(manifestDetails.getCbm());
        manifest.setWeight(manifestDetails.getWeight());
        manifest.setCustomerName(manifestDetails.getCustomerName());
        manifest.setPhoneNo(manifestDetails.getPhoneNo());
        manifest.setAddress(manifestDetails.getAddress());
        manifest.setCountry(manifestDetails.getCountry());
        manifest.setPieces(manifestDetails.getPieces());
        
        // Log the incoming date values
        logger.debug("Received deliveryDate: {}", manifestDetails.getDeliveryDate());
        logger.debug("Received deliveredDate: {}", manifestDetails.getDeliveredDate());
        logger.debug("Received timeSlot: {}", manifestDetails.getTimeSlot());
        
        // Enhanced debugging for delivery date changes
        if (originalManifest.getDeliveryDate() != null && manifestDetails.getDeliveryDate() != null) {
            logger.debug("Comparing dates - Original: {}, New: {}, Equal: {}", 
                originalManifest.getDeliveryDate(), 
                manifestDetails.getDeliveryDate(),
                originalManifest.getDeliveryDate().equals(manifestDetails.getDeliveryDate()));
        }
        
        // Set the date fields directly - the @JsonFormat annotation will handle conversion
        manifest.setDeliveryDate(manifestDetails.getDeliveryDate());
        manifest.setDeliveredDate(manifestDetails.getDeliveredDate());
        
        // Set the time slot field
        String oldTimeSlot = manifest.getTimeSlot();
        manifest.setTimeSlot(manifestDetails.getTimeSlot());
        logger.debug("Updated timeSlot from '{}' to '{}'", oldTimeSlot, manifestDetails.getTimeSlot());
        
        // Check if delivery date has changed and manifest is in INBOUNDED_TO_WAREHOUSE, READY_TO_DELIVER, or PENDING_DELIVER status
        boolean deliveryDateChanged = manifestDetails.getDeliveryDate() != null && 
            (originalManifest.getDeliveryDate() == null || 
             !originalManifest.getDeliveryDate().equals(manifestDetails.getDeliveryDate()));
        
        // Check if delivery date was deleted (set to null)
        boolean deliveryDateDeleted = manifestDetails.getDeliveryDate() == null && 
            originalManifest.getDeliveryDate() != null;
        
        // Log delivery date changes IMMEDIATELY to ensure correct chronological order
        if (deliveryDateChanged || deliveryDateDeleted) {
            try {
                String currentUser = getCurrentUser();
                String remarks = deliveryDateDeleted ? "Delivery date removed" : 
                               originalManifest.getDeliveryDate() == null ? "Delivery date added" : 
                               "Delivery date updated";
                
                logger.info("Delivery date change detected for manifest {}: {} -> {}", 
                    trackingNo, 
                    originalManifest.getDeliveryDate(), 
                    manifest.getDeliveryDate());
                
                trackingLogService.logFieldUpdate(trackingNo, "deliveryDate", 
                    originalManifest.getDeliveryDate() != null ? originalManifest.getDeliveryDate().toString() : null,
                    manifest.getDeliveryDate() != null ? manifest.getDeliveryDate().toString() : null, 
                    currentUser, remarks);
            } catch (Exception e) {
                logger.warn("Failed to log delivery date change for {}: {}", trackingNo, e.getMessage());
            }
            
            // Update the original manifest delivery date to prevent duplicate logging in logManifestUpdate
            originalManifest.setDeliveryDate(manifest.getDeliveryDate());
        }
             
        if (deliveryDateChanged && 
            (manifest.getStatus() == ManifestStatus.INBOUNDED_TO_WAREHOUSE || 
             manifest.getStatus() == ManifestStatus.READY_TO_DELIVER || 
             manifest.getStatus() == ManifestStatus.PENDING_DELIVER)) {
            
            // Update the status based on the new delivery date
            manifest = manifestStatusService.checkAndUpdateStatusBasedOnDeliveryDate(manifest);
            
            // Update the original manifest status to prevent duplicate logging in logManifestUpdate
            originalManifest.setStatus(manifest.getStatus());
        } 
        // Handle delivery date deletion - revert to INBOUNDED_TO_WAREHOUSE if currently in delivery-dependent status
        else if (deliveryDateDeleted && 
            (manifest.getStatus() == ManifestStatus.READY_TO_DELIVER || 
             manifest.getStatus() == ManifestStatus.PENDING_DELIVER)) {
            
            logger.info("Delivery date deleted for manifest {}, reverting status from {} to INBOUNDED_TO_WAREHOUSE", 
                       trackingNo, manifest.getStatus());
            
            ManifestStatus originalStatusForLogging = manifest.getStatus();
            manifest.setStatus(ManifestStatus.INBOUNDED_TO_WAREHOUSE);
            
            // Update the original manifest status to prevent duplicate logging in logManifestUpdate
            originalManifest.setStatus(manifest.getStatus());
            
            // Log the status change due to delivery date deletion
            try {
                String currentUser = getCurrentUser();
                trackingLogService.logStatusChange(trackingNo, originalStatusForLogging, 
                                                 ManifestStatus.INBOUNDED_TO_WAREHOUSE, 
                                                 currentUser, "Status reverted due to delivery date removal");
            } catch (Exception e) {
                logger.warn("Failed to log status change for {}: {}", trackingNo, e.getMessage());
            }
        }
        
        // If we didn't auto-update the status based on delivery date, use the provided status
        // but only if it doesn't skip workflow steps
        if (manifestDetails.getStatus() != null) {
            // Check if the requested status change is valid
            if (isValidStatusTransition(originalStatus, manifestDetails.getStatus())) {
                manifest.setStatus(manifestDetails.getStatus());
                logger.debug("Setting status from {} to {} as requested", originalStatus, manifestDetails.getStatus());
            } else {
                logger.warn("Invalid status transition requested: {} to {}. Keeping original status.", 
                         originalStatus, manifestDetails.getStatus());
            }
        }
        
        // Check if the delivery vehicle was explicitly changed by the user
        boolean deliveryVehicleExplicitlyChanged = false;
        if (manifestDetails.getDeliveryVehicle() != null && 
            !manifestDetails.getDeliveryVehicle().equals(originalDeliveryVehicle)) {
            deliveryVehicleExplicitlyChanged = true;
            manifest.setDeliveryVehicle(manifestDetails.getDeliveryVehicle());
            logger.debug("Using manually set delivery vehicle: {} for manifest {}", 
                      manifestDetails.getDeliveryVehicle(), trackingNo);
        }
        
        manifest.setRemarks(manifestDetails.getRemarks());
        
        // If location is explicitly provided, use it; otherwise recalculate
        if (manifestDetails.getLocation() != null) {
            manifest.setLocation(manifestDetails.getLocation());
            logger.debug("Using manually set location: {} for manifest {}", 
                      manifestDetails.getLocation(), trackingNo);
        } else {
            // Recalculate location
            manifestAllocationUtil.allocateLocation(manifest);
        }
        
        // If delivery vehicle wasn't explicitly changed, auto-assign it based on weight, CBM, and pieces
        if (!deliveryVehicleExplicitlyChanged) {
            manifestAllocationUtil.allocateVehicle(manifest);
            logger.debug("Auto-assigned delivery vehicle: {} for manifest {}", 
                      manifest.getDeliveryVehicle(), trackingNo);
        }
        
        Manifest savedManifest = manifestRepository.save(manifest);
        
        // Check if pieces changed and update manifest status accordingly
        // This is similar to how pallet operations trigger status updates
        if (!Objects.equals(originalManifest.getPieces(), savedManifest.getPieces())) {
            logger.debug("Manifest pieces changed from {} to {} for manifest {}. Checking for status update.", 
                        originalManifest.getPieces(), savedManifest.getPieces(), trackingNo);
            savedManifest = manifestStatusService.checkAndUpdateManifestStatus(savedManifest, false); // false to avoid duplicate logging
        }
        
        // Log all changes made to the manifest
        try {
            String currentUser = getCurrentUser();
            trackingLogService.logManifestUpdate(originalManifest, savedManifest, currentUser, "Manifest updated");
        } catch (Exception e) {
            logger.warn("Failed to log manifest update for {}: {}", trackingNo, e.getMessage());
        }
        
        // Log the saved date values
        logger.debug("Saved deliveryDate: {}", savedManifest.getDeliveryDate());
        logger.debug("Saved deliveredDate: {}", savedManifest.getDeliveredDate());
        
        return savedManifest;
    }
    
    /**
     * Checks if a status transition is valid based on the workflow rules
     * 
     * @param currentStatus The current status
     * @param newStatus The requested new status
     * @return true if the transition is valid, false otherwise
     */
    private boolean isValidStatusTransition(ManifestStatus currentStatus, ManifestStatus newStatus) {
        // Define the normal workflow progression
        ManifestStatus[] workflow = {
            ManifestStatus.CREATED,
            ManifestStatus.ETA_TO_WAREHOUSE,
            ManifestStatus.ARRIVED,
            ManifestStatus.INBOUNDING,
            ManifestStatus.INBOUNDED_TO_WAREHOUSE,
            ManifestStatus.PENDING_DELIVER,
            ManifestStatus.READY_TO_DELIVER,
            ManifestStatus.DELIVERING,
            ManifestStatus.DELIVERED
        };
        
        // Special case 1: Can always move to ON_HOLD from any status
        if (newStatus == ManifestStatus.ON_HOLD) {
            return true;
        }
        
        // Special case 2: From ON_HOLD, can return to any normal status
        if (currentStatus == ManifestStatus.ON_HOLD) {
            return true;
        }
        
        // Special case 3: Can always move to DISCREPANCY from any status
        if (newStatus == ManifestStatus.DISCREPANCY) {
            return true;
        }
        
        // Special case 4: From DISCREPANCY, can move to any normal status
        if (currentStatus == ManifestStatus.DISCREPANCY) {
            return true;
        }
        
        // Special case 5: Disallow manual status change from READY_TO_DELIVER/PENDING_DELIVER to INBOUNDED_TO_WAREHOUSE
        if (newStatus == ManifestStatus.INBOUNDED_TO_WAREHOUSE && 
            (currentStatus == ManifestStatus.READY_TO_DELIVER || currentStatus == ManifestStatus.PENDING_DELIVER)) {
            return false;
        }
        
        // Special case 6: These statuses should only be set with special validation
        // Manual transitions to these statuses are not allowed without validation
        if (newStatus == ManifestStatus.INBOUNDING || 
            newStatus == ManifestStatus.INBOUNDED_TO_WAREHOUSE ||
            newStatus == ManifestStatus.READY_TO_DELIVER ||
            newStatus == ManifestStatus.PENDING_DELIVER) {
            // Return false to prevent manual transition to these statuses without validation
            // The system will handle this and provide an appropriate error message
            return false;
        }
        
        // Normal workflow transition: can only move to the next status or any subsequent status
        int currentIndex = -1;
        int newIndex = -1;
        
        for (int i = 0; i < workflow.length; i++) {
            if (workflow[i] == currentStatus) {
                currentIndex = i;
            }
            if (workflow[i] == newStatus) {
                newIndex = i;
            }
        }
        
        // Can move to the next status or any subsequent status
        return (currentIndex != -1 && newIndex != -1 && newIndex > currentIndex);
    }

    @Override
    public List<Manifest> getAllManifests() {
        return manifestRepository.findAllWithPallets();
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void deleteManifest(String trackingNo) {
        if (!manifestRepository.existsById(trackingNo)) {
            throw new ResourceNotFoundException("Manifest not found with tracking number: " + trackingNo);
        }
        
        // Log manifest deletion before actual deletion
        try {
            String currentUser = getCurrentUser();
            trackingLogService.logManifestDeletion(trackingNo, currentUser, "Manifest deleted");
        } catch (Exception e) {
            logger.warn("Failed to log manifest deletion for {}: {}", trackingNo, e.getMessage());
        }
        
        // Delete any pallets associated with this manifest first
        try {
            palletService.deletePalletsByManifestTrackingNo(trackingNo);
            logger.debug("Deleted all pallets for manifest {}", trackingNo);
        } catch (Exception e) {
            logger.warn("Error deleting pallets for manifest {}: {}", trackingNo, e.getMessage());
            // Continue with manifest deletion even if pallet deletion fails
        }
        
        manifestRepository.deleteById(trackingNo);
        logger.info("Successfully deleted manifest {}", trackingNo);
    }

    @Override
    public Optional<Manifest> getManifestByTrackingNo(String trackingNo) {
        return manifestRepository.findByIdWithPallets(trackingNo);
    }

    @Override
    public List<Manifest> getManifestsByUsername(String username) {
        return manifestRepository.findByClientUsernameWithPallets(username);
    }

    @Override
    public List<Manifest> getManifestsByDriverUsername(String username) {
        return manifestRepository.findByDriverUsernameWithPallets(username);
    }

    @Override
    public Manifest updateManifestStatus(String trackingNo, ManifestStatus status) {
        Manifest manifest = manifestRepository.findById(trackingNo)
                .orElseThrow(() -> new ResourceNotFoundException("Manifest not found with tracking number: " + trackingNo));
        
        ManifestStatus originalStatus = manifest.getStatus();
        logger.debug("Updating manifest {} status from {} to {}", trackingNo, originalStatus, status);
        
        // Check if status is already the requested status
        if (originalStatus.equals(status)) {
            logger.debug("Status update request ignored for manifest {} - already in status {}", trackingNo, status);
            return manifest;
        }
        
        // Special validation for INBOUNDING and INBOUNDED_TO_WAREHOUSE
        if (status == ManifestStatus.INBOUNDING || status == ManifestStatus.INBOUNDED_TO_WAREHOUSE) {
            // Disallow manual status change from READY_TO_DELIVER/PENDING_DELIVER to INBOUNDED_TO_WAREHOUSE
            if (status == ManifestStatus.INBOUNDED_TO_WAREHOUSE && 
                (originalStatus == ManifestStatus.READY_TO_DELIVER || originalStatus == ManifestStatus.PENDING_DELIVER)) {
                throw new IllegalArgumentException("Cannot manually change status from " + originalStatus + 
                    " to INBOUNDED_TO_WAREHOUSE. This transition is only allowed automatically by the system when delivery date changes.");
            }
            
            // Get all pallets for this manifest
            List<Pallet> pallets = palletService.getPalletsByManifestTrackingNo(trackingNo);
            int totalPiecesInPallets = pallets.stream().mapToInt(Pallet::getNoOfPieces).sum();
            int manifestPieces = manifest.getPieces();
            
            if (status == ManifestStatus.INBOUNDING) {
                // For INBOUNDING: must have at least one pallet with pieces but less than manifest pieces
                if (totalPiecesInPallets == 0) {
                    throw new IllegalArgumentException("Cannot set status to INBOUNDING: No pallet pieces have been added yet.");
                }
                if (totalPiecesInPallets >= manifestPieces) {
                    throw new IllegalArgumentException("Cannot set status to INBOUNDING: Total pallet pieces (" + 
                        totalPiecesInPallets + ") must be less than manifest pieces (" + manifestPieces + ").");
                }
            } else if (status == ManifestStatus.INBOUNDED_TO_WAREHOUSE) {
                // For INBOUNDED_TO_WAREHOUSE: total pallet pieces must exactly match manifest pieces
                if (totalPiecesInPallets != manifestPieces) {
                    throw new IllegalArgumentException("Cannot set status to INBOUNDED_TO_WAREHOUSE: Total pallet pieces (" + 
                        totalPiecesInPallets + ") must exactly match manifest pieces (" + manifestPieces + ").");
                }
                if (pallets.isEmpty()) {
                    throw new IllegalArgumentException("Cannot set status to INBOUNDED_TO_WAREHOUSE: No pallets have been created yet.");
                }
            }
        } 
        // Special validation for READY_TO_DELIVER
        else if (status == ManifestStatus.READY_TO_DELIVER) {
            // For READY_TO_DELIVER: must be in INBOUNDED_TO_WAREHOUSE status and have delivery date within 1 day
            if (originalStatus != ManifestStatus.INBOUNDED_TO_WAREHOUSE && originalStatus != ManifestStatus.PENDING_DELIVER) {
                throw new IllegalArgumentException("Cannot set status to READY_TO_DELIVER: Current status must be INBOUNDED_TO_WAREHOUSE or PENDING_DELIVER.");
            }
            
            if (manifest.getDeliveryDate() == null) {
                throw new IllegalArgumentException("Cannot set status to READY_TO_DELIVER: Delivery date must be set.");
            }
            
            LocalDateTime today = LocalDateTime.now().withHour(0).withMinute(0);
            LocalDateTime deliveryDate = manifest.getDeliveryDate().atStartOfDay();
            long diffDays = java.time.temporal.ChronoUnit.DAYS.between(today, deliveryDate);
            
            if (diffDays > 1) {
                throw new IllegalArgumentException("Cannot set status to READY_TO_DELIVER: Delivery date must be today or tomorrow (within 1 day).");
            }
        }
        // Special validation for PENDING_DELIVER
        else if (status == ManifestStatus.PENDING_DELIVER) {
            // For PENDING_DELIVER: must be in INBOUNDED_TO_WAREHOUSE status and have delivery date at least 2 days away
            if (originalStatus != ManifestStatus.INBOUNDED_TO_WAREHOUSE && originalStatus != ManifestStatus.READY_TO_DELIVER) {
                throw new IllegalArgumentException("Cannot set status to PENDING_DELIVER: Current status must be INBOUNDED_TO_WAREHOUSE or READY_TO_DELIVER.");
            }
            
            if (manifest.getDeliveryDate() == null) {
                throw new IllegalArgumentException("Cannot set status to PENDING_DELIVER: Delivery date must be set.");
            }
            
            LocalDateTime today = LocalDateTime.now().withHour(0).withMinute(0);
            LocalDateTime deliveryDate = manifest.getDeliveryDate().atStartOfDay();
            long diffDays = java.time.temporal.ChronoUnit.DAYS.between(today, deliveryDate);
            
            if (diffDays < 2) {
                throw new IllegalArgumentException("Cannot set status to PENDING_DELIVER: Delivery date must be at least 2 days from today.");
            }
        }
        else if (!isValidStatusTransition(originalStatus, status)) {
            throw new IllegalArgumentException("Invalid status transition from " + originalStatus + " to " + status);
        }
        
        // Set the requested status
        manifest.setStatus(status);
        
        // Save the manifest first with the requested status
        Manifest savedManifest = manifestRepository.save(manifest);
        
        // Log the first status change (the manually requested one)
        if (!originalStatus.equals(savedManifest.getStatus())) {
            try {
                String currentUser = getCurrentUser();
                trackingLogService.logStatusChange(trackingNo, originalStatus, savedManifest.getStatus(), 
                                                 currentUser, "Status updated via API");
            } catch (Exception e) {
                logger.warn("Failed to log status change for {}: {}", trackingNo, e.getMessage());
            }
        } else {
            logger.debug("No status change logged for manifest {} - status remained {}", trackingNo, originalStatus);
        }
        
        // If setting to INBOUNDED_TO_WAREHOUSE and delivery date is set, check if we should update status further
        if (status == ManifestStatus.INBOUNDED_TO_WAREHOUSE && manifest.getDeliveryDate() != null) {
            // Update the status based on the delivery date
            // This method will handle its own logging separately for any further status changes
            savedManifest = manifestStatusService.checkAndUpdateStatusBasedOnDeliveryDate(savedManifest);
        }
        
        return savedManifest;
    }

    @Override
    public Long getManifestsCount() {
        // Get all manifests and count them
        List<Manifest> manifests = manifestRepository.findAll();
        Long count = (long) manifests.size();
        
        // Log details for debugging
        if (count > 0) {
            String trackingNos = manifests.stream()
                .limit(5)
                .map(Manifest::getTrackingNo)
                .reduce((a, b) -> a + ", " + b)
                .orElse("");
            
            logger.debug("Found {} manifests. Sample tracking numbers: {}", 
                      count, trackingNos);
        } else {
            logger.debug("No manifests found in database");
        }
        
        return count;
    }

    @Override
    public Optional<Manifest> getManifestByManifestNo(String manifestNo) {
        return manifestRepository.findById(manifestNo);
    }

    @Override
    public List<Manifest> getManifestsByContainerNo(String containerNo) {
        return manifestRepository.findByContainerContainerNoWithPallets(containerNo);
    }

    @Override
    public boolean canContainerAcceptMoreManifests(String containerNo) {
        Container container = containerRepository.findById(containerNo)
                .orElseThrow(() -> new ResourceNotFoundException("Container not found with number: " + containerNo));
        return canCreateManifest(container);
    }

    @Override
    @Transactional
    public List<Manifest> createManifestsBulk(List<Manifest> manifests) {
        if (manifests == null || manifests.isEmpty()) {
            throw new IllegalArgumentException("Manifest list cannot be null or empty");
        }
        
        logger.info("Starting bulk creation of {} manifests", manifests.size());
        List<Manifest> createdManifests = new ArrayList<>();
        
        try {
            // Pre-validate all manifests before creating any
            for (int i = 0; i < manifests.size(); i++) {
                Manifest manifest = manifests.get(i);
                final int rowNumber = i + 2; // Excel row number (1-indexed + header)
                logger.debug("Pre-validating manifest {} of {}: {}", i + 1, manifests.size(), manifest.getTrackingNo());
                
                // Check if container exists
                Container container = containerRepository.findById(manifest.getContainer().getContainerNo())
                        .orElseThrow(() -> new IllegalArgumentException(
                            String.format("Row %d: Container not found with number: %s", 
                                rowNumber, manifest.getContainer().getContainerNo())));
                
                // Check if container can accept more manifests
                if (!canCreateManifest(container)) {
                    throw new IllegalArgumentException(
                        String.format("Row %d: Container %s has reached its manifest limit of %d", 
                            rowNumber, container.getContainerNo(), container.getManifestQuantity()));
                }
                
                // Check if client exists
                if (manifest.getClient() != null && manifest.getClient().getUsername() != null) {
                    clientRepository.findByUsername(manifest.getClient().getUsername())
                        .orElseThrow(() -> new IllegalArgumentException(
                            String.format("Row %d: Client not found with username: %s", 
                                rowNumber, manifest.getClient().getUsername())));
                }
                
                // Check for duplicate tracking numbers in the batch
                for (int j = i + 1; j < manifests.size(); j++) {
                    if (manifest.getTrackingNo().equals(manifests.get(j).getTrackingNo())) {
                        throw new IllegalArgumentException(
                            String.format("Duplicate tracking number '%s' found in rows %d and %d", 
                                manifest.getTrackingNo(), rowNumber, j + 2));
                    }
                }
                
                // Check if tracking number already exists in database
                if (manifestRepository.findById(manifest.getTrackingNo()).isPresent()) {
                    throw new IllegalArgumentException(
                        String.format("Row %d: Manifest with tracking number '%s' already exists", 
                            rowNumber, manifest.getTrackingNo()));
                }
            }
            
            logger.info("Pre-validation completed successfully for all {} manifests", manifests.size());
            
            // Now create all manifests
            for (int i = 0; i < manifests.size(); i++) {
                Manifest manifest = manifests.get(i);
                logger.debug("Creating manifest {} of {}: {}", i + 1, manifests.size(), manifest.getTrackingNo());
                
                // Prepare manifest for creation (similar to individual createManifest method)
                Container container = containerRepository.findById(manifest.getContainer().getContainerNo()).get();
                
                // Auto-assign sequence number
                Integer maxSequence = manifestRepository.findMaxSequenceNoByContainerContainerNo(manifest.getContainer().getContainerNo());
                int nextSequence = maxSequence + 1;
                manifest.setSequenceNo(nextSequence);
                
                logger.debug("Assigned sequence number {} to manifest {}", nextSequence, manifest.getTrackingNo());
                
                // Fetch and set the Client entity
                if (manifest.getClient() != null && manifest.getClient().getUsername() != null) {
                    Client client = clientRepository.findByUsername(manifest.getClient().getUsername()).get();
                    manifest.setClient(client);
                }
                
                // Set created date if not already set
                if (manifest.getCreatedDate() == null) {
                    manifest.setCreatedDate(LocalDateTime.now());
                }
                
                // Set initial status
                if (manifest.getStatus() == null) {
                    manifest.setStatus(ManifestStatus.CREATED);
                }
                
                // Allocate location and vehicle
                manifestAllocationUtil.allocateLocationAndVehicle(manifest);
                
                // Save the manifest
                Manifest savedManifest = manifestRepository.save(manifest);
                createdManifests.add(savedManifest);
                
                // Log manifest creation (use REQUIRES_NEW to prevent rollback affecting this)
                try {
                    String currentUser = getCurrentUser();
                    trackingLogService.logManifestCreation(savedManifest, currentUser, "Manifest created via bulk upload");
                } catch (Exception e) {
                    logger.warn("Failed to log manifest creation for {}: {}", savedManifest.getTrackingNo(), e.getMessage());
                }
            }
            
            logger.info("Successfully created {} manifests in bulk operation", createdManifests.size());
            return createdManifests;
            
        } catch (Exception e) {
            logger.error("Bulk manifest creation failed, rolling back transaction. Error: {}", e.getMessage());
            // Transaction will be automatically rolled back due to @Transactional
            throw e;
        }
    }

    @Override
    public Manifest checkAndUpdateManifestStatus(Manifest manifest) {
        return manifestStatusService.checkAndUpdateManifestStatus(manifest);
    }
    
    /**
     * Helper method to get the current authenticated user
     */
    private String getCurrentUser() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && authentication.getName() != null) {
                return authentication.getName();
            }
        } catch (Exception e) {
            logger.debug("Could not get current user from security context: {}", e.getMessage());
        }
        return "system";
    }

    @Override
    @Transactional
    public Manifest updateRemarksField(String trackingNo, String field, String value) {
        logger.info("Updating {} field for manifest {}", field, trackingNo);

        Optional<Manifest> manifestOpt = manifestRepository.findByIdWithPallets(trackingNo);
        if (!manifestOpt.isPresent()) {
            throw new ResourceNotFoundException("Manifest not found with tracking number: " + trackingNo);
        }

        Manifest manifest = manifestOpt.get();
        String oldValue = null;

        // Update only the specific field
        if ("remarks".equals(field)) {
            oldValue = manifest.getRemarks();
            manifest.setRemarks(value);
        } else if ("driverRemarks".equals(field)) {
            oldValue = manifest.getDriverRemarks();
            manifest.setDriverRemarks(value);
        } else {
            throw new IllegalArgumentException("Invalid field: " + field + ". Must be 'remarks' or 'driverRemarks'");
        }

        // Save the manifest
        Manifest updatedManifest = manifestRepository.save(manifest);

        // Log the change
        try {
            trackingLogService.logFieldUpdate(
                trackingNo,
                field,
                oldValue,
                value,
                getCurrentUser(),
                "Quick edit update"
            );
        } catch (Exception e) {
            logger.warn("Failed to log manifest update: {}", e.getMessage());
        }

        logger.info("Successfully updated {} field for manifest {}", field, trackingNo);
        return updatedManifest;
    }

    @Override
    @Transactional
    public Manifest updateSpecificField(String trackingNo, String fieldName, String fieldValue) {
        logger.info("Updating field {} for manifest {} with direct SQL", fieldName, trackingNo);

        Optional<Manifest> manifestOpt = manifestRepository.findByIdWithPallets(trackingNo);
        if (!manifestOpt.isPresent()) {
            throw new ResourceNotFoundException("Manifest not found with tracking number: " + trackingNo);
        }

        Manifest manifest = manifestOpt.get();
        String oldValue = null;
        String logField = null;

        // Update the specific database field
        if ("remarks".equals(fieldName)) {
            oldValue = manifest.getRemarks();
            manifest.setRemarks(fieldValue);
            logField = "remarks";
        } else if ("driver_remarks".equals(fieldName)) {
            oldValue = manifest.getDriverRemarks();
            manifest.setDriverRemarks(fieldValue);
            logField = "driverRemarks";
        } else {
            throw new IllegalArgumentException("Field not allowed for update: " + fieldName);
        }

        // Save the manifest
        Manifest updatedManifest = manifestRepository.save(manifest);

        // Log the change
        try {
            trackingLogService.logFieldUpdate(
                trackingNo,
                logField,
                oldValue,
                fieldValue,
                getCurrentUser(),
                "Direct field update"
            );
        } catch (Exception e) {
            logger.warn("Failed to log manifest update: {}", e.getMessage());
        }

        logger.info("Successfully updated field {} for manifest {}", fieldName, trackingNo);
        return updatedManifest;
    }
}