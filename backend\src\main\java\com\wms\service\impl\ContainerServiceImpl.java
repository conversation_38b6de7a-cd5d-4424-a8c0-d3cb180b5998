package com.wms.service.impl;

import com.wms.entity.Container;
import com.wms.entity.ContainerStatus;
import com.wms.entity.Manifest;
import com.wms.entity.ManifestStatus;
import com.wms.repository.ContainerRepository;
import com.wms.repository.ManifestRepository;
import com.wms.service.ContainerService;
import com.wms.exception.ResourceNotFoundException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
@Transactional
public class ContainerServiceImpl implements ContainerService {

    private final ContainerRepository containerRepository;
    private final ManifestRepository manifestRepository;
    private static final Logger logger = LoggerFactory.getLogger(ContainerServiceImpl.class);

    @Autowired
    public ContainerServiceImpl(ContainerRepository containerRepository, ManifestRepository manifestRepository) {
        this.containerRepository = containerRepository;
        this.manifestRepository = manifestRepository;
    }

    @Override
    public List<Container> getAllContainers() {
        return containerRepository.findAll();
    }

    @Override
    public Container getContainerByNo(String containerNo) {
        return containerRepository.findById(containerNo)
            .orElseThrow(() -> new ResourceNotFoundException("Container not found with number: " + containerNo));
    }

    @Override
    public List<Container> getContainersByUsername(String username) {
        return containerRepository.findByClientUsername(username);
    }

    @Override
    public List<Container> getContainersByStatus(ContainerStatus status) {
        return containerRepository.findByStatus(status);
    }

    @Override
    public Container createContainer(Container container) {
        // Check if there are existing containers with the same vessel/voyage number
        List<Container> existingContainers = containerRepository.findByVesselVoyageNo(container.getVesselVoyageNo());
        
        // If there are existing containers and they have a portnet ETA set, use that for the new container
        if (!existingContainers.isEmpty()) {
            for (Container existingContainer : existingContainers) {
                if (existingContainer.getPortnetEta() != null) {
                    logger.info("Setting portnet ETA to {} for new container {} based on existing container with the same vessel/voyage number: {}", 
                        existingContainer.getPortnetEta(), container.getContainerNo(), container.getVesselVoyageNo());
                    container.setPortnetEta(existingContainer.getPortnetEta());
                    break;
                }
            }
        }
        
        container.setStatus(ContainerStatus.CREATED);
        container.setCreatedDate(LocalDateTime.now());
        return containerRepository.save(container);
    }

    @Override
    public Container updateContainer(String containerNo, Container containerDetails) {
        Container container = containerRepository.findById(containerNo)
            .orElseThrow(() -> new ResourceNotFoundException("Container not found with number: " + containerNo));

        container.setTruckNo(containerDetails.getTruckNo());
        container.setVesselVoyageNo(containerDetails.getVesselVoyageNo());
        container.setEtaRequestedDate(containerDetails.getEtaRequestedDate());
        container.setManifestQuantity(containerDetails.getManifestQuantity());
        container.setPortnetEta(containerDetails.getPortnetEta());
        container.setEtaAllocated(containerDetails.getEtaAllocated());
        container.setLoadingBay(containerDetails.getLoadingBay());
        container.setUnstuffTeam(containerDetails.getUnstuffTeam());
        container.setRemark(containerDetails.getRemark());

        return containerRepository.save(container);
    }

    @Override
    @Transactional
    public Container updateContainerStatus(String containerNo, ContainerStatus status) {
        Container container = containerRepository.findById(containerNo)
            .orElseThrow(() -> new ResourceNotFoundException("Container not found with number: " + containerNo));

        logger.info("Updating container {} status from {} to {}", containerNo, container.getStatus(), status);
        container.setStatus(status);

        // Update relevant dates based on status
        switch (status) {
            case CONFIRMED:
                // When container is confirmed, automatically set ETA allocated to ETA requested
                if (container.getEtaRequestedDate() != null) {
                    container.setEtaAllocated(container.getEtaRequestedDate());
                    logger.info("Auto-setting ETA allocated to ETA requested date ({}) for confirmed container {}", 
                        container.getEtaRequestedDate(), containerNo);
                }
                break;
            case ARRIVED:
                container.setArrivalDate(LocalDateTime.now());
                break;
            case UNSTUFFING:
                container.setUnstuffDate(LocalDateTime.now());
                break;
            case UNSTUFF_COMPLETED:
                container.setUnstuffCompletedDate(LocalDateTime.now());
                break;
            case PULLED_OUT:
                container.setPullOutDate(LocalDateTime.now());
                break;
        }

        // Save container first
        Container updatedContainer = containerRepository.save(container);

        // Update all manifests associated with this container
        List<Manifest> manifests = manifestRepository.findByContainerContainerNo(containerNo);
        logger.info("Found {} manifests to update for container {}", manifests.size(), containerNo);
        
        int updatedCount = 0;
        for (Manifest manifest : manifests) {
            // Map container status to manifest status
            ManifestStatus manifestStatus = mapContainerStatusToManifestStatus(status);
            
            if (manifestStatus != null) {
                logger.info("Updating manifest {} status from {} to {}", 
                    manifest.getTrackingNo(), manifest.getStatus(), manifestStatus);
                manifest.setStatus(manifestStatus);
                manifestRepository.save(manifest);
                updatedCount++;
            }
        }
        
        logger.info("Successfully updated {} out of {} manifests for container {}", 
            updatedCount, manifests.size(), containerNo);
        
        // Force flush to ensure all changes are committed
        containerRepository.flush();
        manifestRepository.flush();

        return updatedContainer;
    }

    private ManifestStatus mapContainerStatusToManifestStatus(ContainerStatus containerStatus) {
        switch (containerStatus) {
            case CREATED:
                return ManifestStatus.CREATED;
            case CONFIRMED:
                return ManifestStatus.ETA_TO_WAREHOUSE;
            case ARRIVED:
                return ManifestStatus.ARRIVED;
            default:
                return null; // No mapping for other statuses
        }
    }

    @Override
    public void deleteContainer(String containerNo) {
        Container container = containerRepository.findById(containerNo)
            .orElseThrow(() -> new ResourceNotFoundException("Container not found with number: " + containerNo));
        containerRepository.delete(container);
    }

    @Override
    public Long getContainersCount() {
        // Get all containers and count them
        List<Container> containers = containerRepository.findAll();
        Long count = (long) containers.size();
        
        // Log details for debugging
        if (count > 0) {
            String containerNos = containers.stream()
                .limit(5)
                .map(Container::getContainerNo)
                .reduce((a, b) -> a + ", " + b)
                .orElse("");
            
            logger.debug("Found {} containers. Sample container numbers: {}", 
                      count, containerNos);
        } else {
            logger.debug("No containers found in database");
        }
        
        return count;
    }

    @Override
    public Optional<Container> getContainerByContainerNo(String containerNo) {
        return containerRepository.findById(containerNo);
    }
    
    @Override
    @Transactional
    public List<Container> updatePortnetEtaForVesselVoyage(String vesselVoyageNo, LocalDateTime portnetEta) {
        List<Container> containers = containerRepository.findByVesselVoyageNo(vesselVoyageNo);
        
        if (containers.isEmpty()) {
            logger.info("No containers found with vessel/voyage number: {}", vesselVoyageNo);
            return containers;
        }
        
        logger.info("Updating portnet ETA to {} for {} containers with vessel/voyage number: {}", 
                  portnetEta, containers.size(), vesselVoyageNo);
        
        for (Container container : containers) {
            container.setPortnetEta(portnetEta);
            containerRepository.save(container);
        }
        
        // Force flush to ensure all changes are committed
        containerRepository.flush();
        
        return containers;
    }
} 