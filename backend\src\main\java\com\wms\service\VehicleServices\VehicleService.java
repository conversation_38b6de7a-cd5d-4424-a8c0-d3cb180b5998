package com.wms.service.VehicleServices;

import com.wms.dto.VehicleDTO;
import com.wms.entity.vehicle.Vehicle;
import com.wms.entity.vehicle.VehicleStatus;

import java.util.List;

public interface VehicleService {
    VehicleDTO createVehicle(VehicleDTO vehicleDTO);
    VehicleDTO updateVehicle(Long id, VehicleDTO vehicleDTO);
    void deleteVehicle(Long id);
    VehicleDTO getVehicleById(Long id);
    List<VehicleDTO> getAllVehicles();
    List<VehicleDTO> getVehiclesByType(Long vehicleTypeId);
    VehicleDTO updateVehicleStatus(Long id, VehicleStatus status);
    VehicleDTO assignDriverToVehicle(Long id, String driverUsername);
    VehicleDTO removeDriverFromVehicle(Long id, String driverUsername);
    Vehicle getVehicleEntityById(Long id);
} 