-- Insert roles if they don't exist
INSERT INTO roles (id, name) 
SELECT 1, 'ROLE_ADMIN' 
WHERE NOT EXISTS (SELECT 1 FROM roles WHERE id = 1);

INSERT INTO roles (id, name) 
SELECT 2, 'ROLE_MANAGER' 
WHERE NOT EXISTS (SELECT 1 FROM roles WHERE id = 2);

INSERT INTO roles (id, name) 
SELECT 3, 'ROLE_CLIENT' 
WHERE NOT EXISTS (SELECT 1 FROM roles WHERE id = 3);

INSERT INTO roles (id, name) 
SELECT 4, 'ROLE_DRIVER' 
WHERE NOT EXISTS (SELECT 1 FROM roles WHERE id = 4);

-- Insert admin user if it doesn't exist
-- Password is 'admin123' (hashed)
-- This uses a specific BCrypt hash that should work across environments
INSERT INTO users (user_number, username, user_password, user_email, contact, full_name, status, user_type) 
SELECT 1001, 'admin', '$2a$10$kR/sCJ4gdYgHli7Q0y0OUeApvoZRvA/hfXqDMqkCIQOB/QkUE/FDq', '<EMAIL>', '1234567890', 'System Administrator', 'ACTIVE', 'ADMIN'
WHERE NOT EXISTS (SELECT 1 FROM users WHERE username = 'admin');

-- Ensure admin user has correct password even if it exists
UPDATE users SET user_password = '$2a$10$kR/sCJ4gdYgHli7Q0y0OUeApvoZRvA/hfXqDMqkCIQOB/QkUE/FDq'
WHERE username = 'admin';

-- Insert admin details if they don't exist
INSERT INTO admins (username) 
SELECT 'admin' 
WHERE NOT EXISTS (SELECT 1 FROM admins WHERE username = 'admin');

-- Link admin to admin role if it doesn't exist
INSERT INTO user_roles (username, role_id) 
SELECT 'admin', 1 
WHERE NOT EXISTS (SELECT 1 FROM user_roles WHERE username = 'admin' AND role_id = 1);

-- Insert simplified location zones
-- Delete any existing location zones first to avoid duplicates
DELETE FROM location_zones;

-- Create a single entry for each region with all postal code ranges in the description
-- CENTRAL: Codes 01-11, 13-32
INSERT INTO location_zones (id, name, abbreviation, start_postal_code, end_postal_code, special_cases, description, is_active)
VALUES (1, 'CENTRAL', 'C', 1, 11, '13-32', 'Central region - postal codes 01-11, 13-32 (excluding 12)', TRUE);

-- WEST: Code 12, 58-71
INSERT INTO location_zones (id, name, abbreviation, start_postal_code, end_postal_code, special_cases, description, is_active)
VALUES (2, 'WEST', 'W', 58, 71, '12', 'West region - postal codes 12, 58-71', TRUE);

-- EAST: Codes 33-47, 53, 55
INSERT INTO location_zones (id, name, abbreviation, start_postal_code, end_postal_code, special_cases, description, is_active)
VALUES (3, 'EAST', 'E', 33, 47, '53,55', 'East region - postal codes 33-47, 53, 55', TRUE);

-- NORTH_EAST: Codes 48-52, 54, 56-57, 81-82
INSERT INTO location_zones (id, name, abbreviation, start_postal_code, end_postal_code, special_cases, description, is_active)
VALUES (4, 'NORTH_EAST', 'NE', 48, 52, '54,56-57,81-82', 'North East region - postal codes 48-52, 54, 56-57, 81-82', TRUE);

-- NORTH: Codes 72-80, 83
INSERT INTO location_zones (id, name, abbreviation, start_postal_code, end_postal_code, special_cases, description, is_active)
VALUES (5, 'NORTH', 'N', 72, 80, '83', 'North region - postal codes 72-80, 83', TRUE);

-- SOUTH: Any other codes
INSERT INTO location_zones (id, name, abbreviation, start_postal_code, end_postal_code, special_cases, description, is_active)
VALUES (6, 'SOUTH', 'S', 84, 99, NULL, 'South region - postal codes 84-99', TRUE);

