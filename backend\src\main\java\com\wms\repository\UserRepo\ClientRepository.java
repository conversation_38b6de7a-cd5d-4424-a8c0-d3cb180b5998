package com.wms.repository.UserRepo;

import com.wms.entity.user.Client;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface ClientRepository extends JpaRepository<Client, String> {
    @Query("SELECT c FROM Client c LEFT JOIN FETCH c.roles WHERE c.email = :email")
    Optional<Client> findByEmail(String email);

    @Query("SELECT c FROM Client c LEFT JOIN FETCH c.roles WHERE c.username = :username")
    Optional<Client> findByUsername(String username);

    @Query("SELECT COUNT(c) > 0 FROM Client c WHERE c.email = :email")
    boolean existsByEmail(String email);

    @Query("SELECT COUNT(c) > 0 FROM Client c WHERE c.username = :username")
    boolean existsByUsername(String username);
} 