import React, { useRef, useEffect, useState } from 'react';
import { 
  Dialog, 
  DialogTitle, 
  DialogContent, 
  DialogActions, 
  Button, 
  IconButton,
  Box,
  Paper,
  Typography,
  Divider,
  TextField,
  Tooltip,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import PrintIcon from '@mui/icons-material/Print';
import SaveIcon from '@mui/icons-material/Save';
import { useReactToPrint } from 'react-to-print';
import QRCode from 'react-qr-code';
import Barcode from 'react-barcode';
import { Manifest } from '../types/manifest';
import { useToast } from '../contexts/ToastContext';
import AutorenewIcon from '@mui/icons-material/Autorenew';
import { formatDateString } from '../utils/dateUtils';

// Add print-specific styles
const printStyles = `
  @media print {
    @page {
      size: auto;
      margin: 10mm;
    }
    body {
      margin: 0;
      padding: 0;
    }
  }
`;

interface ManifestLabelDialogProps {
  open: boolean;
  onClose: () => void;
  manifest: Manifest | null;
  manifests?: Manifest[]; // Add an optional array of manifests for bulk printing
  onSaveLabelCount?: (trackingNo: string, count: number) => void;
  batchMode?: boolean; // Flag to indicate if we're in batch mode
  getLabelCount?: (trackingNo: string) => number | undefined; // Add a function to get label count for a manifest
}

const ManifestLabelDialog: React.FC<ManifestLabelDialogProps> = ({ 
  open, 
  onClose, 
  manifest,
  manifests = [],
  onSaveLabelCount,
  batchMode = false,
  getLabelCount
}) => {
  // Create ref for the printable content
  const printRef = useRef<HTMLDivElement>(null);
  // State for the number of labels to print
  const [labelCount, setLabelCount] = useState<number>(0);
  // State to track if user has manually changed the label count
  const [customLabelCount, setCustomLabelCount] = useState<boolean>(false);
  // State to track edited label counts for manifests in batch mode
  const [manifestLabelCounts, setManifestLabelCounts] = useState<Record<string, number>>({});
  // State to track which manifests have custom counts in batch mode
  const [customManifestCounts, setCustomManifestCounts] = useState<Record<string, boolean>>({});
  // Get toast context
  const toast = useToast();

  // Initialize label counts for manifests in batch mode
  useEffect(() => {
    if (batchMode && manifests.length > 0) {
      const initialCounts: Record<string, number> = {};
      manifests.forEach(m => {
        // Check if there's a saved label count first
        const savedCount = getLabelCount ? getLabelCount(m.trackingNo) : undefined;
        // Use saved count if available, otherwise use the pieces attribute
        initialCounts[m.trackingNo] = savedCount !== undefined ? savedCount : (m.pieces || 1);
      });
      setManifestLabelCounts(initialCounts);
      setCustomManifestCounts({});
    }
  }, [batchMode, manifests, getLabelCount]);

  // Set default label count based on pieces when manifest changes or initial count is provided
  useEffect(() => {
    if (manifest && !customLabelCount) {
      // First check for a saved count, otherwise use the pieces attribute
      const savedCount = getLabelCount ? getLabelCount(manifest.trackingNo) : undefined;
      setLabelCount(savedCount !== undefined ? savedCount : (manifest.pieces || 1));
    }
  }, [manifest, getLabelCount, customLabelCount]);

  // Log ref value when component updates
  useEffect(() => {
    if (open && printRef.current) {
      console.log("Print ref is ready:", printRef.current);
    }
  }, [open]);

  // Handle label count change
  const handleLabelCountChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(event.target.value) || 1;
    setLabelCount(value);
    setCustomLabelCount(true);
  };

  // Reset to default (pieces count)
  const resetToDefault = () => {
    if (manifest) {
      // Always reset to the manifest's pieces attribute
      setLabelCount(manifest.pieces || 1);
      setCustomLabelCount(false);
    }
  };

  // Save the current label count as default for this manifest
  const handleSaveLabelCount = () => {
    if (manifest && onSaveLabelCount) {
      onSaveLabelCount(manifest.trackingNo, labelCount);
      toast.success(`Saved ${labelCount} as the default label count for this manifest`);
    }
  };

  // Setup react-to-print with the correct contentRef
  const handlePrint = useReactToPrint({
    contentRef: printRef,
    documentTitle: batchMode 
      ? `Batch_Manifest_Labels_${manifests.length}` 
      : `Manifest_${manifest?.trackingNo || 'Label'}`,
    onAfterPrint: () => {
      if (batchMode) {
        console.log(`Batch print completed - ${manifests.length} manifests`);
      } else {
        console.log(`Print completed - ${labelCount} labels`);
      }
    },
    onPrintError: (error) => {
      console.error('Print error:', error);
      alert(`Print error: ${error ? String(error) : 'Unknown error'}`);
    },
    pageStyle: printStyles
  });

  // If in batch mode but no manifests provided, or if not in batch mode and no manifest provided, return null
  if ((batchMode && (!manifests || manifests.length === 0)) || (!batchMode && !manifest)) return null;

  const triggerPrint = () => {
    if (batchMode) {
      console.log(`Attempting to print batch of ${manifests.length} manifests. printRef.current:`, printRef.current);
    } else {
      console.log(`Attempting to print ${labelCount} labels. printRef.current:`, printRef.current);
    }
    
    if (!printRef.current) {
      console.error("Cannot print, printRef.current is null or undefined.");
      alert("Error: Printable content not found. Please try again.");
      return;
    }
    
    try {
      // For each label we want to print
      handlePrint();
    } catch (error) {
      console.error("Error during print:", error);
      alert(`Printing failed: ${error}`);
    }
  };

  // Add a function to get label count for a manifest
  const getManifestLabelCount = (m: Manifest): number => {
    // First check if there's a saved count
    if (getLabelCount) {
      const savedCount = getLabelCount(m.trackingNo);
      if (savedCount !== undefined) return savedCount;
    }
    // Always fall back to the pieces attribute
    return m.pieces || 1;
  };

  // Handle label count change for a specific manifest in batch mode
  const handleBatchLabelCountChange = (trackingNo: string, value: number) => {
    setManifestLabelCounts(prev => ({
      ...prev,
      [trackingNo]: value
    }));
    setCustomManifestCounts(prev => ({
      ...prev,
      [trackingNo]: true
    }));
  };

  // Reset to default count for a specific manifest in batch mode
  const resetManifestToDefault = (m: Manifest) => {
    // Always reset to the manifest's pieces attribute
    const defaultCount = m.pieces || 1;
    setManifestLabelCounts(prev => ({
      ...prev,
      [m.trackingNo]: defaultCount
    }));
    setCustomManifestCounts(prev => {
      const newCustomCounts = { ...prev };
      delete newCustomCounts[m.trackingNo];
      return newCustomCounts;
    });
  };

  // Save the label count for a specific manifest in batch mode
  const handleSaveManifestLabelCount = (m: Manifest) => {
    if (onSaveLabelCount && manifestLabelCounts[m.trackingNo]) {
      onSaveLabelCount(m.trackingNo, manifestLabelCounts[m.trackingNo]);
      toast.success(`Saved ${manifestLabelCounts[m.trackingNo]} as the default label count for manifest ${m.trackingNo}`);
      
      // Remove from custom counts after saving
      setCustomManifestCounts(prev => {
        const newCustomCounts = { ...prev };
        delete newCustomCounts[m.trackingNo];
        return newCustomCounts;
      });
    }
  };

  // Add a function to get the internal ID
  const getInternalId = (m: Manifest | null): string => {
    if (!m) return 'N/A';
    if (m.internalId) return m.internalId;
    
    // Generate internal ID if it doesn't exist and we have container number and sequence number
    if (m.container?.containerNo && m.sequenceNo) {
      return `${m.container.containerNo}-${m.sequenceNo}`;
    }
    
    return 'N/A';
  };

  return (
    <Dialog 
      open={open} 
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          boxShadow: 24,
        },
      }}
    >
      <DialogTitle>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Box>
            {batchMode 
              ? `Batch Manifest Labels (${manifests.length} manifests)` 
              : `Manifest Label - ${manifest?.trackingNo}`}
          </Box>
          <IconButton
            aria-label="close"
            onClick={onClose}
            size="small"
          >
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>
      <DialogContent dividers>
        {!batchMode && manifest && (
          <Box sx={{ mb: 2, display: 'flex', alignItems: 'center', gap: 2 }}>
            <TextField
              label="Number of Labels"
              type="number"
              size="small"
              value={labelCount}
              onChange={handleLabelCountChange}
              inputProps={{ min: 1 }}
              sx={{ width: 120 }}
            />
            {customLabelCount && (
              <Button 
                size="small" 
                onClick={resetToDefault} 
                variant="outlined"
              >
                Reset to Pieces ({manifest.pieces})
              </Button>
            )}
            {onSaveLabelCount && customLabelCount && (
              <Tooltip title="Save this label count as default for future use">
                <Button
                  size="small"
                  variant="outlined"
                  color="success"
                  startIcon={<SaveIcon />}
                  onClick={handleSaveLabelCount}
                >
                  Save Count
                </Button>
              </Tooltip>
            )}
          </Box>
        )}
        
        {batchMode && (
          <Box sx={{ mb: 2 }}>
            <Alert severity="info" sx={{ mb: 2 }}>
              Preparing to print labels for {manifests.length} manifests. Edit the label count for each manifest if needed.
            </Alert>
            <TableContainer component={Paper} variant="outlined" sx={{ maxHeight: '350px', mb: 2 }}>
              <Table size="small" stickyHeader>
                <TableHead>
                  <TableRow>
                    <TableCell sx={{ fontWeight: 'bold' }}>Tracking No</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Internal ID</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Customer</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }} align="center">Label Count</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }} align="center">Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {manifests.map((m) => {
                    const isCustomCount = customManifestCounts[m.trackingNo];
                    const count = manifestLabelCounts[m.trackingNo] || getManifestLabelCount(m);
                    
                    return (
                      <TableRow key={m.trackingNo} hover>
                        <TableCell>{m.trackingNo}</TableCell>
                        <TableCell>{getInternalId(m)}</TableCell>
                        <TableCell>{m.customerName}</TableCell>
                        <TableCell align="center">
                          <TextField
                            type="number"
                            size="small"
                            value={count}
                            onChange={(e) => handleBatchLabelCountChange(m.trackingNo, parseInt(e.target.value) || 1)}
                            inputProps={{ min: 1, style: { textAlign: 'center' } }}
                            sx={{ width: 70 }}
                          />
                        </TableCell>
                        <TableCell align="center">
                          <Box sx={{ display: 'flex', justifyContent: 'center', gap: 1 }}>
                            {isCustomCount && (
                              <Tooltip title="Reset to default count">
                                <IconButton 
                                  size="small" 
                                  onClick={() => resetManifestToDefault(m)}
                                >
                                  <AutorenewIcon fontSize="small" />
                                </IconButton>
                              </Tooltip>
                            )}
                            {onSaveLabelCount && isCustomCount && (
                              <Tooltip title="Save as default for future use">
                                <IconButton 
                                  size="small" 
                                  color="success" 
                                  onClick={() => handleSaveManifestLabelCount(m)}
                                >
                                  <SaveIcon fontSize="small" />
                                </IconButton>
                              </Tooltip>
                            )}
                          </Box>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </TableContainer>
          </Box>
        )}
        
        {/* Content to print - Making sure the ref is properly attached to the div we want to print */}
        <div 
          ref={printRef} 
          style={{ width: '100%' }} 
          className="printable-content"
          data-testid="manifest-print-content"
        >
          {batchMode ? (
            // In batch mode, render all manifests
            manifests.map((m) => {
              // Get the label count for this manifest from the state or fallback to saved/default
              const manifestLabelCount = manifestLabelCounts[m.trackingNo] || 
                (getLabelCount ? (getLabelCount(m.trackingNo) || m.pieces || 1) : (m.pieces || 1));
              
              return Array.from({ length: manifestLabelCount }, (_, labelIndex) => (
                <Box 
                  key={`${m.trackingNo}-${labelIndex}`} 
                  sx={{ 
                    pageBreakAfter: 'always', 
                    mb: 4 
                  }}
        >
          <Paper 
            sx={{ 
                      p: 1.5, 
              width: '100%', 
                      maxWidth: '500px', 
              margin: '0 auto',
              border: '1px solid #ccc'
            }}
          >
                    {/* Company Header */}
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>
                      <Typography variant="subtitle1" fontWeight="bold">
                  Fukuyama Logistics
                </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Label {labelIndex + 1}/{manifestLabelCount}
                </Typography>
            </Box>
            
                    {/* Large Barcode Section */}
                    <Box sx={{ width: '100%', display: 'flex', justifyContent: 'center', mb: 1 }}>
                  <Barcode 
                        value={m.trackingNo} 
                        width={2}
                        height={70}
                        fontSize={12}
                    margin={5}
                        displayValue={false}
                  />
                </Box>
                    
                    {/* Tracking & Internal ID */}
                    <Box sx={{ 
                      mb: 1,
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      bgcolor: '#f5f5f5',
                      p: 1,
                      borderRadius: 1
                    }}>
                      <Typography variant="body2" fontWeight="bold">
                        Tracking No: {m.trackingNo}
                      </Typography>
                      <Typography variant="h5" fontWeight="bold" align="center">
                        {getInternalId(m)}
                      </Typography>
                    </Box>
                    
                    <Divider sx={{ mb: 1 }} />
                    
                    {/* Main content in two columns */}
                    <Box sx={{ display: 'flex', mb: 1 }}>
                      {/* Left column - Client Details */}
                      <Box sx={{ flex: 1, pr: 1, borderRight: '1px dashed #ddd' }}>
                        <Typography variant="subtitle2" fontWeight="bold">Client Details:</Typography>
                        <Typography variant="body2" fontSize="0.85rem">
                          Username: {m.client?.username || 'N/A'}
                        </Typography>
                        <Typography variant="body2" fontSize="0.85rem">
                          Company: {m.client?.companyName || 'N/A'}
                        </Typography>
                        
                        <Typography variant="subtitle2" fontWeight="bold" sx={{ mt: 0.5 }}>Container:</Typography>
                        <Typography variant="body2" fontSize="0.85rem">
                          {m.container?.containerNo || 'N/A'}
                        </Typography>
                        
                        <Typography variant="subtitle2" fontWeight="bold" sx={{ mt: 0.5 }}>Customer:</Typography>
                        <Typography variant="body2" fontSize="0.85rem">
                          {m.customerName || 'N/A'}
                </Typography>
              </Box>
              
                      {/* Right column - Customer Details */}
                      <Box sx={{ flex: 1, pl: 1 }}>
                        <Typography variant="subtitle2" fontWeight="bold">Contact:</Typography>
                        <Typography variant="body2" fontSize="0.85rem">
                          Phone: {m.phoneNo || 'N/A'}
                        </Typography>
                        
                        <Typography variant="subtitle2" fontWeight="bold" sx={{ mt: 0.5 }}>Delivery Address:</Typography>
                        <Typography variant="body2" fontSize="0.85rem" sx={{ lineHeight: 1.2 }}>
                          {m.address || 'N/A'}, 
                          {m.postalCode ? ` ${m.postalCode},` : ''} 
                          {m.country ? ` ${m.country}` : ''}
                        </Typography>
                      </Box>
                    </Box>
                    
                    {/* Shipment Details */}
                    <Typography variant="subtitle2" fontWeight="bold">Shipment Details:</Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', mb: 1 }}>
                      <Box sx={{ width: '50%' }}>
                        <Typography variant="body2" fontSize="0.85rem">
                          <strong>Pieces:</strong> {m.pieces}
                        </Typography>
                      </Box>
                      <Box sx={{ width: '50%' }}>
                        <Typography variant="body2" fontSize="0.85rem">
                          <strong>CBM:</strong> {m.cbm}
                        </Typography>
                      </Box>
                      <Box sx={{ width: '50%' }}>
                        <Typography variant="body2" fontSize="0.85rem">
                          <strong>Weight:</strong> {m.weight} kg
                        </Typography>
                      </Box>
                      <Box sx={{ width: '50%' }}>
                        <Typography variant="body2" fontSize="0.85rem">
                          <strong>Location:</strong> {m.location || 'N/A'}
                        </Typography>
                      </Box>
                      <Box sx={{ width: '50%' }}>
                        <Typography variant="body2" fontSize="0.85rem">
                          <strong>Vehicle:</strong> {m.deliveryVehicle || 'N/A'}
                        </Typography>
                </Box>
                      <Box sx={{ width: '50%' }}>
                        <Typography variant="body2" fontSize="0.85rem">
                          <strong>Pallets:</strong> {m.actualPalletsCount ?? 0}
                </Typography>
              </Box>
            </Box>
            
                    {/* Status and Date - Bottom row */}
                    <Divider sx={{ my: 0.5 }} />
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Box>
                        <Typography variant="body2" fontSize="0.85rem">
                          Status: {m.status ? m.status.replace(/_/g, ' ') : 'N/A'}
                        </Typography>
                        <Typography variant="body2" fontSize="0.85rem">
                          Delivery: {m.deliveryDate ? formatDateString(m.deliveryDate) : 'N/A'}
                        </Typography>
                      </Box>
                      <QRCode
                        value={m.trackingNo}
                        size={60}
                        level="M"
                      />
                    </Box>
                  </Paper>
                </Box>
              ));
            })
          ) : (
            // Single manifest mode
            Array.from({ length: labelCount }, (_, index) => (
              <Box key={index} sx={{ pageBreakAfter: index < labelCount - 1 ? 'always' : 'auto', mb: index < labelCount - 1 ? 4 : 0 }}>
                <Paper 
                  sx={{ 
                    p: 1.5, 
                    width: '100%', 
                    maxWidth: '500px', 
                    margin: '0 auto',
                    border: '1px solid #ccc'
                  }}
                >
                  {/* Company Header */}
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>
                    <Typography variant="subtitle1" fontWeight="bold">
                      Fukuyama Logistics
                </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Label {index + 1}/{labelCount}
                </Typography>
                  </Box>
                  
                  {/* Large Barcode Section */}
                  <Box sx={{ width: '100%', display: 'flex', justifyContent: 'center', mb: 1 }}>
                    <Barcode 
                      value={manifest?.trackingNo || ''} 
                      width={2}
                      height={70}
                      fontSize={12}
                      margin={5}
                      displayValue={false}
                    />
                  </Box>
                  
                  {/* Tracking & Internal ID */}
                  <Box sx={{ 
                    mb: 1,
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    bgcolor: '#f5f5f5',
                    p: 1,
                    borderRadius: 1
                  }}>
                    <Typography variant="body2" fontWeight="bold">
                      Tracking No: {manifest?.trackingNo}
                </Typography>
                    <Typography variant="h5" fontWeight="bold" align="center">
                      {getInternalId(manifest)}
                </Typography>
              </Box>
              
                  <Divider sx={{ mb: 1 }} />
                  
                  {/* Main content in two columns */}
                  <Box sx={{ display: 'flex', mb: 1 }}>
                    {/* Left column - Client Details */}
                    <Box sx={{ flex: 1, pr: 1, borderRight: '1px dashed #ddd' }}>
                      <Typography variant="subtitle2" fontWeight="bold">Client Details:</Typography>
                      <Typography variant="body2" fontSize="0.85rem">
                        Username: {manifest?.client?.username || 'N/A'}
                      </Typography>
                      <Typography variant="body2" fontSize="0.85rem">
                        Company: {manifest?.client?.companyName || 'N/A'}
                </Typography>
                      
                      <Typography variant="subtitle2" fontWeight="bold" sx={{ mt: 0.5 }}>Container:</Typography>
                      <Typography variant="body2" fontSize="0.85rem">
                        {manifest?.container?.containerNo || 'N/A'}
                </Typography>
                      
                      <Typography variant="subtitle2" fontWeight="bold" sx={{ mt: 0.5 }}>Customer:</Typography>
                      <Typography variant="body2" fontSize="0.85rem">
                        {manifest?.customerName || 'N/A'}
                </Typography>
                    </Box>
                    
                    {/* Right column - Customer Details */}
                    <Box sx={{ flex: 1, pl: 1 }}>
                      <Typography variant="subtitle2" fontWeight="bold">Contact:</Typography>
                      <Typography variant="body2" fontSize="0.85rem">
                        Phone: {manifest?.phoneNo || 'N/A'}
                </Typography>
                      
                      <Typography variant="subtitle2" fontWeight="bold" sx={{ mt: 0.5 }}>Delivery Address:</Typography>
                      <Typography variant="body2" fontSize="0.85rem" sx={{ lineHeight: 1.2 }}>
                        {manifest?.address || 'N/A'}, 
                        {manifest?.postalCode ? ` ${manifest.postalCode},` : ''} 
                        {manifest?.country ? ` ${manifest.country}` : ''}
                </Typography>
              </Box>
            </Box>
            
                  {/* Shipment Details */}
                  <Typography variant="subtitle2" fontWeight="bold">Shipment Details:</Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', mb: 1 }}>
                    <Box sx={{ width: '50%' }}>
                      <Typography variant="body2" fontSize="0.85rem">
                        <strong>Pieces:</strong> {manifest?.pieces}
              </Typography>
                    </Box>
                    <Box sx={{ width: '50%' }}>
                      <Typography variant="body2" fontSize="0.85rem">
                        <strong>CBM:</strong> {manifest?.cbm}
                  </Typography>
                    </Box>
                    <Box sx={{ width: '50%' }}>
                      <Typography variant="body2" fontSize="0.85rem">
                        <strong>Weight:</strong> {manifest?.weight} kg
                  </Typography>
                    </Box>
                    <Box sx={{ width: '50%' }}>
                      <Typography variant="body2" fontSize="0.85rem">
                        <strong>Location:</strong> {manifest?.location || 'N/A'}
                  </Typography>
                    </Box>
                    <Box sx={{ width: '50%' }}>
                      <Typography variant="body2" fontSize="0.85rem">
                        <strong>Vehicle:</strong> {manifest?.deliveryVehicle || 'N/A'}
                  </Typography>
                    </Box>
                    <Box sx={{ width: '50%' }}>
                      <Typography variant="body2" fontSize="0.85rem">
                        <strong>Pallets:</strong> {manifest?.actualPalletsCount ?? 0}
                  </Typography>
                    </Box>
            </Box>
            
                  {/* Status and Date - Bottom row */}
                  <Divider sx={{ my: 0.5 }} />
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Box>
                      <Typography variant="body2" fontSize="0.85rem">
                        Status: {manifest?.status ? manifest.status.replace(/_/g, ' ') : 'N/A'}
                </Typography>
                      <Typography variant="body2" fontSize="0.85rem">
                        Delivery: {manifest?.deliveryDate ? formatDateString(manifest.deliveryDate) : 'N/A'}
                </Typography>
              </Box>
                    <QRCode
                      value={manifest?.trackingNo || ''}
                      size={60}
                      level="M"
                    />
            </Box>
          </Paper>
              </Box>
            ))
          )}
        </div>
      </DialogContent>
      <DialogActions>
        <Button 
          variant="contained" 
          startIcon={<PrintIcon />} 
          onClick={triggerPrint}
        >
          {batchMode
            ? `Print ${manifests.length} Manifest Labels`
            : `Print ${labelCount > 1 ? `${labelCount} Labels` : 'Label'}`}
        </Button>
        <Button onClick={onClose}>Close</Button>
      </DialogActions>
    </Dialog>
  );
};

export default ManifestLabelDialog; 