import React, { useState } from 'react';
import { 
  Box, 
  Alert, 
  CircularProgress, 
  Ty<PERSON><PERSON>,
  Button,
  Chip
} from '@mui/material';
import { 
  DataGrid, 
  GridColDef, 
  GridRenderCellParams,
  GridToolbarContainer,
  GridToolbarQuickFilter,
  GridColumnVisibilityModel,
  useGridApiRef,
  GridRowSelectionModel,
  gridPageCountSelector,
  gridPageSizeSelector,
  gridPageSelector,
  useGridApiContext,
  useGridSelector,
  GridPagination
} from '@mui/x-data-grid';
import { Manifest } from '../../types/manifest';
import { Container } from '../../types/Container';
import { ManifestStatus } from '../../types/manifest';

interface ManifestListGridProps {
  manifests: Manifest[];
  columns: GridColDef[];
  visibleColumns: Record<string, boolean>;
  loading: boolean;
  error: string | null;
  onSelectionChange: (selected: Manifest[]) => void;
  onColumnVisibilityChange?: (newVisibleColumns: Record<string, boolean>) => void;
  onEdit?: (manifest: Manifest) => void;
  onDelete?: (trackingNo: string) => void;
  onChangeStatus?: (trackingNo: string, status: string) => void;
  containerContext?: Container | null;
}

// Custom toolbar with quick filter only
function CustomToolbar() {
  return (
    <GridToolbarContainer sx={{
      p: 0.5,
      display: 'flex',
      justifyContent: 'space-between',
      minHeight: '32px',
      '& .MuiInputBase-root': {
        fontSize: '0.75rem',
        height: '28px'
      },
      '& .MuiInputBase-input': {
        padding: '4px 8px'
      }
    }}>
      <GridToolbarQuickFilter />
    </GridToolbarContainer>
  );
}

// Custom pagination component built from scratch
function CustomPagination() {
  const apiRef = useGridApiContext();
  const page = useGridSelector(apiRef, gridPageSelector);
  const pageSize = useGridSelector(apiRef, gridPageSizeSelector);
  const rowCount = apiRef.current.getRowsCount();
  const pageCount = Math.ceil(rowCount / pageSize);

  const handlePageSizeChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const newPageSize = Number(event.target.value);
    apiRef.current.setPageSize(newPageSize);
  };

  const handlePageChange = (newPage: number) => {
    apiRef.current.setPage(newPage);
  };

  return (
    <Box sx={{
      display: 'flex',
      alignItems: 'center',
      padding: '4px 12px',
      borderTop: '1px solid rgba(224, 224, 224, 1)',
      width: '100%'
    }}>
      {/* 1. Rows per page section - forced to the left */}
      <Box sx={{ display: 'flex', alignItems: 'center', mr: 2 }}>
        <Typography variant="caption" mr={1}>
          Rows per page:
        </Typography>
        <select
          value={pageSize}
          onChange={handlePageSizeChange}
          style={{
            padding: '2px 6px',
            border: '1px solid #ddd',
            borderRadius: '4px',
            backgroundColor: '#ffffff',
            color: '#333333',
            textAlign: 'center',
            fontFamily: 'inherit',
            fontSize: '0.75rem',
            minWidth: '50px',
            cursor: 'pointer',
            outline: 'none'
          }}
        >
          {[50, 100].map((size) => (
            <option key={size} value={size}>
              {size}
            </option>
          ))}
        </select>
      </Box>

      {/* 2. Page count display - in the middle */}
      <Typography variant="caption" sx={{ mr: 2 }}>
        {`${page * pageSize + 1}–${Math.min((page + 1) * pageSize, rowCount)} of ${rowCount}`}
      </Typography>

      {/* 3. Navigation buttons - moved to the left */}
      <Box sx={{ display: 'flex', mr: 2 }}>
        <Button
          onClick={() => handlePageChange(page - 1)}
          disabled={page === 0}
          size="small"
          sx={{
            minWidth: '24px',
            width: '24px',
            height: '24px',
            p: 0,
            mr: 0.5,
            borderRadius: '4px',
            border: '1px solid #e0e0e0',
            backgroundColor: '#f5f5f5',
            color: 'text.primary',
            fontSize: '0.7rem',
            '&:hover': {
              backgroundColor: '#e0e0e0',
            },
            '&.Mui-disabled': {
              opacity: 0.5,
              color: 'text.disabled'
            }
          }}
        >
          {'<'}
        </Button>
        <Button
          onClick={() => handlePageChange(page + 1)}
          disabled={page >= pageCount - 1}
          size="small"
          sx={{
            minWidth: '24px',
            width: '24px',
            height: '24px',
            p: 0,
            borderRadius: '4px',
            border: '1px solid #e0e0e0',
            backgroundColor: '#f5f5f5',
            color: 'text.primary',
            fontSize: '0.7rem',
            '&:hover': {
              backgroundColor: '#e0e0e0',
            },
            '&.Mui-disabled': {
              opacity: 0.5,
              color: 'text.disabled'
            }
          }}
        >
          {'>'}
        </Button>
      </Box>

      {/* Empty spacer to push items to the left */}
      <Box sx={{ flexGrow: 1 }} />
    </Box>
  );
}

// Function to get status chip colors - matching ManifestManagement implementation
const getStatusChipColor = (status: ManifestStatus) => {
  switch (status) {
    case ManifestStatus.CREATED:
      return { bg: '#e3f2fd', color: '#1976d2' }; // Light blue
    case ManifestStatus.ETA_TO_WAREHOUSE:
      return { bg: '#fff8e1', color: '#ff8f00' }; // Amber
    case ManifestStatus.ARRIVED:
      return { bg: '#f1f8e9', color: '#689f38' }; // Light green
    case ManifestStatus.ON_HOLD:
      return { bg: '#ffebee', color: '#d32f2f' }; // Red
    case ManifestStatus.INBOUNDING:
      return { bg: '#fff3e0', color: '#ef6c00' }; // Orange
    case ManifestStatus.INBOUNDED_TO_WAREHOUSE:
      return { bg: '#e8eaf6', color: '#3f51b5' }; // Indigo
    case ManifestStatus.READY_TO_DELIVER:
      return { bg: '#e0f7fa', color: '#00838f' }; // Cyan
    case ManifestStatus.PENDING_DELIVER:
      return { bg: '#ede7f6', color: '#673ab7' }; // Deep purple
    case ManifestStatus.DELIVERING:
      return { bg: '#e8f5e9', color: '#2e7d32' }; // Green
    case ManifestStatus.DELIVERED:
      return { bg: '#e0f2f1', color: '#00695c' }; // Teal
    case ManifestStatus.DISCREPANCY:
      return { bg: '#ffcdd2', color: '#c62828' }; // Bright Red
    default:
      return { bg: '#f5f5f5', color: '#757575' }; // Gray
  }
};

// Convert back to regular component without forwardRef
const ManifestListGrid: React.FC<ManifestListGridProps> = ({
  manifests,
  columns,
  visibleColumns,
  loading,
  error,
  onSelectionChange,
  onColumnVisibilityChange,
  onEdit,
  onDelete,
  onChangeStatus,
  containerContext
}) => {
  // Reference to DataGrid API for operations
  const apiRef = useGridApiRef();

  // State to store column widths
  const [columnWidths, setColumnWidths] = React.useState<Record<string, number>>({});
  
  // Generate column visibility model
  const columnVisibilityModel = Object.entries(visibleColumns).reduce(
    (model, [field, isVisible]) => ({
      ...model,
      [field]: isVisible
    }),
    {} as GridColumnVisibilityModel
  );
  
  if (loading) {
    return (
      <Box sx={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '300px'
      }}>
        <CircularProgress />
      </Box>
    );
  }
  
  if (error) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        {error}
      </Alert>
    );
  }
  
  if (!manifests || manifests.length === 0) {
    return (
      <Box sx={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '300px',
        bgcolor: 'background.paper',
        borderRadius: 1,
        border: '1px dashed rgba(0, 0, 0, 0.12)',
      }}>
        <Typography color="text.secondary">
          No manifests found
        </Typography>
      </Box>
    );
  }
  
  // Handle column visibility change in the DataGrid
  const handleColumnVisibilityModelChange = (newModel: GridColumnVisibilityModel) => {
    if (onColumnVisibilityChange) {
      const newVisibleColumns = { ...visibleColumns };
      
      // Update visibility based on the new model
      Object.keys(newModel).forEach((field) => {
        if (field in newVisibleColumns) {
          newVisibleColumns[field] = newModel[field];
        }
      });
      
      onColumnVisibilityChange(newVisibleColumns);
    }
  };
  
  return (
    <Box sx={{ 
      width: '100%',
      minWidth: '100%',
      overflow: 'visible', // Allow content to expand beyond container
    }}>
      <DataGrid
        key="manifest-grid"
        apiRef={apiRef}
        rows={manifests.map(manifest => ({
          ...manifest,
          id: manifest.trackingNo || `id-${Math.random().toString(36).substr(2, 9)}`,
        }))}
        columns={columns.map(column => {
          // Apply stored width if available
          const columnWithWidth = {
            ...column,
            ...(columnWidths[column.field] && { width: columnWidths[column.field] })
          };

          // Add custom rendering for status column
          if (column.field === 'status') {
            return {
              ...columnWithWidth,
              renderCell: (params: GridRenderCellParams) => {
                if (!params || !params.row) return null;
                const status = params.row.status;
                if (!status) return null;

                const { bg, color } = getStatusChipColor(status as ManifestStatus);

                return (
                  <Chip
                    label={String(status).replace(/_/g, ' ')}
                    sx={{
                      backgroundColor: bg,
                      color: color,
                      fontWeight: 'medium',
                      fontSize: '0.65rem',
                      height: '20px',
                      borderRadius: '10px',
                      '& .MuiChip-label': {
                        px: 1
                      }
                    }}
                  />
                );
              }
            };
          }
          return columnWithWidth;
        })}
        columnVisibilityModel={columnVisibilityModel}
        onColumnVisibilityModelChange={handleColumnVisibilityModelChange}
        onColumnResize={(params) => {
          // Store the new column width
          console.log('Column resized:', params.colDef.field, 'new width:', params.width);
          setColumnWidths(prev => ({
            ...prev,
            [params.colDef.field]: params.width
          }));
        }}
        checkboxSelection
        getRowId={(row) => row.trackingNo || row.id}
        onRowSelectionModelChange={(newSelectionModel: GridRowSelectionModel) => {
          if (!onSelectionChange) return;
          
          console.log('Selection model type:', typeof newSelectionModel);
          console.log('Selection model value:', newSelectionModel);
          
          try {
            // Handle the special format of the selection model: {type: 'include', ids: Set}
            let selectedIds: Set<string> | null = null;
            
            // Check if newSelectionModel has the expected structure
            if (
              newSelectionModel && 
              typeof newSelectionModel === 'object' &&
              'ids' in newSelectionModel &&
              newSelectionModel.ids instanceof Set
            ) {
              // Extract the Set of IDs
              selectedIds = newSelectionModel.ids as Set<string>;
              console.log('Selection IDs as Set:', selectedIds);
              
              // Find manifests with tracking numbers in the Set
              const selectedItems = manifests.filter(manifest => 
                manifest.trackingNo && selectedIds?.has(manifest.trackingNo)
              );
              
              console.log('Selected items:', selectedItems);
              onSelectionChange(selectedItems);
            } else if (Array.isArray(newSelectionModel)) {
              // If it's a simple array, use it directly
              const selectedItems = manifests.filter(manifest => 
                manifest.trackingNo && newSelectionModel.includes(manifest.trackingNo)
              );
              
              console.log('Selected items (array):', selectedItems);
              onSelectionChange(selectedItems);
            } else {
              console.log('Unrecognized selection model format');
              onSelectionChange([]);
            }
          } catch (error) {
            console.error('Error in selection handler:', error);
            onSelectionChange([]);
          }
        }}
        pageSizeOptions={[50, 100]}
        initialState={{
          pagination: {
            paginationModel: { pageSize: 100 },
          },
          sorting: {
            sortModel: [{ field: 'internalId', sort: 'asc' }],
          },
          columns: {
            columnVisibilityModel: columnVisibilityModel,
          },
        }}
        onSortModelChange={(model) => {
          // If the sort model is empty (no sorting), revert to default sort by internalId
          if (model.length === 0) {
            // Use setTimeout to avoid state update conflicts
            setTimeout(() => {
              if (apiRef.current) {
                apiRef.current.setSortModel([{ field: 'internalId', sort: 'asc' }]);
              }
            }, 0);
          }
        }}
        keepNonExistentRowsSelected={false}
        density="compact"
        autoHeight
        getRowHeight={() => 45}
        hideFooterSelectedRowCount={true}
        showCellVerticalBorder={false}
        showColumnVerticalBorder={false}
        getEstimatedRowHeight={() => 45}
        columnBufferPx={2000} // Ensure all columns are rendered with sufficient buffer
        getRowClassName={(params) => {
          // Add CSS class based on status
          const status = (params.row.status || '').toLowerCase();
          return `manifest-status-${status}`;
        }}
        slots={{
          toolbar: (props) => (
            <CustomToolbar />
          ),
          noRowsOverlay: () => (
            <Box sx={{ 
              display: 'flex', 
              justifyContent: 'center', 
              alignItems: 'center', 
              height: '100%',
              minHeight: '200px'
            }}>
              <Typography color="text.secondary">
                No manifests found
              </Typography>
            </Box>
          ),
          pagination: CustomPagination,
        }}
        sx={{
          width: '100% !important', // Ensure DataGrid takes full width
          minWidth: '100%', // Maintain minimum width
          border: 'none',
          borderRadius: 2,
          display: 'flex',
          flexDirection: 'column',
          position: 'relative',
          overflow: 'visible', // Allow content to overflow
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
          
          // Hide empty cells
          '& .MuiDataGrid-cellEmpty': {
            display: 'none !important',
          },
          
          // Hide the filler element in header row
          '& .MuiDataGrid-columnHeaders .MuiDataGrid-filler': {
            display: 'none !important',
          },
          
          // Improved row styling
          '& .MuiDataGrid-row': {
            '&:hover': {
              backgroundColor: '#f8f9fa',
              cursor: 'pointer',
            },
            '&.Mui-selected': {
              backgroundColor: '#e3f2fd',
              '&:hover': {
                backgroundColor: '#bbdefb',
              },
            },
          },
          
          // Cell styling improvements
          '& .MuiDataGrid-cell': {
            padding: '4px 8px',
            borderBottom: '1px solid #f0f0f0',
            fontSize: '0.75rem',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'flex-start',
            textAlign: 'left',
            whiteSpace: 'normal',
            wordBreak: 'break-word',
            lineHeight: '1.3',
            height: '45px',
            minHeight: '45px',
            maxHeight: '45px',
            '&:focus': {
              outline: 'none',
            },
          },
          
          '& .MuiDataGrid-root': {
            display: 'flex',
            flexDirection: 'column',
            overflow: 'visible !important',
            height: 'auto !important',
            maxHeight: 'none !important',
            minHeight: '0 !important',
            width: '100% !important', // Full width
          },
          '& .MuiDataGrid-virtualScroller': {
            overflow: 'visible !important',
            width: '100% !important', // Full width
          },
          '& .MuiDataGrid-main': {
            display: 'flex',
            flexDirection: 'column',
            width: '100% !important', // Full width
            flexGrow: 1,
          },
          '& .MuiDataGrid-viewport': {
            width: '100% !important', // Full width
          },
          '& .MuiDataGrid-window': {
            width: '100% !important', // Full width
          },
          '& .MuiDataGrid-dataContainer': {
            width: '100% !important', // Full width
          },
          '& .MuiDataGrid-columnHeadersInner': {
            width: '100% !important', // Full width for headers
          },
          '& .MuiDataGrid-columnHeaders': {
            minHeight: '40px !important',
            lineHeight: '40px !important',
            backgroundColor: '#fafafa',
            borderBottom: '2px solid #e0e0e0',
            padding: '0 0',
            width: '100% !important', // Full width
            '& .MuiDataGrid-columnHeader': {
              fontWeight: 600,
              fontSize: '0.75rem',
              color: '#424242',
              '&:hover': {
                backgroundColor: '#f0f0f0',
              },
            },
          },
          '& .MuiDataGrid-columnHeaderRow': {
            width: '100% !important', // Full width
            display: 'flex',
            flexDirection: 'row',
            overflow: 'visible', // Allow column resizing
          },
          '& .MuiDataGrid-columnsContainer': {
            width: '100% !important', // Full width
          },
          '& .MuiDataGrid-row .MuiDataGrid-cell': {
            overflow: 'hidden',
          },
          '& .MuiDataGrid-cellContent': {
            textAlign: 'left',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'flex-start',
            width: '100%',
            whiteSpace: 'normal',
            wordBreak: 'break-word',
            lineHeight: '1.3',
          },
          '& .MuiDataGrid-footerContainer': {
            minHeight: '40px',
            maxHeight: '40px',
            borderTop: '1px solid rgba(224, 224, 224, 1)',
            overflow: 'hidden',
            padding: '0 8px',
            display: 'flex',
            alignItems: 'center',
          },
          // Fix checkbox alignment in header
          '& .MuiDataGrid-columnHeaderCheckbox': {
            padding: '0 !important',
            height: '40px !important',
            width: '40px !important',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            '& .MuiCheckbox-root': {
              padding: '0',
              margin: '0 auto',
            },
            '& .MuiSvgIcon-root': {
              fontSize: '1rem',
            }
          },
          // Fix checkbox alignment in cells
          '& .MuiDataGrid-cellCheckbox': {
            padding: '0 !important',
            height: '45px !important',
            width: '40px !important',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            '& .MuiCheckbox-root': {
              padding: '0',
              margin: '0 auto',
            },
            '& .MuiSvgIcon-root': {
              fontSize: '1rem',
            }
          },
          // Hide horizontal scrollbar as it's useless
          '& .MuiDataGrid-scrollbar--horizontal': {
            display: 'none !important',
          },
        }}
      />
    </Box>
  );
};

export default ManifestListGrid; 