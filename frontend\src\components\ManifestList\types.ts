import { Manifest, ManifestStatus } from '../../types/manifest';
import { Container } from '../../types/Container';
import { LocationZone } from '../../types/location';
import { VehicleType } from '../../types/Vehicle';

// Viewport options interface for column width calculations
export interface ViewportOptions {
  source?: 'window' | 'container' | 'available' | 'dashboard';
  containerRef?: React.RefObject<HTMLElement>;
  debounceMs?: number;
  offset?: number;
  containerMinWidth?: string | number;
  containerWidth?: number;
  fixedWidth?: number;
}

// Main component props
export interface ManifestListProps {
  // Core data props - optional to allow component to fetch its own data
  manifests?: Manifest[];
  loading?: boolean;
  error?: string | null;
  
  // Context-specific props
  containerContext?: Container | null;
  isContainerView?: boolean;
  
  // Resource props
  locationZones?: LocationZone[];
  vehicleTypes?: VehicleType[];
  
  // Loading states
  locationsLoading?: boolean;
  vehicleTypesLoading?: boolean;
  
  // Callback props
  onManifestUpdate?: (manifest: Manifest) => Promise<void>;
  onManifestDelete?: (trackingNo: string) => Promise<void>;
  onBulkDelete?: (trackingNos: string[]) => Promise<void>;
  onBulkLabelGeneration?: (manifests: Manifest[]) => void;
  onExportToExcel?: (manifests: Manifest[]) => Promise<void>;
  onStatusChange?: (trackingNo: string, status: ManifestStatus) => Promise<void>;
  onRefresh?: () => Promise<void>;

  // Action button callbacks
  onCreateManifest?: () => void;
  onUploadManifest?: () => void;
  
  // UI customization
  customActions?: React.ReactNode;
  
  // Viewport options for column width calculations
  viewportOptions?: ViewportOptions;
}

// Filter state interface
export interface FilterState {
  search: string;
  status: ManifestStatus[];
  dateRange: {
    start: Date | null;
    end: Date | null;
  };
  location: string;
  deliveryVehicle: string;
  hasDeliveryDate: boolean;
}

// Column action handlers
export interface ColumnActionHandlers {
  onEdit: (manifest: Manifest) => void;
  onDelete: (trackingNo: string) => void;
  onPrintLabel: (manifest: Manifest) => void;
  onEditDeliveryDate: (trackingNo: string) => void;
  onEditDeliveryVehicle: (manifest: Manifest) => void;
}

// Dialog handlers
export interface DialogHandlers {
  onDelete?: (trackingNo: string) => Promise<void>;
  onBulkDelete?: (trackingNos: string[]) => Promise<void>;
  onStatusChange?: (trackingNo: string, status: ManifestStatus) => Promise<void>;
  onUpdate?: (manifest: Manifest) => Promise<void>;
}

// Dialog states
export interface DialogStates {
  editManifest: boolean;
  confirmDelete: boolean;
  confirmBulkDelete: boolean;
  changeStatus: boolean;
  labelGeneration: boolean;
  palletManagement: boolean;
  deliveryDate: boolean;
  deliveryVehicle: boolean;
}

// Dialog props
export interface DialogProps {
  editManifest: { manifest: Manifest | null };
  confirmDelete: { trackingNo: string };
  confirmBulkDelete: { trackingNos: string[] };
  changeStatus: { trackingNo: string; status: ManifestStatus };
  labelGeneration: { manifest: Manifest | null };
  palletManagement: { manifest: Manifest | null };
  deliveryDate: { trackingNo: string; currentDate: Date | null };
  deliveryVehicle: { manifest: Manifest | null };
} 