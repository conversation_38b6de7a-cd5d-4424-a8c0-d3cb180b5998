import React from 'react';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { formatInTimeZone, toZonedTime } from 'date-fns-tz';
import { SINGAPORE_TIMEZONE } from '../utils/dateUtils';

interface SingaporeDatePickerProps {
  value: Date | null;
  onChange: (value: Date | null) => void;
  label: string;
  format?: string;
  slotProps?: any;
  [key: string]: any; // Allow other props to be passed through
}

/**
 * DatePicker component that handles Singapore timezone automatically
 * All dates are displayed and edited in Singapore timezone for consistency
 */
export const SingaporeDatePicker: React.FC<SingaporeDatePickerProps> = ({
  value,
  onChange,
  label,
  format = "dd MMM yyyy",
  slotProps,
  ...props
}) => {
  // Convert incoming value to Singapore timezone for display
  const displayValue = value ? toZonedTime(value, SINGAPORE_TIMEZONE) : null;

  // Handle changes and convert back to proper Date object
  const handleChange = (newValue: Date | null) => {
    if (newValue) {
      // The date picker gives us a Date object in the user's local timezone
      // We need to treat it as if it's in Singapore timezone
      const singaporeDate = toZonedTime(newValue, SINGAPORE_TIMEZONE);
      onChange(singaporeDate);
    } else {
      onChange(null);
    }
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <DatePicker
        {...props}
        label={`${label} (SGT)`}
        value={displayValue}
        onChange={handleChange}
        format={format}
        slotProps={{
          ...slotProps,
          textField: {
            ...slotProps?.textField,
            helperText: 'Singapore Time (SGT)',
          },
        }}
      />
    </LocalizationProvider>
  );
}; 