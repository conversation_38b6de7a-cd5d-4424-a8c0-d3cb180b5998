import React, { useState } from 'react';
import { 
  Box, 
  Button, 
  Popover, 
  Typography, 
  Divider, 
  Paper,
  ButtonGroup,
  IconButton,
} from '@mui/material';
import ViewColumnIcon from '@mui/icons-material/ViewColumn';
import CloseIcon from '@mui/icons-material/Close';
import { formatColumnName } from './hooks/useManifestColumns';

interface ManifestListColumnsProps {
  visibleColumns: Record<string, boolean>;
  onColumnToggle: (column: string) => void;
  onResetColumns: () => void;
  onSelectAll?: () => void;
  onSelectNone?: () => void;
  onClose?: () => void;  // Optional callback when the component is closed
  standalone?: boolean;  // Whether the component is standalone or used in a popover
}

const ManifestListColumns: React.FC<ManifestListColumnsProps> = ({
  visibleColumns,
  onColumnToggle,
  onResetColumns,
  onSelectAll,
  onSelectNone,
  onClose,
  standalone = false,
}) => {
  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);
  
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };
  
  const handleClose = () => {
    setAnchorEl(null);
    if (onClose) {
      onClose();
    }
  };
  
  const open = Boolean(anchorEl);
  const id = open ? 'column-visibility-popover' : undefined;
  
  // Column management component that's reused in both standalone mode and popover mode
  const ColumnManager = () => (
    <Box sx={{ 
      width: '100%',
      position: 'relative',
      boxSizing: 'border-box' 
    }}>
      {standalone ? null : (
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
          <Typography variant="subtitle1">
            Manage Columns
          </Typography>
          {standalone && onClose && (
            <IconButton size="small" onClick={onClose} edge="end">
              <CloseIcon />
            </IconButton>
          )}
        </Box>
      )}
      
      {standalone ? null : <Divider sx={{ mb: 2 }} />}
      
      {standalone ? null : (
        <ButtonGroup size="small" variant="outlined" sx={{ mb: 2 }}>
          <Button onClick={onSelectAll}>Select All</Button>
          <Button onClick={onSelectNone}>Select None</Button>
          <Button onClick={onResetColumns}>Reset Default</Button>
        </ButtonGroup>
      )}
      
      {standalone ? null : <Divider sx={{ mb: 2 }} />}
      
      <Box sx={{
        display: 'flex',
        flexWrap: 'wrap',
        gap: 1,
        maxHeight: standalone ? 'none' : 300,
        height: standalone ? 'auto' : undefined,
        overflowY: standalone ? 'visible' : 'auto',
        overflowX: 'hidden',
        pr: 1,
        width: '100%',
        boxSizing: 'border-box'
      }}>
        {[
          'location', 'internalId', 'trackingNo', 'customerName', 'address', 'postalCode',
          'deliveryDate', 'timeSlot', 'phoneNo', 'driverRemarks', 'driver', 'status', 'deliveryVehicle',
          'pieces', 'inboundPieces', 'actualPalletsCount', 'cbm', 'remarks', 'weight',
          'deliveredDate', 'createdDate', 'country', 'container', 'client'
        ].map((column) => {
          // Only show toggle for columns that exist in visibleColumns
          if (!(column in visibleColumns)) return null;

          const isVisible = visibleColumns[column];
          
          return (
            <Button
              key={column}
              size="small"
              variant={isVisible ? "contained" : "outlined"}
              color={isVisible ? "primary" : "inherit"}
              onClick={() => onColumnToggle(column)}
              sx={{ 
                borderRadius: 10,
                px: 1.5,
                py: 0.5,
                minWidth: 'fit-content',
                fontSize: '0.75rem',
                textTransform: 'none',
                boxShadow: isVisible ? 1 : 0
              }}
            >
              {formatColumnName(column)}
            </Button>
          );
        })}
      </Box>
    </Box>
  );
  
  // If this is a standalone component (not in a popover)
  if (standalone) {
    return <ColumnManager />;
  }
  
  // Otherwise, render the button and popover
  return (
    <Box sx={{ mb: 2 }}>
      <Button
        variant="outlined"
        size="small"
        startIcon={<ViewColumnIcon />}
        onClick={handleClick}
        aria-describedby={id}
        sx={{ borderRadius: 2 }}
      >
        Column Visibility
      </Button>
      
      <Popover
        id={id}
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
        sx={{ mt: 0.5 }}
      >
        <ColumnManager />
      </Popover>
    </Box>
  );
};

export default ManifestListColumns; 