package com.wms.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

import com.wms.entity.vehicle.VehicleStatus;

@Data
public class VehicleDTO {
    private Long id;
    
    @NotBlank(message = "License plate is required")
    @Size(max = 20)
    private String licensePlate;
    
    @NotNull(message = "Vehicle type is required")
    private Long vehicleTypeId;
    
    private String vehicleTypeName;
    
    private VehicleStatus status;
    
    private List<String> assignedDriverUsernames;
} 