package com.wms.config;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
class JacksonConfigTest {

    @Autowired
    private ObjectMapper objectMapper;

    static class TestDateContainer {
        private LocalDateTime dateTime;

        public LocalDateTime getDateTime() {
            return dateTime;
        }

        public void setDateTime(LocalDateTime dateTime) {
            this.dateTime = dateTime;
        }
    }

    @Test
    void testLocalDateTimeDeserialization() throws JsonProcessingException {
        // Test ISO format
        String isoJson = "{\"dateTime\":\"2025-06-04T16:00:00\"}";
        TestDateContainer isoResult = objectMapper.readValue(isoJson, TestDateContainer.class);
        assertNotNull(isoResult.getDateTime());
        assertEquals(2025, isoResult.getDateTime().getYear());
        assertEquals(6, isoResult.getDateTime().getMonthValue());
        assertEquals(4, isoResult.getDateTime().getDayOfMonth());
        assertEquals(16, isoResult.getDateTime().getHour());
        assertEquals(0, isoResult.getDateTime().getMinute());

        // Test format with comma: "04 Jun 2025, 16:00"
        String commaJson = "{\"dateTime\":\"04 Jun 2025, 16:00\"}";
        TestDateContainer commaResult = objectMapper.readValue(commaJson, TestDateContainer.class);
        assertNotNull(commaResult.getDateTime());
        assertEquals(2025, commaResult.getDateTime().getYear());
        assertEquals(6, commaResult.getDateTime().getMonthValue());
        assertEquals(4, commaResult.getDateTime().getDayOfMonth());
        assertEquals(16, commaResult.getDateTime().getHour());
        assertEquals(0, commaResult.getDateTime().getMinute());

        // Test format without comma: "04 Jun 2025 16:00"
        String noCommaJson = "{\"dateTime\":\"04 Jun 2025 16:00\"}";
        TestDateContainer noCommaResult = objectMapper.readValue(noCommaJson, TestDateContainer.class);
        assertNotNull(noCommaResult.getDateTime());
        assertEquals(2025, noCommaResult.getDateTime().getYear());
        assertEquals(6, noCommaResult.getDateTime().getMonthValue());
        assertEquals(4, noCommaResult.getDateTime().getDayOfMonth());
        assertEquals(16, noCommaResult.getDateTime().getHour());
        assertEquals(0, noCommaResult.getDateTime().getMinute());
    }
} 