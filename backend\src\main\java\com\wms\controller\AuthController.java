package com.wms.controller;

import com.wms.entity.Role;
import com.wms.entity.user.Admin;
import com.wms.entity.user.Client;
import com.wms.entity.user.Driver;
import com.wms.entity.user.Manager;
import com.wms.entity.user.User;
import com.wms.entity.user.UserStatus;
import com.wms.payload.request.LoginRequest;
import com.wms.payload.request.SignupRequest;
import com.wms.payload.response.JwtResponse;
import com.wms.payload.response.MessageResponse;
import com.wms.repository.RoleRepository;
import com.wms.repository.UserRepo.UserRepository;
import com.wms.security.jwt.JwtUtils;
import com.wms.security.services.UserDetailsImpl;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
@RequestMapping("/api/auth")
public class AuthController {
    private static final Logger logger = LoggerFactory.getLogger(AuthController.class);

    @Autowired
    AuthenticationManager authenticationManager;

    @Autowired
    UserRepository userRepository;

    @Autowired
    RoleRepository roleRepository;

    @Autowired
    JwtUtils jwtUtils;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @GetMapping("/debug/password")
    public ResponseEntity<?> debugPassword() {
        User user = userRepository.findByUsername("admin")
                .orElseThrow(() -> new UsernameNotFoundException("User Not Found with username: admin"));
        
        String storedHash = user.getPassword();
        String newHash = passwordEncoder.encode("admin123");
        
        return ResponseEntity.ok(Map.of(
            "storedHash", storedHash,
            "newHash", newHash,
            "matches", passwordEncoder.matches("admin123", storedHash)
        ));
    }

    @RequestMapping(value = "/debug/reset-admin", method = {RequestMethod.GET, RequestMethod.POST})
    public ResponseEntity<?> resetAdminPassword() {
        try {
            User user = userRepository.findByUsername("admin")
                    .orElseThrow(() -> new UsernameNotFoundException("User Not Found with username: admin"));
            
            String newPassword = "admin123";
            String encodedPassword = passwordEncoder.encode(newPassword);
            user.setPassword(encodedPassword);
            userRepository.save(user);
            
            return ResponseEntity.ok(Map.of(
                "message", "Admin password reset successfully",
                "newHash", encodedPassword
            ));
        } catch (Exception e) {
            logger.error("Failed to reset admin password: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", e.getMessage()));
        }
    }

    private Long getNextUserNumber() {
        Long maxUserNumber = userRepository.findMaxUserNumber();
        return maxUserNumber != null ? maxUserNumber + 1 : 1L;
    }

    @PostMapping("/signin")
    public ResponseEntity<?> authenticateUser(@Valid @RequestBody LoginRequest loginRequest) {
        logger.debug("Login attempt for user: {}", loginRequest.getUsername());

        try {
            Authentication authentication = authenticationManager.authenticate(
                    new UsernamePasswordAuthenticationToken(loginRequest.getUsername(), loginRequest.getPassword()));

            SecurityContextHolder.getContext().setAuthentication(authentication);
            String jwt = jwtUtils.generateJwtToken(authentication);

            UserDetailsImpl userDetails = (UserDetailsImpl) authentication.getPrincipal();
            List<String> roles = userDetails.getAuthorities().stream()
                    .map(item -> item.getAuthority())
                    .collect(Collectors.toList());

            return ResponseEntity.ok(new JwtResponse(jwt,
                    userDetails.getId(),
                    userDetails.getUsername(),
                    userDetails.getEmail(),
                    roles));
        } catch (BadCredentialsException e) {
            logger.error("Authentication failed: Invalid credentials for user {}", loginRequest.getUsername());
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(new MessageResponse("Error: Invalid username or password"));
        } catch (Exception e) {
            logger.error("Authentication failed: {}", e.getMessage());
            if (e.getMessage().contains("deactivated")) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                        .body(new MessageResponse(e.getMessage()));
            }
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(new MessageResponse("Error: Authentication failed"));
        }
    }

    @PostMapping("/signup")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> registerUser(@Valid @RequestBody SignupRequest signUpRequest) {
        if (userRepository.existsByUsername(signUpRequest.getUsername())) {
            return ResponseEntity
                    .badRequest()
                    .body(new MessageResponse("Error: Username is already taken!"));
        }

        if (userRepository.existsByEmail(signUpRequest.getEmail())) {
            return ResponseEntity
                    .badRequest()
                    .body(new MessageResponse("Error: Email is already in use!"));
        }

        // Create new user based on role
        User user;
        Set<String> strRoles = signUpRequest.getRoles();
        if (strRoles != null && !strRoles.isEmpty()) {
            String primaryRole = strRoles.iterator().next(); // Get first role
            switch (primaryRole) {
                case "ROLE_MANAGER":
                    user = new Manager();
                    break;
                case "ROLE_DRIVER":
                    user = new Driver();
                    break;
                case "ROLE_ADMIN":
                    user = new Admin();
                    break;
                default:
                    user = new Client();
            }
        } else {
            // Default to Client if no role specified
            user = new Client();
        }

        // Set common user properties
        user.setUsername(signUpRequest.getUsername());
        user.setEmail(signUpRequest.getEmail());
        user.setPassword(passwordEncoder.encode(signUpRequest.getPassword()));
        user.setContact(signUpRequest.getContact());
        user.setFullName(signUpRequest.getFullName());
        user.setStatus(UserStatus.ACTIVE);
        user.setUserNumber(getNextUserNumber());

        // Set role-specific fields
        if (user instanceof Client) {
            Client client = (Client) user;
            client.setCompanyName(signUpRequest.getCompanyName());
            client.setDesignation(signUpRequest.getDesignation());
            client.setAltDesignation(signUpRequest.getAltDesignation());
            client.setAltContact(signUpRequest.getAltContact());
        } else if (user instanceof Manager) {
            Manager manager = (Manager) user;
            manager.setDepartment(signUpRequest.getDepartment());
        } else if (user instanceof Driver) {
            Driver driver = (Driver) user;
            driver.setLicenseNumber(signUpRequest.getLicenseNumber());
        }

        // Set up roles
        Set<Role> roles = new HashSet<>();
        if (strRoles == null || strRoles.isEmpty()) {
            Role userRole = roleRepository.findByName("ROLE_CLIENT")
                    .orElseThrow(() -> new RuntimeException("Error: Role is not found."));
            roles.add(userRole);
        } else {
            strRoles.forEach(role -> {
                switch (role) {
                    case "ROLE_MANAGER":
                        Role managerRole = roleRepository.findByName("ROLE_MANAGER")
                                .orElseThrow(() -> new RuntimeException("Error: Manager Role is not found."));
                        roles.add(managerRole);
                        break;
                    case "ROLE_DRIVER":
                        Role driverRole = roleRepository.findByName("ROLE_DRIVER")
                                .orElseThrow(() -> new RuntimeException("Error: Driver Role is not found."));
                        roles.add(driverRole);
                        break;
                    case "ROLE_ADMIN":
                        Role adminRole = roleRepository.findByName("ROLE_ADMIN")
                                .orElseThrow(() -> new RuntimeException("Error: Admin Role is not found."));
                        roles.add(adminRole);
                        break;
                    default:
                        Role clientRole = roleRepository.findByName("ROLE_CLIENT")
                                .orElseThrow(() -> new RuntimeException("Error: Client Role is not found."));
                        roles.add(clientRole);
                }
            });
        }

        user.setRoles(roles);
        userRepository.save(user);

        return ResponseEntity.ok(new MessageResponse("User registered successfully!"));
    }
}