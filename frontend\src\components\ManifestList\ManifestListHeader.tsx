import React from 'react';
import { Box, Typography } from '@mui/material';
import { Container } from '../../types/Container';

interface ManifestListHeaderProps {
  isContainerView: boolean;
  containerContext?: Container | null;
  selectedCount: number;
}

const ManifestListHeader: React.FC<ManifestListHeaderProps> = ({
  isContainerView,
  containerContext,
  selectedCount
}) => {
  return (
    <Box sx={{ p: 2, borderBottom: '1px solid rgba(224, 224, 224, 1)' }}>
      {selectedCount > 0 && (
        <Typography variant="caption">
          {selectedCount} manifest{selectedCount !== 1 ? 's' : ''} selected
        </Typography>
      )}
    </Box>
  );
};

export default ManifestListHeader; 