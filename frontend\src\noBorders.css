/* Remove focus outlines for DataGrid */
.MuiDataGrid-root *:focus,
.MuiDataGrid-root *:focus-visible,
.MuiDataGrid-root *:focus-within,
.MuiDataGrid-row,
.MuiDataGrid-cell {
  outline: none !important;
  box-shadow: none !important;
}

.MuiDataGrid-row.Mui-selected {
  background-color: rgba(25, 118, 210, 0.08) !important;
  border: none !important;
  outline: none !important;
}

.MuiDataGrid-cell.Mui-selected {
  outline: none !important;
  border: none !important;
}

.MuiDataGrid-columnHeader:focus,
.MuiDataGrid-cell:focus {
  outline: none !important;
}

.MuiDataGrid-withBorderColor {
  border-color: #f0f0f0 !important;
}

/* MUI v7 specific fixes */
.MuiDataGrid-root [role="cell"]:focus-within {
  outline: none !important;
}

.MuiDataGrid-cell--editing {
  box-shadow: none !important;
  border: none !important;
}

/* Fix for :has selector in modern browsers */
@supports selector(:has(*)) {
  .MuiDataGrid-row:has(.MuiDataGrid-cellCheckbox.Mui-checked) {
    outline: none !important;
    border: none !important;
  }
}

/* More aggressive selection rule */
.MuiDataGrid-root *,
.MuiDataGrid-root *:before,
.MuiDataGrid-root *:after {
  outline: none !important;
  box-shadow: none !important;
}

/* Add enhanced styles for date picker popups */
.MuiCalendarPicker-root,
.MuiPickersDay-root,
.MuiPickersPopper-root,
.MuiPaper-root {
  font-size: 16px !important;
}

.MuiPickersDay-root {
  font-weight: 500 !important;
}

.MuiPickersDay-today {
  border: 1px solid currentColor !important;
  font-weight: 700 !important;
}

.MuiClock-pin,
.MuiClockPointer-root {
  background-color: #1976d2 !important;
}

.MuiClockPointer-thumb {
  border: 2px solid #1976d2 !important;
  background-color: #fff !important;
}

.MuiPickersCalendarHeader-label {
  font-size: 1.1rem !important;
  font-weight: 600 !important;
}

.MuiPickersDay-root.Mui-selected {
  background-color: #1976d2 !important;
  color: white !important;
  font-weight: 700 !important;
}

/* Popup container for date picker */
.MuiPopover-root,
.MuiDialog-root {
  z-index: 9999 !important;
}

/* Increase the size of the calendar paper */
.MuiCalendarPicker-root {
  width: 320px !important;
  margin: 0 !important;
}

.MuiPickersCalendarHeader-switchViewButton {
  opacity: 1 !important;
}

/* Emphasis for active date */
.MuiPickersDay-root.Mui-selected:focus {
  box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.5) !important;
}

/* MUI v8 DatePicker specific styles */
.MuiPickersLayout-root {
  background-color: white !important;
  border-radius: 8px !important;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15) !important;
}

.MuiPickersLayout-contentWrapper {
  min-width: 320px !important;
}

.MuiDateCalendar-root {
  width: 320px !important;
  background-color: white !important;
  padding: 16px !important;
}

/* Make calendar days more visible */
.MuiDayCalendar-weekDayLabel {
  font-weight: bold !important;
  color: #1976d2 !important;
}

.MuiPickersDay-root {
  font-size: 0.875rem !important;
  margin: 2px !important;
  width: 36px !important;
  height: 36px !important;
}

/* Selected day styling */
.MuiPickersDay-root.Mui-selected {
  background-color: #1976d2 !important;
  color: white !important;
  font-weight: bold !important;
}

/* Hover effects */
.MuiPickersDay-root:not(.Mui-selected):hover {
  background-color: rgba(25, 118, 210, 0.15) !important;
}

/* Today styling */
.MuiPickersDay-root.MuiPickersDay-today {
  border: 1px solid #1976d2 !important;
  color: #1976d2 !important;
  font-weight: bold !important;
}

/* Calendar header */
.MuiPickersCalendarHeader-root {
  padding: 8px 16px !important;
  margin-top: 0 !important;
}

/* Month/year selector */
.MuiPickersCalendarHeader-label {
  font-size: 1rem !important;
  font-weight: bold !important;
}

/* Make the dialog much more visible */
.MuiDialog-paper {
  box-shadow: 0 16px 32px rgba(0, 0, 0, 0.2) !important;
} 