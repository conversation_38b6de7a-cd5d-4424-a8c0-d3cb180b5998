# Fukuyama Logistics Warehouse Management System (Fukuyama WMS)

A comprehensive Warehouse Management System with Container Cargo Management capabilities, built with Java Spring Boot and React. This system provides end-to-end management of warehouse operations, container tracking, and user management with role-based access control for Fukuyama Logistics.

## Features

### User Management
- Role-based access control (Admin, Manager, Client, Driver)
- User registration and authentication
- User profile management
- Role-specific dashboards and interfaces

### Container Management
- Container tracking and status monitoring
- Container assignment and scheduling
- Container history and audit trail
- Real-time container status updates

### Manifest Management
- Digital manifest creation and management
- Manifest status tracking
- Document attachment and management
- Manifest history and reporting

### Client Management
- Client profile management
- Company information tracking
- Contact management
- Client-specific container tracking

### Driver Management
- Driver profile management
- License and vehicle information tracking
- Assignment tracking
- Performance monitoring

### Warehouse Operations
- Inventory management
- Location tracking
- Stock movement monitoring
- Warehouse capacity management

### Transportation Management
- Route planning and optimization
- Vehicle tracking
- Delivery scheduling
- Transportation cost management

## Tech Stack

### Backend
- Java 17
- Spring Boot 3.2.3
- Spring Security with JWT authentication
- Spring Data JPA
- MySQL 8.0
- <PERSON>ven
- <PERSON>mbo<PERSON>
- MapStruct
- Swagger/OpenAPI

### Frontend
- React 18 with TypeScript
- Vite
- Material-UI (MUI) v5
- Redux Toolkit for state management
- Axios for API calls
- React Router v6
- ESLint and Prettier
- React Query for data fetching

## Getting Started

### Prerequisites

- Java 17 or higher
- MySQL 8.0 or higher
- Node.js 18 or higher
- Maven 3.8 or higher
- Git

### Initial Setup

1. Clone the repository:
```bash
git clone https://github.com/ZLHWNN/WMS.git
cd WMS
```

2. Database Setup:
   - Install MySQL 8.0 if not already installed
   - Create a new MySQL database:
   ```sql
   CREATE DATABASE fukuyama_wms_db;
   ```
   - The application will automatically create the schema using the `schema.sql` file
   - Initial data will be loaded from `data.sql`

3. Backend Configuration:
   - Navigate to `backend/src/main/resources/application.properties`
   - Update the following properties with your MySQL credentials:
   ```properties
   spring.datasource.url=*******************************************
   spring.datasource.username=your_username
   spring.datasource.password=your_password
   ```

### Running the Backend

1. Navigate to the backend directory:
```bash
cd backend
```

2. Build the project:
```bash
mvn clean install
```

3. Start the Spring Boot application:
```bash
mvn spring-boot:run
```

The backend server will start on `http://localhost:8080`. You can verify it's running by:
- Accessing the Swagger UI at `http://localhost:8080/swagger-ui.html`
- Checking the Spring Boot startup logs for any errors

### Running the Frontend

1. Open a new terminal and navigate to the frontend directory:
```bash
cd frontend
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

The frontend application will start on `http://localhost:5173`

## Default Users

The system comes with pre-configured users for testing:

1. Admin User:
   - Username: admin
   - Password: admin123

2. Manager User:
   - Username: manager
   - Password: manager123

3. Client User:
   - Username: client
   - Password: client123

4. Driver User:
   - Username: driver
   - Password: driver123

## API Documentation

The API documentation is available through Swagger UI at `http://localhost:8080/swagger-ui.html` when the backend is running.

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Contact

For any queries or support, please contact the Fukuyama Logistics project maintainers. 