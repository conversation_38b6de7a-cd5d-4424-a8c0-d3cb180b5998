# ManifestList Component

A reusable, feature-rich component for displaying and managing manifest lists across the WMS application.

## Features

- Unified manifest list display for both standalone and container-specific views
- Advanced filtering capabilities (status, date range, location, etc.)
- Customizable column visibility
- Selection and bulk operations (delete, label generation)
- Responsive design with performance optimizations

## Component Structure

The component is organized into a modular structure:

```
ManifestList/
  ├── hooks/                    # Custom hooks for logic separation
  │   ├── useManifestSelection.ts  # Selection state management
  │   ├── useManifestFilters.ts    # Filtering logic
  │   ├── useManifestColumns.ts    # Column visibility management
  │   ├── useManifestDialogs.ts    # Dialog state management
  │   └── index.ts                 # Export all hooks
  │
  ├── ManifestListHeader.tsx    # Header component
  ├── ManifestListToolbar.tsx   # Toolbar with action buttons
  ├── ManifestListFilters.tsx   # Filters panel
  ├── ManifestListColumns.tsx   # Column visibility controls
  ├── ManifestListGrid.tsx      # Main data grid component
  ├── types.ts                  # TypeScript interfaces
  ├── components.ts             # Export all sub-components
  ├── ManifestList.tsx          # Main component that combines everything
  └── index.ts                  # Public API
```

## Usage

### Basic Usage

```tsx
import ManifestList from '../components/ManifestList';

const MyPage = () => {
  // Fetch manifest data and handle API operations
  const [manifests, setManifests] = useState<Manifest[]>([]);
  const [loading, setLoading] = useState(true);

  // Add your API handlers for CRUD operations
  const handleManifestUpdate = async (manifest) => { /* ... */ };
  const handleManifestDelete = async (trackingNo) => { /* ... */ };

  return (
    <ManifestList
      manifests={manifests}
      loading={loading}
      error={error}
      onManifestUpdate={handleManifestUpdate}
      onManifestDelete={handleManifestDelete}
      onRefresh={handleRefresh}
    />
  );
};
```

### Container-Specific View

```tsx
import ManifestList from '../components/ManifestList';

const ContainerDetail = () => {
  const { container, manifests, loading } = useContainerData(containerNo);

  return (
    <ManifestList
      manifests={manifests}
      loading={loading}
      error={error}
      containerContext={container}
      isContainerView={true}
    />
  );
};
```

## Props

The component accepts the following props:

| Prop Name | Type | Description |
|-----------|------|-------------|
| `manifests` | `Manifest[]` | Array of manifest objects to display |
| `loading` | `boolean` | Loading state for the data |
| `error` | `string \| null` | Error message to display if data loading fails |
| `containerContext` | `Container \| null` | Optional container context for container-specific views |
| `isContainerView` | `boolean` | Whether this is a container-specific view |
| `locationZones` | `LocationZone[]` | Available location zones for filtering |
| `vehicleTypes` | `VehicleType[]` | Available vehicle types for filtering |
| `onManifestUpdate` | `(manifest: Manifest) => Promise<void>` | Handler for updating a manifest |
| `onManifestDelete` | `(trackingNo: string) => Promise<void>` | Handler for deleting a manifest |
| `onBulkDelete` | `(trackingNos: string[]) => Promise<void>` | Handler for bulk deleting manifests |
| `onBulkLabelGeneration` | `(manifests: Manifest[]) => void` | Handler for generating labels for multiple manifests |
| `onExportToExcel` | `(manifests: Manifest[]) => Promise<void>` | Handler for exporting to Excel |
| `onStatusChange` | `(trackingNo: string, status: ManifestStatus) => Promise<void>` | Handler for changing a manifest's status |
| `onRefresh` | `() => Promise<void>` | Handler for refreshing the manifest list |

## Test Page

A test page is available at `/manifest-list-test` that demonstrates the component with mock data. 