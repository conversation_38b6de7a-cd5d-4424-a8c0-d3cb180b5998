-- Insert roles if they don't exist
INSERT INTO roles (id, name) 
SELECT 1, 'ROLE_ADMIN' 
WHERE NOT EXISTS (SELECT 1 FROM roles WHERE id = 1);

INSERT INTO roles (id, name) 
SELECT 2, 'ROLE_MANAGER' 
WHERE NOT EXISTS (SELECT 1 FROM roles WHERE id = 2);

INSERT INTO roles (id, name) 
SELECT 3, 'ROLE_CLIENT' 
WHERE NOT EXISTS (SELECT 1 FROM roles WHERE id = 3);

INSERT INTO roles (id, name) 
SELECT 4, 'ROLE_DRIVER' 
WHERE NOT EXISTS (SELECT 1 FROM roles WHERE id = 4);

-- Insert admin user if it doesn't exist
-- Password is 'admin123' (hashed)
-- This uses a specific BCrypt hash that should work across environments
INSERT INTO users (user_number, username, user_password, user_email, contact, full_name, status, user_type) 
SELECT 1001, 'admin', '$2a$10$kR/sCJ4gdYgHli7Q0y0OUeApvoZRvA/hfXqDMqkCIQOB/QkUE/FDq', '<EMAIL>', '1234567890', 'System Administrator', 'ACTIVE', 'ADMIN'
WHERE NOT EXISTS (SELECT 1 FROM users WHERE username = 'admin');

-- Ensure admin user has correct password even if it exists
UPDATE users SET user_password = '$2a$10$kR/sCJ4gdYgHli7Q0y0OUeApvoZRvA/hfXqDMqkCIQOB/QkUE/FDq'
WHERE username = 'admin';

-- Insert admin details if they don't exist
INSERT INTO admins (username) 
SELECT 'admin' 
WHERE NOT EXISTS (SELECT 1 FROM admins WHERE username = 'admin');

-- Link admin to admin role if it doesn't exist
INSERT INTO user_roles (username, role_id) 
SELECT 'admin', 1 
WHERE NOT EXISTS (SELECT 1 FROM user_roles WHERE username = 'admin' AND role_id = 1);

-- Insert one manager user
INSERT INTO users (user_number, username, user_password, user_email, contact, full_name, status, user_type) 
SELECT 2001, 'manager', '$2a$10$kR/sCJ4gdYgHli7Q0y0OUeApvoZRvA/hfXqDMqkCIQOB/QkUE/FDq', '<EMAIL>', '1234567891', 'John Smith', 'ACTIVE', 'MANAGER'
WHERE NOT EXISTS (SELECT 1 FROM users WHERE username = 'manager');

-- Insert manager details
INSERT INTO managers (username, department) 
SELECT 'manager', 'Operations'
WHERE NOT EXISTS (SELECT 1 FROM managers WHERE username = 'manager');

-- Link manager to manager role
INSERT INTO user_roles (username, role_id) 
SELECT 'manager', 2 
WHERE NOT EXISTS (SELECT 1 FROM user_roles WHERE username = 'manager' AND role_id = 2);

-- Insert one client user
INSERT INTO users (user_number, username, user_password, user_email, contact, full_name, status, user_type) 
SELECT 3001, 'client', '$2a$10$kR/sCJ4gdYgHli7Q0y0OUeApvoZRvA/hfXqDMqkCIQOB/QkUE/FDq', '<EMAIL>', '1234567893', 'Demo Client', 'ACTIVE', 'CLIENT'
WHERE NOT EXISTS (SELECT 1 FROM users WHERE username = 'client');

-- Insert client details
INSERT INTO clients (username, company_name, designation, alt_designation, alt_contact) 
SELECT 'client', 'Demo Company', 'Logistics Manager', 'Warehouse Supervisor', '9876543210'
WHERE NOT EXISTS (SELECT 1 FROM clients WHERE username = 'client');

-- Link client to client role
INSERT INTO user_roles (username, role_id) 
SELECT 'client', 3 
WHERE NOT EXISTS (SELECT 1 FROM user_roles WHERE username = 'client' AND role_id = 3);

-- Insert simplified location zones
-- Delete any existing location zones first to avoid duplicates
DELETE FROM location_zones;

-- Create a single entry for each region with all postal code ranges in the description
-- CENTRAL: Codes 01-11, 13-32
INSERT INTO location_zones (id, name, start_postal_code, end_postal_code, special_cases, description, is_active)
VALUES (1, 'CENTRAL', 1, 11, '13-32', 'Central region - postal codes 01-11, 13-32 (excluding 12)', TRUE);

-- WEST: Code 12, 58-71
INSERT INTO location_zones (id, name, start_postal_code, end_postal_code, special_cases, description, is_active)
VALUES (2, 'WEST', 58, 71, '12', 'West region - postal codes 12, 58-71', TRUE);

-- EAST: Codes 33-47, 53, 55
INSERT INTO location_zones (id, name, start_postal_code, end_postal_code, special_cases, description, is_active)
VALUES (3, 'EAST', 33, 47, '53,55', 'East region - postal codes 33-47, 53, 55', TRUE);

-- NORTH_EAST: Codes 48-52, 54, 56-57, 81-82
INSERT INTO location_zones (id, name, start_postal_code, end_postal_code, special_cases, description, is_active)
VALUES (4, 'NORTH_EAST', 48, 52, '54,56-57,81-82', 'North East region - postal codes 48-52, 54, 56-57, 81-82', TRUE);

-- NORTH: Codes 72-80, 83
INSERT INTO location_zones (id, name, start_postal_code, end_postal_code, special_cases, description, is_active)
VALUES (5, 'NORTH', 72, 80, '83', 'North region - postal codes 72-80, 83', TRUE);

-- SOUTH: Any other codes
INSERT INTO location_zones (id, name, start_postal_code, end_postal_code, special_cases, description, is_active)
VALUES (6, 'SOUTH', 84, 99, NULL, 'South region - postal codes 84-99', TRUE); 