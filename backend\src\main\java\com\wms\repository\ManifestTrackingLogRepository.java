package com.wms.repository;

import com.wms.entity.ManifestTrackingLog;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface ManifestTrackingLogRepository extends JpaRepository<ManifestTrackingLog, Long> {
    
    /**
     * Find all tracking logs for a specific manifest, ordered by most recent first
     */
    List<ManifestTrackingLog> findByTrackingNoOrderByUpdatedAtDescIdDesc(String trackingNo);
    
    /**
     * Find all tracking logs for a specific manifest within a date range
     */
    @Query("SELECT log FROM ManifestTrackingLog log WHERE log.trackingNo = :trackingNo " +
           "AND log.updatedAt BETWEEN :startDate AND :endDate " +
           "ORDER BY log.updatedAt DESC, log.id DESC")
    List<ManifestTrackingLog> findByTrackingNoAndDateRange(
            @Param("trackingNo") String trackingNo, 
            @Param("startDate") LocalDateTime startDate, 
            @Param("endDate") LocalDateTime endDate);
    
    /**
     * Find all tracking logs by action type
     */
    List<ManifestTrackingLog> findByActionTypeOrderByUpdatedAtDescIdDesc(ManifestTrackingLog.ActionType actionType);
    
    /**
     * Find all tracking logs made by a specific user
     */
    List<ManifestTrackingLog> findByUpdatedByOrderByUpdatedAtDescIdDesc(String updatedBy);
    
    /**
     * Find all tracking logs for manifests within a date range
     */
    @Query("SELECT log FROM ManifestTrackingLog log WHERE log.updatedAt BETWEEN :startDate AND :endDate " +
           "ORDER BY log.updatedAt DESC, log.id DESC")
    List<ManifestTrackingLog> findByDateRangeOrderByUpdatedAtDesc(
            @Param("startDate") LocalDateTime startDate, 
            @Param("endDate") LocalDateTime endDate);
    
    /**
     * Count total tracking logs for a specific manifest
     */
    Long countByTrackingNo(String trackingNo);
    
    /**
     * Find latest tracking log for a specific manifest
     */
    @Query("SELECT log FROM ManifestTrackingLog log WHERE log.trackingNo = :trackingNo " +
           "ORDER BY log.updatedAt DESC, log.id DESC LIMIT 1")
    ManifestTrackingLog findLatestByTrackingNo(@Param("trackingNo") String trackingNo);
    
    /**
     * Find all status change logs for a specific manifest
     */
    @Query("SELECT log FROM ManifestTrackingLog log WHERE log.trackingNo = :trackingNo " +
           "AND log.actionType = 'STATUS_CHANGED' ORDER BY log.updatedAt DESC, log.id DESC")
    List<ManifestTrackingLog> findStatusChangesByTrackingNo(@Param("trackingNo") String trackingNo);
} 