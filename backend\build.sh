#!/bin/bash
# Print system information
echo "========== System Information =========="
uname -a
echo "========================================"

# Try to find Java installation
echo "Searching for Java installations..."
find / -name "java" -type f 2>/dev/null | grep -v "tmp" | head -5
find / -name "javac" -type f 2>/dev/null | grep -v "tmp" | head -3

# Check if Java is already in PATH
echo "Java in PATH check:"
which java || echo "Java not found in PATH"

# Try various common Java home locations
for possible_java in \
    /usr/lib/jvm/java-17-openjdk-amd64 \
    /usr/lib/jvm/java-11-openjdk-amd64 \
    /usr/lib/jvm/default-java \
    /opt/jdk \
    /usr/java/latest \
    /opt/hostedtoolcache/Java_Temurin-Hotspot_jdk \
    /opt/java/openjdk
do
    if [ -d "$possible_java" ]; then
        echo "Found possible Java home: $possible_java"
        if [ -x "$possible_java/bin/java" ]; then
            export JAVA_HOME="$possible_java"
            export PATH="$JAVA_HOME/bin:$PATH"
            echo "Set JAVA_HOME to $JAVA_HOME"
            break
        fi
    fi
done

# Attempt to install Java if not found
if ! command -v java &> /dev/null; then
    echo "Java not found, attempting to install OpenJDK 17..."
    apt-get update && apt-get install -y openjdk-17-jdk || \
    (echo "Failed to install via apt, trying alternative method" && \
     apt-get update && apt-get install -y curl && \
     curl -s "https://get.sdkman.io" | bash && \
     source "$HOME/.sdkman/bin/sdkman-init.sh" && \
     sdk install java 17.0.5-tem)
    
    # Set JAVA_HOME for sdkman installation
    possible_java="$HOME/.sdkman/candidates/java/current"
    if [ -d "$possible_java" ]; then
        export JAVA_HOME="$possible_java"
        export PATH="$JAVA_HOME/bin:$PATH"
    fi
fi

# Display Java version
echo "Java version information:"
java -version || echo "Failed to get Java version"

# Make Maven wrapper executable and update rights
echo "Making mvnw executable..."
chmod +x ./mvnw
ls -la ./mvnw

# Check the project structure
echo "Project structure:"
ls -la

# Run Maven build
echo "Starting Maven build..."
./mvnw clean package -DskipTests 