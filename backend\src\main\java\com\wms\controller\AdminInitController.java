package com.wms.controller;

import com.wms.payload.response.MessageResponse;
import com.wms.repository.RoleRepository;
import com.wms.repository.UserRepo.AdminRepository;
import com.wms.repository.UserRepo.UserRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/admin-init")
public class AdminInitController {
    private static final Logger logger = LoggerFactory.getLogger(AdminInitController.class);
    
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private AdminRepository adminRepository;
    
    @Autowired
    private RoleRepository roleRepository;
    
    @Value("${spring.sql.init.mode:always}")
    private String sqlInitMode;

    /**
     * Check if admin user exists and provide diagnostic information
     */
    @GetMapping("/check")
    public ResponseEntity<?> checkAdmin() {
        logger.info("Checking if admin user exists");
        
        boolean adminExists = userRepository.existsByUsername("admin");
        
        Map<String, Object> response = new HashMap<>();
        response.put("adminExists", adminExists);
        response.put("timestamp", System.currentTimeMillis());
        response.put("sqlInitMode", sqlInitMode);
        
        if (adminExists) {
            try {
                com.wms.entity.user.Admin adminUser = adminRepository.findByUsername("admin").orElse(null);
                if (adminUser != null) {
                    response.put("status", adminUser.getStatus());
                    response.put("email", adminUser.getEmail());
                    response.put("userNumber", adminUser.getUserNumber());
                    
                    // Check if admin has ROLE_ADMIN
                    boolean hasAdminRole = roleRepository.findByName("ROLE_ADMIN")
                            .map(role -> adminUser.getRoles().contains(role))
                            .orElse(false);
                    
                    response.put("hasAdminRole", hasAdminRole);
                }
                
                // Check if admin entity exists in the Admin table
                com.wms.entity.user.Admin adminEntity = adminRepository.findById("admin").orElse(null);
                response.put("adminEntityExists", adminEntity != null);
                
            } catch (Exception e) {
                logger.error("Error checking admin details: {}", e.getMessage());
                response.put("error", "Could not retrieve complete admin information");
            }
        } else {
            response.put("suggestion", "You may need to restart the application to trigger data.sql execution");
        }
        
        return ResponseEntity.ok(response);
    }

    /**
     * Get database health diagnostics
     */
    @GetMapping("/database-health")
    public ResponseEntity<?> checkDatabaseHealth() {
        Map<String, Object> response = new HashMap<>();
        response.put("timestamp", System.currentTimeMillis());
        
        try {
            // Check users
            long userCount = userRepository.count();
            response.put("userCount", userCount);
            
            // Check roles
            long roleCount = roleRepository.count();
            response.put("roleCount", roleCount);
            
            // Get list of roles
            java.util.List<com.wms.entity.Role> roles = roleRepository.findAll();
            response.put("roles", roles.stream().map(com.wms.entity.Role::getName).toArray());
            
            // Check basic user types
            boolean adminExists = userRepository.existsByUsername("admin");
            boolean client1Exists = userRepository.existsByUsername("client1");
            boolean manager1Exists = userRepository.existsByUsername("manager1");
            
            response.put("adminUserExists", adminExists);
            response.put("client1Exists", client1Exists);
            response.put("manager1Exists", manager1Exists);
            
            // Based on what's missing, suggest a fix
            if (!adminExists) {
                response.put("recommendation", "Restart the application to execute data.sql");
            } else if (roleCount < 4 || userCount < 7) {
                response.put("recommendation", "Use /api/debug/reload-data to restore all data");
            } else {
                response.put("status", "Database appears healthy");
            }
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Error checking database health: {}", e.getMessage(), e);
            response.put("error", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
}
