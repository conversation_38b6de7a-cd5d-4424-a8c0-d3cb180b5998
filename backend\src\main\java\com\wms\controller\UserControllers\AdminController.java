package com.wms.controller.UserControllers;

import com.wms.dto.ApiResponse;
import com.wms.entity.user.*;
import com.wms.payload.request.UserUpdateRequest;
import com.wms.payload.request.ChangePasswordRequest;
import com.wms.service.UserServices.AdminService;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@CrossOrigin(origins = "*")
@PreAuthorize("hasRole('ADMIN')")
public class AdminController {

    private final AdminService adminService;
    private static final Logger logger = LoggerFactory.getLogger(AdminController.class);

    @Autowired
    public AdminController(AdminService adminService) {
        this.adminService = adminService;
    }

    // Generic user operations
    @GetMapping({"/api/admin/users", "/admin/users"})
    public ResponseEntity<ApiResponse<List<User>>> getAllUsers() {
        logger.debug("Fetching all users...");
        List<User> users = adminService.getAllUsers();
        logger.debug("Found {} users", users.size());
        logger.debug("Users: {}", users);
        return ResponseEntity.ok(ApiResponse.success(users));
    }

    @GetMapping({"/api/admin/users/count", "/admin/users/count"})
    public ResponseEntity<ApiResponse<Long>> getUsersCount() {
        logger.debug("Fetching users count...");
        Long count = adminService.getUsersCount();
        logger.debug("Found {} users", count);
        return ResponseEntity.ok(ApiResponse.success(count));
    }

    @GetMapping({"/api/admin/users/{username}", "/admin/users/{username}"})
    public ResponseEntity<ApiResponse<User>> getUserByUsername(@PathVariable String username) {
        try {
            User user = adminService.getUserByUsername(username);
            return ResponseEntity.ok(ApiResponse.success(user));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.notFound().build();
        }
    }

    @PutMapping({"/api/admin/users/{username}/deactivate", "/admin/users/{username}/deactivate"})
    public ResponseEntity<ApiResponse<User>> deactivateUser(@PathVariable String username) {
        try {
            User deactivatedUser = adminService.deactivateUser(username);
            return ResponseEntity.ok(ApiResponse.success("User deactivated successfully", deactivatedUser));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    @PutMapping({"/api/admin/users/{username}/activate", "/admin/users/{username}/activate"})
    public ResponseEntity<ApiResponse<User>> activateUser(@PathVariable String username) {
        try {
            User activatedUser = adminService.activateUser(username);
            return ResponseEntity.ok(ApiResponse.success("User activated successfully", activatedUser));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    @PutMapping({"/api/admin/users/{username}/change-password", "/admin/users/{username}/change-password"})
    public ResponseEntity<ApiResponse<String>> changeUserPassword(
            @PathVariable String username,
            @Valid @RequestBody ChangePasswordRequest changePasswordRequest) {
        try {
            adminService.changeUserPassword(username, changePasswordRequest.getCurrentPassword(), changePasswordRequest.getNewPassword());
            return ResponseEntity.ok(ApiResponse.success("Password changed successfully"));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            logger.error("Error changing password for user {}: {}", username, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to change password"));
        }
    }

    // Admin specific operations
    @GetMapping({"/api/admin/admins", "/admin/admins"})
    public ResponseEntity<ApiResponse<List<Admin>>> getAllAdmins() {
        List<Admin> admins = adminService.getAllAdmins();
        return ResponseEntity.ok(ApiResponse.success(admins));
    }

    @GetMapping({"/api/admin/admins/{username}", "/admin/admins/{username}"})
    public ResponseEntity<ApiResponse<Admin>> getAdminByUsername(@PathVariable String username) {
        try {
            Admin admin = adminService.getAdminByUsername(username);
            return ResponseEntity.ok(ApiResponse.success(admin));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.notFound().build();
        }
    }

    @PostMapping({"/api/admin/admins", "/admin/admins"})
    public ResponseEntity<ApiResponse<Admin>> createAdmin(@Valid @RequestBody Admin admin) {
        try {
            Admin createdAdmin = adminService.createAdmin(admin);
            return ResponseEntity.ok(ApiResponse.success("Admin created successfully", createdAdmin));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    @PutMapping({"/api/admin/admins/{username}", "/admin/admins/{username}"})
    public ResponseEntity<ApiResponse<Admin>> updateAdmin(
            @PathVariable String username,
            @Valid @RequestBody UserUpdateRequest updateRequest) {
        try {
            // Get the existing admin
            Admin admin = adminService.getAdminByUsername(username);
            
            // Update admin fields from the request
            admin.setEmail(updateRequest.getEmail());
            admin.setContact(updateRequest.getContact());
            admin.setFullName(updateRequest.getFullName());
            
            // Don't set password directly here, let the service handle it
            
            Admin updatedAdmin = adminService.updateAdmin(username, admin, updateRequest.getPassword());
            return ResponseEntity.ok(ApiResponse.success("Admin updated successfully", updatedAdmin));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    @PutMapping({"/api/admin/admins/{username}/deactivate", "/admin/admins/{username}/deactivate"})
    public ResponseEntity<ApiResponse<Admin>> deactivateAdmin(@PathVariable String username) {
        try {
            Admin deactivatedAdmin = adminService.deactivateAdmin(username);
            return ResponseEntity.ok(ApiResponse.success("Admin deactivated successfully", deactivatedAdmin));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    @PutMapping({"/api/admin/admins/{username}/activate", "/admin/admins/{username}/activate"})
    public ResponseEntity<ApiResponse<Admin>> activateAdmin(@PathVariable String username) {
        try {
            Admin activatedAdmin = adminService.activateAdmin(username);
            return ResponseEntity.ok(ApiResponse.success("Admin activated successfully", activatedAdmin));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    // Manager operations
    @GetMapping({"/api/admin/managers", "/admin/managers"})
    public ResponseEntity<ApiResponse<List<Manager>>> getAllManagers() {
        List<Manager> managers = adminService.getAllManagers();
        return ResponseEntity.ok(ApiResponse.success(managers));
    }

    @GetMapping({"/api/admin/managers/{username}", "/admin/managers/{username}"})
    public ResponseEntity<ApiResponse<Manager>> getManagerByUsername(@PathVariable String username) {
        try {
            Manager manager = adminService.getManagerByUsername(username);
            return ResponseEntity.ok(ApiResponse.success(manager));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.notFound().build();
        }
    }

    @PostMapping({"/api/admin/managers", "/admin/managers"})
    public ResponseEntity<ApiResponse<Manager>> createManager(@Valid @RequestBody Manager manager) {
        try {
            Manager createdManager = adminService.createManager(manager);
            return ResponseEntity.ok(ApiResponse.success("Manager created successfully", createdManager));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    @PutMapping({"/api/admin/managers/{username}", "/admin/managers/{username}"})
    public ResponseEntity<ApiResponse<Manager>> updateManager(
            @PathVariable String username,
            @Valid @RequestBody UserUpdateRequest updateRequest) {
        try {
            // Get the existing manager
            Manager manager = adminService.getManagerByUsername(username);
            
            // Update manager fields from the request
            manager.setEmail(updateRequest.getEmail());
            manager.setContact(updateRequest.getContact());
            manager.setFullName(updateRequest.getFullName());
            manager.setDepartment(updateRequest.getDepartment());
            
            // Don't set password directly here, let the service handle it
            
            Manager updatedManager = adminService.updateManager(username, manager, updateRequest.getPassword());
            return ResponseEntity.ok(ApiResponse.success("Manager updated successfully", updatedManager));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    @PutMapping({"/api/admin/managers/{username}/deactivate", "/admin/managers/{username}/deactivate"})
    public ResponseEntity<ApiResponse<Manager>> deactivateManager(@PathVariable String username) {
        try {
            Manager deactivatedManager = adminService.deactivateManager(username);
            return ResponseEntity.ok(ApiResponse.success("Manager deactivated successfully", deactivatedManager));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    @PutMapping({"/api/admin/managers/{username}/activate", "/admin/managers/{username}/activate"})
    public ResponseEntity<ApiResponse<Manager>> activateManager(@PathVariable String username) {
        try {
            Manager activatedManager = adminService.activateManager(username);
            return ResponseEntity.ok(ApiResponse.success("Manager activated successfully", activatedManager));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    // Client operations
    @GetMapping({"/api/admin/clients", "/admin/clients"})
    public ResponseEntity<ApiResponse<List<Client>>> getAllClients() {
        List<Client> clients = adminService.getAllClients();
        return ResponseEntity.ok(ApiResponse.success(clients));
    }

    @GetMapping({"/api/admin/clients/{username}", "/admin/clients/{username}"})
    public ResponseEntity<ApiResponse<Client>> getClientByUsername(@PathVariable String username) {
        try {
            Client client = adminService.getClientByUsername(username);
            return ResponseEntity.ok(ApiResponse.success(client));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.notFound().build();
        }
    }

    @PostMapping({"/api/admin/clients", "/admin/clients"})
    public ResponseEntity<ApiResponse<Client>> createClient(@Valid @RequestBody Client client) {
        try {
            Client createdClient = adminService.createClient(client);
            return ResponseEntity.ok(ApiResponse.success("Client created successfully", createdClient));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    @PutMapping({"/api/admin/clients/{username}", "/admin/clients/{username}"})
    public ResponseEntity<ApiResponse<Client>> updateClient(
            @PathVariable String username,
            @Valid @RequestBody UserUpdateRequest updateRequest) {
        try {
            // Get the existing client
            Client client = adminService.getClientByUsername(username);
            
            // Update client fields from the request
            client.setEmail(updateRequest.getEmail());
            client.setContact(updateRequest.getContact());
            client.setFullName(updateRequest.getFullName());
            client.setCompanyName(updateRequest.getCompanyName());
            client.setDesignation(updateRequest.getDesignation());
            client.setAltContact(updateRequest.getAltContact());
            client.setAltDesignation(updateRequest.getAltDesignation());
            
            // Don't set password directly here, let the service handle it
            
            Client updatedClient = adminService.updateClient(username, client, updateRequest.getPassword());
            return ResponseEntity.ok(ApiResponse.success("Client updated successfully", updatedClient));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    @PutMapping({"/api/admin/clients/{username}/deactivate", "/admin/clients/{username}/deactivate"})
    public ResponseEntity<ApiResponse<Client>> deactivateClient(@PathVariable String username) {
        try {
            Client deactivatedClient = adminService.deactivateClient(username);
            return ResponseEntity.ok(ApiResponse.success("Client deactivated successfully", deactivatedClient));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    @PutMapping({"/api/admin/clients/{username}/activate", "/admin/clients/{username}/activate"})
    public ResponseEntity<ApiResponse<Client>> activateClient(@PathVariable String username) {
        try {
            Client activatedClient = adminService.activateClient(username);
            return ResponseEntity.ok(ApiResponse.success("Client activated successfully", activatedClient));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    // Driver operations
    @GetMapping({"/api/admin/drivers", "/admin/drivers"})
    public ResponseEntity<ApiResponse<List<Driver>>> getAllDrivers() {
        List<Driver> drivers = adminService.getAllDrivers();
        return ResponseEntity.ok(ApiResponse.success(drivers));
    }

    @GetMapping({"/api/admin/drivers/{username}", "/admin/drivers/{username}"})
    public ResponseEntity<ApiResponse<Driver>> getDriverByUsername(@PathVariable String username) {
        try {
            Driver driver = adminService.getDriverByUsername(username);
            return ResponseEntity.ok(ApiResponse.success(driver));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.notFound().build();
        }
    }

    @PostMapping({"/api/admin/drivers", "/admin/drivers"})
    public ResponseEntity<ApiResponse<Driver>> createDriver(@Valid @RequestBody Driver driver) {
        try {
            Driver createdDriver = adminService.createDriver(driver);
            return ResponseEntity.ok(ApiResponse.success("Driver created successfully", createdDriver));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    @PutMapping({"/api/admin/drivers/{username}", "/admin/drivers/{username}"})
    public ResponseEntity<ApiResponse<Driver>> updateDriver(
            @PathVariable String username,
            @Valid @RequestBody UserUpdateRequest updateRequest) {
        try {
            // Get the existing driver
            Driver driver = adminService.getDriverByUsername(username);
            
            // Update driver fields from the request
            driver.setEmail(updateRequest.getEmail());
            driver.setContact(updateRequest.getContact());
            driver.setFullName(updateRequest.getFullName());
            driver.setLicenseNumber(updateRequest.getLicenseNumber());
            
            // Don't set password directly here, let the service handle it
            
            Driver updatedDriver = adminService.updateDriver(username, driver, updateRequest.getPassword());
            return ResponseEntity.ok(ApiResponse.success("Driver updated successfully", updatedDriver));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    @PutMapping({"/api/admin/drivers/{username}/deactivate", "/admin/drivers/{username}/deactivate"})
    public ResponseEntity<ApiResponse<Driver>> deactivateDriver(@PathVariable String username) {
        try {
            Driver deactivatedDriver = adminService.deactivateDriver(username);
            return ResponseEntity.ok(ApiResponse.success("Driver deactivated successfully", deactivatedDriver));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    @PutMapping({"/api/admin/drivers/{username}/activate", "/admin/drivers/{username}/activate"})
    public ResponseEntity<ApiResponse<Driver>> activateDriver(@PathVariable String username) {
        try {
            Driver activatedDriver = adminService.activateDriver(username);
            return ResponseEntity.ok(ApiResponse.success("Driver activated successfully", activatedDriver));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }
}