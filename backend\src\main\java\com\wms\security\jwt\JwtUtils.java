package com.wms.security.jwt;

import com.wms.security.services.UserDetailsImpl;
import io.jsonwebtoken.*;
import io.jsonwebtoken.io.Decoders;
import io.jsonwebtoken.security.Keys;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.stereotype.Component;

import java.security.Key;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class JwtUtils {
    private static final Logger logger = LoggerFactory.getLogger(JwtUtils.class);

    @Value("${wms.app.jwtSecret}")
    private String jwtSecret;

    @Value("${wms.app.jwtExpirationMs}")
    private int jwtExpirationMs;
    
    @Autowired
    private JwtTokenStore tokenStore;

    public String generateJwtToken(Authentication authentication) {
        UserDetailsImpl userPrincipal = (UserDetailsImpl) authentication.getPrincipal();

        // Extract roles for storing in claims
        String roles = userPrincipal.getAuthorities().stream()
                .map(GrantedAuthority::getAuthority)
                .collect(Collectors.joining(","));
                
        Map<String, Object> claims = new HashMap<>();
        claims.put("roles", roles);
        claims.put("email", userPrincipal.getEmail());
        claims.put("id", userPrincipal.getId());
        
        Date now = new Date();
        Date expiration = new Date(now.getTime() + jwtExpirationMs);
        
        String token = Jwts.builder()
                .setClaims(claims)
                .setSubject(userPrincipal.getUsername())
                .setIssuedAt(now)
                .setExpiration(expiration)
                .signWith(key(), SignatureAlgorithm.HS256)
                .compact();
                
        // Store the token in the token store
        Claims tokenClaims = Jwts.parserBuilder()
                .setSigningKey(key())
                .build()
                .parseClaimsJws(token)
                .getBody();
                
        tokenStore.storeToken(token, userPrincipal.getUsername(), tokenClaims);
        
        return token;
    }

    public String generateTokenFromUsername(String username) {
        Date now = new Date();
        Date expiration = new Date(now.getTime() + jwtExpirationMs);
        
        Map<String, Object> claims = new HashMap<>();
        claims.put("roles", "ROLE_ADMIN"); // Default to admin for test tokens
        
        String token = Jwts.builder()
                .setClaims(claims)
                .setSubject(username)
                .setIssuedAt(now)
                .setExpiration(expiration)
                .signWith(key(), SignatureAlgorithm.HS256)
                .compact();
                
        // Store the token in the token store
        Claims tokenClaims = Jwts.parserBuilder()
                .setSigningKey(key())
                .build()
                .parseClaimsJws(token)
                .getBody();
                
        tokenStore.storeToken(token, username, tokenClaims);
        
        return token;
    }

    private Key key() {
        return Keys.hmacShaKeyFor(Decoders.BASE64.decode(jwtSecret));
    }

    public String getUserNameFromJwtToken(String token) {
        // Try to get username from token store first
        return tokenStore.getUsernameFromToken(token)
                .orElseGet(() -> {
                    // Fall back to parsing the token
                    try {
                        String username = Jwts.parserBuilder()
                                .setSigningKey(key())
                                .build()
                                .parseClaimsJws(token)
                                .getBody()
                                .getSubject();
                                
                        // Store the token for future use if valid
                        if (username != null) {
                            Claims claims = Jwts.parserBuilder()
                                    .setSigningKey(key())
                                    .build()
                                    .parseClaimsJws(token)
                                    .getBody();
                                    
                            tokenStore.storeToken(token, username, claims);
                        }
                        
                        return username;
                    } catch (Exception e) {
                        logger.error("Failed to extract username from token: {}", e.getMessage());
                        return null;
                    }
                });
    }

    public boolean validateJwtToken(String authToken) {
        // Check token store first
        if (tokenStore.validateToken(authToken)) {
            return true;
        }
        
        try {
            Claims claims = Jwts.parserBuilder()
                    .setSigningKey(key())
                    .build()
                    .parseClaimsJws(authToken)
                    .getBody();
                    
            // Valid token - store it
            String username = claims.getSubject();
            tokenStore.storeToken(authToken, username, claims);
            
            return true;
        } catch (MalformedJwtException e) {
            logger.error("Invalid JWT token: {}", e.getMessage());
        } catch (ExpiredJwtException e) {
            logger.error("JWT token is expired: {}", e.getMessage());
            // Remove expired token from store
            tokenStore.invalidateToken(authToken);
        } catch (UnsupportedJwtException e) {
            logger.error("JWT token is unsupported: {}", e.getMessage());
        } catch (IllegalArgumentException e) {
            logger.error("JWT claims string is empty: {}", e.getMessage());
        }

        return false;
    }
    
    public Claims getClaimsFromToken(String token) {
        return tokenStore.getClaimsFromToken(token)
                .orElseGet(() -> {
                    try {
                        return Jwts.parserBuilder()
                                .setSigningKey(key())
                                .build()
                                .parseClaimsJws(token)
                                .getBody();
                    } catch (Exception e) {
                        logger.error("Failed to extract claims from token: {}", e.getMessage());
                        return null;
                    }
                });
    }
} 