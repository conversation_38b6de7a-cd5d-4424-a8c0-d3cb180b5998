import { useEffect } from 'react';

/**
 * Custom hook to set the document title for the current page
 * @param title - The page title (will be suffixed with "| Fukuyama WMS")
 * @param deps - Optional dependency array for when to update the title
 */
export const usePageTitle = (title: string, deps: any[] = []) => {
  useEffect(() => {
    const originalTitle = document.title;
    document.title = `${title} | Fukuyama WMS`;
    
    // Cleanup function to restore original title (optional, but good practice)
    return () => {
      // Only restore if we're unmounting and title hasn't been changed by another component
      if (document.title === `${title} | Fukuyama WMS`) {
        document.title = originalTitle;
      }
    };
  }, [title, ...deps]);
};

export default usePageTitle; 