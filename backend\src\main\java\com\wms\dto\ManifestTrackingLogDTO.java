package com.wms.dto;

import com.wms.entity.ManifestTrackingLog;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ManifestTrackingLogDTO {
    
    private Long id;
    private String trackingNo;
    private String previousStatus;
    private String newStatus;
    private String fieldName;
    private String oldValue;
    private String newValue;
    private String actionType;
    private String updatedBy;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;
    
    private String remarks;
    private String ipAddress;
    private String userAgent;
    
    public static ManifestTrackingLogDTO fromEntity(ManifestTrackingLog entity) {
        return ManifestTrackingLogDTO.builder()
                .id(entity.getId())
                .trackingNo(entity.getTrackingNo())
                .previousStatus(entity.getPreviousStatus())
                .newStatus(entity.getNewStatus())
                .fieldName(entity.getFieldName())
                .oldValue(entity.getOldValue())
                .newValue(entity.getNewValue())
                .actionType(entity.getActionType().toString())
                .updatedBy(entity.getUpdatedBy())
                .updatedAt(entity.getUpdatedAt())
                .remarks(entity.getRemarks())
                .ipAddress(entity.getIpAddress())
                .userAgent(entity.getUserAgent())
                .build();
    }
} 