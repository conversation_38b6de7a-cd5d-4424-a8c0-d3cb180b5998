package com.wms.service;

import com.wms.entity.Manifest;
import com.wms.entity.ManifestStatus;

/**
 * Service for handling manifest status updates
 */
public interface ManifestStatusService {
    
    /**
     * Checks if there's a discrepancy between manifest pieces and total pallet pieces
     * and updates the manifest status accordingly
     * 
     * @param manifest The manifest to check for discrepancy
     * @return The updated manifest if status changed, or the original manifest if no change
     */
    Manifest checkAndUpdateManifestStatus(Manifest manifest);
    
    /**
     * Checks if there's a discrepancy between manifest pieces and total pallet pieces
     * and updates the manifest status accordingly
     * 
     * @param manifest The manifest to check for discrepancy
     * @param logStatusChange Whether to log the status change
     * @return The updated manifest if status changed, or the original manifest if no change
     */
    Manifest checkAndUpdateManifestStatus(Manifest manifest, boolean logStatusChange);
    
    /**
     * Checks if the status should be updated based on the delivery date
     * Only applies to manifests in INBOUNDED_TO_WAREHOUSE, READY_TO_DELIVER, or PENDING_DELIVER status
     * 
     * @param manifest The manifest to check
     * @return The updated manifest if status changed, or the original manifest if no change
     */
    Manifest checkAndUpdateStatusBasedOnDeliveryDate(Manifest manifest);
    
    /**
     * Manually triggers a check of all manifests with delivery dates to update their statuses
     * This can be called on-demand to update statuses without waiting for the scheduled task
     * 
     * @return The number of manifests that had their status updated
     */
    int updateAllManifestStatusesBasedOnDeliveryDates();
} 