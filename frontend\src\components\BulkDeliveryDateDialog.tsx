import React, { useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  Alert,
  CircularProgress
} from '@mui/material';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import SplitDeliveryDatePicker from './SplitDeliveryDatePicker';

interface BulkDeliveryDateDialogProps {
  open: boolean;
  selectedCount: number;
  onClose: () => void;
  onSave: (date: Date | null) => Promise<void>;
  loading: boolean;
  error: string | null;
}

const BulkDeliveryDateDialog: React.FC<BulkDeliveryDateDialogProps> = ({
  open,
  selectedCount,
  onClose,
  onSave,
  loading,
  error
}) => {
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [selectedTimeSlot, setSelectedTimeSlot] = useState<string | null>(null);

  const handleSave = async () => {
    await onSave(selectedDate);
  };

  const handleClearDate = () => {
    setSelectedDate(null);
    setSelectedTimeSlot(null);
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Box display="flex" alignItems="center">
          <CalendarTodayIcon sx={{ mr: 1 }} />
          Update Delivery Date for {selectedCount} Manifest{selectedCount !== 1 ? 's' : ''}
        </Box>
      </DialogTitle>
      <DialogContent>
        <Box sx={{ mt: 2, mb: 3 }}>
          <Typography variant="body1" gutterBottom>
            Set a new delivery date and time slot for all selected manifests, or clear existing delivery dates.
          </Typography>
          
          <SplitDeliveryDatePicker
            label="Delivery Date & Time Slot"
            value={selectedDate}
            onChange={setSelectedDate}
            timeSlotValue={selectedTimeSlot}
            onTimeSlotChange={setSelectedTimeSlot}
            fullWidth
            helperText="Leave empty to clear delivery dates for all selected manifests."
            slotProps={{
              datePicker: {
                popper: {
                  sx: { zIndex: 10000 }
                }
              }
            }}
            sx={{ mt: 2 }}
          />
          
          {error && (
            <Alert severity="error" sx={{ mt: 2 }}>
              {error}
            </Alert>
          )}
        </Box>
      </DialogContent>
      <DialogActions sx={{ px: 3, py: 2 }}>
        <Button onClick={handleClearDate} variant="outlined" color="secondary">
          Clear Date
        </Button>
        <Button onClick={onClose} disabled={loading}>
          Cancel
        </Button>
        <Button 
          onClick={handleSave} 
          variant="contained" 
          color="primary"
          disabled={loading}
          startIcon={loading ? <CircularProgress size={16} /> : <CalendarTodayIcon />}
        >
          {selectedDate ? 'Update All Dates' : 'Clear All Dates'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default BulkDeliveryDateDialog; 