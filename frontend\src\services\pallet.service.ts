import axios from 'axios';
import { Pallet, PalletFormData } from '../types/Pallet';
import { ApiResponse } from '../types/api';
import API_CONFIG from '../config/api.config';

const API_BASE_URL = API_CONFIG.baseUrl;

class PalletService {
  async getAllPallets(token: string): Promise<ApiResponse<Pallet[]>> {
    const response = await axios.get<ApiResponse<Pallet[]>>(
      `${API_BASE_URL}/api/pallets`, 
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      }
    );
    return response.data;
  }

  async getPalletByPalletNo(palletNo: string, token: string): Promise<ApiResponse<Pallet>> {
    const response = await axios.get<ApiResponse<Pallet>>(
      `${API_BASE_URL}/api/pallets/${palletNo}`, 
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      }
    );
    return response.data;
  }

  async getPalletsByManifestTrackingNo(manifestTrackingNo: string, token: string): Promise<ApiResponse<Pallet[]>> {
    const response = await axios.get<ApiResponse<Pallet[]>>(
      `${API_BASE_URL}/api/manifests/${manifestTrackingNo}/pallets`, 
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      }
    );
    return response.data;
  }

  async createPallet(pallet: PalletFormData & { manifestTrackingNo: string }, token: string): Promise<ApiResponse<Pallet>> {
    const palletData = {
      manifest: { trackingNo: pallet.manifestTrackingNo },
      noOfPieces: pallet.noOfPieces,
      totalPiecesReference: pallet.totalPiecesReference
    };
    
    const response = await axios.post<ApiResponse<Pallet>>(
      `${API_BASE_URL}/api/pallets`, 
      palletData,
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      }
    );
    return response.data;
  }

  async createPalletsForManifest(manifestTrackingNo: string, pallets: PalletFormData[], token: string): Promise<ApiResponse<Pallet[]>> {
    const palletsData = pallets.map(pallet => ({
      noOfPieces: pallet.noOfPieces,
      totalPiecesReference: pallet.totalPiecesReference
    }));
    
    const response = await axios.post<ApiResponse<Pallet[]>>(
      `${API_BASE_URL}/api/manifests/${manifestTrackingNo}/pallets`, 
      palletsData,
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      }
    );
    return response.data;
  }

  async updatePallet(palletNo: string, pallet: PalletFormData, token: string): Promise<ApiResponse<Pallet>> {
    const response = await axios.put<ApiResponse<Pallet>>(
      `${API_BASE_URL}/api/pallets/${palletNo}`, 
      pallet,
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      }
    );
    return response.data;
  }

  async deletePallet(palletNo: string, token: string): Promise<ApiResponse<void>> {
    const response = await axios.delete<ApiResponse<void>>(
      `${API_BASE_URL}/api/pallets/${palletNo}`, 
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      }
    );
    return response.data;
  }

  async deletePalletsByManifestTrackingNo(manifestTrackingNo: string, token: string): Promise<ApiResponse<void>> {
    const response = await axios.delete<ApiResponse<void>>(
      `${API_BASE_URL}/api/manifests/${manifestTrackingNo}/pallets`, 
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      }
    );
    return response.data;
  }

  async countPalletsByManifestTrackingNo(manifestTrackingNo: string, token: string): Promise<ApiResponse<number>> {
    const response = await axios.get<ApiResponse<number>>(
      `${API_BASE_URL}/api/manifests/${manifestTrackingNo}/pallets/count`, 
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      }
    );
    return response.data;
  }
  
  async getTotalPiecesInPallets(manifestTrackingNo: string, token: string): Promise<ApiResponse<number>> {
    const response = await axios.get<ApiResponse<number>>(
      `${API_BASE_URL}/api/manifests/${manifestTrackingNo}/pallets/total-pieces`, 
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      }
    );
    return response.data;
  }
  
  async isPalletPiecesMatchingManifest(manifestTrackingNo: string, token: string): Promise<ApiResponse<boolean>> {
    const response = await axios.get<ApiResponse<boolean>>(
      `${API_BASE_URL}/api/manifests/${manifestTrackingNo}/pallets/is-matching`, 
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      }
    );
    return response.data;
  }
}

export default new PalletService(); 