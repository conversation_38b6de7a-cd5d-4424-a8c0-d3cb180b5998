import { forwardRef } from 'react';
import { Box, Paper, Typography, Divider } from '@mui/material';
import QRCode from 'react-qr-code';
import Barcode from 'react-barcode';
import { Manifest } from '../types/manifest';
import { formatDateString } from '../utils/dateUtils';

interface ManifestLabelProps {
  manifest: Manifest;
  labelIndex: number;
  labelCount: number;
}

const ManifestLabel = forwardRef<HTMLDivElement, ManifestLabelProps>(
  ({ manifest, labelIndex, labelCount }, ref) => {
    return (
      <Box sx={{ width: '100%' }} ref={ref}>
        <Paper 
          sx={{ 
            p: 2, 
            width: '100%', 
            maxWidth: '800px', 
            margin: '0 auto',
            border: '1px solid #ccc'
          }}
        >
          {/* Company Header */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>
            <Typography variant="subtitle1" fontWeight="bold">
              Fukuyama Logistics
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Label {labelIndex + 1}/{labelCount}
            </Typography>
          </Box>
          
          {/* Header with tracking number and company info */}
          <Box mb={2}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
              <Typography variant="h5" fontWeight="bold">
                WMS Logistics
              </Typography>
              <Typography variant="h6" fontWeight="bold">
                Tracking No: {manifest.trackingNo}
              </Typography>
            </Box>
            <Divider sx={{ mb: 2 }} />
          </Box>
          
          {/* Barcode and QR Code */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
            <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
              <Box sx={{ mb: 1 }}>
                <Barcode 
                  value={manifest.trackingNo} 
                  width={1.5}
                  height={50}
                  fontSize={14}
                  margin={5}
                />
              </Box>
              <Typography variant="caption">
                Tracking No: {manifest.trackingNo}
              </Typography>
            </Box>
            
            <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
              <Box sx={{ mb: 1, p: 1, bgcolor: 'white' }}>
                <QRCode
                  value={manifest.trackingNo}
                  size={100}
                  level="H"
                />
              </Box>
              <Typography variant="caption">
                Scan for tracking
              </Typography>
            </Box>
          </Box>
          
          {/* Main content */}
          <Box sx={{ display: 'flex', flexWrap: 'wrap', mb: 2 }}>
            {/* Client and Container Info */}
            <Box sx={{ flex: '1 1 50%', minWidth: '250px', pr: 2 }}>
              <Typography variant="subtitle2" fontWeight="bold">Client Details:</Typography>
              <Typography variant="body2">
                Username: {manifest.client?.username || 'N/A'}
              </Typography>
              <Typography variant="body2">
                Company: {manifest.client?.companyName || 'N/A'}
              </Typography>
              <Typography variant="subtitle2" fontWeight="bold" sx={{ mt: 1 }}>Container:</Typography>
              <Typography variant="body2">
                {manifest.container?.containerNo || 'N/A'}
              </Typography>
              <Typography variant="subtitle2" fontWeight="bold" sx={{ mt: 1 }}>Internal ID:</Typography>
              <Typography variant="body2">
                {manifest.internalId || 'N/A'}
              </Typography>
            </Box>
            
            {/* Customer Info */}
            <Box sx={{ flex: '1 1 50%', minWidth: '250px' }}>
              <Typography variant="subtitle2" fontWeight="bold">Customer Details:</Typography>
              <Typography variant="body2">
                Name: {manifest.customerName || 'N/A'}
              </Typography>
              <Typography variant="body2">
                Phone: {manifest.phoneNo || 'N/A'}
              </Typography>
              <Typography variant="body2">
                Address: {manifest.address || 'N/A'}
              </Typography>
              <Typography variant="body2">
                Postal Code: {manifest.postalCode || 'N/A'}
              </Typography>
              <Typography variant="body2">
                Country: {manifest.country || 'N/A'}
              </Typography>
            </Box>
          </Box>
          
          {/* Shipment Info */}
          <Box>
            <Divider sx={{ my: 1 }} />
            <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
              <Typography variant="subtitle2" fontWeight="bold">
                Pieces: {manifest.pieces || 0}
              </Typography>
              <Typography variant="subtitle2" fontWeight="bold">
                CBM: {manifest.cbm || 0}
              </Typography>
              <Typography variant="subtitle2" fontWeight="bold">
                Pallets: {manifest.actualPalletsCount || 0}
              </Typography>
            </Box>
          </Box>
          
          {/* Status and Date Info */}
          <Box>
            <Divider sx={{ my: 1 }} />
            <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
              <Typography variant="body2">
                Status: {manifest.status.replace(/_/g, ' ')}
              </Typography>
              <Typography variant="body2">
                Delivery Date: {manifest.deliveryDate ? formatDateString(manifest.deliveryDate) : 'N/A'}
              </Typography>
            </Box>
          </Box>
        </Paper>
      </Box>
    );
  }
);

ManifestLabel.displayName = 'ManifestLabel';

export default ManifestLabel; 