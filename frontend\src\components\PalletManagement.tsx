import React, { useState, useEffect, useRef } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  CircularProgress,
  Card,
  CardContent,
  Tooltip,
  InputAdornment,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Save as SaveIcon,
  Delete as DeleteIcon,
  Label as LabelIcon,
  Clear as ClearIcon,
} from '@mui/icons-material';
import { useToast } from '../contexts/ToastContext';
import { Pallet, PalletFormData } from '../types/Pallet';
import { useAuth } from '../contexts/AuthContext';
import palletService from '../services/pallet.service';
import PalletLabelDialog from './PalletLabelDialog';
import { Manifest, ManifestStatus } from '../types/manifest';

interface PalletManagementProps {
  manifestTrackingNo: string;
  manifestPieces: number;
  manifest: Manifest;
  readonly?: boolean;
  onPalletsChange?: (palletCount: number) => void;
  onPalletOperationComplete?: () => void;
}

const PalletManagement: React.FC<PalletManagementProps> = ({
  manifestTrackingNo,
  manifestPieces,
  manifest,
  readonly = false,
  onPalletsChange,
  onPalletOperationComplete
}) => {
  const { currentUser } = useAuth();
  const toast = useToast();
  const [pallets, setPallets] = useState<Pallet[]>([]);
  const [loading, setLoading] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingPallet, setEditingPallet] = useState<Pallet | null>(null);
  const [formData, setFormData] = useState<PalletFormData>({
    noOfPieces: 0,
    totalPiecesReference: manifestPieces,
  });
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [palletToDelete, setPalletToDelete] = useState<string | null>(null);
  const [labelDialogOpen, setLabelDialogOpen] = useState(false);
  const [labelDialogPallets, setLabelDialogPallets] = useState<Pallet[]>([]);
  const [previousStatus, setPreviousStatus] = useState<ManifestStatus | null>(null);
  const [needsRefreshAfterLabel, setNeedsRefreshAfterLabel] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const confirmInputRef = useRef<HTMLButtonElement>(null);

  useEffect(() => {
    fetchPallets();
    // Store the current manifest status when component mounts
    if (manifest) {
      setPreviousStatus(manifest.status);
    }
  }, [manifestTrackingNo]);

  // Effect to focus the input field when dialog opens
  useEffect(() => {
    if (dialogOpen) {
      // Use multiple attempts to ensure focus
      const focusInput = () => {
        if (inputRef.current) {
          // Force blur on any other element first
          if (document.activeElement instanceof HTMLElement) {
            document.activeElement.blur();
          }
          
          // Focus our input and select text if needed
          inputRef.current.focus();
          if (editingPallet) {
            inputRef.current.select();
          }
          
          console.log('Input field focused:', document.activeElement === inputRef.current);
        }
      };
      
      // Try immediately
      focusInput();
      
      // Try again after a short delay to ensure dialog is fully rendered
      const timer1 = setTimeout(focusInput, 50);
      
      // And one more time with a longer delay as a fallback
      const timer2 = setTimeout(focusInput, 200);
      
      return () => {
        clearTimeout(timer1);
        clearTimeout(timer2);
      };
    }
  }, [dialogOpen, editingPallet]);

  // Additional handler to focus when dialog content is clicked
  const handleDialogContentClick = () => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
  };

  const fetchPallets = async () => {
    if (!currentUser?.token) return;
    
    try {
      setLoading(true);
      const response = await palletService.getPalletsByManifestTrackingNo(manifestTrackingNo, currentUser.token);
      if (response.success) {
        setPallets(response.data);
        if (onPalletsChange) {
          onPalletsChange(response.data.length);
        }
      } else {
        toast.error(response.message || 'Failed to fetch pallets');
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || 'Error fetching pallets');
    } finally {
      setLoading(false);
    }
  };

  // Function to fetch pallets and check status after pallet operations
  const fetchPalletsAndCheckStatus = async () => {
    if (!currentUser?.token) return;
    
    try {
      setLoading(true);
      const response = await palletService.getPalletsByManifestTrackingNo(manifestTrackingNo, currentUser.token);
      if (response.success) {
        setPallets(response.data);
        if (onPalletsChange) {
          onPalletsChange(response.data.length);
        }
        
        // Let the parent component handle manifest status updates
        if (onPalletOperationComplete) {
          console.log('Calling onPalletOperationComplete after pallet operation');
          onPalletOperationComplete();
        }
      } else {
        toast.error(response.message || 'Failed to fetch pallets');
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || 'Error fetching pallets');
    } finally {
      setLoading(false);
    }
  };

  const handleOpenDialog = (pallet?: Pallet) => {
    if (pallet) {
      setEditingPallet(pallet);
      setFormData({
        noOfPieces: pallet.noOfPieces,
        totalPiecesReference: pallet.totalPiecesReference,
      });
    } else {
      setEditingPallet(null);
      setFormData({
        noOfPieces: 0,
        totalPiecesReference: manifestPieces,
      });
    }
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setEditingPallet(null);
    // Ensure confirmation dialog is also closed when main dialog closes
    setConfirmDialogOpen(false);
  };

  const resetForm = () => {
    setFormData({
      noOfPieces: 0,
      totalPiecesReference: manifestPieces,
    });
    setEditingPallet(null);
    // Ensure confirmation dialog is closed when form is reset
    setConfirmDialogOpen(false);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    // Submit form when Enter is pressed
    if (e.key === 'Enter') {
      e.preventDefault();
      // Only submit if the form is valid (pieces > 0)
      if (formData.noOfPieces >= 1) {
        // Show confirmation dialog instead of submitting directly
        setConfirmDialogOpen(true);
      }
    }
    // Close dialog when Escape is pressed
    else if (e.key === 'Escape') {
      handleCloseDialog();
    }
  };

  const handleConfirmKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleSubmit();
      setConfirmDialogOpen(false);
    } else if (e.key === 'Escape') {
      setConfirmDialogOpen(false);
    }
  };

  // Focus the confirm button when confirmation dialog opens
  useEffect(() => {
    if (confirmDialogOpen && confirmInputRef.current) {
      setTimeout(() => {
        confirmInputRef.current?.focus();
      }, 100);
    }
  }, [confirmDialogOpen]);

  const handleSubmit = async () => {
    if (!currentUser?.token) return;

    if (formData.noOfPieces < 1) {
      toast.error('Number of pieces must be at least 1');
      return;
    }

    try {
      setLoading(true);
      
      if (editingPallet) {
        const response = await palletService.updatePallet(editingPallet.palletNo, formData, currentUser.token);
        if (response.success) {
          toast.success('Pallet updated successfully');
          await fetchPalletsAndCheckStatus();

          // Ensure confirmation dialog is closed for edit operations
          setConfirmDialogOpen(false);
        } else {
          toast.error(response.message || 'Failed to update pallet');
        }
      } else {
        const response = await palletService.createPallet(
          { ...formData, manifestTrackingNo },
          currentUser.token
        );
        
        if (response.success) {
          toast.success('Pallet created successfully');

          // Close confirmation dialog before opening label dialog
          setConfirmDialogOpen(false);

          // Automatically generate label for the newly created pallet
          if (response.data) {
            setLabelDialogPallets([response.data]);
            setLabelDialogOpen(true);
            setNeedsRefreshAfterLabel(true);

            // IMPORTANT: Update the pallets state immediately to include the new pallet
            // This ensures the inbounded pieces calculation in the label is correct
            // Without this, the label would show stale cumulative pieces count
            setPallets(prevPallets => {
              const updatedPallets = [...prevPallets, response.data];
              const totalInboundedPieces = updatedPallets.reduce((sum, p) => sum + p.noOfPieces, 0);
              console.log(`📊 Updated pallets state for label: ${updatedPallets.length} pallets, total pieces: ${totalInboundedPieces}`);

              // Emit the selective update event here with the correct calculated values
              // This ensures we have the most up-to-date pallet data
              setTimeout(() => {
                // Validate the calculation before emitting
                console.log(`🔍 Validating inbounded pieces calculation:`);
                console.log(`   Pallets: ${JSON.stringify(updatedPallets.map(p => ({ palletNo: p.palletNo, pieces: p.noOfPieces })))}`);
                console.log(`   Total pieces: ${totalInboundedPieces} (calculated from ${updatedPallets.length} pallets)`);

                const updateEvent = new CustomEvent('pallet-operation-selective-update', {
                  detail: {
                    type: 'pallet_created',
                    manifestTrackingNo: manifestTrackingNo,
                    palletCount: updatedPallets.length,
                    inboundedPieces: totalInboundedPieces,
                    timestamp: new Date().toISOString()
                  }
                });
                window.dispatchEvent(updateEvent);
                console.log(`📡 Emitted selective update event: ${updatedPallets.length} pallets, ${totalInboundedPieces} inbounded pieces`);
              }, 50); // Small delay to ensure state is updated

              return updatedPallets;
            });

            // IMPORTANT: We delay the full refresh until after the label dialog closes
            // This prevents the page refresh from interrupting the label printing process
            console.log('🏷️ Pallet created successfully. Updated pallets state and opening label dialog. Will refresh after printing.');
          } else {
            // If no label dialog, refresh immediately
            console.log('📦 Pallet created without label dialog. Refreshing immediately.');
            await fetchPalletsAndCheckStatus();
          }
        } else {
          toast.error(response.message || 'Failed to create pallet');
        }
      }
      
      setDialogOpen(false);
      resetForm();
    } catch (error) {
      console.error('Error submitting pallet:', error);
      toast.error('An error occurred while processing the pallet');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmitWithConfirmation = () => {
    if (!confirmDialogOpen) {
      setConfirmDialogOpen(true);
    } else {
      handleSubmit();
      // Note: handleSubmit() now handles closing the confirmation dialog internally
      // so we don't need to set it here to avoid race conditions
    }
  };

  const handleDeletePallet = async (palletNo: string) => {
    if (!currentUser?.token) return;

    setPalletToDelete(palletNo);
    setDeleteDialogOpen(true);
  };

  const confirmDeletePallet = async () => {
    if (!currentUser?.token || !palletToDelete) return;

    try {
      setLoading(true);
      console.log(`Attempting to delete pallet: ${palletToDelete}`);
      
      const response = await palletService.deletePallet(palletToDelete, currentUser.token);
      if (response.success) {
        console.log(`Successfully deleted pallet: ${palletToDelete}`);
        toast.success('Pallet deleted successfully');
        await fetchPalletsAndCheckStatus();
      } else {
        console.error(`Failed to delete pallet ${palletToDelete}:`, response.message);
        toast.error(response.message || 'Failed to delete pallet');
      }
    } catch (error: any) {
      console.error(`Error deleting pallet ${palletToDelete}:`, error);
      const errorMessage = error.response?.data?.message || error.message || 'Error deleting pallet';
      toast.error(errorMessage);
    } finally {
      setLoading(false);
      setDeleteDialogOpen(false);
      setPalletToDelete(null);
    }
  };

  const cancelDeletePallet = () => {
    setDeleteDialogOpen(false);
    setPalletToDelete(null);
  };

  const handleOpenLabelDialog = () => {
    if (pallets.length === 0) {
      toast.warning('No pallets found to generate labels');
      return;
    }
    // Generate labels for all pallets
    setLabelDialogPallets(pallets);
    setLabelDialogOpen(true);
  };

  const handleCloseLabelDialog = async () => {
    setLabelDialogOpen(false);
    setLabelDialogPallets([]);

    // Also close the confirmation dialog if it's still open
    if (confirmDialogOpen) {
      setConfirmDialogOpen(false);
    }

    // If we need to update data after label dialog closes (after pallet creation), do it now
    if (needsRefreshAfterLabel) {
      console.log('🔄 Label dialog closed after pallet creation. Updating data without page refresh...');
      setNeedsRefreshAfterLabel(false);

      // Instead of full refresh, just update the necessary data
      await updateDataAfterPalletCreation();
      console.log('✅ Data update completed. Pallet creation workflow finished.');
    }
  };

  // Function to update only necessary data without full page refresh
  const updateDataAfterPalletCreation = async () => {
    if (!currentUser?.token) return;

    try {
      // Update pallet count callback if provided (for parent components)
      if (onPalletsChange) {
        onPalletsChange(pallets.length);
      }

      // Note: Selective update event is now emitted directly from setPallets callback
      // This ensures we have the most accurate and up-to-date pallet data

      // For parent components that prefer the traditional callback approach
      // We'll add a small delay to allow them to listen for the selective update event first
      setTimeout(() => {
        if (onPalletOperationComplete) {
          console.log('🔔 Notifying parent component of pallet operation completion (traditional callback)');
          onPalletOperationComplete();
        }
      }, 150); // Slightly longer delay to allow selective update to process first

      console.log('📊 Data updated successfully without page refresh');
    } catch (error) {
      console.error('Error updating data after pallet creation:', error);
      // Fallback to full refresh if selective update fails
      console.log('⚠️ Falling back to full refresh due to error');
      await fetchPalletsAndCheckStatus();
    }
  };

  const handleGenerateIndividualLabel = (pallet: Pallet) => {
    setLabelDialogPallets([pallet]);
    setLabelDialogOpen(true);
  };

  const totalPiecesInPallets = pallets.reduce((sum, pallet) => sum + pallet.noOfPieces, 0);
  const hasDiscrepancy = totalPiecesInPallets > manifestPieces;
  const isExactMatch = totalPiecesInPallets === manifestPieces && totalPiecesInPallets > 0;
  const isPartialMatch = totalPiecesInPallets > 0 && totalPiecesInPallets < manifestPieces;

  // Sort pallets once before rendering
  const sortedPallets = [...pallets].sort((a, b) => {
    // Extract numeric parts from pallet numbers
    const aMatches = a.palletNo.match(/[pP](\d+)$/);
    const bMatches = b.palletNo.match(/[pP](\d+)$/);
    
    // If both have numeric parts, compare them numerically
    if (aMatches && bMatches) {
      return parseInt(aMatches[1]) - parseInt(bMatches[1]);
    }
    
    // Extract any numbers from the pallet numbers as fallback
    const aNum = a.palletNo.match(/(\d+)/)?.[1];
    const bNum = b.palletNo.match(/(\d+)/)?.[1];
    
    // If both have numbers, compare them numerically
    if (aNum && bNum) {
      return parseInt(aNum) - parseInt(bNum);
    }
    
    // Fallback to string comparison
    return a.palletNo.localeCompare(b.palletNo);
  });

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">Pallets</Typography>
        {!readonly && (
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Button
              variant="outlined"
              startIcon={<LabelIcon />}
              onClick={handleOpenLabelDialog}
              disabled={loading || pallets.length === 0}
              size="small"
            >
              Generate All Labels
            </Button>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => handleOpenDialog()}
              disabled={loading}
            >
              Add Pallet
            </Button>
          </Box>
        )}
      </Box>

      <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', sm: 'repeat(2, 1fr)' }, gap: 2, mb: 3 }}>
        <Card>
          <CardContent sx={{ textAlign: 'center' }}>
            <Typography variant="h4" color="primary">
              {pallets.length}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Total Pallets
            </Typography>
          </CardContent>
        </Card>
        <Card sx={
          hasDiscrepancy 
            ? { border: '1px solid #f44336' } 
            : isExactMatch 
              ? { border: '1px solid #4caf50' } 
              : isPartialMatch 
                ? { border: '1px solid #ef6c00' }
                : {}
        }>
          <CardContent sx={{ textAlign: 'center' }}>
            <Typography variant="h4" color={
              hasDiscrepancy 
                ? 'error.main' 
                : isExactMatch 
                  ? 'success.main' 
                  : isPartialMatch 
                    ? 'warning.dark'
                    : 'text.primary'
            }>
              {totalPiecesInPallets} / {manifestPieces}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Inbounded Pieces / Pieces
            </Typography>
            {hasDiscrepancy && (
              <Chip 
                label="Discrepancy Detected" 
                color="error" 
                size="small" 
                sx={{ mt: 1 }}
              />
            )}
            {isExactMatch && (
              <Chip 
                label="Inbounded to Warehouse" 
                color="success" 
                size="small" 
                sx={{ mt: 1 }}
              />
            )}
            {isPartialMatch && (
              <Chip 
                label="Inbounding" 
                color="warning" 
                size="small" 
                sx={{ mt: 1 }}
              />
            )}
          </CardContent>
        </Card>
      </Box>

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Pallet No.</TableCell>
              <TableCell align="right">Pieces on pallet</TableCell>
              <TableCell align="right">Inbounded Pieces/Pieces</TableCell>
              {!readonly && <TableCell align="center">Actions</TableCell>}
            </TableRow>
          </TableHead>
          <TableBody>
            {sortedPallets.map((pallet, index) => {
              // Calculate cumulative pieces up to and including this pallet
              const cumulativePieces = sortedPallets
                .slice(0, index + 1)
                .reduce((sum, p) => sum + p.noOfPieces, 0);
                
              return (
                <TableRow key={pallet.palletNo}>
                  <TableCell>
                    <Chip label={pallet.palletNo} variant="outlined" size="small" />
                  </TableCell>
                  <TableCell align="right">{pallet.noOfPieces}</TableCell>
                  <TableCell align="right">{cumulativePieces}/{pallet.totalPiecesReference}</TableCell>
                  {!readonly && (
                    <TableCell align="center">
                      <Tooltip title="Edit">
                        <IconButton size="small" onClick={() => handleOpenDialog(pallet)}>
                          <EditIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Delete">
                        <IconButton 
                          size="small" 
                          onClick={() => handleDeletePallet(pallet.palletNo)}
                          sx={{ ml: 1, color: 'error.main' }}
                          disabled={loading}
                        >
                          <DeleteIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Generate Label">
                        <IconButton 
                          size="small" 
                          onClick={() => handleGenerateIndividualLabel(pallet)}
                          sx={{ ml: 1, color: 'primary.main' }}
                          disabled={loading}
                        >
                          <LabelIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  )}
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </TableContainer>

      <Dialog 
        open={dialogOpen} 
        onClose={handleCloseDialog} 
        maxWidth="sm" 
        fullWidth
        disableAutoFocus
        disableRestoreFocus
        disableEnforceFocus
      >
        <DialogTitle>
          {editingPallet ? 'Edit Pallet' : 'Add New Pallet'}
        </DialogTitle>
        <DialogContent onClick={handleDialogContentClick}>
          <TextField
            label="Number of Pieces"
            type="number"
            fullWidth
            margin="normal"
            value={formData.noOfPieces === 0 ? '' : formData.noOfPieces}
            onChange={(e) => {
              // Parse the input value and ensure it's not negative
              const inputValue = e.target.value;
              const parsedValue = inputValue === '' ? 0 : parseInt(inputValue);
              
              // Only update if the value is not negative
              if (parsedValue >= 0) {
                setFormData(prev => ({
                  ...prev,
                  noOfPieces: parsedValue
                }));
              }
            }}
            onKeyDown={handleKeyDown}
            inputProps={{
              min: 0, // Set minimum value to 0 to prevent negative numbers in the input
            }}
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    edge="end"
                    size="small"
                    onClick={(e) => {
                      e.stopPropagation(); // Prevent dialog click handler from firing
                      setFormData(prev => ({
                        ...prev,
                        noOfPieces: 0
                      }));
                      // Refocus the input after clearing
                      setTimeout(() => inputRef.current?.focus(), 0);
                    }}
                    title="Clear"
                  >
                    <ClearIcon fontSize="small" />
                  </IconButton>
                </InputAdornment>
              )
            }}
            helperText={
              formData.noOfPieces < 1 
                ? "Enter the number of pieces for this pallet (minimum 1 required). Press Enter to submit." 
                : "Press Enter to submit or click Create button."
            }
            inputRef={inputRef}
            autoFocus
            onFocus={(e) => e.currentTarget.select()} // Select all text when focused
          />
          <Box sx={{ mt: 2, mb: 1 }}>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              Inbounded Pieces/Manifest Pieces
            </Typography>
            <Typography variant="body1" sx={{ fontWeight: 500 }}>
              {(() => {
                // Calculate total inbounded pieces so far
                const totalInbounded = pallets.reduce((sum, p) => sum + p.noOfPieces, 0);
                
                // If editing, subtract the current pallet's pieces to get the total without this pallet
                const inboundedWithoutCurrent = editingPallet 
                  ? totalInbounded - editingPallet.noOfPieces 
                  : totalInbounded;
                
                // Add the current form value to get the projected total
                const projectedTotal = inboundedWithoutCurrent + formData.noOfPieces;
                
                return `${projectedTotal}/${formData.totalPiecesReference}`;
              })()}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              Shows projected inbounded pieces after saving this pallet
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button 
            onClick={handleSubmitWithConfirmation} 
            variant="contained" 
            startIcon={<SaveIcon />}
            disabled={formData.noOfPieces < 1}
          >
            {editingPallet ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Confirmation Dialog */}
      <Dialog 
        open={confirmDialogOpen} 
        onClose={() => setConfirmDialogOpen(false)}
        maxWidth="sm"
        fullWidth
        onKeyDown={handleConfirmKeyDown}
      >
        <DialogTitle>
          Confirm {editingPallet ? 'Update' : 'Create'} Pallet
        </DialogTitle>
        <DialogContent>
          <Typography variant="body1" gutterBottom>
            Are you sure you want to {editingPallet ? 'update' : 'create'} a pallet with the following details?
          </Typography>
          <Box sx={{ mt: 2, p: 2, bgcolor: 'background.paper', borderRadius: 1, border: '1px solid #e0e0e0' }}>
            <Typography variant="body1" fontWeight="bold">
              Pieces on pallet: {formData.noOfPieces}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Inbounded Pieces/Manifest Pieces: {(() => {
                // Calculate total inbounded pieces so far
                const totalInbounded = pallets.reduce((sum, p) => sum + p.noOfPieces, 0);
                
                // If editing, subtract the current pallet's pieces to get the total without this pallet
                const inboundedWithoutCurrent = editingPallet 
                  ? totalInbounded - editingPallet.noOfPieces 
                  : totalInbounded;
                
                // Add the current form value to get the projected total
                const projectedTotal = inboundedWithoutCurrent + formData.noOfPieces;
                
                return `${projectedTotal}/${formData.totalPiecesReference}`;
              })()}
            </Typography>
            {editingPallet && (
              <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                Pallet No: {editingPallet.palletNo}
              </Typography>
            )}
          </Box>
          <Typography variant="caption" sx={{ display: 'block', mt: 2, color: 'text.secondary' }}>
            Press Enter to confirm or Escape to cancel
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setConfirmDialogOpen(false)}>
            Cancel
          </Button>
          <Button 
            onClick={handleSubmit}
            variant="contained" 
            color="primary"
            ref={confirmInputRef}
            autoFocus
          >
            Confirm
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={cancelDeletePallet} maxWidth="sm" fullWidth>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete pallet <strong>{palletToDelete}</strong>?
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={cancelDeletePallet} disabled={loading}>Cancel</Button>
          <Button 
            onClick={confirmDeletePallet} 
            variant="contained" 
            color="error" 
            startIcon={loading ? <CircularProgress size={16} /> : <DeleteIcon />}
            disabled={loading}
          >
            {loading ? 'Deleting...' : 'Delete'}
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* Pallet Label Dialog */}
      <PalletLabelDialog
        open={labelDialogOpen}
        onClose={handleCloseLabelDialog}
        pallets={labelDialogPallets}
        manifest={manifest}
        allManifestPallets={pallets}
      />
    </Box>
  );
};

export default PalletManagement; 