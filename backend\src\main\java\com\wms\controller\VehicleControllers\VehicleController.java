package com.wms.controller.VehicleControllers;

import com.wms.dto.ApiResponse;
import com.wms.dto.VehicleDTO;
import com.wms.entity.vehicle.VehicleStatus;
import com.wms.service.VehicleServices.VehicleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/api/vehicles")
@CrossOrigin(origins = "*", maxAge = 3600)
public class VehicleController {

    @Autowired
    private VehicleService vehicleService;

    @PostMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public ResponseEntity<VehicleDTO> createVehicle(@Valid @RequestBody VehicleDTO vehicleDTO) {
        VehicleDTO createdVehicle = vehicleService.createVehicle(vehicleDTO);
        return new ResponseEntity<>(createdVehicle, HttpStatus.CREATED);
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public ResponseEntity<VehicleDTO> updateVehicle(
            @PathVariable Long id,
            @Valid @RequestBody VehicleDTO vehicleDTO) {
        VehicleDTO updatedVehicle = vehicleService.updateVehicle(id, vehicleDTO);
        return ResponseEntity.ok(updatedVehicle);
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public ResponseEntity<ApiResponse<Void>> deleteVehicle(@PathVariable Long id) {
        vehicleService.deleteVehicle(id);
        return ResponseEntity.ok(ApiResponse.success("Vehicle deleted successfully", null));
    }

    @GetMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER') or hasRole('DRIVER') or hasRole('CLIENT')")
    public ResponseEntity<VehicleDTO> getVehicleById(@PathVariable Long id) {
        VehicleDTO vehicle = vehicleService.getVehicleById(id);
        return ResponseEntity.ok(vehicle);
    }

    @GetMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER') or hasRole('DRIVER') or hasRole('CLIENT')")
    public ResponseEntity<List<VehicleDTO>> getAllVehicles() {
        List<VehicleDTO> vehicles = vehicleService.getAllVehicles();
        return ResponseEntity.ok(vehicles);
    }

    @GetMapping("/type/{typeId}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER') or hasRole('DRIVER') or hasRole('CLIENT')")
    public ResponseEntity<List<VehicleDTO>> getVehiclesByType(@PathVariable Long typeId) {
        List<VehicleDTO> vehicles = vehicleService.getVehiclesByType(typeId);
        return ResponseEntity.ok(vehicles);
    }

    @PutMapping("/{id}/status")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public ResponseEntity<VehicleDTO> updateVehicleStatus(
            @PathVariable Long id,
            @RequestParam VehicleStatus status) {
        VehicleDTO updatedVehicle = vehicleService.updateVehicleStatus(id, status);
        return ResponseEntity.ok(updatedVehicle);
    }

    @PostMapping("/{id}/drivers/{username}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public ResponseEntity<VehicleDTO> assignDriverToVehicle(
            @PathVariable Long id,
            @PathVariable String username) {
        VehicleDTO updatedVehicle = vehicleService.assignDriverToVehicle(id, username);
        return ResponseEntity.ok(updatedVehicle);
    }

    @DeleteMapping("/{id}/drivers/{username}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public ResponseEntity<VehicleDTO> removeDriverFromVehicle(
            @PathVariable Long id,
            @PathVariable String username) {
        VehicleDTO updatedVehicle = vehicleService.removeDriverFromVehicle(id, username);
        return ResponseEntity.ok(updatedVehicle);
    }
} 