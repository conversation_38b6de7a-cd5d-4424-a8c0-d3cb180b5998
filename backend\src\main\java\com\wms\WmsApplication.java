package com.wms;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.annotation.EnableScheduling;
import jakarta.annotation.PostConstruct;
import java.util.TimeZone;

@SpringBootApplication
@EnableScheduling
public class WmsApplication {
    private static final Logger logger = LoggerFactory.getLogger(WmsApplication.class);
    
    @Value("${spring.datasource.url}")
    private String datasourceUrl;
    
    @Value("${spring.datasource.username}")
    private String datasourceUsername;
    
    @Value("${spring.profiles.active:dev}")
    private String activeProfile;

    @PostConstruct
    public void init() {
        // Set the JVM timezone to Singapore
        TimeZone.setDefault(TimeZone.getTimeZone("Asia/Singapore"));
        logger.info("Application timezone set to: {}", TimeZone.getDefault().getID());
    }

    public static void main(String[] args) {
        SpringApplication.run(WmsApplication.class, args);
    }
    
    @Bean
    public CommandLineRunner logApplicationStartup() {
        return args -> {
            logger.info("=================================================");
            logger.info("WMS Application Starting with profile: {}", activeProfile);
            logger.info("Database connection: {}", datasourceUrl);
            logger.info("Database username: {}", datasourceUsername);
            logger.info("JVM Total Memory: {}MB", Runtime.getRuntime().totalMemory() / (1024 * 1024));
            logger.info("Available processors: {}", Runtime.getRuntime().availableProcessors());
            logger.info("Timezone: {}", TimeZone.getDefault().getID());
            logger.info("=================================================");
        };
    }
} 