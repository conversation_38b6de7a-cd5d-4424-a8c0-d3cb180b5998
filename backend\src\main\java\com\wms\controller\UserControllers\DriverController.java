package com.wms.controller.UserControllers;

import com.wms.dto.ApiResponse;
import com.wms.dto.VehicleDTO;
import com.wms.entity.user.Driver;
import com.wms.service.impl.DriverServiceImpl;
import com.wms.security.services.UserDetailsImpl;

import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@CrossOrigin(origins = "*")
public class DriverController {

    private final DriverServiceImpl driverService;

    @Autowired
    public DriverController(DriverServiceImpl driverService) {
        this.driverService = driverService;
    }

    @GetMapping("/api/users/drivers")
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER')")
    public ResponseEntity<List<Driver>> getDriversForVehicleAssignment() {
        List<Driver> drivers = driverService.getAllDrivers();
        return ResponseEntity.ok(drivers);
    }

    @GetMapping({"/api/drivers", "/drivers"})
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER')")
    public ResponseEntity<ApiResponse<List<Driver>>> getAllDrivers() {
        List<Driver> drivers = driverService.getAllDrivers();
        return ResponseEntity.ok(ApiResponse.success(drivers));
    }

    @GetMapping({"/api/drivers/{username}", "/drivers/{username}"})
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER') or #username == authentication.principal.username")
    public ResponseEntity<ApiResponse<Driver>> getDriverByUsername(@PathVariable String username) {
        return driverService.getDriverByUsername(username)
                .map(driver -> ResponseEntity.ok(ApiResponse.success(driver)))
                .orElse(ResponseEntity.notFound().build());
    }

    @GetMapping({"/api/drivers/{username}/vehicles", "/drivers/{username}/vehicles"})
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER') or #username == authentication.principal.username")
    public ResponseEntity<ApiResponse<List<VehicleDTO>>> getDriverVehicles(@PathVariable String username) {
        List<VehicleDTO> vehicles = driverService.getVehiclesByDriver(username);
        return ResponseEntity.ok(ApiResponse.success(vehicles));
    }

    @PostMapping({"/api/drivers", "/drivers"})
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Driver>> createDriver(@Valid @RequestBody Driver driver) {
        try {
            Driver createdDriver = driverService.createDriver(driver);
            return ResponseEntity.ok(ApiResponse.success("Driver created successfully", createdDriver));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    @PutMapping({"/api/drivers/{username}", "/drivers/{username}"})
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER') or #username == authentication.principal.username")
    public ResponseEntity<ApiResponse<Driver>> updateDriver(
            @PathVariable String username,
            @Valid @RequestBody Driver driver) {
        try {
            Driver updatedDriver = driverService.updateDriver(username, driver);
            return ResponseEntity.ok(ApiResponse.success("Driver updated successfully", updatedDriver));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    @PutMapping({"/api/drivers/{username}/deactivate", "/drivers/{username}/deactivate"})
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER')")
    public ResponseEntity<ApiResponse<Driver>> deactivateDriver(@PathVariable String username) {
        try {
            Driver deactivatedDriver = driverService.deactivateDriver(username);
            return ResponseEntity.ok(ApiResponse.success("Driver deactivated successfully", deactivatedDriver));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }
} 