import { Client } from './User';

export enum ContainerStatus {
  CREATED = 'CREATED',
  CONFIRMED = 'CONFIRMED',
  ARRIVED = 'ARRIVED',
  UNSTUFFING = 'UNSTUFFING',
  UNSTUFF_COMPLETED = 'UNSTUFF_COMPLETED',
  READY_TO_PULL_OUT = 'READY_TO_PULL_OUT',
  PULLED_OUT = 'PULLED_OUT',
  RED_SEAL = 'RED_SEAL'
}

export interface Container {
  containerNo: string;
  client: Client;
  truckNo: string;
  vesselVoyageNo: string;
  etaRequestedDate: string; // ISO date string
  manifestQuantity: number;
  portnetEta?: string; // ISO date string
  etaAllocated?: string; // ISO date string
  createdDate?: string; // ISO date string
  confirmedDate?: string; // ISO date string
  arrivalDate?: string; // ISO date string
  loadingBay?: string;
  unstuffDate?: string; // ISO date string
  unstuffCompletedDate?: string; // ISO date string
  pullOutDate?: string; // ISO date string
  unstuffTeam?: string;
  remark?: string;
  status: ContainerStatus;
  etaRejected?: boolean;
  etaRejectionReason?: string;
} 