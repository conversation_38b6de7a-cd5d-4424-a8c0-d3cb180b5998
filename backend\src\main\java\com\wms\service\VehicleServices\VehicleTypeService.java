package com.wms.service.VehicleServices;

import com.wms.dto.VehicleTypeDTO;
import com.wms.entity.vehicle.VehicleType;

import java.util.List;
import java.util.Optional;

public interface VehicleTypeService {
    VehicleTypeDTO createVehicleType(VehicleTypeDTO vehicleTypeDTO);
    VehicleTypeDTO updateVehicleType(Long id, VehicleTypeDTO vehicleTypeDTO);
    void deleteVehicleType(Long id);
    VehicleTypeDTO getVehicleTypeById(Long id);
    List<VehicleTypeDTO> getAllVehicleTypes();
    VehicleType getVehicleTypeEntityById(Long id);
    
    /**
     * Determines the appropriate vehicle type based on weight and CBM
     * @param weight The weight of the manifest in kg
     * @param cbm The cubic meters of the manifest
     * @return The best matching vehicle type, or empty if no match found
     */
    Optional<VehicleType> determineVehicleTypeByWeightAndCbm(Double weight, Double cbm);
    
    /**
     * Determines the appropriate vehicle type based on weight, CBM, and pieces
     * @param weight The weight of the manifest in kg
     * @param cbm The cubic meters of the manifest
     * @param pieces The number of pieces in the manifest
     * @return The best matching vehicle type, or empty if no match found
     */
    Optional<VehicleType> determineVehicleTypeByWeightCbmAndPieces(Double weight, Double cbm, Integer pieces);
    
    /**
     * Gets the abbreviation for a vehicle type by name
     * @param vehicleTypeName The name of the vehicle type
     * @return The abbreviation if found, otherwise a generated abbreviation based on the name
     */
    String getVehicleTypeAbbreviation(String vehicleTypeName);
} 