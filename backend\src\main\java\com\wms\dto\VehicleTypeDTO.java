package com.wms.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import jakarta.validation.constraints.DecimalMin;

public class VehicleTypeDTO {
    private Long id;
    
    @NotBlank(message = "Type name is required")
    @Size(max = 50)
    private String name;
    
    @Size(max = 10, message = "Abbreviation must not exceed 10 characters")
    private String abbreviation;
    
    @Size(max = 255)
    private String description;
    
    @DecimalMin(value = "0.0", message = "Minimum weight must be non-negative")
    private Double minWeight;
    
    @DecimalMin(value = "0.0", message = "Maximum weight must be non-negative")
    private Double maxWeight;
    
    @DecimalMin(value = "0.0", message = "Minimum CBM must be non-negative")
    private Double minCbm;
    
    @DecimalMin(value = "0.0", message = "Maximum CBM must be non-negative")
    private Double maxCbm;
    
    @DecimalMin(value = "0.0", message = "Minimum CBM per piece must be non-negative")
    private Double minCbmPerPiece;
    
    @DecimalMin(value = "0.0", message = "Maximum CBM per piece must be non-negative")
    private Double maxCbmPerPiece;
    
    private Boolean isActive = true;

    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public String getName() { return name; }
    public void setName(String name) { this.name = name; }

    public String getAbbreviation() { return abbreviation; }
    public void setAbbreviation(String abbreviation) { this.abbreviation = abbreviation; }

    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }

    public Double getMinWeight() { return minWeight; }
    public void setMinWeight(Double minWeight) { this.minWeight = minWeight; }

    public Double getMaxWeight() { return maxWeight; }
    public void setMaxWeight(Double maxWeight) { this.maxWeight = maxWeight; }

    public Double getMinCbm() { return minCbm; }
    public void setMinCbm(Double minCbm) { this.minCbm = minCbm; }

    public Double getMaxCbm() { return maxCbm; }
    public void setMaxCbm(Double maxCbm) { this.maxCbm = maxCbm; }
    
    public Double getMinCbmPerPiece() { return minCbmPerPiece; }
    public void setMinCbmPerPiece(Double minCbmPerPiece) { this.minCbmPerPiece = minCbmPerPiece; }
    
    public Double getMaxCbmPerPiece() { return maxCbmPerPiece; }
    public void setMaxCbmPerPiece(Double maxCbmPerPiece) { this.maxCbmPerPiece = maxCbmPerPiece; }

    public Boolean getIsActive() { return isActive; }
    public void setIsActive(Boolean isActive) { this.isActive = isActive; }
} 