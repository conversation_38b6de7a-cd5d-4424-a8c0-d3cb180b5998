{"scripts": {"build": "cd frontend && npm install --no-audit --legacy-peer-deps && npm run build"}, "devDependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/material": "^7.0.2", "@types/jest": "^29.5.14", "@types/react": "^19.1.2", "@types/sockjs-client": "^1.5.4", "@types/stompjs": "^2.3.9", "@types/testing-library__jest-dom": "^5.14.9", "@types/testing-library__react": "^10.0.1"}, "dependencies": {"@mui/icons-material": "^7.0.2", "@mui/x-data-grid": "^8.1.0", "@mui/x-date-pickers": "^8.2.0", "@stomp/stompjs": "^7.1.1", "@types/axios": "^0.9.36", "@types/file-saver": "^2.0.7", "@types/react-router-dom": "^5.3.3", "axios": "^1.8.4", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "luxon": "^3.6.1", "react-router-dom": "^7.5.1", "sockjs-client": "^1.6.1", "stompjs": "^2.3.3", "xlsx": "^0.18.5", "xlsx-js-style": "^1.2.0"}}