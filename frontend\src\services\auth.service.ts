import axios from 'axios';
import API_CONFIG from '../config/api.config';

const API_URL = `${API_CONFIG.baseUrl}${API_CONFIG.authEndpoint}`;

interface User {
  token: string;
  id: number;
  username: string;
  email: string;
  roles: string[];
}

// Store the current user in memory instead of localStorage
let currentUser: User | null = null;

const login = async (username: string, password: string): Promise<User> => {
  try {
    console.log('Environment:', process.env.NODE_ENV);
    console.log('API Config:', {
      baseUrl: API_CONFIG.baseUrl,
      authEndpoint: API_CONFIG.authEndpoint
    });
    console.log('Attempting login with:', { username });
    console.log('Full API URL:', API_URL + '/signin');
    
    const response = await axios.post<User>(API_URL + '/signin', {
      username,
      password,
    }, {
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      withCredentials: false
    });
    
    console.log('Login response:', response.data);
    
    if (response.data.token) {
      currentUser = response.data;
    }
    
    return response.data;
  } catch (error) {
    console.error('Login error details:', {
      error,
      message: error instanceof Error ? error.message : 'Unknown error',
      code: error && typeof error === 'object' && 'code' in error ? (error as any).code : undefined,
      response: error && typeof error === 'object' && 'response' in error ? (error as any).response : undefined
    });

    if (error && typeof error === 'object' && 'response' in error) {
      const axiosError = error as { response?: { data?: any; status?: number } };
      console.error('Response data:', axiosError.response?.data);
      console.error('Response status:', axiosError.response?.status);
      
      if (axiosError.response?.status === 401) {
        throw new Error('Invalid username or password');
      } else if (axiosError.response?.status === 404) {
        throw new Error('Login service not found');
      } else if (axiosError.response?.status === 500) {
        throw new Error('Server error occurred');
      }
    } else if (error && typeof error === 'object' && 'code' in error) {
      const axiosError = error as { code?: string };
      if (axiosError.code === 'ECONNABORTED') {
        throw new Error('Request timed out. Please check if the backend server is running.');
      } else if (axiosError.code === 'ERR_NETWORK') {
        throw new Error('Network error. Please check your internet connection and if the backend server is accessible.');
      }
    }
    throw error;
  }
};

const logout = (): void => {
  currentUser = null;
};

const getCurrentUser = (): User | null => {
  return currentUser;
};

const isAuthenticated = (): boolean => {
  return !!currentUser?.token;
};

const hasRole = (role: string): boolean => {
  return currentUser?.roles?.includes(role) || false;
};

const authService = {
  login,
  logout,
  getCurrentUser,
  isAuthenticated,
  hasRole,
};

export default authService; 