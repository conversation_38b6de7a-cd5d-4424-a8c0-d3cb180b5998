package com.wms.service;

import com.wms.entity.Pallet;
import java.util.List;
import java.util.Optional;

public interface PalletService {
    
    List<Pallet> getAllPallets();
    
    Optional<Pallet> getPalletByPalletNo(String palletNo);
    
    List<Pallet> getPalletsByManifestTrackingNo(String manifestTrackingNo);
    
    Pallet createPallet(Pallet pallet);
    
    Pallet updatePallet(String palletNo, Pallet pallet);
    
    void deletePallet(String palletNo);
    
    void deletePalletsByManifestTrackingNo(String manifestTrackingNo);
    
    List<Pallet> createPalletsForManifest(String manifestTrackingNo, List<Pallet> pallets);
    
    Long countPalletsByManifestTrackingNo(String manifestTrackingNo);
    
    int getTotalPiecesInPallets(String manifestTrackingNo);
    
    boolean isPalletPiecesMatchingManifest(String manifestTrackingNo);
} 