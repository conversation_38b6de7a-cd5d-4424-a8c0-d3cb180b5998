import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Typography,
  Alert,
  Paper,
  CircularProgress,
  Tabs,
  Tab,
  Snackbar,
  Stack,
  Chip,
  IconButton,
  Tooltip,
  useTheme,
  DialogContentText,
  InputAdornment,
  AlertTitle,
  FormControlLabel,
  Switch,
} from '@mui/material';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { DataGrid } from '@mui/x-data-grid';
import type { GridColDef, GridRenderCellParams } from '@mui/x-data-grid';
import SearchIcon from '@mui/icons-material/Search';
import EventAvailableIcon from '@mui/icons-material/EventAvailable';
import CloseIcon from '@mui/icons-material/Close';
import VisibilityIcon from '@mui/icons-material/Visibility';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import LocalShippingOutlinedIcon from '@mui/icons-material/LocalShippingOutlined';
import DirectionsBoatOutlinedIcon from '@mui/icons-material/DirectionsBoatOutlined';
import { useAuth } from '../contexts/AuthContext';
import usePageTitle from '../hooks/usePageTitle';
import containerService from '../services/container.service';
import { Container, ContainerStatus } from '../types/Container';
import { format } from 'date-fns';
import { styled } from '@mui/material/styles';
import { css } from '@emotion/react';
import { Global } from '@emotion/react';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import { formatDateString, formatDate } from '../utils/dateUtils';
import CustomDatePickerWithCounts from '../components/CustomDatePickerWithCounts';

// Interface for container details dialog
interface ContainerDetailsProps {
  open: boolean;
  onClose: () => void;
  container: Container | null;
}

// Interface for updating ETA after rejection
interface UpdateEtaDialogProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (newEtaRequestedDate: string) => void;
  containerNo: string;
  rejectionReason?: string;
}

// Update the Container interface to include size property
interface ExtendedContainer extends Container {
  size?: string;
}

// Add a global style to ensure popups appear in front
const globalStyles = css`
  .MuiPickersPopper-root, 
  .MuiDialog-root,
  .MuiPopover-root,
  .MuiModal-root {
    z-index: 9999 !important;
  }
  
  @keyframes pulse {
    0% {
      opacity: 1;
    }
    50% {
      opacity: 0.6;
    }
    100% {
      opacity: 1;
    }
  }
`;

// Create a styled DateTimePicker wrapper
const StyledDateTimePicker = styled(DateTimePicker)({
  '& .MuiOutlinedInput-root': {
    borderRadius: 1,
    '&:hover .MuiOutlinedInput-notchedOutline': {
      borderColor: '#1976d2',
    },
  },
});

const ClientContainerManagement: React.FC = () => {
  const { currentUser, hasRole } = useAuth();
  
  // Set page title
  usePageTitle('My Containers');
  
  const [containers, setContainers] = useState<Container[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [filterState, setFilterState] = useState({
    tab: 0,
    search: '',
  });
  const theme = useTheme();
  
  // State for container details dialog
  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);
  const [selectedContainer, setSelectedContainer] = useState<Container | null>(null);
  
  // State for ETA update dialog (when responding to rejection)
  const [etaUpdateDialogOpen, setEtaUpdateDialogOpen] = useState(false);
  const [containerForEtaUpdate, setContainerForEtaUpdate] = useState<string>('');
  const [rejectionReason, setRejectionReason] = useState<string>('');

  useEffect(() => {
    fetchMyContainers();
  }, []);

  const fetchMyContainers = async () => {
    if (!currentUser?.token) {
      setError('Authentication required');
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      // Use getMyContainers instead of getAllContainers - this will only return the client's containers
      const response = await containerService.getMyContainers(currentUser.token);
      
      // Check response format and handle accordingly
      const containerData = response.data || [];
      setContainers(containerData);
    } catch (err: any) {
      if (err.response) {
        setError(`Failed to fetch containers: ${err.response.data?.message || err.message}`);
      } else {
        setError('Failed to fetch containers: Unknown error');
      }
      console.error('Error fetching containers:', err);
    } finally {
      setLoading(false);
    }
  };

  const formatDateTime = (dateStr?: string) => {
    return formatDateString(dateStr);
  };

  // Handle view container details
  const handleViewDetails = (container: Container) => {
    setSelectedContainer(container);
    setDetailsDialogOpen(true);
  };
  
  // Handle ETA update (when client responds to rejection)
  const handleEtaUpdateRequest = (containerNo: string, reason?: string) => {
    setContainerForEtaUpdate(containerNo);
    setRejectionReason(reason || '');
    setEtaUpdateDialogOpen(true);
  };
  
  const handleEtaUpdateSubmit = async (newEtaRequestedDate: string) => {
    if (!currentUser?.token) {
      setError('Authentication required');
      return;
    }
    
    try {
      // Find the container to update
      const containerToUpdate = containers.find(c => c.containerNo === containerForEtaUpdate);
      
      if (!containerToUpdate) {
        setError('Container not found');
        return;
      }
      
      // Update container with new ETA requested date and reset rejection flags
      const updatedContainer: Container = {
        ...containerToUpdate,
        etaRequestedDate: newEtaRequestedDate,
        etaRejected: false,
        etaRejectionReason: ''
      };
      
      // Send update to backend
      await containerService.updateContainer(
        containerForEtaUpdate,
        updatedContainer,
        currentUser.token
      );
      
      setSuccessMessage('ETA Request date updated successfully. Waiting for confirmation.');
      fetchMyContainers();
      setEtaUpdateDialogOpen(false);
    } catch (err: any) {
      if (err.response) {
        setError(`Failed: ${err.response.data?.message || err.message}`);
      } else {
        setError('Failed: Unknown error');
      }
      console.error('Error updating ETA:', err);
    }
  };

  const handleSuccessClose = () => {
    setSuccessMessage(null);
  };

  // Function to determine status chip colors
  const getStatusChipColor = (status: ContainerStatus) => {
    switch (status) {
      case ContainerStatus.CREATED:
        return { bg: 'rgba(255, 167, 38, 0.1)', color: 'warning.main' };
      case ContainerStatus.CONFIRMED:
        return { bg: 'rgba(33, 150, 243, 0.1)', color: 'info.main' };
      case ContainerStatus.ARRIVED:
        return { bg: 'rgba(0, 200, 83, 0.1)', color: 'success.main' };
      case ContainerStatus.UNSTUFFING:
        return { bg: 'rgba(255, 193, 7, 0.1)', color: 'warning.main' };
      case ContainerStatus.UNSTUFF_COMPLETED:
        return { bg: 'rgba(0, 200, 83, 0.1)', color: 'success.main' };
      case ContainerStatus.READY_TO_PULL_OUT:
        return { bg: 'rgba(0, 200, 83, 0.1)', color: 'success.main' };
      case ContainerStatus.PULLED_OUT:
        return { bg: 'rgba(76, 175, 80, 0.1)', color: 'success.dark' };
      default:
        return { bg: 'rgba(158, 158, 158, 0.1)', color: 'text.secondary' };
    }
  };

  const columns: GridColDef[] = [
    {
      field: 'containerNo',
      headerName: 'Container No',
      flex: 1,
      minWidth: 150,
      renderCell: (params: GridRenderCellParams) => (
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <LocalShippingOutlinedIcon sx={{ mr: 1, color: theme.palette.primary.main }} />
          <Typography sx={{ fontWeight: 'bold' }}>
            {params.value}
          </Typography>
          {params.row.etaRejected && (
            <Chip 
              size="small"
              label="Action Required" 
              color="error"
              sx={{ 
                ml: 1,
                height: 20,
                fontSize: '0.7rem',
                fontWeight: 'bold',
                animation: 'pulse 1.5s infinite'
              }}
            />
          )}
        </Box>
      ),
    },
    {
      field: 'createdDate',
      headerName: 'Created On',
      flex: 1,
      minWidth: 180,
      renderCell: (params: GridRenderCellParams) => {
        try {
          if (params.row && params.row.createdDate) {
            return (
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <CalendarTodayIcon sx={{ mr: 1, color: theme.palette.grey[600] }} fontSize="small" />
                <span>{formatDateTime(params.row.createdDate)}</span>
              </Box>
            );
          }
          return <span>-</span>;
        } catch (err) {
          console.error('Error rendering createdDate:', err);
          return <span>-</span>;
        }
      },
    },
    {
      field: 'etaRequestedDate',
      headerName: 'ETA Requested',
      flex: 1,
      minWidth: 180,
      renderCell: (params: GridRenderCellParams) => {
        try {
          if (params.row && params.row.etaRequestedDate) {
            return (
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <EventAvailableIcon sx={{ mr: 1, color: theme.palette.grey[600] }} fontSize="small" />
                <span>{formatDateTime(params.row.etaRequestedDate)}</span>
              </Box>
            );
          }
          return <span>-</span>;
        } catch (err) {
          console.error('Error rendering etaRequestedDate:', err);
          return <span>-</span>;
        }
      },
    },
    {
      field: 'truckNo',
      headerName: 'Truck No',
      flex: 1,
      minWidth: 120,
    },
    {
      field: 'vesselVoyageNo',
      headerName: 'Vessel Voyage',
      flex: 1,
      minWidth: 150,
      renderCell: (params: GridRenderCellParams) => (
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <DirectionsBoatOutlinedIcon sx={{ mr: 1, color: theme.palette.grey[600] }} fontSize="small" />
          <span>{params.value}</span>
        </Box>
      ),
    },
    {
      field: 'manifestQuantity',
      headerName: 'Quantity',
      flex: 0.7,
      minWidth: 100,
    },
    {
      field: 'status',
      headerName: 'Status',
      flex: 1,
      minWidth: 150,
      renderCell: (params: GridRenderCellParams) => {
        const status = params.row.status;
        const { bg, color } = getStatusChipColor(status);
        
        return (
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Chip
              label={status.replace(/_/g, ' ')}
              sx={{
                backgroundColor: bg,
                color: color,
                fontWeight: 'medium',
                fontSize: '0.75rem',
              }}
            />
            
            {/* Show ETA rejected indicator with action button */}
            {params.row.etaRejected && (
              <Tooltip title={`ETA was rejected: ${params.row.etaRejectionReason}. Click to update.`}>
                <Box 
                  sx={{
                    ml: 1,
                    bgcolor: 'error.main',
                    color: 'white',
                    borderRadius: '50%',
                    width: 24,
                    height: 24,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontSize: '0.75rem',
                    fontWeight: 'bold',
                    cursor: 'pointer',
                    '&:hover': {
                      bgcolor: 'error.dark',
                    }
                  }}
                  onClick={() => handleEtaUpdateRequest(params.row.containerNo, params.row.etaRejectionReason)}
                >
                  !
                </Box>
              </Tooltip>
            )}
          </Box>
        );
      },
    },
    {
      field: 'etaAllocated',
      headerName: 'ETA Allocated',
      flex: 1,
      minWidth: 180,
      renderCell: (params: GridRenderCellParams) => {
        return params.row.etaAllocated ? formatDateTime(params.row.etaAllocated) : '-';
      },
    },
    {
      field: 'actions',
      headerName: 'Actions',
      flex: 0.7,
      minWidth: 100,
      sortable: false,
      renderCell: (params: GridRenderCellParams) => (
        <Stack direction="row" spacing={1}>
          <Tooltip title="View Details">
            <IconButton
              size="small"
              onClick={() => handleViewDetails(params.row)}
              sx={{ color: 'primary.main' }}
            >
              <VisibilityIcon fontSize="small" />
            </IconButton>
          </Tooltip>
          
          {/* Add update ETA button if the ETA was rejected */}
          {params.row.etaRejected && (
            <Tooltip title="Update ETA Request">
              <IconButton
                size="small"
                onClick={() => handleEtaUpdateRequest(params.row.containerNo, params.row.etaRejectionReason)}
                sx={{ color: 'warning.main' }}
              >
                <CalendarTodayIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          )}
        </Stack>
      ),
    },
  ];

  const getFilteredContainers = () => {
    let filtered = [...containers];
    
    // Apply tab filtering
    if (filterState.tab !== 0) { // Not "All" tab
      if (filterState.tab === 1) {
        // "Needs Attention" tab - show containers with rejected ETA
        filtered = filtered.filter(container => container.etaRejected);
      } else {
        // Status tabs
        const statusMapping = [
          null, // Index 0 is "All" (no filtering)
          null, // Index 1 is "Needs Attention" (handled above)
          ContainerStatus.CREATED,
          ContainerStatus.CONFIRMED,
          null, // Index 4 is "ETA Set" (handled separately if needed)
          ContainerStatus.ARRIVED,
          ContainerStatus.UNSTUFFING,
          ContainerStatus.UNSTUFF_COMPLETED,
          ContainerStatus.READY_TO_PULL_OUT,
          ContainerStatus.PULLED_OUT
        ];
        
        const targetStatus = statusMapping[filterState.tab];
        if (targetStatus) {
          filtered = filtered.filter(container => container.status === targetStatus);
        }
      }
    }
    
    // Apply search filtering if search term exists
    if (filterState.search.trim()) {
      const searchTerm = filterState.search.toLowerCase().trim();
      filtered = filtered.filter(
        container =>
          container.containerNo.toLowerCase().includes(searchTerm) ||
          (container.truckNo || '').toLowerCase().includes(searchTerm) ||
          (container.vesselVoyageNo || '').toLowerCase().includes(searchTerm)
      );
    }
    
    return filtered;
  };

  const handleTabChange = (_: React.SyntheticEvent, newValue: number) => {
    setFilterState(prev => ({ ...prev, tab: newValue }));
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setFilterState({
      ...filterState,
      search: event.target.value,
    });
  };

  // Container Details Dialog Component
  const ContainerDetailsDialog: React.FC<ContainerDetailsProps> = ({ open, onClose, container }) => {
    if (!container) return null;
    const theme = useTheme();
    const statusColors = getStatusChipColor(container.status);
    const containerWithSize = container as ExtendedContainer;
    
    return (
      <Dialog 
        open={open} 
        onClose={onClose}
        fullWidth
        maxWidth="md"
        PaperProps={{
          sx: {
            borderRadius: 2,
            boxShadow: 24,
          },
        }}
      >
        <DialogTitle sx={{ 
          bgcolor: theme.palette.primary.main, 
          color: 'white',
          px: 3,
          py: 2 
        }}>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Typography variant="h6">
              Container Details: {container.containerNo}
            </Typography>
            <IconButton onClick={onClose} size="small" sx={{ color: 'white' }}>
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent sx={{ p: 3 }}>
          <Box sx={{ mb: 3 }}>
            <Chip 
              label={container.status} 
              sx={{ 
                bgcolor: statusColors.bg,
                color: statusColors.color,
                fontWeight: 'bold',
                fontSize: '0.875rem',
                px: 1
              }} 
            />
          </Box>
          
          <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', md: '1fr 1fr' }, gap: 3 }}>
            <Paper 
              elevation={0} 
              sx={{ 
                p: 2, 
                borderRadius: 2, 
                bgcolor: 'rgba(25, 118, 210, 0.04)', 
                border: '1px solid rgba(25, 118, 210, 0.2)'
              }}
            >
              <Typography variant="subtitle1" sx={{ fontWeight: 'bold', mb: 2, color: theme.palette.primary.main }}>
                Container Information
              </Typography>
              
              <Stack spacing={2}>
                <Box>
                  <Typography variant="body2" color="text.secondary">Container No</Typography>
                  <Typography variant="body1" sx={{ fontWeight: 500 }}>
                    {container.containerNo}
                  </Typography>
                </Box>
                
                <Box>
                  <Typography variant="body2" color="text.secondary">Created On</Typography>
                  <Typography variant="body1">
                    {formatDateTime(container.createdDate)}
                  </Typography>
                </Box>
                
                <Box>
                  <Typography variant="body2" color="text.secondary">Size</Typography>
                  <Typography variant="body1">
                    {containerWithSize.size || '-'}
                  </Typography>
                </Box>
              </Stack>
            </Paper>
            
            <Paper 
              elevation={0} 
              sx={{ 
                p: 2, 
                borderRadius: 2, 
                bgcolor: 'rgba(76, 175, 80, 0.04)', 
                border: '1px solid rgba(76, 175, 80, 0.2)'
              }}
            >
              <Typography variant="subtitle1" sx={{ fontWeight: 'bold', mb: 2, color: theme.palette.success.main }}>
                Shipping Details
              </Typography>
              
              <Stack spacing={2}>
                <Box>
                  <Typography variant="body2" color="text.secondary">Vessel & Voyage No</Typography>
                  <Typography variant="body1" sx={{ fontWeight: 500 }}>
                    {container.vesselVoyageNo || '-'}
                  </Typography>
                </Box>
                
                <Box>
                  <Typography variant="body2" color="text.secondary">Truck No</Typography>
                  <Typography variant="body1">
                    {container.truckNo || '-'}
                  </Typography>
                </Box>
                
                <Box>
                  <Typography variant="body2" color="text.secondary">ETA Requested Date</Typography>
                  <Typography variant="body1">
                    {formatDateTime(container.etaRequestedDate)}
                  </Typography>
                </Box>
              </Stack>
            </Paper>
            
            {container.etaRejected && (
              <Box sx={{ gridColumn: { md: '1 / 3' } }}>
                <Alert 
                  severity="warning" 
                  sx={{ 
                    borderRadius: 2, 
                    boxShadow: '0 2px 6px rgba(0,0,0,0.08)',
                    mt: 1 
                  }}
                  action={
                    <Button 
                      color="warning" 
                      size="small" 
                      onClick={() => {
                        onClose();
                        handleEtaUpdateRequest(container.containerNo, container.etaRejectionReason);
                      }}
                    >
                      Update ETA
                    </Button>
                  }
                >
                  <AlertTitle>ETA Requested Date Rejected</AlertTitle>
                  <Typography variant="body2" gutterBottom>
                    {container.etaRejectionReason?.split('\n\nSuggested date:')[0] || 'No reason provided.'}
                  </Typography>
                  
                  {container.etaRejectionReason?.includes('Suggested date:') && (
                    <Box sx={{ mt: 1, pt: 1, borderTop: '1px dashed rgba(0,0,0,0.1)' }}>
                      <Typography variant="subtitle2" color="primary" sx={{ fontWeight: 'bold' }}>
                        Suggested Alternative Date:
                      </Typography>
                      <Typography variant="body2">
                        {(() => {
                          try {
                            const dateMatch = container.etaRejectionReason.match(/Suggested date:\s*(.*?)(\n|$)/);
                            if (dateMatch && dateMatch[1]) {
                              const suggestedDate = new Date(dateMatch[1].trim());
                              return isNaN(suggestedDate.getTime()) 
                                ? dateMatch[1].trim() 
                                : formatDate(suggestedDate);
                            }
                            return 'Date information not available';
                          } catch (e) {
                            return 'Date information not available';
                          }
                        })()}
                      </Typography>
                      <Button 
                        variant="text" 
                        color="primary" 
                        size="small" 
                        startIcon={<CalendarTodayIcon />}
                        onClick={() => {
                          onClose();
                          handleEtaUpdateRequest(container.containerNo, container.etaRejectionReason);
                        }}
                        sx={{ mt: 1 }}
                      >
                        Respond to Suggested Date
                      </Button>
                    </Box>
                  )}
                </Alert>
              </Box>
            )}
          </Box>
        </DialogContent>
        <DialogActions sx={{ px: 3, py: 2, bgcolor: 'grey.50' }}>
          <Button onClick={onClose} variant="outlined">Close</Button>
          {container.etaRejected && (
            <Button 
              variant="contained" 
              color="primary" 
              onClick={() => {
                onClose();
                handleEtaUpdateRequest(container.containerNo, container.etaRejectionReason);
              }}
            >
              Update ETA Request
            </Button>
          )}
        </DialogActions>
      </Dialog>
    );
  };

  // Update ETA Dialog Component
  const UpdateEtaDialog: React.FC<UpdateEtaDialogProps> = ({ 
    open, 
    onClose, 
    onSubmit, 
    containerNo,
    rejectionReason 
  }) => {
    const theme = useTheme();
    const [newEtaRequestedDate, setNewEtaRequestedDate] = useState<Date | null>(new Date());
    const [error, setError] = useState<string | null>(null);
    const [useSuggestedDate, setUseSuggestedDate] = useState<boolean>(true);
    const [suggestedDate, setSuggestedDate] = useState<Date | null>(null);
    
    // Extract the suggested date from the rejection reason if available
    useEffect(() => {
      if (open && rejectionReason) {
        // Look for the suggested date in the rejection reason
        const dateMatch = rejectionReason.match(/Suggested date:\s*(.*?)(\n|$)/);
        
        if (dateMatch && dateMatch[1]) {
          try {
            const extractedDate = new Date(dateMatch[1].trim());
            if (!isNaN(extractedDate.getTime())) {
              setSuggestedDate(extractedDate);
              setNewEtaRequestedDate(extractedDate);
            }
          } catch (e) {
            console.error('Failed to parse suggested date:', e);
          }
        }
      }
    }, [open, rejectionReason]);
    
    const handleSubmit = () => {
      if (!newEtaRequestedDate) {
        setError('Please select a new ETA Requested Date');
        return;
      }
      
      onSubmit(formatDate(newEtaRequestedDate));
      setNewEtaRequestedDate(new Date());
    };
    
    const handleToggleUseSuggested = () => {
      setUseSuggestedDate(!useSuggestedDate);
      if (!useSuggestedDate && suggestedDate) {
        // If switching back to using suggested date
        setNewEtaRequestedDate(suggestedDate);
      }
    };
    
    const suggestedDateStr = suggestedDate
      ? formatDate(suggestedDate)
      : null;
    
    return (
      <Dialog 
        open={open} 
        onClose={onClose}
        fullWidth
        maxWidth="sm"
        PaperProps={{
          sx: {
            borderRadius: 2,
            boxShadow: 24,
          },
        }}
      >
        <DialogTitle sx={{ 
          bgcolor: theme.palette.primary.main, 
          color: 'white',
          px: 3,
          py: 2 
        }}>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Typography variant="h6">
              Update ETA Request
            </Typography>
            <IconButton onClick={onClose} size="small" sx={{ color: 'white' }}>
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent sx={{ px: 3, py: 3 }}>
          {error && (
            <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
              {error}
            </Alert>
          )}
          
          {suggestedDate && (
            <Alert 
              severity="info" 
              sx={{ mb: 2, borderRadius: 2 }}
              action={
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={useSuggestedDate}
                        onChange={handleToggleUseSuggested}
                        size="small"
                        color="primary"
                      />
                    }
                    label="Use suggested date"
                    sx={{ mb: 0 }}
                  />
                </Box>
              }
            >
              <AlertTitle>Suggested Date Available</AlertTitle>
              The admin suggested: <strong>{suggestedDateStr}</strong>
            </Alert>
          )}
          
          <DialogContentText sx={{ mb: 3 }}>
            {suggestedDate 
              ? useSuggestedDate 
                ? "You're accepting the suggested ETA date. Click 'Submit' to confirm."
                : "Choose a different ETA Requested Date for your container:"
              : "Please select a new ETA Requested Date for container:"
            }
            <Typography variant="subtitle2" sx={{ mt: 1, fontWeight: 'bold' }}>
              Container: {containerNo}
            </Typography>
          </DialogContentText>
          
          <CustomDatePickerWithCounts
            label="ETA Requested Date"
            value={newEtaRequestedDate}
            onChange={(newValue) => {
              setNewEtaRequestedDate(newValue);
              // If user is changing the date, automatically switch to custom date mode
              if (suggestedDate && useSuggestedDate) {
                setUseSuggestedDate(false);
              }
            }}
            containers={containers}
            format="dd MMM yyyy HH:mm"
            ampm={false}
            slotProps={{
              textField: {
                fullWidth: true,
                disabled: suggestedDate !== null && useSuggestedDate,
                sx: { 
                  width: '100%',
                  opacity: (suggestedDate && useSuggestedDate) ? 0.7 : 1
                }
              }
            }}
          />
        </DialogContent>
        <DialogActions sx={{ px: 3, py: 2, bgcolor: 'grey.50' }}>
          <Button onClick={onClose} variant="outlined">Cancel</Button>
          <Button 
            onClick={handleSubmit} 
            variant="contained" 
            color="primary"
            startIcon={useSuggestedDate ? <CheckCircleIcon /> : <CalendarTodayIcon />}
          >
            {useSuggestedDate ? 'Accept Suggested Date' : 'Submit New Date'}
          </Button>
        </DialogActions>
      </Dialog>
    );
  };

  return (
    <>
      <Global styles={globalStyles} />
      <Box className="no-cell-focus-outline" sx={{ p: 3, maxWidth: '1400px', mx: 'auto' }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Typography variant="h4" sx={{ fontWeight: 'bold', color: theme.palette.primary.main }}>
              My Containers
            </Typography>
            {containers.some(c => c.etaRejected) && (
              <Chip
                label={`${containers.filter(c => c.etaRejected).length} needs attention`}
                color="error"
                size="small"
                sx={{ ml: 2, fontWeight: 'bold', height: 'auto', py: 0.5 }}
                onClick={() => setFilterState({ ...filterState, tab: 1 })}
              />
            )}
          </Box>
        </Box>

        {/* Welcome message */}
        <Paper sx={{ p: 3, mb: 3, borderLeft: '4px solid #1976d2', bgcolor: 'rgba(25, 118, 210, 0.04)' }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Box>
              <Typography variant="h6" gutterBottom>
                My Containers Dashboard
              </Typography>
              <Typography variant="body1" color="textSecondary">
                View and manage your containers. Track status updates and respond to ETA rejections if needed.
              </Typography>
            </Box>
            {containers.some(c => c.etaRejected) && (
              <Button
                variant="contained"
                color="error"
                size="small"
                onClick={() => setFilterState({ ...filterState, tab: 1 })}
                startIcon={<CalendarTodayIcon />}
                sx={{ borderRadius: 2, animation: 'pulse 1.5s infinite', fontWeight: 'bold' }}
              >
                View Rejected ETAs
              </Button>
            )}
          </Box>
        </Paper>

        {error && (
          <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
            {error}
          </Alert>
        )}

        {containers.some(c => c.etaRejected) && (
          <Alert 
            severity="warning" 
            sx={{ 
              mb: 3, 
              borderRadius: 2,
              border: '1px solid',
              borderColor: 'warning.light',
              boxShadow: 1
            }}
            action={
              <Button 
                color="warning" 
                size="small" 
                variant="contained"
                disableElevation
                onClick={() => setFilterState({ ...filterState, tab: 1 })}
                sx={{ borderRadius: 1.5 }}
              >
                View Now
              </Button>
            }
          >
            <AlertTitle>Action Required</AlertTitle>
            You have {containers.filter(c => c.etaRejected).length} container{containers.filter(c => c.etaRejected).length !== 1 ? 's' : ''} with rejected ETA requests that need your attention.
          </Alert>
        )}

        <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' }, gap: 2, mb: 3 }}>
          <Paper 
            sx={{ 
              flexGrow: 1, 
              borderRadius: 2, 
              boxShadow: 'none', 
              border: '1px solid rgba(224, 224, 224, 0.5)',
              overflow: 'hidden',
            }}
          >
            <Tabs
              value={filterState.tab}
              onChange={handleTabChange}
              aria-label="container status tabs"
              variant="scrollable"
              scrollButtons="auto"
              sx={{
                '& .MuiTab-root': {
                  fontWeight: 500,
                  py: 1.5,
                  px: 3,
                  minWidth: 'auto',
                  textTransform: 'none',
                  fontSize: '0.9rem',
                  color: 'text.secondary',
                  '&:focus, &:focus-visible': {
                    outline: 'none'
                  }
                },
                '& .Mui-selected': {
                  color: theme.palette.primary.main,
                  fontWeight: 'bold'
                },
                '& .MuiTabs-indicator': {
                  height: 3,
                  borderRadius: '3px 3px 0 0',
                },
              }}
            >
              <Tab label="All Containers" />
              <Tab 
                label={
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <span>Needs Attention</span>
                    {containers.some(c => c.etaRejected) && (
                      <Box 
                        component="span" 
                        sx={{ 
                          ml: 1, 
                          bgcolor: 'error.main', 
                          color: 'white', 
                          borderRadius: '50%', 
                          width: 20, 
                          height: 20, 
                          fontSize: '0.75rem',
                          display: 'inline-flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          animation: 'pulse 1.5s infinite'
                        }}
                      >
                        {containers.filter(c => c.etaRejected).length}
                      </Box>
                    )}
                  </Box>
                }
              />
              <Tab label="Created" />
              <Tab label="Confirmed" />
              <Tab label="ETA Set" />
              <Tab label="Arrived" />
              <Tab label="Unstuffing" />
              <Tab label="Unstuffed" />
              <Tab label="Ready to Pull Out" />
              <Tab label="Pulled Out" />
            </Tabs>
          </Paper>

          <TextField
            placeholder="Search by container no, truck no, or vessel voyage..."
            variant="outlined"
            size="small"
            value={filterState.search}
            onChange={handleSearchChange}
            sx={{ 
              width: { xs: '100%', md: '300px' },
              bgcolor: 'white',
              borderRadius: 1,
              '& .MuiOutlinedInput-root': {
                borderRadius: 1,
                '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                  borderColor: theme.palette.primary.main,
                  borderWidth: '1px'
                },
                '&:hover .MuiOutlinedInput-notchedOutline': {
                  borderColor: 'rgba(0, 0, 0, 0.42)'
                }
              },
              '& .MuiInputBase-root': {
                '&:focus, &:focus-visible, &:focus-within': {
                  outline: 'none',
                  boxShadow: 'none'
                }
              }
            }}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon color="action" />
                </InputAdornment>
              ),
              endAdornment: filterState.search && (
                <InputAdornment position="end">
                  <IconButton size="small" onClick={() => setFilterState({...filterState, search: ''})}>
                    <CloseIcon fontSize="small" />
                  </IconButton>
                </InputAdornment>
              ),
            }}
          />
        </Box>

        <Paper sx={{ width: '100%', borderRadius: 2, boxShadow: 2 }}>
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%', minHeight: '200px' }}>
              <CircularProgress />
            </Box>
          ) : error ? (
            <Alert severity="error">{error}</Alert>
          ) : getFilteredContainers().length === 0 ? (
            <Alert severity="info">No containers found. Try changing the filter or contact support.</Alert>
          ) : (
            <div style={{ width: '100%' }}>
              <DataGrid
                rows={getFilteredContainers()}
                columns={columns}
                getRowId={(row) => row.containerNo || Math.random().toString(36).substr(2, 9)}
                initialState={{
                  pagination: {
                    paginationModel: { pageSize: 10 }
                  },
                  sorting: {
                    sortModel: [{ field: 'createdDate', sort: 'desc' }],
                  },
                }}
                getRowClassName={(params) => 
                  params.row.etaRejected ? 'rejected-eta-row' : ''
                }
                pageSizeOptions={[10, 25, 50]}
                disableColumnMenu={false}
                disableRowSelectionOnClick
                density="standard"
                autoHeight={true}
                getEstimatedRowHeight={() => 60}
                getRowHeight={() => 'auto'}
                className="no-borders-datagrid"
                sx={{
                  width: '100%',
                  '&, & *': {
                    outline: 'none !important',
                  },
                  '& *:focus, & *:focus-visible, & *:focus-within': {
                    outline: 'none !important',
                    border: 'none !important',
                  },
                  '& .MuiDataGrid-virtualScroller': {
                    outline: 'none !important',
                  },
                  '& .MuiDataGrid-root .MuiDataGrid-cell:focus-within': {
                    outline: 'none !important',
                  },
                  '& .MuiDataGrid-row.Mui-selected': {
                    backgroundColor: 'rgba(25, 118, 210, 0.08)',
                    border: 'none !important',
                    outline: 'none !important',
                  },
                  '& .MuiDataGrid-cell.Mui-selected': {
                    outline: 'none !important',
                    border: 'none !important',
                    borderColor: 'transparent !important',
                  },
                  '& .MuiDataGrid-cell:focus, & .MuiDataGrid-row:focus': {
                    outline: 'none !important',
                  },
                  '& .MuiDataGrid-cell': {
                    borderBottom: '1px solid #f0f0f0',
                    padding: '8px 16px',
                    display: 'flex',
                    alignItems: 'center',
                    overflow: 'visible',
                  },
                  '& .rejected-eta-row': {
                    backgroundColor: 'rgba(244, 67, 54, 0.05)',
                    '&:hover': {
                      backgroundColor: 'rgba(244, 67, 54, 0.08)',
                    },
                    border: '1px solid rgba(244, 67, 54, 0.2) !important',
                  }
                }}
              />
            </div>
          )}
        </Paper>
      </Box>

      {/* Success message snackbar */}
      <Snackbar
        open={!!successMessage}
        autoHideDuration={6000}
        onClose={handleSuccessClose}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
        sx={{
          '& .MuiSnackbarContent-root': {
            bgcolor: 'success.main',
            color: '#fff',
            borderRadius: 2,
            boxShadow: 3,
          }
        }}
      >
        <Alert 
          onClose={handleSuccessClose} 
          severity="success" 
          variant="filled"
          sx={{ width: '100%' }}
        >
          {successMessage}
        </Alert>
      </Snackbar>

      {/* Container details dialog */}
      <ContainerDetailsDialog 
        open={detailsDialogOpen}
        onClose={() => setDetailsDialogOpen(false)}
        container={selectedContainer}
      />
      
      {/* ETA update dialog */}
      <UpdateEtaDialog
        open={etaUpdateDialogOpen}
        onClose={() => setEtaUpdateDialogOpen(false)}
        onSubmit={handleEtaUpdateSubmit}
        containerNo={containerForEtaUpdate}
        rejectionReason={rejectionReason}
      />
    </>
  );
};

export default ClientContainerManagement; 