import React, { createContext, useContext, useState, useEffect } from 'react';

interface LayoutContextType {
  isCollapsed: boolean;
  setIsCollapsed: (collapsed: boolean) => void;
  mobileOpen: boolean;
  setMobileOpen: (open: boolean) => void;
  drawerWidth: number;
  expandedDrawerWidth: number;
  collapsedDrawerWidth: number;
}

const defaultContext: LayoutContextType = {
  isCollapsed: false,
  setIsCollapsed: () => {},
  mobileOpen: false,
  setMobileOpen: () => {},
  drawerWidth: 200,
  expandedDrawerWidth: 200,
  collapsedDrawerWidth: 56
};

export const LayoutContext = createContext<LayoutContextType>(defaultContext);

export const useLayoutContext = () => useContext(LayoutContext);

interface LayoutProviderProps {
  children: React.ReactNode;
}

export const LayoutProvider: React.FC<LayoutProviderProps> = ({ children }) => {
  // Check localStorage for saved state, default to expanded
  const getSavedSidebarState = (): boolean => {
    try {
      const saved = localStorage.getItem('sidebarCollapsed');
      return saved === 'true';
    } catch (e) {
      return false;
    }
  };

  const [isCollapsed, setIsCollapsed] = useState<boolean>(getSavedSidebarState());
  const [mobileOpen, setMobileOpen] = useState<boolean>(false);
  
  const expandedDrawerWidth = 200; // Reduced from 250
  const collapsedDrawerWidth = 56; // Reduced from 68
  const drawerWidth = isCollapsed ? collapsedDrawerWidth : expandedDrawerWidth;

  // Save sidebar state to localStorage whenever it changes
  useEffect(() => {
    try {
      localStorage.setItem('sidebarCollapsed', isCollapsed.toString());
    } catch (e) {
      console.error('Failed to save sidebar state to localStorage', e);
    }
  }, [isCollapsed]);

  return (
    <LayoutContext.Provider
      value={{
        isCollapsed,
        setIsCollapsed,
        mobileOpen,
        setMobileOpen,
        drawerWidth,
        expandedDrawerWidth,
        collapsedDrawerWidth
      }}
    >
      {children}
    </LayoutContext.Provider>
  );
}; 