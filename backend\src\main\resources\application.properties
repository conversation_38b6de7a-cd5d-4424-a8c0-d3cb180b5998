# JWT Configuration
wms.app.jwtSecret=404E635266556A586E3272357538782F413F4428472B4B6250645367566B5970
wms.app.jwtExpirationMs=86400000

# Database Configuration
spring.datasource.url=****************************************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=fukuyamait
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# JPA and Hibernate Configuration
spring.jpa.hibernate.ddl-auto=none
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL8Dialect

# Data initialization
spring.sql.init.mode=always
spring.jpa.defer-datasource-initialization=true
spring.sql.init.platform=mysql
spring.sql.init.schema-locations=classpath:schema.sql
spring.sql.init.data-locations=classpath:data.sql
spring.sql.init.continue-on-error=true

# Server Configuration
server.port=${PORT:8080}
spring.jackson.time-zone=Asia/Singapore

# CORS Configuration
spring.web.cors.allowed-origins=https://wmsfuku.vercel.app,https://*.vercel.app,http://localhost:5173
spring.web.cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
spring.web.cors.allowed-headers=*
spring.web.cors.allow-credentials=true
spring.web.cors.max-age=3600

# Security Configuration
spring.security.filter.order=10

# Logging Configuration
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE
logging.level.org.springframework.security=DEBUG
logging.level.org.springframework.transaction=TRACE
logging.level.org.hibernate.engine.transaction.internal.TransactionImpl=DEBUG
logging.level.org.springframework.orm.jpa=DEBUG
logging.level.org.springframework.jdbc.core=TRACE 