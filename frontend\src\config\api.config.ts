/**
 * API Configuration
 * This file contains the configuration for the API endpoints.
 * It uses the production backend URL for deployed environments.
 */

// Determine if we're in development or production
const isDevelopment = window.location.hostname === 'localhost';

// Set the backend URL based on the environment
const BACKEND_URL = isDevelopment 
  ? 'http://localhost:8080'
  : 'https://wms-203681166157.asia-southeast1.run.app';

// Log API configuration
console.log('API Config - Environment:', isDevelopment ? 'Development' : 'Production');
console.log('API Config - Using backend URL:', BACKEND_URL);

// Configure axios defaults
import axios from 'axios';

// Set default headers
axios.defaults.headers.common['Content-Type'] = 'application/json';
axios.defaults.headers.common['Accept'] = 'application/json';

// Define a custom event for token expiration
export const TOKEN_EXPIRED_EVENT = 'token_expired';

// Set up axios interceptor for authentication
axios.interceptors.request.use(
  (config) => {
    // Don't add Authorization header for login request
    if (config.url?.includes('/auth/signin') || config.url?.includes('/test-auth/')) {
      return config;
    }
    
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add axios interceptor for error handling
axios.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('API Error:', error.response?.status, error.response?.data);
    
    // Check if the error is due to an expired token (401 Unauthorized)
    if (error.response && error.response.status === 401) {
      console.log('Token expired or invalid. Dispatching token expired event.');
      // Dispatch a custom event to notify the app about token expiration
      window.dispatchEvent(new CustomEvent(TOKEN_EXPIRED_EVENT));
    }
    
    return Promise.reject(error);
  }
);

const API_CONFIG = {
  // Base URL for API requests
  baseUrl: BACKEND_URL,
  
  // API endpoints (with /api prefix since it's in the backend context path)
  authEndpoint: '/api/auth',
  testAuthEndpoint: '/api/test-auth',
  containersEndpoint: '/api/containers',
  manifestsEndpoint: '/api/manifests',
  usersEndpoint: '/api/users',
};

export default API_CONFIG; 