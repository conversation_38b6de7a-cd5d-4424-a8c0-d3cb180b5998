package com.wms.service.impl;

import com.wms.entity.user.Manager;
import com.wms.entity.user.UserStatus;
import com.wms.repository.UserRepo.ManagerRepository;
import com.wms.service.UserServices.ManagerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Service
@Transactional
public class ManagerServiceImpl implements ManagerService {

    private final ManagerRepository managerRepository;

    @Autowired
    public ManagerServiceImpl(ManagerRepository managerRepository) {
        this.managerRepository = managerRepository;
    }

    @Override
    public List<Manager> getAllManagers() {
        return managerRepository.findAll();
    }

    @Override
    public Manager getManagerByUsername(String username) {
        return managerRepository.findByUsername(username)
                .orElseThrow(() -> new IllegalArgumentException("Manager not found with username: " + username));
    }

    @Override
    public Manager createManager(Manager manager) {
        if (managerRepository.existsByEmail(manager.getEmail())) {
            throw new IllegalArgumentException("Email already exists");
        }
        if (managerRepository.existsByUsername(manager.getUsername())) {
            throw new IllegalArgumentException("Username already exists");
        }
        manager.setStatus(UserStatus.ACTIVE);
        return managerRepository.save(manager);
    }

    @Override
    public Manager updateManager(String username, Manager managerDetails) {
        Manager manager = managerRepository.findByUsername(username)
                .orElseThrow(() -> new IllegalArgumentException("Manager not found with username: " + username));

        if (manager.getStatus() == UserStatus.INACTIVE) {
            throw new IllegalArgumentException("Cannot update an inactive manager");
        }

        // Check if email is being changed and if new email already exists
        if (!manager.getEmail().equals(managerDetails.getEmail()) && 
            managerRepository.existsByEmail(managerDetails.getEmail())) {
            throw new IllegalArgumentException("Email already exists");
        }

        // Username cannot be changed as it's the primary key
        if (!username.equals(managerDetails.getUsername())) {
            throw new IllegalArgumentException("Username cannot be changed");
        }

        // Update manager-specific fields
        manager.setDepartment(managerDetails.getDepartment());
        
        // Update common user fields
        manager.setEmail(managerDetails.getEmail());
        manager.setContact(managerDetails.getContact());
        manager.setPassword(managerDetails.getPassword());

        return managerRepository.save(manager);
    }

    @Override
    public Manager deactivateManager(String username) {
        Manager manager = managerRepository.findByUsername(username)
                .orElseThrow(() -> new IllegalArgumentException("Manager not found with username: " + username));
        
        if (manager.getStatus() == UserStatus.INACTIVE) {
            throw new IllegalArgumentException("Manager is already inactive");
        }
        
        manager.setStatus(UserStatus.INACTIVE);
        return managerRepository.save(manager);
    }
} 