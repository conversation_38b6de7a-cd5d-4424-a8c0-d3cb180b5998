package com.wms.service;

import com.wms.entity.ManifestTrackingLog;
import com.wms.entity.Manifest;
import com.wms.entity.ManifestStatus;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

public interface ManifestTrackingLogService {
    
    /**
     * Create a tracking log entry
     */
    ManifestTrackingLog createTrackingLog(ManifestTrackingLog trackingLog);
    
    /**
     * Get all tracking logs for a specific manifest
     */
    List<ManifestTrackingLog> getTrackingLogsByManifest(String trackingNo);
    
    /**
     * Get tracking logs for a manifest within a date range
     */
    List<ManifestTrackingLog> getTrackingLogsByManifestAndDateRange(
            String trackingNo, LocalDateTime startDate, LocalDateTime endDate);
    
    /**
     * Get all tracking logs by action type
     */
    List<ManifestTrackingLog> getTrackingLogsByActionType(ManifestTrackingLog.ActionType actionType);
    
    /**
     * Get all tracking logs made by a specific user
     */
    List<ManifestTrackingLog> getTrackingLogsByUser(String username);
    
    /**
     * Get the latest tracking log for a manifest
     */
    Optional<ManifestTrackingLog> getLatestTrackingLog(String trackingNo);
    
    /**
     * Get status change history for a manifest
     */
    List<ManifestTrackingLog> getStatusChangeHistory(String trackingNo);
    
    /**
     * Log a status change for a manifest
     */
    ManifestTrackingLog logStatusChange(String trackingNo, ManifestStatus previousStatus, 
                                       ManifestStatus newStatus, String updatedBy, String remarks);
    
    /**
     * Log a field update for a manifest
     */
    ManifestTrackingLog logFieldUpdate(String trackingNo, String fieldName, String oldValue, 
                                     String newValue, String updatedBy, String remarks);
    
    /**
     * Log manifest creation
     */
    ManifestTrackingLog logManifestCreation(Manifest manifest, String createdBy, String remarks);
    
    /**
     * Log manifest deletion
     */
    ManifestTrackingLog logManifestDeletion(String trackingNo, String deletedBy, String remarks);
    
    /**
     * Log multiple field updates in batch for a manifest update
     */
    List<ManifestTrackingLog> logManifestUpdate(Manifest oldManifest, Manifest newManifest, 
                                              String updatedBy, String remarks);
    
    /**
     * Count total tracking logs for a manifest
     */
    Long countTrackingLogs(String trackingNo);
    
    /**
     * Get tracking logs within a date range (for reporting)
     */
    List<ManifestTrackingLog> getTrackingLogsInDateRange(LocalDateTime startDate, LocalDateTime endDate);
    
    /**
     * Log pallet addition to a manifest
     */
    ManifestTrackingLog logPalletAddition(String trackingNo, String palletId, String addedBy, String remarks);
    
    /**
     * Log pallet deletion from a manifest
     */
    ManifestTrackingLog logPalletDeletion(String trackingNo, String palletId, String deletedBy, String remarks);
    
    /**
     * Delete a specific tracking log (admin only)
     */
    void deleteTrackingLog(Long logId);
    
    /**
     * Delete all tracking logs for a specific manifest (admin only)
     */
    void deleteAllTrackingLogsForManifest(String trackingNo);
} 