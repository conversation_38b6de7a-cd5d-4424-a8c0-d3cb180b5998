# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# production
/build
/dist

# misc
.DS_Store
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

npm-debug.log*
yarn-debug.log*
yarn-error.log*

# IDE
.idea/
.vscode/
*.swp
*.swo 
.vercel

# Backend - Java/Maven
backend/target/
backend/target/**
*.class
*.jar
*.war
*.ear
*.log
*.iml
.project
.classpath
.settings/
