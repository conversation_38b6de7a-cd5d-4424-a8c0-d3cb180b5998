package com.wms.service.impl;

import com.wms.dto.VehicleDTO;
import com.wms.entity.user.Driver;
import com.wms.entity.user.UserStatus;
import com.wms.entity.vehicle.Vehicle;
import com.wms.repository.UserRepo.DriverRepository;
import com.wms.repository.VehicleRepo.VehicleRepository;
import com.wms.service.UserServices.DriverService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Transactional
public class DriverServiceImpl implements DriverService {

    private final DriverRepository driverRepository;
    private final VehicleRepository vehicleRepository;

    @Autowired
    public DriverServiceImpl(DriverRepository driverRepository, VehicleRepository vehicleRepository) {
        this.driverRepository = driverRepository;
        this.vehicleRepository = vehicleRepository;
    }

    @Override
    public List<Driver> getAllDrivers() {
        return driverRepository.findAll();
    }

    @Override
    public Optional<Driver> getDriverByUsername(String username) {
        return driverRepository.findByUsername(username);
    }

    @Override
    public Driver createDriver(Driver driver) {
        if (driverRepository.existsByUsername(driver.getUsername())) {
            throw new IllegalArgumentException("A driver with this username already exists");
        }
        if (driverRepository.findByEmail(driver.getEmail()).isPresent()) {
            throw new IllegalArgumentException("Email already exists");
        }
        driver.setStatus(UserStatus.ACTIVE);
        return driverRepository.save(driver);
    }

    @Override
    public Driver updateDriver(String username, Driver driver) {
        Driver existingDriver = driverRepository.findByUsername(username)
                .orElseThrow(() -> new IllegalArgumentException("Driver not found with username: " + username));

        if (existingDriver.getStatus() == UserStatus.INACTIVE) {
            throw new IllegalArgumentException("Cannot update an inactive driver");
        }

        // Check if email is being changed and if new email already exists
        if (!existingDriver.getEmail().equals(driver.getEmail()) && 
            driverRepository.findByEmail(driver.getEmail()).isPresent()) {
            throw new IllegalArgumentException("Email already exists");
        }

        // Username cannot be changed as it's the primary key
        if (!username.equals(driver.getUsername())) {
            throw new IllegalArgumentException("Username cannot be changed");
        }

        // Update driver-specific fields
        existingDriver.setLicenseNumber(driver.getLicenseNumber());
        
        // Update common user fields
        existingDriver.setEmail(driver.getEmail());
        existingDriver.setContact(driver.getContact());
        
        // Only update password if it's not null or empty
        if (driver.getPassword() != null && !driver.getPassword().isEmpty()) {
            existingDriver.setPassword(driver.getPassword());
        }

        return driverRepository.save(existingDriver);
    }

    @Override
    public Driver deactivateDriver(String username) {
        Driver driver = driverRepository.findByUsername(username)
                .orElseThrow(() -> new IllegalArgumentException("Driver not found with username: " + username));

        if (driver.getStatus() == UserStatus.INACTIVE) {
            throw new IllegalArgumentException("Driver is already inactive");
        }

        driver.setStatus(UserStatus.INACTIVE);
        return driverRepository.save(driver);
    }

    @Override
    public boolean existsByEmail(String email) {
        return driverRepository.findByEmail(email).isPresent();
    }

    @Override
    public boolean existsByUsername(String username) {
        return driverRepository.existsByUsername(username);
    }

    public List<VehicleDTO> getVehiclesByDriver(String username) {
        Driver driver = driverRepository.findByUsername(username)
                .orElseThrow(() -> new IllegalArgumentException("Driver not found with username: " + username));
        
        List<Vehicle> vehicles = vehicleRepository.findByAssignedDriversContaining(driver);
        
        return vehicles.stream()
                .map(this::mapVehicleToDTO)
                .collect(Collectors.toList());
    }

    private VehicleDTO mapVehicleToDTO(Vehicle vehicle) {
        VehicleDTO dto = new VehicleDTO();
        dto.setId(vehicle.getId());
        dto.setLicensePlate(vehicle.getLicensePlate());
        dto.setVehicleTypeId(vehicle.getVehicleType().getId());
        dto.setVehicleTypeName(vehicle.getVehicleType().getName());
        dto.setStatus(vehicle.getStatus());
        
        List<String> driverUsernames = vehicle.getAssignedDrivers().stream()
                .map(Driver::getUsername)
                .collect(Collectors.toList());
        dto.setAssignedDriverUsernames(driverUsernames);
        
        return dto;
    }
} 