import React, { useMemo } from 'react';
import { Box, useTheme, Tooltip } from '@mui/material';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { PickersDay } from '@mui/x-date-pickers/PickersDay';
import { Container } from '../types/Container';
import { formatInTimeZone, toZonedTime } from 'date-fns-tz';
import { SINGAPORE_TIMEZONE } from '../utils/dateUtils';

interface CustomDatePickerWithCountsProps {
  label: string;
  value: Date | null;
  onChange: (date: Date | null) => void;
  containers: Container[];
  format?: string;
  ampm?: boolean;
  slotProps?: any;
}

// Custom day component that shows container counts
const CustomDay = (props: any) => {
  const { day, outsideCurrentMonth, containerCounts, ...other } = props;
  const theme = useTheme();
  
  // Format date as YYYY-MM-DD for lookup (using Singapore timezone)
  const dateKey = formatInTimeZone(day, SINGAPORE_TIMEZONE, 'yyyy-MM-dd');
  const count = containerCounts[dateKey] || 0;
  
  // Format the date for display in tooltip (Singapore timezone)
  const displayDate = formatInTimeZone(day, SINGAPORE_TIMEZONE, 'EEEE, MMMM d, yyyy');
  
  const tooltipText = `${count} container${count !== 1 ? 's' : ''} with ETA allocated on ${displayDate} (SGT)`;
  
  return (
    <Box sx={{ position: 'relative', display: 'inline-block' }}>
      <PickersDay
        {...other}
        day={day}
        outsideCurrentMonth={outsideCurrentMonth}
        sx={{
          position: 'relative',
          '&:hover': {
            backgroundColor: theme.palette.action.hover,
          },
        }}
      />
      {count > 0 && (
        <Tooltip title={tooltipText} arrow placement="top">
          <Box
            sx={{
              position: 'absolute',
              top: 2,
              right: 2,
              minWidth: 16,
              height: 16,
              borderRadius: '50%',
              backgroundColor: theme.palette.primary.main,
              color: 'white',
              fontSize: '10px',
              fontWeight: 'bold',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              pointerEvents: 'none',
              zIndex: 1,
            }}
          >
            {count > 99 ? '99+' : count}
          </Box>
        </Tooltip>
      )}
    </Box>
  );
};

// Create a wrapper for the date with timezone
const DatePickerWrapper = (props: any) => {
  const { containerCounts, ...datePickerProps } = props;
  
  return (
    <DateTimePicker
      {...datePickerProps}
      slots={{
        day: CustomDay,
      }}
      slotProps={{
        ...datePickerProps.slotProps,
        day: {
          containerCounts,
        },
        textField: {
          ...datePickerProps.slotProps?.textField,
          helperText: 'Singapore Time (SGT)',
        },
      }}
    />
  );
};

const CustomDatePickerWithCounts: React.FC<CustomDatePickerWithCountsProps> = ({
  label,
  value,
  onChange,
  containers,
  format = "dd MMM yyyy HH:mm",
  ampm = false,
  slotProps = {},
  ...otherProps
}) => {
  // Calculate container counts by date (using Singapore timezone)
  const containerCounts = useMemo(() => {
    const counts: { [key: string]: number } = {};
    
    containers.forEach(container => {
      if (container.etaAllocated) {
        try {
          const etaDate = new Date(container.etaAllocated);
          if (!isNaN(etaDate.getTime())) {
            // Convert to Singapore timezone and format as YYYY-MM-DD
            const dateKey = formatInTimeZone(etaDate, SINGAPORE_TIMEZONE, 'yyyy-MM-dd');
            counts[dateKey] = (counts[dateKey] || 0) + 1;
          }
        } catch (error) {
          console.warn('Invalid etaAllocated date:', container.etaAllocated);
        }
      }
    });
    
    return counts;
  }, [containers]);

  // Convert incoming value to Singapore timezone for display
  const displayValue = value ? toZonedTime(value, SINGAPORE_TIMEZONE) : null;

  // Handle changes and convert back to proper Date object
  const handleChange = (newValue: Date | null) => {
    if (newValue) {
      // The date picker gives us a Date object in the user's local timezone
      // We need to treat it as if it's in Singapore timezone
      const singaporeDate = toZonedTime(newValue, SINGAPORE_TIMEZONE);
      onChange(singaporeDate);
    } else {
      onChange(null);
    }
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <DatePickerWrapper
        label={`${label} (SGT)`}
        value={displayValue}
        onChange={handleChange}
        format={format}
        ampm={ampm}
        containerCounts={containerCounts}
        slotProps={slotProps}
        {...otherProps}
      />
    </LocalizationProvider>
  );
};

export default CustomDatePickerWithCounts; 