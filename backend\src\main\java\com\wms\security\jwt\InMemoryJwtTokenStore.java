package com.wms.security.jwt;

import io.jsonwebtoken.Claims;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

/**
 * In-memory implementation of JwtTokenStore
 * Stores tokens and associated data in memory with scheduled cleanup
 */
@Component
public class InMemoryJwtTokenStore implements JwtTokenStore {
    private static final Logger logger = LoggerFactory.getLogger(InMemoryJwtTokenStore.class);
    
    // Store tokens with their claims
    private final Map<String, Claims> tokenStore = new ConcurrentHashMap<>();
    
    // Map tokens to usernames for quick lookup
    private final Map<String, String> tokenToUsername = new ConcurrentHashMap<>();
    
    @Override
    public void storeToken(String token, String username, Claims claims) {
        logger.debug("Storing token for user: {}", username);
        tokenStore.put(token, claims);
        tokenToUsername.put(token, username);
    }
    
    @Override
    public Optional<String> getUsernameFromToken(String token) {
        logger.debug("Getting username from token");
        return Optional.ofNullable(tokenToUsername.get(token));
    }
    
    @Override
    public Optional<Claims> getClaimsFromToken(String token) {
        logger.debug("Getting claims from token");
        return Optional.ofNullable(tokenStore.get(token));
    }
    
    @Override
    public boolean validateToken(String token) {
        if (!tokenStore.containsKey(token)) {
            logger.debug("Token not found in store");
            return false;
        }
        
        Claims claims = tokenStore.get(token);
        boolean isValid = !claims.getExpiration().before(new Date());
        
        if (!isValid) {
            logger.debug("Token expired, removing from store");
            invalidateToken(token);
        }
        
        return isValid;
    }
    
    @Override
    public void invalidateToken(String token) {
        logger.debug("Invalidating token");
        tokenStore.remove(token);
        tokenToUsername.remove(token);
    }
    
    /**
     * Scheduled task to clean up expired tokens
     * Runs every hour
     */
    @Scheduled(fixedRate = 3600000) // Run every hour
    public void cleanupExpiredTokens() {
        logger.info("Running scheduled cleanup of expired tokens");
        Date now = new Date();
        
        tokenStore.entrySet().removeIf(entry -> {
            if (entry.getValue().getExpiration().before(now)) {
                String token = entry.getKey();
                tokenToUsername.remove(token);
                return true;
            }
            return false;
        });
        
        logger.info("Token store cleanup complete. Current token count: {}", tokenStore.size());
    }
} 