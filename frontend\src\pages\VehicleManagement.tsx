import React, { useState, useEffect } from 'react';
import {
  Box,
  <PERSON>ton,
  Dialog,
  <PERSON>alogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  IconButton,
  InputLabel,
  MenuItem,
  Paper,
  Select,
  SelectChangeEvent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Typography,
  Snackbar,
  Alert,
  CircularProgress,
  Chip,
  FormHelperText,
  Breadcrumbs,
  Link,
  Divider
} from '@mui/material';
import { Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon, NavigateNext } from '@mui/icons-material';
import { Vehicle, VehicleType, VehicleStatus, Driver } from '../types/Vehicle';
import * as vehicleService from '../services/vehicle.service';
import * as userService from '../services/user.service';
import { useLocation, useNavigate } from 'react-router-dom';

const VehicleManagement: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const queryParams = new URLSearchParams(location.search);
  const typeIdParam = queryParams.get('typeId');
  
  // Add ref for the Select component
  const driversSelectRef = React.useRef<any>(null);
  
  const [vehicles, setVehicles] = useState<Vehicle[]>([]);
  const [filteredVehicles, setFilteredVehicles] = useState<Vehicle[]>([]);
  const [vehicleTypes, setVehicleTypes] = useState<VehicleType[]>([]);
  const [selectedVehicleType, setSelectedVehicleType] = useState<number | null>(
    typeIdParam ? parseInt(typeIdParam) : null
  );
  const [drivers, setDrivers] = useState<Driver[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [openDialog, setOpenDialog] = useState<boolean>(false);
  const [dialogMode, setDialogMode] = useState<'add' | 'edit'>('add');
  const [driversSelectOpen, setDriversSelectOpen] = useState<boolean>(false);
  const [currentVehicle, setCurrentVehicle] = useState<Vehicle>({
    id: null,
    licensePlate: '',
    vehicleTypeId: 0,
    vehicleTypeName: '',
    status: VehicleStatus.AVAILABLE,
    assignedDriverUsernames: []
  });
  const [snackbar, setSnackbar] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'error';
  }>({
    open: false,
    message: '',
    severity: 'success',
  });

  useEffect(() => {
    fetchVehicles();
    fetchVehicleTypes();
    fetchDrivers();
  }, []);

  useEffect(() => {
    if (vehicles.length > 0) {
      filterVehicles();
    }
  }, [vehicles, selectedVehicleType]);

  const fetchVehicles = async () => {
    setLoading(true);
    try {
      let data: Vehicle[];
      
      if (typeIdParam) {
        const typeId = parseInt(typeIdParam);
        data = await vehicleService.getVehiclesByType(typeId);
        setSelectedVehicleType(typeId);
      } else {
        data = await vehicleService.getVehicles();
      }
      
      setVehicles(data);
      setFilteredVehicles(data);
      setError(null);
    } catch (err) {
      console.error('Error fetching vehicles:', err);
      setError('Failed to fetch vehicles. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const fetchVehicleTypes = async () => {
    try {
      const data = await vehicleService.getVehicleTypes();
      setVehicleTypes(data);
    } catch (err) {
      console.error('Error fetching vehicle types:', err);
      setSnackbar({
        open: true,
        message: 'Failed to fetch vehicle types.',
        severity: 'error',
      });
    }
  };

  const fetchDrivers = async () => {
    try {
      const data = await vehicleService.getDrivers();
      console.log('Fetched drivers:', data);
      setDrivers(data);
    } catch (err) {
      console.error('Error fetching drivers:', err);
      setSnackbar({
        open: true,
        message: 'Failed to fetch drivers.',
        severity: 'error',
      });
    }
  };

  const filterVehicles = () => {
    if (selectedVehicleType === null) {
      setFilteredVehicles(vehicles);
    } else {
      const filtered = vehicles.filter(
        vehicle => vehicle.vehicleTypeId === selectedVehicleType
      );
      setFilteredVehicles(filtered);
    }
  };

  const handleOpenDialog = (mode: 'add' | 'edit', vehicle?: Vehicle) => {
    setDialogMode(mode);
    setDriversSelectOpen(false);
    if (mode === 'edit' && vehicle) {
      setCurrentVehicle({ ...vehicle });
    } else {
      setCurrentVehicle({
        id: null,
        licensePlate: '',
        vehicleTypeId: selectedVehicleType || (vehicleTypes.length > 0 ? vehicleTypes[0].id || 0 : 0),
        vehicleTypeName: '',
        status: VehicleStatus.AVAILABLE,
        assignedDriverUsernames: []
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setCurrentVehicle(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSelectChange = (e: SelectChangeEvent<any>) => {
    const { name, value } = e.target;
    setCurrentVehicle(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleDriverSelectionChange = (e: SelectChangeEvent<string[]>) => {
    const value = e.target.value as string[];
    console.log('Driver selection changed:', value);
    
    // If "none" is selected, clear all drivers and close dropdown
    if (value.includes('none')) {
      setCurrentVehicle(prev => ({
        ...prev,
        assignedDriverUsernames: []
      }));
      setDriversSelectOpen(false);
      return;
    }
    
    // Otherwise, set the selected drivers
    setCurrentVehicle(prev => ({
      ...prev,
      assignedDriverUsernames: value
    }));
    
    // Close dropdown after any selection for better UX
    setDriversSelectOpen(false);
  };

  const handleStatusChange = (e: SelectChangeEvent<VehicleStatus>) => {
    const value = e.target.value as VehicleStatus;
    setCurrentVehicle(prev => ({
      ...prev,
      status: value
    }));
  };

  const handleSubmit = async () => {
    try {
      if (dialogMode === 'add') {
        await vehicleService.createVehicle(currentVehicle);
        setSnackbar({
          open: true,
          message: 'Vehicle created successfully!',
          severity: 'success',
        });
      } else {
        if (currentVehicle.id !== null) {
          await vehicleService.updateVehicle(currentVehicle.id, currentVehicle);
          setSnackbar({
            open: true,
            message: 'Vehicle updated successfully!',
            severity: 'success',
          });
        }
      }
      handleCloseDialog();
      fetchVehicles();
    } catch (err) {
      console.error('Error saving vehicle:', err);
      setSnackbar({
        open: true,
        message: 'Failed to save vehicle. Please try again.',
        severity: 'error',
      });
    }
  };

  const handleDelete = async (id: number) => {
    if (window.confirm('Are you sure you want to delete this vehicle?')) {
      try {
        await vehicleService.deleteVehicle(id);
        setSnackbar({
          open: true,
          message: 'Vehicle deleted successfully!',
          severity: 'success',
        });
        fetchVehicles();
      } catch (err) {
        console.error('Error deleting vehicle:', err);
        setSnackbar({
          open: true,
          message: 'Failed to delete vehicle.',
          severity: 'error',
        });
      }
    }
  };

  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  const getStatusChipColor = (status: VehicleStatus) => {
    switch (status) {
      case VehicleStatus.AVAILABLE:
        return 'success';
      case VehicleStatus.IN_USE:
        return 'primary';
      case VehicleStatus.MAINTENANCE:
        return 'warning';
      case VehicleStatus.OUT_OF_SERVICE:
        return 'error';
      default:
        return 'default';
    }
  };

  const getVehicleTypeName = (typeId: number) => {
    const type = vehicleTypes.find(t => t.id === typeId);
    return type ? type.name : 'Unknown';
  };

  const getDriverNames = (usernames: string[]) => {
    return usernames.map(username => {
      const driver = drivers.find(d => d.username === username);
      return driver ? driver.fullName : username;
    }).join(', ');
  };

  const getCurrentVehicleTypeName = () => {
    if (selectedVehicleType === null) return null;
    const type = vehicleTypes.find(t => t.id === selectedVehicleType);
    return type ? type.name : 'Unknown Type';
  };

  return (
    <Box sx={{ height: '100%' }}>
      <Box sx={{ mb: 3 }}>
        <Breadcrumbs separator={<NavigateNext fontSize="small" />}>
          <Link 
            color="inherit" 
            href="/vehicle-types" 
            onClick={(e) => {
              e.preventDefault();
              navigate('/vehicle-types');
            }}
            sx={{ cursor: 'pointer', textDecoration: 'none' }}
          >
            Vehicle Types
          </Link>
          {selectedVehicleType !== null && (
            <Typography color="text.primary">{getCurrentVehicleTypeName()} Vehicles</Typography>
          )}
          {selectedVehicleType === null && (
            <Typography color="text.primary">All Vehicles</Typography>
          )}
        </Breadcrumbs>
      </Box>
      
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Typography variant="h4" sx={{ mb: 1 }}>
            Vehicle Management
            {selectedVehicleType !== null && (
              <Chip 
                label={getCurrentVehicleTypeName()} 
                color="primary" 
                size="small" 
                sx={{ ml: 2 }}
              />
            )}
          </Typography>
        </Box>
        <Button
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
          onClick={() => handleOpenDialog('add')}
        >
          Add Vehicle
        </Button>
      </Box>

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
          <CircularProgress />
        </Box>
      ) : error ? (
        <Alert severity="error" sx={{ mt: 2 }}>
          {error}
        </Alert>
      ) : (
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>ID</TableCell>
                <TableCell>License Plate</TableCell>
                <TableCell>Type</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Assigned Drivers</TableCell>
                <TableCell align="right">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {filteredVehicles.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} align="center">
                    No vehicles found. Create one to get started.
                  </TableCell>
                </TableRow>
              ) : (
                filteredVehicles.map((vehicle) => (
                  <TableRow key={vehicle.id}>
                    <TableCell>{vehicle.id}</TableCell>
                    <TableCell>{vehicle.licensePlate}</TableCell>
                    <TableCell>{vehicle.vehicleTypeName}</TableCell>
                    <TableCell>
                      <Chip 
                        label={vehicle.status} 
                        color={getStatusChipColor(vehicle.status) as any}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>{getDriverNames(vehicle.assignedDriverUsernames)}</TableCell>
                    <TableCell align="right">
                      <IconButton
                        color="primary"
                        onClick={() => handleOpenDialog('edit', vehicle)}
                      >
                        <EditIcon />
                      </IconButton>
                      <IconButton
                        color="error"
                        onClick={() => vehicle.id && handleDelete(vehicle.id)}
                      >
                        <DeleteIcon />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>
      )}

      {/* Add/Edit Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {dialogMode === 'add' ? 'Add New Vehicle' : 'Edit Vehicle'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, mt: 1 }}>
            <Box sx={{ flex: '1 1 40%', minWidth: '250px' }}>
              <TextField
                autoFocus
                name="licensePlate"
                label="License Plate"
                type="text"
                fullWidth
                variant="outlined"
                value={currentVehicle.licensePlate}
                onChange={handleInputChange}
                required
              />
            </Box>
            <Box sx={{ flex: '1 1 40%', minWidth: '250px' }}>
              <FormControl fullWidth>
                <InputLabel id="drivers-label">Assigned Drivers</InputLabel>
                <Select
                  ref={driversSelectRef}
                  labelId="drivers-label"
                  id="assignedDriverUsernames"
                  name="assignedDriverUsernames"
                  multiple
                  open={driversSelectOpen}
                  onOpen={() => setDriversSelectOpen(true)}
                  onClose={() => setDriversSelectOpen(false)}
                  value={currentVehicle.assignedDriverUsernames && currentVehicle.assignedDriverUsernames.length > 0 ? currentVehicle.assignedDriverUsernames : []}
                  label="Assigned Drivers"
                  onChange={handleDriverSelectionChange}
                  MenuProps={{
                    autoFocus: false,
                    sx: {
                      zIndex: 10001,
                      '& .MuiPaper-root': {
                        zIndex: 10001
                      }
                    }
                  }}
                  sx={{
                    borderRadius: 1,
                    '&:hover .MuiOutlinedInput-notchedOutline': {
                      borderColor: 'primary.main',
                    },
                  }}
                  renderValue={(selected) => (
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                      {!selected || (selected as string[]).length === 0 ? (
                        <Chip 
                          key="none" 
                          label="No drivers assigned" 
                          size="small"
                          color="default"
                        />
                      ) : (selected as string[]).map((username) => {
                        const driver = drivers.find(d => d.username === username);
                        return (
                          <Chip 
                            key={username} 
                            label={driver ? driver.fullName || username : username} 
                            size="small" 
                          />
                        );
                      })}
                    </Box>
                  )}
                >
                  <MenuItem 
                    value="none"
                    sx={{
                      '&:hover': {
                        backgroundColor: 'action.hover'
                      }
                    }}
                  >
                    <em>No drivers assigned</em>
                  </MenuItem>
                  <Divider />
                  {drivers && drivers.length > 0 ? (
                    drivers.map((driver) => (
                      <MenuItem 
                        key={driver.username} 
                        value={driver.username}
                        sx={{
                          '&:hover': {
                            backgroundColor: 'action.hover'
                          }
                        }}
                      >
                        {driver.fullName || driver.username} ({driver.username})
                      </MenuItem>
                    ))
                  ) : (
                    <MenuItem disabled>No drivers available</MenuItem>
                  )}
                </Select>
                {drivers.length === 0 && (
                  <FormHelperText>
                    No drivers available. Please create drivers first.
                  </FormHelperText>
                )}
              </FormControl>
            </Box>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button 
            onClick={handleSubmit} 
            variant="contained" 
            color="primary"
            disabled={!currentVehicle.licensePlate}
          >
            {dialogMode === 'add' ? 'Add' : 'Update'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default VehicleManagement; 