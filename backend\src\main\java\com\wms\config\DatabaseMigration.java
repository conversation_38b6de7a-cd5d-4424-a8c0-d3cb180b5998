package com.wms.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

@Component
public class DatabaseMigration implements CommandLineRunner {

    private static final Logger logger = LoggerFactory.getLogger(DatabaseMigration.class);

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Override
    public void run(String... args) throws Exception {
        try {
            logger.info("Running database migrations...");

            // Migration 1: Fix manifest_tracking_logs constraints
            migrateManifestTrackingLogs();

            // Migration 2: Add abbreviation column to vehicle_types
            migrateVehicleTypesAbbreviation();

        } catch (Exception e) {
            logger.error("Database migration failed: {}", e.getMessage());
            // Don't throw exception to prevent application startup failure
        }
    }

    private void migrateManifestTrackingLogs() {
        try {
            logger.info("Running migration to fix manifest_tracking_logs constraints...");

            // Check if the column exists and has NOT NULL constraint
            String checkSql = "SELECT IS_NULLABLE FROM INFORMATION_SCHEMA.COLUMNS " +
                             "WHERE TABLE_SCHEMA = 'wms_db' " +
                             "AND TABLE_NAME = 'manifest_tracking_logs' " +
                             "AND COLUMN_NAME = 'new_status'";

            String isNullable = jdbcTemplate.queryForObject(checkSql, String.class);

            if ("NO".equals(isNullable)) {
                logger.info("Found NOT NULL constraint on new_status column, removing it...");

                // Remove NOT NULL constraint from new_status column
                String migrationSql = "ALTER TABLE manifest_tracking_logs MODIFY COLUMN new_status VARCHAR(50) NULL";
                jdbcTemplate.execute(migrationSql);

                logger.info("Successfully removed NOT NULL constraint from new_status column");
            } else {
                logger.info("new_status column is already nullable, no migration needed");
            }

        } catch (Exception e) {
            logger.error("Migration for manifest_tracking_logs failed: {}", e.getMessage());
        }
    }

    private void migrateVehicleTypesAbbreviation() {
        try {
            logger.info("Running migration to add abbreviation column to vehicle_types...");

            // Check if abbreviation column exists
            String checkSql = "SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS " +
                             "WHERE TABLE_SCHEMA = 'wms_db' " +
                             "AND TABLE_NAME = 'vehicle_types' " +
                             "AND COLUMN_NAME = 'abbreviation'";

            Integer columnExists = jdbcTemplate.queryForObject(checkSql, Integer.class);

            if (columnExists == 0) {
                logger.info("abbreviation column not found, adding it...");

                // Add abbreviation column to vehicle_types table
                String migrationSql = "ALTER TABLE vehicle_types ADD COLUMN abbreviation VARCHAR(10) AFTER name";
                jdbcTemplate.execute(migrationSql);

                logger.info("Successfully added abbreviation column to vehicle_types table");
            } else {
                logger.info("abbreviation column already exists, no migration needed");
            }

        } catch (Exception e) {
            logger.error("Migration for vehicle_types abbreviation failed: {}", e.getMessage());
        }
    }
} 