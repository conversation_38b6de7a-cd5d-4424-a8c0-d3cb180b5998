services:
  - type: web
    name: wms-backend
    env: docker
    repo: https://github.com/ZLHWNN/WMS.git
    dockerfilePath: ./backend/Dockerfile
    dockerContext: ./backend
    plan: free
    envVars:
      - key: DATABASE_URL
        sync: false
      - key: DATABASE_USERNAME
        sync: false
      - key: DATABASE_PASSWORD
        sync: false
      - key: CORS_ALLOWED_ORIGINS
        value: https://wmsfuku-hlpf0oqq9-zlhwnns-projects.vercel.app
      - key: JWT_SECRET
        generateValue: true
      - key: SPRING_PROFILES_ACTIVE
        value: prod
      - key: PORT
        value: 8080 