import axios from 'axios';
import { <PERSON><PERSON><PERSON>, ManifestStatus, ManifestTrackingHistory } from '../types/manifest';
import { ApiResponse } from '../types/api';
import API_CONFIG from '../config/api.config';
import { formatDateForApi, formatDateOnly, formatDateStringToIso, parseDateString } from '../utils/dateUtils';
import { getTimeSlotIdFromDate } from '../utils/dateUtils';

const API_BASE_URL = API_CONFIG.baseUrl;

// Helper function to validate manifest data before submission
const validateManifestData = (manifest: Manifest): {isValid: boolean, errors: string[]} => {
  const errors: string[] = [];
  
  // Check required fields
  if (!manifest.customerName) errors.push("Customer name is required");
  if (!manifest.address) errors.push("Address is required");
  if (!manifest.postalCode) errors.push("Postal code is required");
  if (!manifest.phoneNo) errors.push("Phone number is required");
  
  // Check numeric fields
  if (typeof manifest.pieces !== 'number' || manifest.pieces <= 0) 
    errors.push("Pieces must be a positive number");
  
  if (typeof manifest.cbm !== 'number' || manifest.cbm < 0) 
    errors.push("CBM must be a non-negative number");
    
  if (typeof manifest.weight !== 'number' || manifest.weight < 0) 
    errors.push("Weight must be a non-negative number");
  
  // Check that client is properly set
  if (!manifest.client || !manifest.client.username) 
    errors.push("Client information is missing");
    
  // Check that container is properly set
  if (!manifest.container || !manifest.container.containerNo) 
    errors.push("Container information is missing");
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

class ManifestService {
  async getAllManifests(token: string): Promise<ApiResponse<Manifest[]>> {
    try {
      const response = await axios.get<ApiResponse<Manifest[]>>(
        `${API_BASE_URL}/api/manifests`, 
        {
      headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
      }
        }
      );
      
      // Calculate inbound pieces for each manifest
      if (response.data.success && response.data.data) {
        response.data.data = await this.calculateInboundPiecesForManifests(response.data.data, token);
      }
      
    return response.data;
    } catch (error: any) {
      console.error('Error fetching all manifests:', error);
      return {
        success: false,
        message: error.response?.data?.message || 'Failed to fetch manifests',
        data: []
      };
    }
  }

  async getManifestByTrackingNo(trackingNo: string, token: string): Promise<ApiResponse<Manifest>> {
    try {
    const response = await axios.get<ApiResponse<Manifest>>(`${API_BASE_URL}/api/manifests/${trackingNo}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
      
      // Calculate inbound pieces for this manifest
      if (response.data.success && response.data.data) {
        try {
          const inboundPiecesResponse = await axios.get<ApiResponse<number>>(
            `${API_BASE_URL}/api/manifests/${trackingNo}/pallets/total-pieces`,
            {
              headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
              }
            }
          );
          
          if (inboundPiecesResponse.data.success) {
            response.data.data.inboundPieces = inboundPiecesResponse.data.data || 0;
          } else {
            response.data.data.inboundPieces = 0;
          }
        } catch (error) {
          console.error(`Error fetching inbound pieces for manifest ${trackingNo}:`, error);
          response.data.data.inboundPieces = 0;
        }
      }
      
    return response.data;
    } catch (error: any) {
      console.error('Error fetching manifest by tracking number:', error);
      return {
        success: false,
        message: error.response?.data?.message || 'Failed to fetch manifest',
        data: {} as Manifest
      };
    }
  }

  async getMyManifests(token: string): Promise<ApiResponse<Manifest[]>> {
    const response = await axios.get<ApiResponse<Manifest[]>>(`${API_BASE_URL}/api/manifests/my-manifests`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    return response.data;
  }

  async getAssignedManifests(token: string): Promise<ApiResponse<Manifest[]>> {
    const response = await axios.get<ApiResponse<Manifest[]>>(`${API_BASE_URL}/api/manifests/assigned`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    return response.data;
  }

  async createManifest(manifest: Manifest, token: string): Promise<ApiResponse<Manifest>> {
    console.log('Creating manifest:', manifest);
    
    // Validate manifest data
    const validation = validateManifestData(manifest);
    if (!validation.isValid) {
      console.error('Manifest validation failed:', validation.errors);
      // We'll still proceed with the API call, but log the issues
    }
    
    // Remove frontend-only computed properties that the backend doesn't recognize
    const { 
      createdDate, 
      noOfPalletsFromObjects, 
      inboundPieces, 
      actualPalletsCount, 
      ...manifestWithoutFrontendFields 
    } = manifest;
    
    // Extract time slot from delivery date if not already provided
    const timeSlot = manifest.timeSlot || (manifest.deliveryDate ? getTimeSlotIdFromDate(manifest.deliveryDate) : null);
    
    const manifestData = {
      ...manifestWithoutFrontendFields,
      client: { username: manifest.client.username },
      driver: manifest.driver?.username ? { username: manifest.driver.username } : null,
      timeSlot: timeSlot, // Include time slot - backend now supports it
      // Format dates using our utility function
      deliveryDate: manifest.deliveryDate ?
        (typeof manifest.deliveryDate === 'string'
          ? manifest.deliveryDate.includes(' ') ?
              // Convert "19 Jul 2025" format to ISO "2025-07-19" format
              formatDateStringToIso(manifest.deliveryDate)
              : manifest.deliveryDate
          : formatDateForApi(manifest.deliveryDate as Date))
        : null,
      deliveredDate: manifest.deliveredDate ?
        (typeof manifest.deliveredDate === 'string'
          ? manifest.deliveredDate.includes(' ') ?
              // Convert "19 Jul 2025" format to ISO "2025-07-19" format
              formatDateStringToIso(manifest.deliveredDate)
              : manifest.deliveredDate
          : formatDateForApi(manifest.deliveredDate as Date))
        : null,
      // Ensure numeric fields are properly typed
      pieces: typeof manifest.pieces === 'string' ? parseInt(manifest.pieces) : manifest.pieces,
      cbm: typeof manifest.cbm === 'string' ? parseFloat(manifest.cbm) : manifest.cbm,
      weight: typeof manifest.weight === 'string' ? parseFloat(manifest.weight) : manifest.weight
    };
    
    console.log('Final manifest data being sent:', manifestData);
    // Log the exact JSON string being sent to help debug parsing issues
    console.log('JSON payload:', JSON.stringify(manifestData, null, 2));
    
    // Strict JSON validation - ensure the payload can be properly serialized
    let jsonString: string;
    try {
      jsonString = JSON.stringify(manifestData);
    } catch (error) {
      console.error('Failed to serialize manifest data to JSON:', error);
      throw new Error('Invalid manifest data: cannot be converted to JSON');
    }
    
    try {
      const response = await axios.post<ApiResponse<Manifest>>(
        `${API_BASE_URL}/api/manifests`, 
        manifestData, 
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          transformRequest: [(data: any) => {
            // Return the pre-validated JSON string instead of letting axios re-stringify
            return jsonString;
          }] as any
        }
      );
      return response.data;
    } catch (error: any) {
      console.error('Error in createManifest API call:', error);
      if (error.response?.status === 500) {
        console.error('Server error details:', error.response.data);
      }
      throw error;
    }
  }

  async createManifestsBulk(manifests: Manifest[], token: string): Promise<ApiResponse<Manifest[]>> {
    console.log('Creating manifests in bulk:', manifests.length);
    
    // Validate all manifests
    const validationErrors: string[] = [];
    manifests.forEach((manifest, index) => {
      const validation = validateManifestData(manifest);
      if (!validation.isValid) {
        validationErrors.push(`Row ${index + 2}: ${validation.errors.join(', ')}`);
      }
    });
    
    if (validationErrors.length > 0) {
      console.error('Bulk manifest validation failed:', validationErrors);
      throw new Error(`Validation failed:\n${validationErrors.join('\n')}`);
    }
    
    // Clean up the manifest data for all manifests
    const manifestsData = manifests.map(manifest => {
      const { 
        createdDate, 
        noOfPalletsFromObjects, 
        inboundPieces, 
        actualPalletsCount, 
        ...manifestWithoutFrontendFields 
      } = manifest;
      
      // Extract time slot from delivery date if not already provided
      const timeSlot = manifest.timeSlot || getTimeSlotIdFromDate(manifest.deliveryDate || null);
      
      return {
        ...manifestWithoutFrontendFields,
        client: { username: manifest.client.username },
        driver: manifest.driver?.username ? { username: manifest.driver.username } : null,
        timeSlot: timeSlot, // Include time slot - backend now supports it
        deliveryDate: manifest.deliveryDate ?
          (typeof manifest.deliveryDate === 'string'
            ? manifest.deliveryDate.includes(' ') ?
                // Convert "19 Jul 2025" format to ISO "2025-07-19" format
                formatDateStringToIso(manifest.deliveryDate)
                : manifest.deliveryDate
            : formatDateForApi(manifest.deliveryDate as Date))
          : null,
        deliveredDate: manifest.deliveredDate ?
          (typeof manifest.deliveredDate === 'string'
            ? manifest.deliveredDate.includes(' ') ?
                // Convert "19 Jul 2025" format to ISO "2025-07-19" format
                formatDateStringToIso(manifest.deliveredDate)
                : manifest.deliveredDate
            : formatDateForApi(manifest.deliveredDate as Date))
          : null,
        pieces: typeof manifest.pieces === 'string' ? parseInt(manifest.pieces) : manifest.pieces,
        cbm: typeof manifest.cbm === 'string' ? parseFloat(manifest.cbm) : manifest.cbm,
        weight: typeof manifest.weight === 'string' ? parseFloat(manifest.weight) : manifest.weight
      };
    });
    
    console.log('Final bulk manifest data being sent:', manifestsData);
    
    try {
      const response = await axios.post<ApiResponse<Manifest[]>>(
        `${API_BASE_URL}/api/manifests/bulk`, 
        manifestsData, 
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );
      return response.data;
    } catch (error: any) {
      console.error('Error in createManifestsBulk API call:', error);
      if (error.response?.status === 500) {
        console.error('Server error details:', error.response.data);
      }
      throw error;
    }
  }

  async updateManifest(trackingNo: string, manifest: Manifest, token: string): Promise<ApiResponse<Manifest>> {
    console.log('Updating manifest:', trackingNo, manifest);
    
    // Remove frontend-only computed properties that the backend doesn't recognize
    const { 
      noOfPalletsFromObjects, 
      inboundPieces, 
      actualPalletsCount, 
      ...cleanManifest 
    } = manifest;
    
    // Extract time slot from delivery date if not already provided
    // If timeSlot is not provided (null or undefined), try to determine it from the delivery date
    let timeSlot = manifest.timeSlot;
    if (!manifest.timeSlot && manifest.deliveryDate) {
      timeSlot = getTimeSlotIdFromDate(manifest.deliveryDate || null) || 'morning_12'; // Default to morning_12 if null
    }
    
    console.log(`Time slot for manifest ${trackingNo}: ${timeSlot || 'null'} (original: ${manifest.timeSlot || 'null'})`);
    
    // Create a date with the appropriate time based on the time slot if needed
    let dateWithTimeSlot = null;
    if (manifest.deliveryDate && timeSlot) {
      try {
        // Parse the date using our utility function
        if (typeof manifest.deliveryDate === 'string') {
          dateWithTimeSlot = parseDateString(manifest.deliveryDate);
        } else {
          // Use the Date object directly
          dateWithTimeSlot = manifest.deliveryDate as Date;
        }
        
        // Make sure we have a valid date
        if (dateWithTimeSlot && !isNaN(dateWithTimeSlot.getTime())) {
          // Set hours based on time slot
          if (timeSlot === 'no_time') {
            // For 'no_time' slot, keep the date as is without setting specific time
            dateWithTimeSlot.setHours(0, 0, 0, 0);
          } else if (timeSlot === 'morning_12') {
            dateWithTimeSlot.setHours(9, 0, 0, 0);
          } else if (timeSlot === 'morning_13') {
            dateWithTimeSlot.setHours(9, 30, 0, 0);
          } else if (timeSlot === 'afternoon') {
            dateWithTimeSlot.setHours(14, 0, 0, 0);
          }

          console.log(`Date with time slot applied: ${dateWithTimeSlot}`);
        } else {
          dateWithTimeSlot = null;
        }
      } catch (error) {
        console.error('Error processing date with time slot:', error);
        dateWithTimeSlot = null;
      }
    }
    
      const manifestData = {
        ...cleanManifest,
      client: { username: manifest.client.username },
      driver: manifest.driver?.username ? { username: manifest.driver.username } : null,
      timeSlot: timeSlot, // Use our updated timeSlot value
        // Format dates using our utility function
      deliveryDate: manifest.deliveryDate ?
        (dateWithTimeSlot ? formatDateForApi(dateWithTimeSlot) :
          typeof manifest.deliveryDate === 'string'
            ? manifest.deliveryDate.includes(' ') ?
                formatDateStringToIso(manifest.deliveryDate)
                : manifest.deliveryDate
            : formatDateForApi(manifest.deliveryDate as Date))
          : null,
      deliveredDate: manifest.deliveredDate ?
        (typeof manifest.deliveredDate === 'string'
          ? manifest.deliveredDate.includes(' ') ?
              formatDateStringToIso(manifest.deliveredDate)
              : manifest.deliveredDate
          : formatDateForApi(manifest.deliveredDate as Date))
          : null,
      // Ensure numeric fields are properly typed
      pieces: typeof manifest.pieces === 'string' ? parseInt(manifest.pieces) : manifest.pieces,
      cbm: typeof manifest.cbm === 'string' ? parseFloat(manifest.cbm) : manifest.cbm,
      weight: typeof manifest.weight === 'string' ? parseFloat(manifest.weight) : manifest.weight
      };
      
      console.log('Final manifest data being sent:', {
        ...manifestData,
        deliveryDate: manifestData.deliveryDate,
      deliveredDate: manifestData.deliveredDate,
      timeSlot: manifestData.timeSlot
      });
      
    const url = `${API_BASE_URL}/api/manifests/${trackingNo}`;
    console.log(`Making API request to: ${url}`);
    
    try {
      const response = await axios.put<ApiResponse<Manifest>>(
        url, 
        manifestData,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );
      
      console.log('Manifest update successful:', response.data);
      console.log(`Updated location: ${response.data.data?.location}`);
      
      // Calculate inbound pieces for the updated manifest
      if (response.data.success && response.data.data) {
        try {
          const inboundPiecesResponse = await axios.get<ApiResponse<number>>(
            `${API_BASE_URL}/api/manifests/${trackingNo}/pallets/total-pieces`,
            {
              headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
              }
            }
          );
          
          if (inboundPiecesResponse.data.success) {
            response.data.data.inboundPieces = inboundPiecesResponse.data.data || 0;
            console.log(`Updated manifest inbound pieces: ${response.data.data.inboundPieces}`);
          }
        } catch (inboundError) {
          console.error('Error fetching inbound pieces after update:', inboundError);
          response.data.data.inboundPieces = 0;
        }
        
        if (response.data.data.deliveryDate) {
          console.log('Received deliveryDate from backend:', response.data.data.deliveryDate);
        }
        if (response.data.data.deliveredDate) {
          console.log('Received deliveredDate from backend:', response.data.data.deliveredDate);
        }
      }
      
      return response.data;
    } catch (error) {
      console.error('Error in updateManifest:', error);
      throw error;
    }
  }

  async updateManifestStatus(trackingNo: string, status: ManifestStatus, token: string, remarks?: string): Promise<ApiResponse<Manifest>> {
    console.log(`Updating status: trackingNo=${trackingNo}, status=${status}`);
    
    // Properly encode the status parameter
    const encodedStatus = encodeURIComponent(status);
    const url = `${API_BASE_URL}/api/manifests/${trackingNo}/status?status=${encodedStatus}`;
    
    console.log(`Making API request to: ${url}`);
    
    try {
      // Make the API call to update the status (backend now handles tracking logs automatically)
      const response = await axios.put<ApiResponse<Manifest>>(
        url, 
        {}, // Empty body
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );
      
      console.log('Status update successful:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error in updateManifestStatus:', error);
      throw error;
    }
  }

  // Update only remarks fields (targeted update with minimal payload)
  async updateManifestRemarks(
    trackingNo: string,
    field: 'remarks' | 'driverRemarks',
    value: string,
    token: string
  ): Promise<ApiResponse<Manifest>> {
    console.log(`Updating ${field} for manifest ${trackingNo} with value:`, value);

    try {
      // Try using a query parameter approach first (most efficient)
      const url = `${API_BASE_URL}/api/manifests/${trackingNo}/remarks`;

      console.log('Sending targeted remarks update:', {
        trackingNo,
        field,
        value: value.substring(0, 50) + (value.length > 50 ? '...' : '') // Log truncated value
      });

      const response = await axios.put<ApiResponse<Manifest>>(
        url,
        {
          field: field,
          value: value
        },
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );

      console.log('Targeted remarks update successful:', response.data);
      return response.data;
    } catch (error: any) {
      console.log('Targeted endpoint not available, trying query parameter approach...');

      // Try query parameter approach
      try {
        const queryUrl = `${API_BASE_URL}/api/manifests/${trackingNo}?updateField=${field}&value=${encodeURIComponent(value)}`;

        const response = await axios.put<ApiResponse<Manifest>>(
          queryUrl,
          {}, // Empty body since data is in query params
          {
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          }
        );

        console.log('Query parameter update successful:', response.data);
        return response.data;
      } catch (queryError: any) {
        console.log('Query parameter approach failed, trying quick-update endpoint...');

        // Try the quick-update endpoint
        try {
          const quickUpdateUrl = `${API_BASE_URL}/api/manifests/${trackingNo}/quick-update?updateField=${field}&value=${encodeURIComponent(value)}`;

          const response = await axios.put<ApiResponse<Manifest>>(
            quickUpdateUrl,
            {}, // Empty body since data is in query params
            {
              headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
              }
            }
          );

          console.log('Quick update successful:', response.data);
          return response.data;
        } catch (quickUpdateError: any) {
          console.log('Quick update failed, using direct SQL approach...');

          // Final fallback: Use a direct SQL update approach
          try {
            const sqlUrl = `${API_BASE_URL}/api/manifests/${trackingNo}/update-field`;

            const response = await axios.patch<ApiResponse<Manifest>>(
              sqlUrl,
              {
                fieldName: field === 'driverRemarks' ? 'driver_remarks' : 'remarks', // Use actual DB column names
                fieldValue: value,
                trackingNo: trackingNo
              },
              {
                headers: {
                  'Authorization': `Bearer ${token}`,
                  'Content-Type': 'application/json'
                }
              }
            );

            console.log('Direct SQL update successful:', response.data);
            return response.data;
          } catch (sqlError: any) {
            console.error('All update approaches failed:', sqlError);
            return {
              success: false,
              message: `Failed to update ${field}. Please try refreshing the page and trying again.`,
              data: null as any
            };
          }
        }
      }
    }
  }

  async deleteManifest(trackingNo: string, token: string): Promise<ApiResponse<void>> {
    const response = await axios.delete<ApiResponse<void>>(`${API_BASE_URL}/api/manifests/${trackingNo}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    return response.data;
  }

  async getManifestsCount(token: string): Promise<ApiResponse<number>> {
    const response = await axios.get<ApiResponse<number>>(`${API_BASE_URL}/api/manifests/count`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    return response.data;
  }

  async getManifestsByDriverUsername(username: string, token: string): Promise<ApiResponse<Manifest[]>> {
    const response = await axios.get<ApiResponse<Manifest[]>>(`${API_BASE_URL}/api/manifests/driver/${username}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    return response.data;
  }

  async getManifestsByContainer(containerNo: string, token: string): Promise<ApiResponse<Manifest[]>> {
    try {
      const response = await axios.get<ApiResponse<Manifest[]>>(
        `${API_BASE_URL}/api/manifests/container/${containerNo}`, 
        {
      headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
      }
        }
      );
      
      // Calculate inbound pieces for each manifest
      if (response.data.success && response.data.data) {
        response.data.data = await this.calculateInboundPiecesForManifests(response.data.data, token);
      }
      
    return response.data;
    } catch (error: any) {
      console.error('Error fetching manifests by container:', error);
      return {
        success: false,
        message: error.response?.data?.message || 'Failed to fetch manifests for container',
        data: []
      };
    }
  }

  // Helper method to calculate inbound pieces for a list of manifests
  private async calculateInboundPiecesForManifests(manifests: Manifest[], token: string): Promise<Manifest[]> {
    const manifestsWithInboundPieces = await Promise.all(
      manifests.map(async (manifest) => {
        try {
          // Get total pieces from pallets for this manifest
          const response = await axios.get<ApiResponse<number>>(
            `${API_BASE_URL}/api/manifests/${manifest.trackingNo}/pallets/total-pieces`,
            {
              headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
              }
            }
          );
          
          if (response.data.success) {
            return {
              ...manifest,
              inboundPieces: response.data.data || 0
            };
          } else {
            return {
              ...manifest,
              inboundPieces: 0
            };
          }
        } catch (error) {
          console.error(`Error fetching inbound pieces for manifest ${manifest.trackingNo}:`, error);
          return {
            ...manifest,
            inboundPieces: 0
          };
        }
      })
    );
    
    return manifestsWithInboundPieces;
  }

  // New tracking log methods using backend APIs
  async getManifestTrackingHistory(trackingNo: string, token: string): Promise<ApiResponse<ManifestTrackingHistory[]>> {
    const response = await axios.get<ApiResponse<ManifestTrackingHistory[]>>(`${API_BASE_URL}/api/manifests/${trackingNo}/tracking-logs`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    return response.data;
  }

  async deleteTrackingLog(logId: number, token: string): Promise<ApiResponse<void>> {
    const response = await axios.delete<ApiResponse<void>>(`${API_BASE_URL}/api/tracking-logs/${logId}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    return response.data;
  }

  async deleteAllTrackingLogsForManifest(trackingNo: string, token: string): Promise<ApiResponse<void>> {
    const response = await axios.delete<ApiResponse<void>>(`${API_BASE_URL}/api/manifests/${trackingNo}/tracking-logs`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    return response.data;
  }

  async getManifestStatusChangeHistory(trackingNo: string, token: string): Promise<ApiResponse<ManifestTrackingHistory[]>> {
    try {
      console.log(`Fetching status change history for manifest: ${trackingNo}`);
      const response = await axios.get<ApiResponse<ManifestTrackingHistory[]>>(
        `${API_BASE_URL}/api/manifests/${trackingNo}/tracking-logs/status-changes`,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );
      
      console.log('Status change history fetched successfully:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error fetching status change history:', error);
      return {
        success: false,
        message: 'Failed to retrieve status change history',
        data: []
      };
    }
  }

  async getLatestTrackingLog(trackingNo: string, token: string): Promise<ApiResponse<ManifestTrackingHistory | null>> {
    try {
      console.log(`Fetching latest tracking log for manifest: ${trackingNo}`);
      const response = await axios.get<ApiResponse<ManifestTrackingHistory>>(
        `${API_BASE_URL}/api/manifests/${trackingNo}/tracking-logs/latest`,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );
      
      console.log('Latest tracking log fetched successfully:', response.data);
      return response.data;
    } catch (error: any) {
      if (error.response?.status === 404) {
        return {
          success: true,
          message: 'No tracking logs found',
          data: null
        };
      }
      console.error('Error fetching latest tracking log:', error);
      return {
        success: false,
        message: 'Failed to retrieve latest tracking log',
        data: null
      };
    }
  }

  async getTrackingLogCount(trackingNo: string, token: string): Promise<ApiResponse<number>> {
    try {
      console.log(`Fetching tracking log count for manifest: ${trackingNo}`);
      const response = await axios.get<ApiResponse<number>>(
        `${API_BASE_URL}/api/manifests/${trackingNo}/tracking-logs/count`,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );
      
      console.log('Tracking log count fetched successfully:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error fetching tracking log count:', error);
      return {
        success: false,
        message: 'Failed to retrieve tracking log count',
        data: 0
      };
    }
  }

  async getTrackingLogsByDateRange(trackingNo: string, startDate: string, endDate: string, token: string): Promise<ApiResponse<ManifestTrackingHistory[]>> {
    try {
      console.log(`Fetching tracking logs for manifest ${trackingNo} between ${startDate} and ${endDate}`);
      const response = await axios.get<ApiResponse<ManifestTrackingHistory[]>>(
        `${API_BASE_URL}/api/manifests/${trackingNo}/tracking-logs/date-range`,
        {
          params: {
            startDate,
            endDate
          },
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );
      
      console.log('Date range tracking logs fetched successfully:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error fetching date range tracking logs:', error);
      return {
        success: false,
        message: 'Failed to retrieve date range tracking logs',
        data: []
      };
    }
  }

  async getNextSequenceNumber(containerNo: string, token: string): Promise<ApiResponse<number>> {
    try {
      // First get all manifests for the container
      const manifests = await this.getManifestsByContainer(containerNo, token);
      
      if (!manifests.success) {
        throw new Error(manifests.message || 'Failed to fetch manifests');
      }
      
      // Find the maximum sequence number
      let maxSequence = 0;
      if (manifests.data.length > 0) {
        maxSequence = Math.max(...manifests.data
          .map(m => m.sequenceNo || 0)
          .filter(seq => typeof seq === 'number'));
      }
      
      // Return the next sequence number (max + 1)
      return {
        success: true,
        message: 'Next sequence number retrieved',
        data: maxSequence + 1
      };
    } catch (error: any) {
      console.error('Error fetching next sequence number:', error);
      return {
        success: false,
        message: error.message || 'Failed to fetch next sequence number',
        data: 0
      };
    }
  }

  // Local storage methods for custom label counts
  
  /**
   * Save a custom label count for a manifest
   * @param trackingNo The manifest tracking number
   * @param count The custom label count to save
   */
  saveLabelCount(trackingNo: string, count: number): void {
    try {
      // Get existing label counts from localStorage
      const labelCountsStr = localStorage.getItem('manifestLabelCounts');
      const labelCounts = labelCountsStr ? JSON.parse(labelCountsStr) : {};
      
      // Update the count for this manifest
      labelCounts[trackingNo] = count;
      
      // Save back to localStorage
      localStorage.setItem('manifestLabelCounts', JSON.stringify(labelCounts));
      
      console.log(`Saved label count ${count} for manifest ${trackingNo}`);
    } catch (error) {
      console.error('Failed to save label count to localStorage:', error);
    }
  }
  
  /**
   * Get the saved custom label count for a manifest
   * @param trackingNo The manifest tracking number
   * @returns The saved label count, or undefined if none exists
   */
  getLabelCount(trackingNo: string): number | undefined {
    try {
      // Get existing label counts from localStorage
      const labelCountsStr = localStorage.getItem('manifestLabelCounts');
      if (!labelCountsStr) return undefined;
      
      const labelCounts = JSON.parse(labelCountsStr);
      return labelCounts[trackingNo];
    } catch (error) {
      console.error('Failed to retrieve label count from localStorage:', error);
      return undefined;
    }
  }

  async updateBulkDeliveryDates(trackingNos: string[], deliveryDate: string | null, token: string, timeSlot: string | null = null): Promise<ApiResponse<Manifest[]>> {
    console.log(`Updating delivery dates for ${trackingNos.length} manifests to ${deliveryDate} with time slot ${timeSlot || 'default'}`);
    console.log('Tracking numbers:', trackingNos);
    
    try {
      console.log(`API call to: ${API_BASE_URL}/api/manifests/bulk-update-delivery-date`);
      
      // The backend's LocalDate.parse() method expects exactly YYYY-MM-DD format
      // We're now sending a properly formatted date from the component, but let's double-check
      let formattedDeliveryDate = deliveryDate;
      
      // If the date contains timezone information or time component, remove it
      if (deliveryDate) {
        try {
          if (deliveryDate.includes(' ') && deliveryDate.match(/^\d{1,2}\s+[A-Za-z]{3}\s+\d{4}$/)) {
            // Format like "19 Jul 2025"
            formattedDeliveryDate = formatDateStringToIso(deliveryDate);
          } else if (deliveryDate.includes('T') || deliveryDate.includes('+') ||
                    deliveryDate.includes('Z') || deliveryDate.includes('-', 10)) {
            // Parse the date and format it to YYYY-MM-DD (date only, no time)
          const date = new Date(deliveryDate);
            formattedDeliveryDate = formatDateForApi(date);
          }
          console.log(`Reformatted date to: ${formattedDeliveryDate}`);
        } catch (e) {
          console.warn(`Failed to normalize date format: ${deliveryDate}`, e);
        }
      }
      
      console.log('Request payload with formatted date:', { trackingNos, deliveryDate: formattedDeliveryDate, timeSlot });
      
      const response = await axios.post<ApiResponse<Manifest[]>>(
        `${API_BASE_URL}/api/manifests/bulk-update-delivery-date`,
        { trackingNos, deliveryDate: formattedDeliveryDate, timeSlot },
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );
      
      console.log('Bulk delivery dates update response:', response.data);
      console.log('Updated manifests count:', response.data.data?.length || 0);
      
      // Check for failures in the response message
      const failureMatch = response.data.message?.match(/\((\d+) failed\)/);
      if (failureMatch && parseInt(failureMatch[1]) > 0) {
        console.warn(`${failureMatch[1]} manifest updates failed according to response message`);
      }
      
      if (response.data.success) {
        console.log('Delivery dates update request was successful');
        
        // Format the dates in the response data to ensure consistent display
        if (response.data.data && response.data.data.length > 0) {
          // Format the delivery dates in the response data
          response.data.data = response.data.data.map(manifest => {
            if (manifest.deliveryDate) {
              try {
                // Create a Date object from the backend date string
                const dateObj = new Date(manifest.deliveryDate);
                
                // Format the date using our utility function to ensure consistent display
                // This uses the Singapore timezone formatting for consistency (date only, no time)
                const formattedDisplayDate = formatDateOnly(dateObj);
                
                return {
                  ...manifest,
                  deliveryDate: formattedDisplayDate
                };
              } catch (e) {
                console.warn(`Failed to format date for manifest ${manifest.trackingNo}:`, e);
                return manifest;
              }
            }
            return manifest;
          });
          
          const sampleManifests = response.data.data.slice(0, Math.min(3, response.data.data.length));
          console.log('Sample updated manifests:', sampleManifests.map(m => ({
            trackingNo: m.trackingNo,
            deliveryDate: m.deliveryDate
          })));
        } else {
          console.warn('No manifests were returned in the response data');
          console.log('This might indicate that the backend updated the manifests but did not return them');
          console.log('Recommend refreshing all manifests to get the latest data');
        }
      } else {
        console.error('Failed to update delivery dates:', response.data.message);
      }
      
      return response.data;
    } catch (error: any) {
      console.error('Error in updateBulkDeliveryDates API call:', error);
      console.error('Error details:', error.message);
      
      if (error.response) {
        console.error('Response status:', error.response.status);
        console.error('Response headers:', error.response.headers);
        console.error('Response data:', error.response.data);
      }
      
      throw error;
    }
  }

  /**
   * Quick update of delivery vehicle for a manifest
   * @param trackingNo The manifest tracking number
   * @param deliveryVehicle The new delivery vehicle (null or empty for auto-assignment)
   * @param token Authentication token
   * @returns Promise with the updated manifest
   */
  async updateManifestDeliveryVehicle(
    trackingNo: string, 
    deliveryVehicle: string | null, 
    token: string
  ): Promise<ApiResponse<Manifest>> {
    try {
      console.log(`Quick updating delivery vehicle for manifest ${trackingNo} to: ${deliveryVehicle || 'auto-assign'}`);
      
      const response = await axios.put<ApiResponse<Manifest>>(
        `${API_BASE_URL}/api/manifests/${trackingNo}/delivery-vehicle`,
        {
          deliveryVehicle: deliveryVehicle || null
        },
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );
      
      console.log('Delivery vehicle updated successfully:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('Error updating delivery vehicle:', error);
      
      if (error.response?.data?.message) {
        return {
          success: false,
          message: error.response.data.message,
          data: {} as Manifest
        };
      }
      
      return {
        success: false,
        message: 'Failed to update delivery vehicle',
        data: {} as Manifest
      };
    }
  }
}

const manifestService = new ManifestService();
export default manifestService; 