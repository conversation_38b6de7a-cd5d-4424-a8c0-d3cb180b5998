import axios from 'axios';
import { Client, Driver } from '../types/User';
import { Vehicle } from '../types/Vehicle';
import API_CONFIG from '../config/api.config';

// Direct URL instead of using process.env
const API_URL = `${API_CONFIG.baseUrl}/api`;

interface ApiResponse<T> {
  success: boolean;
  message: string;
  data: T;
}

class UserService {
  async getAllClients(token: string) {
    return axios.get<ApiResponse<Client[]>>(`${API_URL}/clients`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
  }

  async getClients(token: string): Promise<ApiResponse<Client[]>> {
    const response = await axios.get<ApiResponse<Client[]>>(`${API_URL}/clients`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
    return response.data;
  }

  async getDrivers(token: string): Promise<ApiResponse<Driver[]>> {
    const response = await axios.get<ApiResponse<Driver[]>>(`${API_URL}/drivers`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
    return response.data;
  }

  async getDriverVehicles(username: string, token: string): Promise<ApiResponse<Vehicle[]>> {
    const response = await axios.get<ApiResponse<Vehicle[]>>(`${API_URL}/drivers/${username}/vehicles`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
    return response.data;
  }
}

export default new UserService(); 