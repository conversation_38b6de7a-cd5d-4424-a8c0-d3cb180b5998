import React from 'react';
import { LayoutProvider } from './LayoutContext';
import LayoutContainer from './LayoutContainer';
import SidebarDrawer from './SidebarDrawer';
import AppHeader from './AppHeader';
import ContentArea from './ContentArea';

interface DashboardLayoutProps {
  children: React.ReactNode;
}

const DashboardLayout: React.FC<DashboardLayoutProps> = ({ children }) => {
  return (
    <LayoutProvider>
      <LayoutContainer>
        <SidebarDrawer />
        <AppHeader />
        <ContentArea>{children}</ContentArea>
      </LayoutContainer>
    </LayoutProvider>
  );
};

export default DashboardLayout;

// Export individual components for direct use if needed
export {
  LayoutProvider,
  LayoutContainer,
  SidebarDrawer,
  AppHeader,
  ContentArea
}; 