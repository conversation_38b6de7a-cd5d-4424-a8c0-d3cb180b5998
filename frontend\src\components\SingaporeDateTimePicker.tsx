import React from 'react';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { formatInTimeZone, toZonedTime } from 'date-fns-tz';
import { SINGAPORE_TIMEZONE } from '../utils/dateUtils';

interface SingaporeDateTimePickerProps {
  value: Date | null;
  onChange: (value: Date | null) => void;
  label: string;
  format?: string;
  slotProps?: any;
  ampm?: boolean;
  [key: string]: any; // Allow other props to be passed through
}

/**
 * DateTimePicker component that handles Singapore timezone automatically
 * All dates are displayed and edited in Singapore timezone for consistency
 */
export const SingaporeDateTimePicker: React.FC<SingaporeDateTimePickerProps> = ({
  value,
  onChange,
  label,
  format = "dd MMM yyyy HH:mm",
  slotProps,
  ...props
}) => {
  // Convert incoming value to Singapore timezone for display
  const displayValue = value ? toZonedTime(value, SINGAPORE_TIMEZONE) : null;

  // Handle changes and convert back to proper Date object
  const handleChange = (newValue: Date | null) => {
    if (newValue) {
      // The date picker gives us a Date object in the user's local timezone
      // We need to treat it as if it's in Singapore timezone
      const singaporeDate = toZonedTime(newValue, SINGAPORE_TIMEZONE);
      onChange(singaporeDate);
    } else {
      onChange(null);
    }
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <DateTimePicker
        {...props}
        label={`${label} (SGT)`}
        value={displayValue}
        onChange={handleChange}
        format={format}
        ampm={false}
        slotProps={{
          ...slotProps,
          textField: {
            ...slotProps?.textField,
            helperText: 'Singapore Time (SGT)',
          },
        }}
      />
    </LocalizationProvider>
  );
}; 