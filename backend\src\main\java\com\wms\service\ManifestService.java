package com.wms.service;

import com.wms.entity.Manifest;
import com.wms.entity.ManifestStatus;
import com.wms.entity.Container;
import java.util.List;
import java.util.Optional;

public interface ManifestService {
    List<Manifest> getAllManifests();
    Optional<Manifest> getManifestByTrackingNo(String trackingNo);
    List<Manifest> getManifestsByUsername(String username);
    List<Manifest> getManifestsByDriverUsername(String username);
    List<Manifest> getManifestsByContainerNo(String containerNo);
    Manifest createManifest(Manifest manifest);
    Manifest updateManifest(String trackingNo, Manifest manifest);
    void deleteManifest(String trackingNo);
    Manifest updateManifestStatus(String trackingNo, ManifestStatus status);
    Long getManifestsCount();
    Optional<Manifest> getManifestByManifestNo(String manifestNo);
    
    /**
     * Checks if a container can accept more manifests based on its manifestQuantity
     * 
     * @param containerNo The container number to check
     * @return true if more manifests can be created, false otherwise
     */
    boolean canContainerAcceptMoreManifests(String containerNo);
    
    /**
     * Creates multiple manifests in a single transaction.
     * If any manifest fails to create, the entire operation is rolled back.
     * 
     * @param manifests List of manifests to create
     * @return List of successfully created manifests
     * @throws RuntimeException if any manifest creation fails
     */
    List<Manifest> createManifestsBulk(List<Manifest> manifests);
    
    /**
     * Checks if there's a discrepancy between manifest pieces and total pallet pieces
     * and updates the manifest status accordingly
     *
     * @param manifest The manifest to check for discrepancy
     * @return The updated manifest if status changed, or the original manifest if no change
     */
    Manifest checkAndUpdateManifestStatus(Manifest manifest);

    /**
     * Updates only the remarks field for a manifest (optimized for quick edits)
     *
     * @param trackingNo The tracking number of the manifest
     * @param field The field to update ("remarks" or "driverRemarks")
     * @param value The new value for the field
     * @return The updated manifest
     */
    Manifest updateRemarksField(String trackingNo, String field, String value);

    /**
     * Updates a specific field using direct SQL (fallback method)
     *
     * @param trackingNo The tracking number of the manifest
     * @param fieldName The database column name to update
     * @param fieldValue The new value for the field
     * @return The updated manifest
     */
    Manifest updateSpecificField(String trackingNo, String fieldName, String fieldValue);
}