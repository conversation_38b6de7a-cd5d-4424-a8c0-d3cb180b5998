html, body, #root {
  height: 100%;
  width: 100%;
  min-height: 100%;
  min-width: 100%;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

#root {
  max-width: none;
  padding: 0;
  margin: 0;
  text-align: initial;
}

/* Ensure content sections take full width */
main[role="main"] {
  width: 100% !important;
  min-width: 100% !important;
  height: 100% !important;
  min-height: 100% !important;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}

.MuiToolbar-root {
  width: 100% !important;
  min-width: 100% !important;
  display: flex;
  flex-direction: row;
}

main[role="main"] > * {
  width: 100%;
  min-width: 100%;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

/* Page content should fill available space */
.page-container {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  width: 100%;
  min-height: 100%;
  height: 100%;
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}
