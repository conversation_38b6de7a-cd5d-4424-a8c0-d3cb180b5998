import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

interface PrivateRouteProps {
  children: React.ReactNode;
  roles?: string[];
}

const PrivateRoute: React.FC<PrivateRouteProps> = ({ children, roles }) => {
  const { currentUser, hasRole } = useAuth();

  console.log('PrivateRoute check:', {
    path: window.location.pathname,
    hasUser: !!currentUser,
    username: currentUser?.username,
    requiredRoles: roles,
    currentRoles: currentUser?.roles
  });

  if (!currentUser) {
    console.log('No user found, redirecting to login');
    return <Navigate to="/login" replace />;
  }

  // If roles are specified, check if user has at least one of the required roles
  if (roles && roles.length > 0) {
    const hasRequiredRole = roles.some(role => hasRole(role));
    console.log('Role check:', {
      requiredRoles: roles,
      hasRequiredRole,
      currentRoles: currentUser.roles
    });
    if (!hasRequiredRole) {
      console.log('User lacks required role, redirecting to dashboard');
      return <Navigate to="/dashboard" replace />;
    }
  }

  console.log('Access granted to route');
  return <>{children}</>;
};

export default PrivateRoute; 