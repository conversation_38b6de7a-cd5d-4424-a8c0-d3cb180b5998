import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Typography,
  Paper,
  Card,
  CardContent,
  CardHeader,
  IconButton,
  Divider,
  Button,
  Chip,
  Avatar,
  Switch,
  FormControlLabel,
  CircularProgress,
  Tooltip,
  Badge,
  useTheme,
  alpha,
  Tab,
  Tabs,
  Menu,
  MenuItem,
  Stack,
} from '@mui/material';
import RefreshIcon from '@mui/icons-material/Refresh';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import NotificationsIcon from '@mui/icons-material/Notifications';
import FilterListIcon from '@mui/icons-material/FilterList';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import TrendingDownIcon from '@mui/icons-material/TrendingDown';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import DateRangeIcon from '@mui/icons-material/DateRange';
import InventoryIcon from '@mui/icons-material/Inventory';
import LocalShippingIcon from '@mui/icons-material/LocalShipping';
import PeopleIcon from '@mui/icons-material/People';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';
import WarningIcon from '@mui/icons-material/Warning';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import { format } from 'date-fns';
import { formatDate, getSingaporeNow, formatDateWithTimezone } from '../utils/dateUtils';
import axios from 'axios';
import { useAuth } from '../contexts/AuthContext';
import { useThemeContext } from '../contexts/ThemeContext';
import usePageTitle from '../hooks/usePageTitle';
import API_CONFIG from '../config/api.config';

// Import chart libraries
import {
  AreaChart, Area, LineChart, Line, BarChart, Bar, PieChart, Pie, Cell,
  XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, Legend, ResponsiveContainer
} from 'recharts';

// Mock data for charts
const lineChartData = [
  { month: 'Jan', containers: 4000, manifests: 2400, deliveries: 2400 },
  { month: 'Feb', containers: 3000, manifests: 1398, deliveries: 2210 },
  { month: 'Mar', containers: 2000, manifests: 9800, deliveries: 2290 },
  { month: 'Apr', containers: 2780, manifests: 3908, deliveries: 2000 },
  { month: 'May', containers: 1890, manifests: 4800, deliveries: 2181 },
  { month: 'Jun', containers: 2390, manifests: 3800, deliveries: 2500 },
  { month: 'Jul', containers: 3490, manifests: 4300, deliveries: 2100 },
  { month: 'Aug', containers: 4000, manifests: 2400, deliveries: 2400 },
  { month: 'Sep', containers: 3000, manifests: 1398, deliveries: 2210 },
  { month: 'Oct', containers: 2000, manifests: 9800, deliveries: 2290 },
  { month: 'Nov', containers: 2780, manifests: 3908, deliveries: 2000 },
  { month: 'Dec', containers: 1890, manifests: 4800, deliveries: 2181 },
];

const areaChartData = [
  { date: '2023-01', value: 1200 },
  { date: '2023-02', value: 1900 },
  { date: '2023-03', value: 3000 },
  { date: '2023-04', value: 1800 },
  { date: '2023-05', value: 2800 },
  { date: '2023-06', value: 2000 },
  { date: '2023-07', value: 3500 },
  { date: '2023-08', value: 3700 },
  { date: '2023-09', value: 2500 },
  { date: '2023-10', value: 2800 },
  { date: '2023-11', value: 3800 },
  { date: '2023-12', value: 4000 },
];

const barChartData = [
  { name: 'Jan', value: 400 },
  { name: 'Feb', value: 300 },
  { name: 'Mar', value: 500 },
  { name: 'Apr', value: 280 },
  { name: 'May', value: 590 },
  { name: 'Jun', value: 320 },
  { name: 'Jul', value: 390 },
  { name: 'Aug', value: 490 },
];

const pieChartData = [
  { name: 'DELIVERED', value: 400, color: '#4caf50' },
  { name: 'PENDING', value: 300, color: '#ff9800' },
  { name: 'ON_HOLD', value: 200, color: '#f44336' },
  { name: 'READY', value: 100, color: '#2196f3' },
];

const ModernDashboard: React.FC = () => {
  const theme = useTheme();
  const { mode, toggleColorMode } = useThemeContext();
  const { currentUser, hasRole } = useAuth();
  
  // Set page title
  usePageTitle('Modern Dashboard');
  
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [tabValue, setTabValue] = useState(0);
  const [notificationCount, setNotificationCount] = useState(3);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [filterAnchorEl, setFilterAnchorEl] = useState<null | HTMLElement>(null);
  const [timeRange, setTimeRange] = useState('month');

  // Mock data for dashboard metrics
  const [dashboardData, setDashboardData] = useState({
    totalContainers: 1256,
    totalManifests: 3427,
    totalDeliveries: 2891,
    pendingDeliveries: 47,
    onHoldDeliveries: 12,
    deliverySuccess: 98.2,
    todayScheduled: 24,
    todayCompleted: 18,
    weeklyGrowth: 12.5,
  });

  const handleRefresh = () => {
    setRefreshing(true);
    // Simulate API call with timeout
    setTimeout(() => {
      setRefreshing(false);
    }, 1500);
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleFilterClick = (event: React.MouseEvent<HTMLElement>) => {
    setFilterAnchorEl(event.currentTarget);
  };

  const handleFilterClose = () => {
    setFilterAnchorEl(null);
  };

  const handleTimeRangeChange = (range: string) => {
    setTimeRange(range);
    handleFilterClose();
  };

  // Fetch dashboard data
  useEffect(() => {
    const fetchDashboardData = async () => {
      if (!currentUser?.token) return;
      
      setLoading(true);
      try {
        // In a real app, you would fetch real data here
        // const response = await axios.get(`${API_CONFIG.baseUrl}/api/dashboard/metrics`, {
        //   headers: { Authorization: `Bearer ${currentUser.token}` }
        // });
        // setDashboardData(response.data);
        
        // For now, we'll just simulate a delay
        setTimeout(() => {
          setLoading(false);
        }, 1000);
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
        setLoading(false);
      }
    };
    
    fetchDashboardData();
  }, [currentUser]);

  return (
    <Box sx={{
      p: 3,
      width: '100%',
      height: '100%',
      boxSizing: 'border-box',
      display: 'flex',
      flexDirection: 'column',
    }}>
      {/* Dashboard Header */}
      <Box sx={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        mb: 3,
        flexWrap: 'wrap',
        gap: 2,
        flexShrink: 0,
      }}>
        <Typography variant="h4" fontWeight="bold">
          Logistics Dashboard
        </Typography>
        
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <FormControlLabel
            control={
              <Switch 
                checked={mode === 'dark'} 
                onChange={toggleColorMode} 
                color="primary" 
              />
            }
            label={mode === 'dark' ? "Dark Mode" : "Light Mode"}
          />
          
          <Tooltip title="Notifications">
            <IconButton color="primary">
              <Badge badgeContent={notificationCount} color="error">
                <NotificationsIcon />
              </Badge>
            </IconButton>
          </Tooltip>
          
          <Tooltip title="Filter">
            <IconButton 
              color="primary"
              onClick={handleFilterClick}
            >
              <FilterListIcon />
            </IconButton>
          </Tooltip>
          <Menu
            anchorEl={filterAnchorEl}
            open={Boolean(filterAnchorEl)}
            onClose={handleFilterClose}
          >
            <MenuItem 
              onClick={() => handleTimeRangeChange('day')}
              selected={timeRange === 'day'}
            >
              Today
            </MenuItem>
            <MenuItem 
              onClick={() => handleTimeRangeChange('week')}
              selected={timeRange === 'week'}
            >
              This Week
            </MenuItem>
            <MenuItem 
              onClick={() => handleTimeRangeChange('month')}
              selected={timeRange === 'month'}
            >
              This Month
            </MenuItem>
            <MenuItem 
              onClick={() => handleTimeRangeChange('quarter')}
              selected={timeRange === 'quarter'}
            >
              This Quarter
            </MenuItem>
            <MenuItem 
              onClick={() => handleTimeRangeChange('year')}
              selected={timeRange === 'year'}
            >
              This Year
            </MenuItem>
          </Menu>
          
          <Tooltip title="Refresh Data">
            <IconButton 
              color="primary" 
              onClick={handleRefresh}
              sx={{ animation: refreshing ? 'spin 1s linear infinite' : 'none' }}
            >
              <RefreshIcon />
            </IconButton>
          </Tooltip>
          
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <CalendarTodayIcon sx={{ fontSize: 20, color: theme.palette.grey[600] }} />
            <Typography variant="h6" sx={{ fontWeight: 500, color: theme.palette.grey[700] }}>
              {formatDateWithTimezone(getSingaporeNow())}
            </Typography>
          </Box>
        </Box>
      </Box>

      {/* Dashboard Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3, flexShrink: 0 }}>
        <Tabs 
          value={tabValue} 
          onChange={handleTabChange} 
          aria-label="dashboard tabs"
          textColor="primary"
          indicatorColor="primary"
        >
          <Tab label="Overview" />
          <Tab label="Containers" />
          <Tab label="Manifests" />
          <Tab label="Deliveries" />
          <Tab label="Analytics" />
        </Tabs>
      </Box>

      {/* Main Dashboard Content */}
      <Box sx={{
        flex: 1,
        minHeight: 0,
        overflow: 'auto',
        display: tabValue === 0 ? 'block' : 'flex',
        alignItems: 'stretch',
        justifyContent: 'stretch',
      }}>
        {/* Key Metrics Cards */}
        <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', sm: 'repeat(2, 1fr)', md: 'repeat(4, 1fr)' }, gap: 3, mb: 3 }}>
          {/* Total Containers */}
          <Card 
            elevation={2} 
            sx={{ 
              borderRadius: 2, 
              height: '100%',
              transition: 'transform 0.3s, box-shadow 0.3s',
              '&:hover': {
                transform: 'translateY(-4px)',
                boxShadow: 6,
              },
            }}
          >
            <CardContent sx={{ p: 2 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="subtitle1" color="text.secondary">Total Containers</Typography>
                <Avatar sx={{ bgcolor: alpha(theme.palette.primary.main, 0.1), width: 40, height: 40 }}>
                  <InventoryIcon color="primary" />
                </Avatar>
              </Box>
              <Typography variant="h4" fontWeight="bold">
                {dashboardData.totalContainers.toLocaleString()}
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                <Chip 
                  icon={<TrendingUpIcon fontSize="small" />} 
                  label={`+${dashboardData.weeklyGrowth}%`} 
                  size="small"
                  color="success"
                  sx={{ mr: 1 }}
                />
                <Typography variant="body2" color="text.secondary">
                  vs last period
                </Typography>
              </Box>
            </CardContent>
          </Card>

          {/* Total Manifests */}
          <Card 
            elevation={2} 
            sx={{ 
              borderRadius: 2, 
              height: '100%',
              transition: 'transform 0.3s, box-shadow 0.3s',
              '&:hover': {
                transform: 'translateY(-4px)',
                boxShadow: 6,
              },
            }}
          >
            <CardContent sx={{ p: 2 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="subtitle1" color="text.secondary">Total Manifests</Typography>
                <Avatar sx={{ bgcolor: alpha(theme.palette.info.main, 0.1), width: 40, height: 40 }}>
                  <LocalShippingIcon color="info" />
                </Avatar>
              </Box>
              <Typography variant="h4" fontWeight="bold">
                {dashboardData.totalManifests.toLocaleString()}
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                <Chip 
                  icon={<TrendingUpIcon fontSize="small" />} 
                  label="+8.2%" 
                  size="small"
                  color="success"
                  sx={{ mr: 1 }}
                />
                <Typography variant="body2" color="text.secondary">
                  vs last period
                </Typography>
              </Box>
            </CardContent>
          </Card>

          {/* Pending Deliveries */}
          <Card 
            elevation={2} 
            sx={{ 
              borderRadius: 2, 
              height: '100%',
              transition: 'transform 0.3s, box-shadow 0.3s',
              '&:hover': {
                transform: 'translateY(-4px)',
                boxShadow: 6,
              },
            }}
          >
            <CardContent sx={{ p: 2 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="subtitle1" color="text.secondary">Pending Deliveries</Typography>
                <Avatar sx={{ bgcolor: alpha(theme.palette.warning.main, 0.1), width: 40, height: 40 }}>
                  <WarningIcon color="warning" />
                </Avatar>
              </Box>
              <Typography variant="h4" fontWeight="bold">
                {dashboardData.pendingDeliveries}
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                <Chip 
                  icon={<TrendingDownIcon fontSize="small" />} 
                  label="-3.5%" 
                  size="small"
                  color="success"
                  sx={{ mr: 1 }}
                />
                <Typography variant="body2" color="text.secondary">
                  vs last period
                </Typography>
              </Box>
            </CardContent>
          </Card>

          {/* Delivery Success Rate */}
          <Card 
            elevation={2} 
            sx={{ 
              borderRadius: 2, 
              height: '100%',
              transition: 'transform 0.3s, box-shadow 0.3s',
              '&:hover': {
                transform: 'translateY(-4px)',
                boxShadow: 6,
              },
            }}
          >
            <CardContent sx={{ p: 2 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="subtitle1" color="text.secondary">Success Rate</Typography>
                <Avatar sx={{ bgcolor: alpha(theme.palette.success.main, 0.1), width: 40, height: 40 }}>
                  <CheckCircleIcon color="success" />
                </Avatar>
              </Box>
              <Typography variant="h4" fontWeight="bold">
                {dashboardData.deliverySuccess}%
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                <Chip 
                  icon={<TrendingUpIcon fontSize="small" />} 
                  label="+0.8%" 
                  size="small"
                  color="success"
                  sx={{ mr: 1 }}
                />
                <Typography variant="body2" color="text.secondary">
                  vs last period
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Box>

        {/* Charts Row 1 */}
        <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', lg: '8fr 4fr' }, gap: 3, mb: 3 }}>
          {/* Line Chart */}
          <Card elevation={2} sx={{ borderRadius: 2 }}>
            <CardHeader 
              title="Logistics Performance" 
              action={
                <IconButton aria-label="settings" onClick={handleMenuClick}>
                  <MoreVertIcon />
                </IconButton>
              }
            />
            <Menu
              anchorEl={anchorEl}
              open={Boolean(anchorEl)}
              onClose={handleMenuClose}
            >
              <MenuItem onClick={handleMenuClose}>Export as PNG</MenuItem>
              <MenuItem onClick={handleMenuClose}>Export as CSV</MenuItem>
              <MenuItem onClick={handleMenuClose}>View Full Report</MenuItem>
            </Menu>
            <Divider />
            <CardContent sx={{ height: 360, p: 2 }}>
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={lineChartData}
                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" stroke={alpha(theme.palette.text.secondary, 0.2)} />
                  <XAxis dataKey="month" stroke={theme.palette.text.secondary} />
                  <YAxis stroke={theme.palette.text.secondary} />
                  <RechartsTooltip />
                  <Legend />
                  <Line 
                    type="monotone" 
                    dataKey="containers" 
                    stroke={theme.palette.primary.main} 
                    activeDot={{ r: 8 }} 
                    strokeWidth={2}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="manifests" 
                    stroke={theme.palette.secondary.main} 
                    strokeWidth={2}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="deliveries" 
                    stroke={theme.palette.success.main} 
                    strokeWidth={2}
                  />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Pie Chart */}
          <Card elevation={2} sx={{ borderRadius: 2 }}>
            <CardHeader title="Delivery Status Distribution" />
            <Divider />
            <CardContent sx={{ height: 360, p: 2 }}>
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={pieChartData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={120}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  >
                    {pieChartData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <RechartsTooltip />
                </PieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Box>

        {/* Charts Row 2 */}
        <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', md: 'repeat(2, 1fr)' }, gap: 3 }}>
          {/* Area Chart */}
          <Card elevation={2} sx={{ borderRadius: 2 }}>
            <CardHeader title="Monthly Delivery Trend" />
            <Divider />
            <CardContent sx={{ height: 300, p: 2 }}>
              <ResponsiveContainer width="100%" height="100%">
                <AreaChart
                  data={areaChartData}
                  margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
                >
                  <CartesianGrid strokeDasharray="3 3" stroke={alpha(theme.palette.text.secondary, 0.2)} />
                  <XAxis dataKey="date" stroke={theme.palette.text.secondary} />
                  <YAxis stroke={theme.palette.text.secondary} />
                  <RechartsTooltip />
                  <Area 
                    type="monotone" 
                    dataKey="value" 
                    stroke={theme.palette.primary.main}
                    fill={alpha(theme.palette.primary.main, 0.2)}
                  />
                </AreaChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Bar Chart */}
          <Card elevation={2} sx={{ borderRadius: 2 }}>
            <CardHeader title="Container Volume by Month" />
            <Divider />
            <CardContent sx={{ height: 300, p: 2 }}>
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={barChartData}
                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" stroke={alpha(theme.palette.text.secondary, 0.2)} />
                  <XAxis dataKey="name" stroke={theme.palette.text.secondary} />
                  <YAxis stroke={theme.palette.text.secondary} />
                  <RechartsTooltip />
                  <Bar 
                    dataKey="value" 
                    fill={theme.palette.info.main} 
                    radius={[4, 4, 0, 0]}
                  />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Box>
      </Box>

      {/* Other tabs content would go here */}
      {tabValue !== 0 && (
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%', width: '100%' }}>
          <Typography variant="h5" color="text.secondary">
            {["Containers", "Manifests", "Deliveries", "Analytics"][tabValue - 1]} content coming soon
          </Typography>
        </Box>
      )}
    </Box>
  );
};

export default ModernDashboard; 